#!/usr/bin/env python3
"""
架构验证测试脚本 - 验证新的可辨识联合类型架构是否正常工作
"""

import asyncio
import sys
import os
import json

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.models.presentation_model import (
    TextElementSchema,
    KpiCardSchema,
    ChartBlueprintSchema,
    ImageElementSchema,
    Element,
    DetailedSlideBlueprintSchema,
    StructuredPresentationStyleSchema,
    ChartConfig
)

async def test_complete_architecture():
    """测试完整的架构流程"""
    print("🚀 开始测试新架构的完整流程")
    print("=" * 50)
    
    # === 1. 测试创建各种元素 ===
    print("=== 1. 测试创建各种元素 ===")
    
    # 创建文本元素
    text_element = TextElementSchema(
        type="title",
        content="中国房地产市场发展趋势分析",
        target_area="title_area",
        animation_style="fade-in-up"
    )
    print(f"✅ 文本元素: {text_element.type} - {text_element.content}")
    
    # 创建KPI卡片
    kpi_element = KpiCardSchema(
        type="kpi_card",
        title="市场增长率",
        value="15.2%",
        change="****%",
        icon_fontawesome_class="fa-solid fa-chart-line-up",
        target_area="kpi_card_1",
        animation_style="slide-in-left"
    )
    print(f"✅ KPI卡片: {kpi_element.title} - {kpi_element.value}")
    
    # 创建图表蓝图
    chart_config = ChartConfig(
        chart_canvas_id="chart_market_trend",
        chart_type="line",
        chart_js_data={
            "labels": ["2020", "2021", "2022", "2023", "2024E"],
            "datasets": [{
                "label": "房价指数",
                "data": [100, 110, 105, 98, 102],
                "borderColor": "#3b82f6",
                "backgroundColor": "rgba(59, 130, 246, 0.1)"
            }]
        },
        chart_js_options={
            "responsive": True,
            "maintainAspectRatio": False
        },
        chart_title="房价趋势指数",
        data_source_description="基于国家统计局数据"
    )
    
    chart_element = ChartBlueprintSchema(
        type="chart",
        title="房价趋势指数 (2020-2024E)",
        chart_type="line",
        data_fabrication_instruction="创建房价趋势指数图表，显示2020年基准100点，2021年达到峰值110点，之后逐步回调至2024年预期102点",
        final_chart_js_config=chart_config,
        target_area="chart_area_1",
        animation_style="zoom-in"
    )
    print(f"✅ 图表蓝图: {chart_element.title} - {chart_element.chart_type}")
    
    # 创建图片元素
    image_element = ImageElementSchema(
        type="image",
        generation_prompt="Modern residential buildings with glass facades at sunset, professional real estate photography style, warm lighting, urban skyline background",
        alt_text="现代住宅建筑群",
        target_area="image_area",
        animation_style="fade-in"
    )
    print(f"✅ 图片元素: {image_element.alt_text}")
    
    # === 2. 测试创建DetailedSlideBlueprintSchema ===
    print("\n=== 2. 测试创建详细蓝图 ===")
    
    blueprint = DetailedSlideBlueprintSchema(
        slide_number=1,
        layout_template_name="DataDashboardLayout",
        background_style_description="linear-gradient(135deg, #0f172a 0%, #1e293b 100%)",
        key_elements=[text_element, kpi_element, chart_element, image_element],
        speaker_notes="本张幻灯片展示了中国房地产市场的核心发展趋势。市场增长率为15.2%，相比上一季度增长3.8%，显示出稳健的复苏态势。房价趋势指数从2020年基准100点上升至2021年峰值110点后，经历了市场调整期，预计2024年将稳定在102点左右。这一趋势反映了政策调控的有效性和市场的理性回归。"
    )
    
    print(f"✅ 详细蓝图创建成功:")
    print(f"   - 幻灯片编号: {blueprint.slide_number}")
    print(f"   - 布局模板: {blueprint.layout_template_name}")
    print(f"   - 元素数量: {len(blueprint.key_elements)}")
    for i, element in enumerate(blueprint.key_elements):
        print(f"     {i+1}. {element.type}")
    
    # === 3. 测试JSON序列化/反序列化 ===
    print("\n=== 3. 测试JSON序列化/反序列化 ===")
    
    # 序列化
    blueprint_json = blueprint.model_dump_json(indent=2)
    print(f"✅ JSON序列化成功，长度: {len(blueprint_json)} 字符")
    
    # 反序列化
    blueprint_dict = json.loads(blueprint_json)
    blueprint_restored = DetailedSlideBlueprintSchema.model_validate(blueprint_dict)
    print(f"✅ JSON反序列化成功")
    print(f"   - 幻灯片编号: {blueprint_restored.slide_number}")
    print(f"   - 元素数量: {len(blueprint_restored.key_elements)}")
    
    # === 4. 测试可辨识联合类型工作原理 ===
    print("\n=== 4. 测试可辨识联合类型 ===")
    
    for i, element in enumerate(blueprint_restored.key_elements):
        print(f"元素 {i+1}: type='{element.type}', 实际类型={type(element).__name__}")
        if isinstance(element, TextElementSchema):
            print(f"   - 文本内容: {element.content[:30]}...")
        elif isinstance(element, KpiCardSchema):
            print(f"   - KPI标题: {element.title}, 数值: {element.value}")
        elif isinstance(element, ChartBlueprintSchema):
            print(f"   - 图表标题: {element.title}, 类型: {element.chart_type}")
        elif isinstance(element, ImageElementSchema):
            print(f"   - 图片描述: {element.alt_text}")
    
    # === 5. 测试指令合成（模拟） ===
    print("\n=== 5. 测试指令合成模拟 ===")
    
    # 模拟指令合成过程
    prompt_parts = [
        f"# 指令：为幻灯片 {blueprint.slide_number} 生成HTML代码",
        f"## 布局模板: {blueprint.layout_template_name}",
        f"## 背景样式: {blueprint.background_style_description}",
        "\n## 核心元素:"
    ]
    
    for i, element in enumerate(blueprint.key_elements):
        element_prompt = f"\n### 元素 {i+1}: {element.type.upper()}"
        if isinstance(element, TextElementSchema):
            element_prompt += f"\n- 内容: '{element.content}'\n- 目标区域: '{element.target_area}'"
        elif isinstance(element, KpiCardSchema):
            element_prompt += f"\n- 标题: '{element.title}'\n- 数值: '{element.value}'\n- 变化: '{element.change}'"
        elif isinstance(element, ChartBlueprintSchema):
            element_prompt += f"\n- 标题: '{element.title}'\n- 类型: {element.chart_type}"
        elif isinstance(element, ImageElementSchema):
            element_prompt += f"\n- 描述: '{element.alt_text}'\n- 目标区域: '{element.target_area}'"
        prompt_parts.append(element_prompt)
    
    synthesized_prompt = "\n".join(prompt_parts)
    print(f"✅ 指令合成成功，长度: {len(synthesized_prompt)} 字符")
    print(f"指令预览:\n{synthesized_prompt[:200]}...\n")
    
    print("🎉 所有测试通过！新架构工作正常")
    print("✅ 可辨识联合类型实现成功")
    print("✅ 数据模型一致性验证通过")
    print("✅ JSON序列化/反序列化正常")
    print("✅ 指令合成流程可行")

if __name__ == "__main__":
    asyncio.run(test_complete_architecture()) 