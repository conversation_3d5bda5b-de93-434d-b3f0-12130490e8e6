{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\LeftNav.js\",\n  _s = $RefreshSig$();\n// frontend/src/components/LeftNav.js\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom'; // Import useNavigate and useLocation\nimport { FaBrain, FaFilePowerpoint, FaTable, FaComments, FaImage, FaVideo, FaThLarge, FaCloud, FaUserCircle, FaCog } from 'react-icons/fa'; // 移除未使用的图标\nimport Logo from './Logo';\nimport ChatHistoryList from './ChatHistoryList';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LeftNav = ({\n  isTaskListOpen,\n  onToggleTaskList,\n  historyItems,\n  currentProjectId,\n  onSelectHistoryItem,\n  onDeleteHistoryItem,\n  isLoadingHistory\n}) => {\n  _s();\n  const [activeItem, setActiveItem] = useState('');\n  const navigate = useNavigate(); // Initialize useNavigate\n  const location = useLocation(); // Get current location\n\n  const navItems = [\n  // { id: '首页', name: '首页', icon: <FaHome size={20} />, path: '/' }, // Removed, main app handles project history\n  {\n    id: '超级智能体',\n    name: '超级智能体',\n    icon: /*#__PURE__*/_jsxDEV(FaBrain, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 41\n    }, this)\n  }, {\n    id: 'AI幻灯片',\n    name: 'AI幻灯片',\n    icon: /*#__PURE__*/_jsxDEV(FaFilePowerpoint, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 41\n    }, this),\n    path: '/'\n  },\n  // Updated path to root\n  {\n    id: 'AI聊天',\n    name: 'AI聊天',\n    icon: /*#__PURE__*/_jsxDEV(FaComments, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 39\n    }, this),\n    path: '/chat'\n  },\n  // Updated path\n  {\n    id: 'AI表格',\n    name: 'AI表格',\n    icon: /*#__PURE__*/_jsxDEV(FaTable, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 39\n    }, this)\n  }, {\n    id: '图片工作室',\n    name: '图片工作室',\n    icon: /*#__PURE__*/_jsxDEV(FaImage, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 41\n    }, this)\n  }, {\n    id: '视频生成',\n    name: '视频生成',\n    icon: /*#__PURE__*/_jsxDEV(FaVideo, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 39\n    }, this)\n  }, {\n    id: '所有智能体',\n    name: '所有智能体',\n    icon: /*#__PURE__*/_jsxDEV(FaThLarge, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 41\n    }, this)\n  }, {\n    id: 'AI云盘',\n    name: 'AI 云盘',\n    icon: /*#__PURE__*/_jsxDEV(FaCloud, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 40\n    }, this)\n  }, {\n    id: '我',\n    name: '我',\n    icon: /*#__PURE__*/_jsxDEV(FaUserCircle, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 33\n    }, this)\n  }];\n\n  // 根据当前路径设置活动项\n  useEffect(() => {\n    const currentPath = location.pathname;\n    const currentItem = navItems.find(item => item.path === currentPath);\n    if (currentItem) {\n      setActiveItem(currentItem.id);\n    } else if (currentPath === '/') {\n      setActiveItem('AI幻灯片');\n    }\n  }, [location.pathname, navItems]);\n  const handleNavigate = (path, itemId) => {\n    setActiveItem(itemId);\n    navigate(path);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen flex flex-col bg-white border-r border-gray-200 w-60\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center h-16 px-4 border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center pl-4\",\n        children: /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/\",\n          className: \"flex items-center\",\n          onClick: e => {\n            e.preventDefault();\n            navigate('/');\n          },\n          children: [/*#__PURE__*/_jsxDEV(Logo, {\n            size: 32\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-bold text-2xl text-gray-800 ml-2\",\n            children: \"TikTodo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"flex-grow p-2 space-y-1 custom-scrollbar overflow-y-auto\",\n      children: [navItems.map(item => /*#__PURE__*/_jsxDEV(\"a\", {\n        href: item.path || '#' // Use item.path if available, otherwise '#'\n        ,\n        onClick: e => {\n          e.preventDefault(); // Prevent default link behavior\n          if (item.path) {\n            handleNavigate(item.path, item.id);\n          } else {\n            setActiveItem(item.id); // For items without a path\n          }\n        },\n        className: `flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors duration-150\n              ${activeItem === item.id ? 'bg-tiktodo-blue-light text-tiktodo-blue' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}\n            `,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mr-3\",\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this), item.name]\n      }, item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-200 my-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), (location.pathname === '/chat' || location.pathname === '/') && /*#__PURE__*/_jsxDEV(ChatHistoryList, {\n        historyItems: historyItems,\n        currentProjectId: currentProjectId,\n        onSelectHistoryItem: onSelectHistoryItem,\n        onDeleteHistoryItem: onDeleteHistoryItem,\n        isLoading: isLoadingHistory\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"flex items-center px-3 py-2.5 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150 w-full\",\n        children: [/*#__PURE__*/_jsxDEV(FaCog, {\n          size: 20,\n          className: \"mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), \"\\u8BBE\\u7F6E\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(LeftNav, \"3cUpDwxN5XZFfa5trV8c9K9WSG4=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = LeftNav;\nexport default LeftNav;\nvar _c;\n$RefreshReg$(_c, \"LeftNav\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "FaBrain", "FaFilePowerpoint", "FaTable", "FaComments", "FaImage", "FaVideo", "FaThLarge", "FaCloud", "FaUserCircle", "FaCog", "Logo", "ChatHistoryList", "jsxDEV", "_jsxDEV", "LeftNav", "isTaskListOpen", "onToggleTaskList", "historyItems", "currentProjectId", "onSelectHistoryItem", "onDeleteHistoryItem", "isLoadingHistory", "_s", "activeItem", "setActiveItem", "navigate", "location", "navItems", "id", "name", "icon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "currentPath", "pathname", "currentItem", "find", "item", "handleNavigate", "itemId", "className", "children", "href", "onClick", "e", "preventDefault", "map", "isLoading", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/LeftNav.js"], "sourcesContent": ["// frontend/src/components/LeftNav.js\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom'; // Import useNavigate and useLocation\nimport {\n  FaBrain, FaFilePowerpoint, FaTable, FaComments, FaImage, FaVideo, FaThLarge, FaCloud, FaUserCircle, FaCog\n} from 'react-icons/fa'; // 移除未使用的图标\nimport Logo from './Logo';\nimport ChatHistoryList from './ChatHistoryList';\n\nconst LeftNav = ({ \n  isTaskListOpen, \n  onToggleTaskList, \n  historyItems, \n  currentProjectId, \n  onSelectHistoryItem, \n  onDeleteHistoryItem, \n  isLoadingHistory \n}) => {\n  const [activeItem, setActiveItem] = useState('');\n  const navigate = useNavigate(); // Initialize useNavigate\n  const location = useLocation(); // Get current location\n\n  const navItems = [\n    // { id: '首页', name: '首页', icon: <FaHome size={20} />, path: '/' }, // Removed, main app handles project history\n    { id: '超级智能体', name: '超级智能体', icon: <FaBrain size={20} /> },\n    { id: 'AI幻灯片', name: 'AI幻灯片', icon: <FaFilePowerpoint size={20} />, path: '/' }, // Updated path to root\n    { id: 'AI聊天', name: 'AI聊天', icon: <FaComments size={20} />, path: '/chat' }, // Updated path\n    { id: 'AI表格', name: 'AI表格', icon: <FaTable size={20} /> },\n    { id: '图片工作室', name: '图片工作室', icon: <FaImage size={20} /> },\n    { id: '视频生成', name: '视频生成', icon: <FaVideo size={20} /> },\n    { id: '所有智能体', name: '所有智能体', icon: <FaThLarge size={20} /> },\n    { id: 'AI云盘', name: 'AI 云盘', icon: <FaCloud size={20} /> },\n    { id: '我', name: '我', icon: <FaUserCircle size={20} /> },\n  ];\n\n  // 根据当前路径设置活动项\n  useEffect(() => {\n    const currentPath = location.pathname;\n    const currentItem = navItems.find(item => item.path === currentPath);\n    if (currentItem) {\n      setActiveItem(currentItem.id);\n    } else if (currentPath === '/') {\n      setActiveItem('AI幻灯片');\n    }\n  }, [location.pathname, navItems]);\n\n  const handleNavigate = (path, itemId) => {\n    setActiveItem(itemId);\n    navigate(path);\n  };\n\n  return (\n    <div className=\"h-screen flex flex-col bg-white border-r border-gray-200 w-60\">\n      {/* Main Left Navigation */}\n      <div className=\"flex items-center h-16 px-4 border-b border-gray-200\">\n        {/* TikTodo Logo - Clickable to navigate to home page */}\n        <div className=\"flex items-center pl-4\">\n          <a href=\"/\" className=\"flex items-center\" onClick={(e) => { e.preventDefault(); navigate('/'); }}>\n            <Logo size={32} />\n            <span className=\"font-bold text-2xl text-gray-800 ml-2\">TikTodo</span>\n          </a>\n        </div>\n      </div>\n      <nav className=\"flex-grow p-2 space-y-1 custom-scrollbar overflow-y-auto\">\n        {navItems.map((item) => (\n          <a\n            key={item.id}\n            href={item.path || '#'} // Use item.path if available, otherwise '#'\n            onClick={(e) => {\n                e.preventDefault(); // Prevent default link behavior\n                if (item.path) {\n                    handleNavigate(item.path, item.id);\n                } else {\n                    setActiveItem(item.id); // For items without a path\n                }\n            }}\n            className={`flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors duration-150\n              ${\n                activeItem === item.id\n                  ? 'bg-tiktodo-blue-light text-tiktodo-blue'\n                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'\n              }\n            `}\n          >\n            <span className=\"mr-3\">{item.icon}</span>\n            {item.name}\n          </a>\n        ))}\n        \n        {/* 在导航和聊天历史记录之间添加分割线 */}\n        <div className=\"border-t border-gray-200 my-2\"></div>\n        \n        {/* 在首页和聊天页面都显示历史记录 */}\n        {(location.pathname === '/chat' || location.pathname === '/') && (\n          <ChatHistoryList \n            historyItems={historyItems}\n            currentProjectId={currentProjectId}\n            onSelectHistoryItem={onSelectHistoryItem}\n            onDeleteHistoryItem={onDeleteHistoryItem}\n            isLoading={isLoadingHistory}\n          />\n        )}\n      </nav>\n      <div className=\"p-4 border-t border-gray-200\">\n        <button\n          className=\"flex items-center px-3 py-2.5 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150 w-full\"\n        >\n          <FaCog size={20} className=\"mr-3\" />\n          设置\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default LeftNav; "], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB,CAAC,CAAC;AAC7D,SACEC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEC,YAAY,EAAEC,KAAK,QACpG,gBAAgB,CAAC,CAAC;AACzB,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,OAAO,GAAGA,CAAC;EACfC,cAAc;EACdC,gBAAgB;EAChBC,YAAY;EACZC,gBAAgB;EAChBC,mBAAmB;EACnBC,mBAAmB;EACnBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM6B,QAAQ,GAAG3B,WAAW,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM4B,QAAQ,GAAG3B,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEhC,MAAM4B,QAAQ,GAAG;EACf;EACA;IAAEC,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEjB,OAAA,CAACb,OAAO;MAAC+B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC3D;IAAEP,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEjB,OAAA,CAACZ,gBAAgB;MAAC8B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAI,CAAC;EAAE;EACjF;IAAER,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,eAAEjB,OAAA,CAACV,UAAU;MAAC4B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAQ,CAAC;EAAE;EAC7E;IAAER,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,eAAEjB,OAAA,CAACX,OAAO;MAAC6B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACzD;IAAEP,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEjB,OAAA,CAACT,OAAO;MAAC2B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC3D;IAAEP,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,eAAEjB,OAAA,CAACR,OAAO;MAAC0B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACzD;IAAEP,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEjB,OAAA,CAACP,SAAS;MAACyB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC7D;IAAEP,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEjB,OAAA,CAACN,OAAO;MAACwB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC1D;IAAEP,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,eAAEjB,OAAA,CAACL,YAAY;MAACuB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CACzD;;EAED;EACAtC,SAAS,CAAC,MAAM;IACd,MAAMwC,WAAW,GAAGX,QAAQ,CAACY,QAAQ;IACrC,MAAMC,WAAW,GAAGZ,QAAQ,CAACa,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACL,IAAI,KAAKC,WAAW,CAAC;IACpE,IAAIE,WAAW,EAAE;MACff,aAAa,CAACe,WAAW,CAACX,EAAE,CAAC;IAC/B,CAAC,MAAM,IAAIS,WAAW,KAAK,GAAG,EAAE;MAC9Bb,aAAa,CAAC,OAAO,CAAC;IACxB;EACF,CAAC,EAAE,CAACE,QAAQ,CAACY,QAAQ,EAAEX,QAAQ,CAAC,CAAC;EAEjC,MAAMe,cAAc,GAAGA,CAACN,IAAI,EAAEO,MAAM,KAAK;IACvCnB,aAAa,CAACmB,MAAM,CAAC;IACrBlB,QAAQ,CAACW,IAAI,CAAC;EAChB,CAAC;EAED,oBACEvB,OAAA;IAAK+B,SAAS,EAAC,+DAA+D;IAAAC,QAAA,gBAE5EhC,OAAA;MAAK+B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eAEnEhC,OAAA;QAAK+B,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrChC,OAAA;UAAGiC,IAAI,EAAC,GAAG;UAACF,SAAS,EAAC,mBAAmB;UAACG,OAAO,EAAGC,CAAC,IAAK;YAAEA,CAAC,CAACC,cAAc,CAAC,CAAC;YAAExB,QAAQ,CAAC,GAAG,CAAC;UAAE,CAAE;UAAAoB,QAAA,gBAC/FhC,OAAA,CAACH,IAAI;YAACqB,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClBtB,OAAA;YAAM+B,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNtB,OAAA;MAAK+B,SAAS,EAAC,0DAA0D;MAAAC,QAAA,GACtElB,QAAQ,CAACuB,GAAG,CAAET,IAAI,iBACjB5B,OAAA;QAEEiC,IAAI,EAAEL,IAAI,CAACL,IAAI,IAAI,GAAI,CAAC;QAAA;QACxBW,OAAO,EAAGC,CAAC,IAAK;UACZA,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;UACpB,IAAIR,IAAI,CAACL,IAAI,EAAE;YACXM,cAAc,CAACD,IAAI,CAACL,IAAI,EAAEK,IAAI,CAACb,EAAE,CAAC;UACtC,CAAC,MAAM;YACHJ,aAAa,CAACiB,IAAI,CAACb,EAAE,CAAC,CAAC,CAAC;UAC5B;QACJ,CAAE;QACFgB,SAAS,EAAE;AACvB,gBACgBrB,UAAU,KAAKkB,IAAI,CAACb,EAAE,GAClB,yCAAyC,GACzC,qDAAqD;AACzE,aACc;QAAAiB,QAAA,gBAEFhC,OAAA;UAAM+B,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAEJ,IAAI,CAACX;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACxCM,IAAI,CAACZ,IAAI;MAAA,GAnBLY,IAAI,CAACb,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBX,CACJ,CAAC,eAGFtB,OAAA;QAAK+B,SAAS,EAAC;MAA+B;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAGpD,CAACT,QAAQ,CAACY,QAAQ,KAAK,OAAO,IAAIZ,QAAQ,CAACY,QAAQ,KAAK,GAAG,kBAC1DzB,OAAA,CAACF,eAAe;QACdM,YAAY,EAAEA,YAAa;QAC3BC,gBAAgB,EAAEA,gBAAiB;QACnCC,mBAAmB,EAAEA,mBAAoB;QACzCC,mBAAmB,EAAEA,mBAAoB;QACzC+B,SAAS,EAAE9B;MAAiB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNtB,OAAA;MAAK+B,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3ChC,OAAA;QACE+B,SAAS,EAAC,wJAAwJ;QAAAC,QAAA,gBAElKhC,OAAA,CAACJ,KAAK;UAACsB,IAAI,EAAE,EAAG;UAACa,SAAS,EAAC;QAAM;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEtC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACb,EAAA,CAxGIR,OAAO;EAAA,QAUMhB,WAAW,EACXC,WAAW;AAAA;AAAAqD,EAAA,GAXxBtC,OAAO;AA0Gb,eAAeA,OAAO;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}