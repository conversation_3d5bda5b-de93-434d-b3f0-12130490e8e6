{"version": 3, "file": "static/js/95.ff217aad.chunk.js", "mappings": "mMAIA,MAmJA,EAnJsBA,IASf,IATgB,SACrBC,EAAQ,WACRC,EAAU,YACVC,EAAc,EAAC,OACfC,EAAM,aACNC,EAAY,eACZC,EAAc,gBACdC,EAAe,oBACfC,GACDR,EACC,MAAOS,EAAaC,IAAkBC,EAAAA,EAAAA,WAAS,IACxCC,EAAYC,IAAiBF,EAAAA,EAAAA,WAAS,IACtCG,EAAUC,IAAeJ,EAAAA,EAAAA,WAAS,GAEnCK,EAAgB,CACpB,CAAEC,IAAK,MAAOC,MAAO,MAAOC,MAAMC,EAAAA,EAAAA,KAACC,EAAAA,IAAS,CAACC,UAAU,UACvD,CAAEL,IAAK,OAAQC,MAAO,OAAQC,MAAMC,EAAAA,EAAAA,KAACG,EAAAA,IAAgB,CAACD,UAAU,WAG5DE,EAAiB,CACrB,CAAEP,IAAK,WAAYC,MAAO,2BAAQC,MAAMC,EAAAA,EAAAA,KAACK,EAAAA,IAAM,CAACH,UAAU,UAC1D,CAAEL,IAAK,aAAcC,MAAO,6CAAWC,MAAMC,EAAAA,EAAAA,KAACM,EAAAA,IAAiB,CAACJ,UAAU,WAGtEK,EAAc,CAClB,CAAEV,IAAK,YAAaC,MAAO,2CAAmBC,MAAMC,EAAAA,EAAAA,KAACM,EAAAA,IAAiB,CAACJ,UAAU,WAUnF,OACEM,EAAAA,EAAAA,MAAA,OAAKN,UAAU,gBAAeO,SAAA,EAC5BD,EAAAA,EAAAA,MAAA,OAAKN,UAAU,wBAAuBO,SAAA,EACpCT,EAAAA,EAAAA,KAAA,UACEU,QAASzB,EACTiB,UAAU,8FACVS,MAAM,iCACN,aAAW,iCAAOF,UAElBT,EAAAA,EAAAA,KAACY,EAAAA,IAAW,OAEdZ,EAAAA,EAAAA,KAACa,EAAAA,IAAS,CAACX,UAAU,gCACrBF,EAAAA,EAAAA,KAAA,QAAME,UAAU,qBAAqBS,MAAO9B,EAAS4B,SAAE5B,KACvD2B,EAAAA,EAAAA,MAAA,QAAMN,UAAU,kBAAiBO,SAAA,CAC9B1B,EAAY,MAAID,EAAW,gBAIhC0B,EAAAA,EAAAA,MAAA,OAAKN,UAAU,iBAAgBO,SAAA,EAC7BD,EAAAA,EAAAA,MAAA,UACEE,QAAS1B,EACT8B,SAAyB,IAAfhC,EACVoB,UAAU,gBACV,aAAW,iCAAOO,SAAA,EAElBT,EAAAA,EAAAA,KAACe,EAAAA,IAAM,CAACb,UAAU,SAAS,mBAI7BM,EAAAA,EAAAA,MAAA,OAAKN,UAAU,WAAUO,SAAA,EACvBD,EAAAA,EAAAA,MAAA,UACEE,QAAUM,IAAOA,EAAEC,kBAAmB3B,GAAe4B,IAAMA,IAAIzB,GAAc,GAAQE,GAAY,IACjGmB,SAAyB,IAAfhC,EACVoB,UAAU,gBACV,aAAW,eACX,gBAAeb,EAAYoB,SAAA,EAE3BT,EAAAA,EAAAA,KAACmB,EAAAA,IAAO,CAACjB,UAAU,SAAS,cAE7Bb,IACCW,EAAAA,EAAAA,KAAA,OAAKE,UAAU,mBAAkBO,SAC9BL,EAAegB,KAAIC,IAClBb,EAAAA,EAAAA,MAAA,KAEEc,KAAK,IACLZ,QAAUM,IAAQA,EAAEO,iBAAkBpC,EAAgBkC,EAAIxB,KAAMP,GAAe,IAC/E,aAAY+B,EAAIvB,MAAMW,SAAA,CAErBY,EAAItB,KAAK,IAAEsB,EAAIvB,QALXuB,EAAIxB,aAanBW,EAAAA,EAAAA,MAAA,OAAKN,UAAU,WAAUO,SAAA,EACvBD,EAAAA,EAAAA,MAAA,UACEE,QAAUM,IAAOA,EAAEC,kBAAmBxB,GAAcyB,IAAMA,IAAI5B,GAAe,GAAQK,GAAY,IACjGmB,SAAyB,IAAfhC,EACVoB,UAAU,gBACV,aAAW,eACX,gBAAeV,EAAWiB,SAAA,EAE1BT,EAAAA,EAAAA,KAACwB,EAAAA,IAAU,CAACtB,UAAU,SAAS,YAAQF,EAAAA,EAAAA,KAAA,QAAMyB,MAAO,CAACC,SAAU,SAAUC,gBAAiB,UAAWC,MAAO,QAASC,QAAS,UAAWC,aAAc,MAAOC,WAAY,IAAKC,SAAU,WAAYC,IAAK,QAAQxB,SAAC,YAEpNjB,IACCQ,EAAAA,EAAAA,KAAA,OAAKE,UAAU,mBAAkBO,SAC9Bb,EAAcwB,KAAIC,IACjBb,EAAAA,EAAAA,MAAA,KAEEc,KAAK,IACLZ,QAAUM,IAAQA,EAAEO,iBAAkBrC,EAAemC,EAAIxB,KAAMJ,GAAc,IAC7E,kCAAAyC,OAAkBb,EAAIvB,OAAQW,SAAA,CAE7BY,EAAItB,KAAK,IAAEsB,EAAIvB,QALXuB,EAAIxB,aAanBW,EAAAA,EAAAA,MAAA,OAAKN,UAAU,WAAUO,SAAA,EACvBT,EAAAA,EAAAA,KAAA,UACEU,QAAUM,IAAOA,EAAEC,kBAAmBtB,GAAYuB,IAAMA,IAAI5B,GAAe,GAAQG,GAAc,IACjGS,UAAU,gBACV,aAAW,2BACX,gBAAeR,EAASe,UAExBT,EAAAA,EAAAA,KAACmC,EAAAA,IAAW,MAEbzC,IACCM,EAAAA,EAAAA,KAAA,OAAKE,UAAU,mBAAkBO,SAC9BF,EAAYa,KAAIC,IACfb,EAAAA,EAAAA,MAAA,KAEEc,KAAK,IACLZ,QAAUM,IAAQA,EAAEO,iBAAkBnC,EAAoBiC,EAAIxB,KAAMF,GAAY,IAChF,aAAY0B,EAAIvB,MAAMW,SAAA,CAErBY,EAAItB,KAAK,IAAEsB,EAAIvB,QALXuB,EAAIxB,mB,cCnI3B,MAAMuC,GAAYC,EAAAA,EAAAA,aAAW,CAACC,EAAOC,KACnC,MAAOC,EAAaC,IAAkBlD,EAAAA,EAAAA,WAAS,GA4G/C,OA1GAmD,EAAAA,EAAAA,qBAAoBH,GAAK,MACvBI,YAAaC,SACJ,IAAIC,SAAQ,CAACC,EAASC,KAC3B,IACEN,GAAe,GACfO,QAAQC,IAAI,iCAAkCC,EAAOC,OAAQ,UAU7DC,YAAW,KACT,8BACGC,MAAKzE,IAAyB,IAAtB0E,QAASC,GAAO3E,EACvB,mCACGyE,MAAKG,IAA+B,IAA5BF,QAASG,GAAaD,EAO7B,MAAME,EAAM,IAAIH,EAAM,CACpBI,YAAa,YACbC,KAAM,OAGahB,WACnB,IAEEI,QAAQC,IAAI,mCAGZ,IAAK,IAAIY,EAAI,EAAGA,EAAIX,EAAOC,OAAQU,IAAK,CAClCA,EAAI,GAAGH,EAAII,UAUf,MAOMC,EAPa,CACjBC,MAAO,KACPC,OAAQ,IACRC,UAAWA,IAAM,uCAIQA,UAAU,aACrCR,EAAIS,SACFJ,EACA,MACA,EAAG,EACHL,EAAIU,SAASC,SAASC,WACtBZ,EAAIU,SAASC,SAASE,aAIxBvB,QAAQC,IAAI,mBAADf,OAAoB2B,EAAE,EAAC,KAAA3B,OAAIgB,EAAOC,QAC/C,CAGAO,EAAIc,KAAK,GAADtC,OAAII,EAAMmC,UAAY,eAAc,SAC5CzB,QAAQC,IAAI,wBACZR,GAAe,GACfK,GACF,CAAE,MAAO4B,GACP1B,QAAQ0B,MAAM,2BAA4BA,GAC1CjC,GAAe,GACfM,EAAO2B,EACT,GAIFC,MAEDC,OAAMC,IACL7B,QAAQ0B,MAAM,8BAA+BG,GAC7CpC,GAAe,GACfM,EAAO8B,SAGZD,OAAMC,IACL7B,QAAQ0B,MAAM,wBAAyBG,GACvCpC,GAAe,GACfM,EAAO8B,QAEV,IAEL,CAAE,MAAOH,GACP1B,QAAQ0B,MAAM,oBAAqBA,GACnCjC,GAAe,GACfM,EAAO2B,EACT,SAMFlC,GAEAhC,EAAAA,EAAAA,MAAA,OAAKN,UAAU,4FAA2FO,SAAA,EACxGT,EAAAA,EAAAA,KAAA,OAAKE,UAAU,qFACfF,EAAAA,EAAAA,KAAA,QAAME,UAAU,wBAAuBO,SAAC,uCAMvC,QAGT2B,EAAU0C,YAAc,YAExB,UCwFA,EAjNwBC,KACtB,MAAMC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OAETjC,OAAQkC,EAAkB,GAAE,aAAEC,EAAe,EAAC,kBAAEC,EAAoB,4BAAWN,EAASO,OAAS,CAAC,GAEnGrC,EAAQsC,IAAajG,EAAAA,EAAAA,UAAS6F,IAC9B5C,EAAaC,IAAkBlD,EAAAA,EAAAA,WAAS,IACxCkG,EAAaC,IAAkBnG,EAAAA,EAAAA,UAAS,KACxCoG,EAAmBC,IAAwBrG,EAAAA,EAAAA,UAAS8F,GAErDQ,GAAeC,EAAAA,EAAAA,QAAO,MACtBC,GAAuBD,EAAAA,EAAAA,QAAO,OAEpCE,EAAAA,EAAAA,YAAU,KACHZ,GAA8C,IAA3BA,EAAgBjC,QAGtC+B,EAAS,IAAK,CAAEe,SAAS,IAE3BT,EAAUJ,GAGVc,SAASvF,MAAQ2E,GAAqB,6BACrC,CAACF,EAAiBE,EAAmBJ,IAExC,MAAMiB,GAAkBC,EAAAA,EAAAA,cAAY,KAClCR,GAAqBS,GAAQC,KAAKC,IAAI,EAAGF,EAAO,OAC/C,IAEGG,GAAkBJ,EAAAA,EAAAA,cAAY,KAClCR,GAAqBS,GAAQC,KAAKG,IAAIvD,EAAOC,OAAS,EAAGkD,EAAO,OAC/D,CAACnD,IAEEwD,EAAyBA,KAC7BxB,EAAS,OAGXc,EAAAA,EAAAA,YAAU,KACR,MAAMW,EAAiB3F,IACP,eAAVA,EAAEnB,KAAkC,aAAVmB,EAAEnB,KAAgC,MAAVmB,EAAEnB,IACtD2G,IACmB,cAAVxF,EAAEnB,KAAiC,WAAVmB,EAAEnB,IACpCsG,IACmB,WAAVnF,EAAEnB,KACX6G,KAKJ,OADAE,OAAOC,iBAAiB,UAAWF,GAC5B,KACLC,OAAOE,oBAAoB,UAAWH,MAEvC,CAACH,EAAiBL,IAmErB,IAAKjD,GAA4B,IAAlBA,EAAOC,OACpB,OACE3C,EAAAA,EAAAA,MAAA,OAAKN,UAAU,qCAAoCO,SAAA,EACjDT,EAAAA,EAAAA,KAAC+G,EAAa,CACVlI,SAAUyG,EACVxG,WAAY,EACZE,OAAQA,OACRC,aAAcA,IAAMiG,EAAS,KAC7BhG,eAAgBA,OAChBC,gBAAiBA,OACjBC,oBAAqBA,UAEzBY,EAAAA,EAAAA,KAAA,OAAKE,UAAU,wDAAuDO,SAAC,sFAOxDyC,EAAOyC,GAE5B,OACEnF,EAAAA,EAAAA,MAAA,OAAKN,UAAU,mDAAkDO,SAAA,EAC/DT,EAAAA,EAAAA,KAAC+G,EAAa,CACZlI,SAAUyG,EACVxG,WAAYoE,EAAOC,OACnBpE,YAAa4G,EAAoB,EACjC3G,OA3FoBgI,KAExBJ,OAAOK,OAAOC,YAAY,CACxBC,KAAM,qBACNC,QAAS,CAAE/B,aAAcM,IACxB,MAuFC1G,aAAcyH,EACdxH,eArFe0D,UACnB,GAAKM,GAA4B,IAAlBA,EAAOC,QAQtB,GAHAV,GAAe,GACfiD,EAAe,IAEG,QAAd2B,GAAuBxB,EAAayB,QACtC,UAEQzB,EAAayB,QAAQ3E,YAAYO,GAEvCqE,MAAM,qCACR,CAAE,MAAO7C,GACP1B,QAAQ0B,MAAM,+BAAYA,GAC1BgB,EAAe,0EACf6B,MAAM,sCAAe7C,EAAM8C,SAAW,IACxC,CAAC,QACC/E,GAAe,EACjB,MACK,GAAkB,SAAd4E,EACT,IACEE,MAAM,8DACR,CAAC,QACC9E,GAAe,EACjB,OAzBE8E,MAAM,iEAoFNpI,gBAvDiBsI,IACrB,GAAkB,aAAdA,EACF,IAAK,IAADC,EAGF,MAAMC,GAA8B,QAAdD,EAAA1C,EAASO,aAAK,IAAAmC,OAAA,EAAdA,EAAgBE,YAAahB,OAAO5B,SAAS6C,OAAM,uBAAA3F,OAA0BoD,EAAkBW,QAAQ,OAAQ,MACrI6B,UAAUC,UAAUC,UAAUL,GAC3BtE,MAAK,IAAMkE,MAAM,2DAAgBI,KACjC/C,OAAMC,IACL7B,QAAQ0B,MAAM,wCAAWG,GACzB0C,MAAM,8CAAa1C,EAAI2C,SAAW,OAExC,CAAE,MAAO9C,GACP1B,QAAQ0B,MAAM,oDAAaA,GAC3B6C,MAAM,+DACR,GAyCEnI,oBArCqBqI,IACP,cAAdA,GACFF,MAAM,8EAuCL/E,IACChC,EAAAA,EAAAA,MAAA,OAAKN,UAAU,sBAAqBO,SAAA,EAClCT,EAAAA,EAAAA,KAAA,OAAKE,UAAU,0FACfF,EAAAA,EAAAA,KAAA,QAAAS,SAAM,2DAKTgF,IACCzF,EAAAA,EAAAA,KAAA,OAAKE,UAAU,kDAAiDO,UAC9DD,EAAAA,EAAAA,MAAA,OAAKN,UAAU,uEAAsEO,SAAA,EACnFT,EAAAA,EAAAA,KAAA,QAAME,UAAU,OAAMO,SAAC,kBACvBT,EAAAA,EAAAA,KAAA,QAAAS,SAAOgF,KACPzF,EAAAA,EAAAA,KAAA,UACEE,UAAU,uCACVQ,QAASA,IAAMgF,EAAe,IAAIjF,SACnC,eAQPT,EAAAA,EAAAA,KAAA,OACEuC,IAAKwD,EACL7F,UAAU,8CAA6CO,SAEtDyC,EAAO9B,KAAI,CAAC6G,EAAOC,KAClB1H,EAAAA,EAAAA,MAAA,OAEEN,UAAU,gBAAeO,SAAA,EAGzBD,EAAAA,EAAAA,MAAA,OAAKN,UAAU,4FAA2FO,SAAA,CACvGyH,EAAQ,EAAE,MAAIhF,EAAOC,WAGxBnD,EAAAA,EAAAA,KAAA,OAAKE,UAAU,SAAQO,UACrBT,EAAAA,EAAAA,KAACmI,EAAAA,EAAa,CACZC,QAASH,EAAMI,IAAE,uBAAAnG,OAA2BgG,GAC5CI,cAAeL,EAAMM,KACrBC,kBAAkB,QAZjBP,EAAMI,IAAE,qBAAAnG,OAAyBgG,SAmB5ClI,EAAAA,EAAAA,KAACoC,EAAS,CAACG,IAAKsD,EAAcpB,SAAUa,O", "sources": ["components/PlayerToolbar.js", "components/PdfExport.js", "views/SlidePlayerView.js"], "sourcesContent": ["import React, { useState } from 'react';\n// 引入 react-icons/fa 图标库\nimport { FaPlay, FaArrowLeft, FaShareAlt, FaDownload, FaEllipsisH, FaFilePdf, FaFilePowerpoint, FaCopy, FaExternalLinkAlt, FaGlobe, FaFileAlt } from 'react-icons/fa';\n\nconst PlayerToolbar = ({\n  fileName,\n  totalPages,\n  currentPage = 1,\n  onPlay, // 这个回调将用于触发 App.js 的全屏播放\n  onExitPlayer,\n  onExportSelect,\n  onPublishSelect,\n  onMoreOptionsSelect\n}) => {\n  const [showPublish, setShowPublish] = useState(false);\n  const [showExport, setShowExport] = useState(false);\n  const [showMore, setShowMore] = useState(false);\n\n  const exportOptions = [\n    { key: 'pdf', label: 'PDF', icon: <FaFilePdf className=\"mr-2\" /> },\n    { key: 'pptx', label: 'PPTX', icon: <FaFilePowerpoint className=\"mr-2\" /> }\n  ];\n\n  const publishOptions = [\n    { key: 'copyLink', label: '复制链接', icon: <FaCopy className=\"mr-2\" /> },\n    { key: 'openNewTab', label: '在新标签页打开', icon: <FaExternalLinkAlt className=\"mr-2\" /> }\n  ];\n  \n  const moreOptions = [\n    { key: 'editCanva', label: '在Canva和Figma中编辑', icon: <FaExternalLinkAlt className=\"mr-2\" /> },\n  ];\n\n  // Close dropdowns when clicking outside\n  const handleClickOutside = () => {\n    setShowPublish(false);\n    setShowExport(false);\n    setShowMore(false);\n  };\n\n  return (\n    <div className=\"viewer-header\">\n      <div className=\"project-title-section\">\n        <button \n          onClick={onExitPlayer} \n          className=\"p-2 mr-3 text-gray-600 hover:text-blue-500 rounded-full hover:bg-gray-100 transition-colors\" \n          title=\"返回编辑器\"\n          aria-label=\"返回编辑器\"\n        >\n          <FaArrowLeft />\n        </button>\n        <FaFileAlt className=\"text-gray-500 mr-2 text-lg\" />\n        <span className=\"project-title-text\" title={fileName}>{fileName}</span>\n        <span className=\"page-count-text\">\n          {currentPage} / {totalPages} pages\n        </span>\n      </div>\n      \n      <div className=\"action-buttons\">\n        <button \n          onClick={onPlay} \n          disabled={totalPages === 0}\n          className=\"action-button\"\n          aria-label=\"播放幻灯片\"\n        >\n          <FaPlay className=\"mr-2\" /> Play Slides\n        </button>\n        \n        {/* Publish Dropdown */}\n        <div className=\"dropdown\">\n          <button \n            onClick={(e) => {e.stopPropagation(); setShowPublish(p => !p); setShowExport(false); setShowMore(false);}} \n            disabled={totalPages === 0}\n            className=\"action-button\"\n            aria-label=\"发布\"\n            aria-expanded={showPublish}\n          >\n            <FaGlobe className=\"mr-2\" /> Publish\n          </button>\n          {showPublish && (\n            <div className=\"dropdown-content\">\n              {publishOptions.map(opt => (\n                <a \n                  key={opt.key} \n                  href=\"#\" \n                  onClick={(e) => { e.preventDefault(); onPublishSelect(opt.key); setShowPublish(false);}} \n                  aria-label={opt.label}\n                >\n                  {opt.icon} {opt.label}\n                </a>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Export Dropdown */}\n        <div className=\"dropdown\">\n          <button \n            onClick={(e) => {e.stopPropagation(); setShowExport(p => !p); setShowPublish(false); setShowMore(false);}} \n            disabled={totalPages === 0}\n            className=\"action-button\"\n            aria-label=\"导出\"\n            aria-expanded={showExport}\n          >\n            <FaDownload className=\"mr-2\" /> Export <span style={{fontSize: '0.6rem', backgroundColor: '#ff9800', color: 'white', padding: '1px 4px', borderRadius: '4px', fontWeight: 700, position: 'relative', top: '-1px'}}>Beta</span>\n          </button>\n          {showExport && (\n            <div className=\"dropdown-content\">\n              {exportOptions.map(opt => (\n                <a \n                  key={opt.key} \n                  href=\"#\" \n                  onClick={(e) => { e.preventDefault(); onExportSelect(opt.key); setShowExport(false);}} \n                  aria-label={`导出为${opt.label}`}\n                >\n                  {opt.icon} {opt.label}\n                </a>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* More Options Dropdown */}\n        <div className=\"dropdown\">\n          <button \n            onClick={(e) => {e.stopPropagation(); setShowMore(p => !p); setShowPublish(false); setShowExport(false);}} \n            className=\"action-button\"\n            aria-label=\"更多选项\"\n            aria-expanded={showMore}\n          >\n            <FaEllipsisH />\n          </button>\n          {showMore && (\n            <div className=\"dropdown-content\">\n              {moreOptions.map(opt => (\n                <a \n                  key={opt.key} \n                  href=\"#\" \n                  onClick={(e) => { e.preventDefault(); onMoreOptionsSelect(opt.key); setShowMore(false);}} \n                  aria-label={opt.label}\n                >\n                  {opt.icon} {opt.label}\n                </a>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PlayerToolbar; ", "import React, { forwardRef, useImperativeHandle, useState } from 'react';\n// Import dependencies for PDF generation\n// Note: You'll need to run: npm install jspdf html2canvas\n\nconst PdfExport = forwardRef((props, ref) => {\n  const [isExporting, setIsExporting] = useState(false);\n\n  useImperativeHandle(ref, () => ({\n    exportToPdf: async (slides) => {\n      return new Promise((resolve, reject) => {\n        try {\n          setIsExporting(true);\n          console.log('Started PDF export process for', slides.length, 'slides');\n          \n          // In a real implementation, we would:\n          // 1. Dynamically import jsPDF and html2canvas (for code splitting)\n          // 2. Create temporary DOM elements for each slide\n          // 3. Use html2canvas to capture each slide as an image\n          // 4. Add each image to a jsPDF document\n          // 5. Save the PDF\n\n          // This simulates the PDF creation process\n          setTimeout(() => {\n            import('jspdf')\n              .then(({ default: jsPDF }) => {\n                import('html2canvas')\n                  .then(({ default: html2canvas }) => {\n                    // This is a simplified example - in a real app, you'd need to handle:\n                    // - Rendering slides with correct dimensions\n                    // - Waiting for all content (including images/charts) to load\n                    // - Proper error handling for each slide\n\n                    // Create new PDF document (A4 size by default)\n                    const pdf = new jsPDF({\n                      orientation: 'landscape', // Slides are typically landscape\n                      unit: 'mm',\n                    });\n\n                    const exportSlides = async () => {\n                      try {\n                        // Mock HTML to Canvas conversion\n                        console.log('Simulating PDF export of slides');\n                        \n                        // For demo purposes - would actually render & convert slides\n                        for (let i = 0; i < slides.length; i++) {\n                          if (i > 0) pdf.addPage();\n                          \n                          // In a real implementation:\n                          // 1. Create a temporary container for the slide\n                          // 2. Insert the slide HTML\n                          // 3. Wait for content to load (images, charts)\n                          // 4. Use html2canvas to capture the slide\n                          // 5. Add the image to the PDF\n                          \n                          // Mock - would be an actual HTML canvas element\n                          const mockCanvas = { \n                            width: 1280, \n                            height: 720,\n                            toDataURL: () => 'data:image/png;base64,mockImageData' \n                          };\n                          \n                          // Add slide image to PDF\n                          const imgData = mockCanvas.toDataURL('image/png');\n                          pdf.addImage(\n                            imgData, \n                            'PNG', \n                            0, 0, \n                            pdf.internal.pageSize.getWidth(), \n                            pdf.internal.pageSize.getHeight()\n                          );\n                          \n                          // Update progress (in a real app, you might show a progress indicator)\n                          console.log(`Processed slide ${i+1}/${slides.length}`);\n                        }\n                        \n                        // Save the PDF\n                        pdf.save(`${props.filename || 'presentation'}.pdf`);\n                        console.log('PDF export completed');\n                        setIsExporting(false);\n                        resolve();\n                      } catch (error) {\n                        console.error('Error during PDF export:', error);\n                        setIsExporting(false);\n                        reject(error);\n                      }\n                    };\n                    \n                    // Start the export process\n                    exportSlides();\n                  })\n                  .catch(err => {\n                    console.error('Failed to load html2canvas:', err);\n                    setIsExporting(false);\n                    reject(err);\n                  });\n              })\n              .catch(err => {\n                console.error('Failed to load jsPDF:', err);\n                setIsExporting(false);\n                reject(err);\n              });\n          }, 500); // Simulate dynamic import delay\n          \n        } catch (error) {\n          console.error('PDF export error:', error);\n          setIsExporting(false);\n          reject(error);\n        }\n      });\n    }\n  }));\n\n  // Render a loading indicator when exporting (optional)\n  if (isExporting) {\n    return (\n      <div className=\"fixed bottom-4 right-4 bg-white p-3 rounded-lg shadow-lg z-50 flex items-center space-x-2\">\n        <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full\"></div>\n        <span className=\"text-sm text-gray-700\">正在导出 PDF...</span>\n      </div>\n    );\n  }\n\n  // This component doesn't render anything when not exporting\n  return null;\n});\n\nPdfExport.displayName = 'PdfExport';\n\nexport default PdfExport; ", "import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport PlayerToolbar from '../components/PlayerToolbar';\nimport SlideRenderer from '../components/SlideRenderer';\nimport PdfExport from '../components/PdfExport';\nimport { FaArrowLeft, FaArrowRight, FaTimes } from 'react-icons/fa';\n\nconst SlidePlayerView = () => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  \n  const { slides: slidesFromState = [], initialIndex = 0, presentationTitle = \"演示文稿\" } = location.state || {};\n  \n  const [slides, setSlides] = useState(slidesFromState);\n  const [isExporting, setIsExporting] = useState(false);\n  const [exportError, setExportError] = useState('');\n  const [currentSlideIndex, setCurrentSlideIndex] = useState(initialIndex);\n\n  const pdfExportRef = useRef(null);\n  const slidesDisplayAreaRef = useRef(null);\n\n  useEffect(() => {\n    if (!slidesFromState || slidesFromState.length === 0) {\n      // console.warn(\"SlidePlayerView: No slides provided.\");\n      // 可以考虑如果 slides 为空则导航回主页或显示错误信息\n      navigate('/', { replace: true });\n    }\n    setSlides(slidesFromState);\n    \n    // Set title based on presentation title\n    document.title = presentationTitle || \"演示文稿\";\n  }, [slidesFromState, presentationTitle, navigate]);\n  \n  const handlePrevSlide = useCallback(() => {\n    setCurrentSlideIndex(prev => Math.max(0, prev - 1));\n  }, []);\n    \n  const handleNextSlide = useCallback(() => {\n    setCurrentSlideIndex(prev => Math.min(slides.length - 1, prev + 1));\n  }, [slides]);\n\n  const handleExitPresentation = () => {\n    navigate('/');\n  };\n\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      if (e.key === 'ArrowRight' || e.key === 'PageDown' || e.key === ' ') {\n        handleNextSlide();\n      } else if (e.key === 'ArrowLeft' || e.key === 'PageUp') {\n        handlePrevSlide();\n      } else if (e.key === 'Escape') {\n        handleExitPresentation();\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => {\n      window.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [handleNextSlide, handlePrevSlide]);\n\n  // 请求全屏模式（在用户首次交互后）\n  const requestFullScreen = () => {\n    // 发送消息给父窗口，请求全屏\n    window.parent.postMessage({\n      type: 'request-fullscreen',\n      payload: { initialIndex: currentSlideIndex }\n    }, '*');\n  };\n  \n  const handleExport = async (formatKey) => {\n    if (!slides || slides.length === 0) {\n        alert(\"没有可导出的幻灯片。\");\n        return;\n    }\n    \n    setIsExporting(true);\n    setExportError('');\n    \n    if (formatKey === 'pdf' && pdfExportRef.current) {\n      try {\n        // 假设 exportToPdf 接受 slides 数组\n        await pdfExportRef.current.exportToPdf(slides);\n        // 成功消息可以通过 UI 提示或 toast 显示\n        alert('PDF 导出成功。');\n      } catch (error) {\n        console.error(\"PDF导出失败:\", error);\n        setExportError('PDF 导出失败，请稍后再试。');\n        alert('PDF 导出失败。' + (error.message || ''));\n      } finally {\n        setIsExporting(false);\n      }\n    } else if (formatKey === 'pptx') {\n      try {\n        alert('PPTX 导出功能暂未实现。');\n      } finally {\n        setIsExporting(false);\n      }\n    }\n  };\n  \n  const handlePublish = (actionKey) => {\n    if (actionKey === 'copyLink') {\n      try {\n        // 理想情况下，这里应该生成一个指向此演示文稿的唯一、持久的链接\n        // 为简单起见，我们复制当前查看器页面的链接（如果适用）或一个占位符\n        const shareableLink = location.state?.shareLink || window.location.origin + `/share/presentation/${presentationTitle.replace(/\\s+/g, '-')}`; // 示例链接\n        navigator.clipboard.writeText(shareableLink)\n          .then(() => alert('链接已复制到剪贴板: ' + shareableLink))\n          .catch(err => {\n            console.error(\"复制链接失败:\", err);\n            alert('复制链接失败。' + (err.message || ''));\n          });\n      } catch (error) {\n        console.error(\"分享链接处理失败:\", error);\n        alert('处理分享链接时出错。');\n      }\n    }\n  };\n  \n  const handleMoreOptions = (actionKey) => {\n    if (actionKey === 'editCanva') {\n      alert('在Canva中编辑功能暂未实现。');\n    }\n  };\n\n  if (!slides || slides.length === 0) {\n    return (\n      <div className=\"flex flex-col h-screen bg-gray-200\">\n        <PlayerToolbar\n            fileName={presentationTitle}\n            totalPages={0}\n            onPlay={() => {}}\n            onExitPlayer={() => navigate(\"/\")}\n            onExportSelect={() => {}}\n            onPublishSelect={() => {}}\n            onMoreOptionsSelect={() => {}}\n      />\n        <div className=\"flex-1 flex items-center justify-center text-gray-600\">\n            没有幻灯片可供查看或导出。\n        </div>\n      </div>\n    );\n  }\n\n  const currentSlide = slides[currentSlideIndex];\n\n  return (\n    <div className=\"flex flex-col h-full overflow-hidden bg-gray-100\">\n      <PlayerToolbar\n        fileName={presentationTitle}\n        totalPages={slides.length}\n        currentPage={currentSlideIndex + 1}\n        onPlay={requestFullScreen}\n        onExitPlayer={handleExitPresentation}\n        onExportSelect={handleExport}\n        onPublishSelect={handlePublish}\n        onMoreOptionsSelect={handleMoreOptions}\n      />\n      \n      {/* 导出状态提示 */}\n      {isExporting && (\n        <div className=\"export-notification\">\n          <div className=\"animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full mr-2\"></div>\n          <span>正在导出，请稍候...</span>\n        </div>\n      )}\n      \n      {/* 错误提示 */}\n      {exportError && (\n        <div className=\"fixed inset-x-0 top-16 flex justify-center z-50\">\n          <div className=\"bg-red-50 text-red-700 px-4 py-2 rounded-md shadow flex items-center\">\n            <span className=\"mr-2\">⚠️</span>\n            <span>{exportError}</span>\n            <button \n              className=\"ml-2 text-red-500 hover:text-red-700\" \n              onClick={() => setExportError('')}\n            >\n              ×\n            </button>\n          </div>\n        </div>\n      )}\n      \n      {/* Scrollable area for all slides - styled like viewer.html */}\n      <div \n        ref={slidesDisplayAreaRef}\n        className=\"slides-display-area flex-1 custom-scrollbar\"\n      >\n        {slides.map((slide, index) => (\n          <div \n            key={slide.id || `player-slide-item-${index}`} \n            className=\"slide-wrapper\"\n          >\n            {/* Page number indicator */}\n            <div className=\"absolute top-2 left-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-md z-10\">\n              {index + 1} / {slides.length}\n            </div>\n            {/* SlideRenderer container */}\n            <div className=\"w-full\">\n              <SlideRenderer\n                slideId={slide.id || `player-slide-render-${index}`}\n                slideFullHtml={slide.html}\n                isAppEditingMode={false}\n              />\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      <PdfExport ref={pdfExportRef} filename={presentationTitle} />\n    </div>\n  );\n};\n\nexport default SlidePlayerView; "], "names": ["_ref", "fileName", "totalPages", "currentPage", "onPlay", "onExitPlayer", "onExportSelect", "onPublishSelect", "onMoreOptionsSelect", "showPublish", "setShowPublish", "useState", "showExport", "setShowExport", "showMore", "setShowMore", "exportOptions", "key", "label", "icon", "_jsx", "FaFilePdf", "className", "FaFilePowerpoint", "publishOptions", "FaCopy", "FaExternalLinkAlt", "moreOptions", "_jsxs", "children", "onClick", "title", "FaArrowLeft", "FaFileAlt", "disabled", "FaPlay", "e", "stopPropagation", "p", "FaGlobe", "map", "opt", "href", "preventDefault", "FaDownload", "style", "fontSize", "backgroundColor", "color", "padding", "borderRadius", "fontWeight", "position", "top", "concat", "FaEllipsisH", "PdfExport", "forwardRef", "props", "ref", "isExporting", "setIsExporting", "useImperativeHandle", "exportToPdf", "async", "Promise", "resolve", "reject", "console", "log", "slides", "length", "setTimeout", "then", "default", "jsPDF", "_ref2", "html2canvas", "pdf", "orientation", "unit", "i", "addPage", "imgData", "width", "height", "toDataURL", "addImage", "internal", "pageSize", "getWidth", "getHeight", "save", "filename", "error", "exportSlides", "catch", "err", "displayName", "SlidePlayerView", "location", "useLocation", "navigate", "useNavigate", "slidesFromState", "initialIndex", "presentationTitle", "state", "setSlides", "exportError", "setExportError", "currentSlideIndex", "setCurrentSlideIndex", "pdfExportRef", "useRef", "slidesDisplayAreaRef", "useEffect", "replace", "document", "handlePrevSlide", "useCallback", "prev", "Math", "max", "handleNextSlide", "min", "handleExitPresentation", "handleKeyDown", "window", "addEventListener", "removeEventListener", "PlayerToolbar", "requestFullScreen", "parent", "postMessage", "type", "payload", "formatKey", "current", "alert", "message", "action<PERSON>ey", "_location$state", "shareableLink", "shareLink", "origin", "navigator", "clipboard", "writeText", "slide", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slideId", "id", "slideFullHtml", "html", "isAppEditingMode"], "sourceRoot": ""}