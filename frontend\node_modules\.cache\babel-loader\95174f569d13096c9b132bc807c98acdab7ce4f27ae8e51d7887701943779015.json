{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\MiddlePane.js\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect, useState } from 'react';\nimport ChatMessage from './ChatMessage';\nimport { FaPlus, FaHistory } from 'react-icons/fa';\nimport MessageInput from './MessageInput';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MiddlePane = ({\n  chatMessages,\n  onSendMessage,\n  isGenerating,\n  onViewSearchResults,\n  title = \"AI 幻灯片\",\n  onTitleClick,\n  onBackClick,\n  tools = [],\n  currentStep = null,\n  className = \"\",\n  onToggleHistoryDrawer\n}) => {\n  _s();\n  const messagesEndRef = useRef(null);\n  const [isEditingTitle, setIsEditingTitle] = useState(false);\n  const [editableTitle, setEditableTitle] = useState(title || '新对话');\n  const titleRef = useRef(null);\n\n  // 自动滚动到最新消息\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [chatMessages, tools, currentStep]);\n\n  // Update editable title when prop changes\n  useEffect(() => {\n    setEditableTitle(title || '新对话');\n  }, [title]);\n\n  // 处理标题编辑完成\n  const handleTitleEditComplete = () => {\n    setIsEditingTitle(false);\n    if (editableTitle.trim() !== '' && editableTitle !== title) {\n      onTitleClick(editableTitle);\n    } else {\n      setEditableTitle(title || '新对话');\n    }\n  };\n\n  // Handle clicks outside the title input to save changes\n  useEffect(() => {\n    if (isEditingTitle) {\n      const handleClickOutside = e => {\n        if (titleRef.current && !titleRef.current.contains(e.target)) {\n          handleTitleEditComplete();\n        }\n      };\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n      };\n    }\n  }, [isEditingTitle, title, handleTitleEditComplete]);\n  const handleHistoryClick = () => {\n    if (onToggleHistoryDrawer) {\n      onToggleHistoryDrawer();\n    }\n  };\n\n  // 处理工具查看按钮点击\n  const handleToolView = toolId => {\n    if (onViewSearchResults) {\n      onViewSearchResults(toolId);\n    }\n  };\n\n  // 处理标题输入键盘事件\n  const handleTitleKeyDown = e => {\n    if (e.key === 'Enter') {\n      handleTitleEditComplete();\n    } else if (e.key === 'Escape') {\n      setIsEditingTitle(false);\n      setEditableTitle(title || '新对话');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `border-r border-gray-200 flex flex-col bg-gray-50 max-h-full ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-16 px-3 border-b border-gray-200 bg-white flex items-center sticky top-0 z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onBackClick,\n        className: \"p-2 mr-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100\",\n        title: \"\\u65B0\\u5EFA\\u5E7B\\u706F\\u7247\",\n        children: /*#__PURE__*/_jsxDEV(FaPlus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          id: \"history-icon\",\n          className: \"p-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100 mr-2\",\n          onClick: handleHistoryClick,\n          title: \"\\u5386\\u53F2\\u4EFB\\u52A1\",\n          children: /*#__PURE__*/_jsxDEV(FaHistory, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), isEditingTitle ? /*#__PURE__*/_jsxDEV(\"input\", {\n        ref: titleRef,\n        type: \"text\",\n        value: editableTitle,\n        onChange: e => setEditableTitle(e.target.value),\n        className: \"flex-1 text-base font-medium text-gray-800 border-b border-gray-300 pb-1 focus:outline-none focus:border-blue-500\",\n        autoFocus: true,\n        onKeyDown: handleTitleKeyDown,\n        onBlur: handleTitleEditComplete\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 text-base font-medium text-gray-800 cursor-pointer py-1 px-2 rounded hover:bg-gray-100 overflow-hidden whitespace-nowrap text-ellipsis\",\n        onClick: () => setIsEditingTitle(true),\n        title: editableTitle,\n        children: editableTitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4 space-y-5 custom-scrollbar\",\n      style: {\n        height: \"calc(100% - 110px)\"\n      },\n      children: [chatMessages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: /*#__PURE__*/_jsxDEV(ChatMessage, {\n          message: message,\n          onViewSearchResults: onViewSearchResults\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, message.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this)), currentStep && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"current-step bg-gray-50 border-l-4 border-blue-500 p-3 rounded my-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"step-indicator font-medium text-blue-500\",\n          children: currentStep.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"step-description text-sm text-gray-600 mt-1\",\n          children: currentStep.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2 bg-gray-50 sticky bottom-0 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(MessageInput, {\n        onSendMessage: onSendMessage,\n        isGenerating: isGenerating,\n        placeholder: \"\\u8F93\\u5165\\u60A8\\u60F3\\u5236\\u4F5C\\u7684\\u5E7B\\u706F\\u7247\\u5185\\u5BB9...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(MiddlePane, \"rGZ207vHd5Yo2Csg4aQQj8jFVpQ=\");\n_c = MiddlePane;\nexport default MiddlePane;\nvar _c;\n$RefreshReg$(_c, \"MiddlePane\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "ChatMessage", "FaPlus", "FaHistory", "MessageInput", "jsxDEV", "_jsxDEV", "MiddlePane", "chatMessages", "onSendMessage", "isGenerating", "onViewSearchResults", "title", "onTitleClick", "onBackClick", "tools", "currentStep", "className", "onToggleHistoryDrawer", "_s", "messagesEndRef", "isEditingTitle", "setIsEditingTitle", "editableTitle", "setEditableTitle", "titleRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleTitleEditComplete", "trim", "handleClickOutside", "e", "contains", "target", "document", "addEventListener", "removeEventListener", "handleHistoryClick", "handleToolView", "toolId", "handleTitleKeyDown", "key", "children", "onClick", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "ref", "type", "value", "onChange", "autoFocus", "onKeyDown", "onBlur", "style", "height", "map", "message", "name", "description", "placeholder", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/MiddlePane.js"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\nimport ChatMessage from './ChatMessage';\nimport { FaPlus, FaHistory } from 'react-icons/fa';\nimport MessageInput from './MessageInput';\n\nconst MiddlePane = ({ \n  chatMessages, \n  onSendMessage, \n  isGenerating, \n  onViewSearchResults,\n  title = \"AI 幻灯片\",\n  onTitleClick,\n  onBackClick,\n  tools = [],\n  currentStep = null,\n  className = \"\",\n  onToggleHistoryDrawer\n}) => {\n  const messagesEndRef = useRef(null);\n  const [isEditingTitle, setIsEditingTitle] = useState(false);\n  const [editableTitle, setEditableTitle] = useState(title || '新对话');\n  const titleRef = useRef(null);\n\n  // 自动滚动到最新消息\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [chatMessages, tools, currentStep]);\n\n  // Update editable title when prop changes\n  useEffect(() => {\n    setEditableTitle(title || '新对话');\n  }, [title]);\n\n  // 处理标题编辑完成\n  const handleTitleEditComplete = () => {\n    setIsEditingTitle(false);\n    if (editableTitle.trim() !== '' && editableTitle !== title) {\n      onTitleClick(editableTitle);\n    } else {\n      setEditableTitle(title || '新对话');\n    }\n  };\n\n  // Handle clicks outside the title input to save changes\n  useEffect(() => {\n    if (isEditingTitle) {\n      const handleClickOutside = (e) => {\n        if (titleRef.current && !titleRef.current.contains(e.target)) {\n          handleTitleEditComplete();\n        }\n      };\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n      };\n    }\n  }, [isEditingTitle, title, handleTitleEditComplete]);\n\n  const handleHistoryClick = () => {\n    if (onToggleHistoryDrawer) {\n      onToggleHistoryDrawer();\n    }\n  };\n\n  // 处理工具查看按钮点击\n  const handleToolView = (toolId) => {\n    if (onViewSearchResults) {\n      onViewSearchResults(toolId);\n    }\n  };\n\n  // 处理标题输入键盘事件\n  const handleTitleKeyDown = (e) => {\n    if (e.key === 'Enter') {\n      handleTitleEditComplete();\n    } else if (e.key === 'Escape') {\n      setIsEditingTitle(false);\n      setEditableTitle(title || '新对话');\n    }\n  };\n\n  return (\n    <div className={`border-r border-gray-200 flex flex-col bg-gray-50 max-h-full ${className}`}>\n      {/* Header with title and new presentation button - Make sticky */}\n      <div className=\"h-16 px-3 border-b border-gray-200 bg-white flex items-center sticky top-0 z-10\">\n        <button \n          onClick={onBackClick}\n          className=\"p-2 mr-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100\"\n          title=\"新建幻灯片\"\n        >\n          <FaPlus size={16} />\n        </button>\n\n        {/* History icon */}\n        <div className=\"relative\">\n          <button\n            id=\"history-icon\"\n            className=\"p-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100 mr-2\"\n            onClick={handleHistoryClick}\n            title=\"历史任务\"\n          >\n            <FaHistory size={16} />\n          </button>\n        </div>\n\n        {isEditingTitle ? (\n          <input\n            ref={titleRef}\n            type=\"text\"\n            value={editableTitle}\n            onChange={(e) => setEditableTitle(e.target.value)}\n            className=\"flex-1 text-base font-medium text-gray-800 border-b border-gray-300 pb-1 focus:outline-none focus:border-blue-500\"\n            autoFocus\n            onKeyDown={handleTitleKeyDown}\n            onBlur={handleTitleEditComplete}\n          />\n        ) : (\n          <div \n            className=\"flex-1 text-base font-medium text-gray-800 cursor-pointer py-1 px-2 rounded hover:bg-gray-100 overflow-hidden whitespace-nowrap text-ellipsis\"\n            onClick={() => setIsEditingTitle(true)}\n            title={editableTitle}\n          >\n            {editableTitle}\n          </div>\n        )}\n      </div>\n\n      {/* Messages area - Adjust to account for sticky header and footer */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-5 custom-scrollbar\" style={{ height: \"calc(100% - 110px)\" }}>\n        {chatMessages.map((message) => (\n          <div key={message.id} className=\"space-y-2\">\n          <ChatMessage \n            message={message}\n            onViewSearchResults={onViewSearchResults}\n            />\n          </div>\n        ))}\n\n        {currentStep && (\n          <div className=\"current-step bg-gray-50 border-l-4 border-blue-500 p-3 rounded my-4\">\n            <span className=\"step-indicator font-medium text-blue-500\">{currentStep.name}</span>\n            <p className=\"step-description text-sm text-gray-600 mt-1\">{currentStep.description}</p>\n          </div>\n        )}\n        \n       \n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input area - Make sticky at bottom */}\n      <div className=\"p-2 bg-gray-50 sticky bottom-0 border-t border-gray-200\">\n        <MessageInput \n          onSendMessage={onSendMessage} \n          isGenerating={isGenerating}\n          placeholder=\"输入您想制作的幻灯片内容...\"\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default MiddlePane; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAClD,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,UAAU,GAAGA,CAAC;EAClBC,YAAY;EACZC,aAAa;EACbC,YAAY;EACZC,mBAAmB;EACnBC,KAAK,GAAG,QAAQ;EAChBC,YAAY;EACZC,WAAW;EACXC,KAAK,GAAG,EAAE;EACVC,WAAW,GAAG,IAAI;EAClBC,SAAS,GAAG,EAAE;EACdC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,cAAc,GAAGtB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAACY,KAAK,IAAI,KAAK,CAAC;EAClE,MAAMa,QAAQ,GAAG3B,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACA,MAAM4B,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAP,cAAc,CAACQ,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED/B,SAAS,CAAC,MAAM;IACd2B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAClB,YAAY,EAAEO,KAAK,EAAEC,WAAW,CAAC,CAAC;;EAEtC;EACAjB,SAAS,CAAC,MAAM;IACdyB,gBAAgB,CAACZ,KAAK,IAAI,KAAK,CAAC;EAClC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;;EAEX;EACA,MAAMmB,uBAAuB,GAAGA,CAAA,KAAM;IACpCT,iBAAiB,CAAC,KAAK,CAAC;IACxB,IAAIC,aAAa,CAACS,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIT,aAAa,KAAKX,KAAK,EAAE;MAC1DC,YAAY,CAACU,aAAa,CAAC;IAC7B,CAAC,MAAM;MACLC,gBAAgB,CAACZ,KAAK,IAAI,KAAK,CAAC;IAClC;EACF,CAAC;;EAED;EACAb,SAAS,CAAC,MAAM;IACd,IAAIsB,cAAc,EAAE;MAClB,MAAMY,kBAAkB,GAAIC,CAAC,IAAK;QAChC,IAAIT,QAAQ,CAACG,OAAO,IAAI,CAACH,QAAQ,CAACG,OAAO,CAACO,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAC,EAAE;UAC5DL,uBAAuB,CAAC,CAAC;QAC3B;MACF,CAAC;MACDM,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;MAC1D,OAAO,MAAM;QACXI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC/D,CAAC;IACH;EACF,CAAC,EAAE,CAACZ,cAAc,EAAET,KAAK,EAAEmB,uBAAuB,CAAC,CAAC;EAEpD,MAAMS,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAItB,qBAAqB,EAAE;MACzBA,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMuB,cAAc,GAAIC,MAAM,IAAK;IACjC,IAAI/B,mBAAmB,EAAE;MACvBA,mBAAmB,CAAC+B,MAAM,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIT,CAAC,IAAK;IAChC,IAAIA,CAAC,CAACU,GAAG,KAAK,OAAO,EAAE;MACrBb,uBAAuB,CAAC,CAAC;IAC3B,CAAC,MAAM,IAAIG,CAAC,CAACU,GAAG,KAAK,QAAQ,EAAE;MAC7BtB,iBAAiB,CAAC,KAAK,CAAC;MACxBE,gBAAgB,CAACZ,KAAK,IAAI,KAAK,CAAC;IAClC;EACF,CAAC;EAED,oBACEN,OAAA;IAAKW,SAAS,EAAE,gEAAgEA,SAAS,EAAG;IAAA4B,QAAA,gBAE1FvC,OAAA;MAAKW,SAAS,EAAC,iFAAiF;MAAA4B,QAAA,gBAC9FvC,OAAA;QACEwC,OAAO,EAAEhC,WAAY;QACrBG,SAAS,EAAC,+EAA+E;QACzFL,KAAK,EAAC,gCAAO;QAAAiC,QAAA,eAEbvC,OAAA,CAACJ,MAAM;UAAC6C,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAGT7C,OAAA;QAAKW,SAAS,EAAC,UAAU;QAAA4B,QAAA,eACvBvC,OAAA;UACE8C,EAAE,EAAC,cAAc;UACjBnC,SAAS,EAAC,+EAA+E;UACzF6B,OAAO,EAAEN,kBAAmB;UAC5B5B,KAAK,EAAC,0BAAM;UAAAiC,QAAA,eAEZvC,OAAA,CAACH,SAAS;YAAC4C,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL9B,cAAc,gBACbf,OAAA;QACE+C,GAAG,EAAE5B,QAAS;QACd6B,IAAI,EAAC,MAAM;QACXC,KAAK,EAAEhC,aAAc;QACrBiC,QAAQ,EAAGtB,CAAC,IAAKV,gBAAgB,CAACU,CAAC,CAACE,MAAM,CAACmB,KAAK,CAAE;QAClDtC,SAAS,EAAC,mHAAmH;QAC7HwC,SAAS;QACTC,SAAS,EAAEf,kBAAmB;QAC9BgB,MAAM,EAAE5B;MAAwB;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,gBAEF7C,OAAA;QACEW,SAAS,EAAC,+IAA+I;QACzJ6B,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAAC,IAAI,CAAE;QACvCV,KAAK,EAAEW,aAAc;QAAAsB,QAAA,EAEpBtB;MAAa;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN7C,OAAA;MAAKW,SAAS,EAAC,uDAAuD;MAAC2C,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAqB,CAAE;MAAAhB,QAAA,GAC5GrC,YAAY,CAACsD,GAAG,CAAEC,OAAO,iBACxBzD,OAAA;QAAsBW,SAAS,EAAC,WAAW;QAAA4B,QAAA,eAC3CvC,OAAA,CAACL,WAAW;UACV8D,OAAO,EAAEA,OAAQ;UACjBpD,mBAAmB,EAAEA;QAAoB;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC,GAJMY,OAAO,CAACX,EAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKf,CACN,CAAC,EAEDnC,WAAW,iBACVV,OAAA;QAAKW,SAAS,EAAC,qEAAqE;QAAA4B,QAAA,gBAClFvC,OAAA;UAAMW,SAAS,EAAC,0CAA0C;UAAA4B,QAAA,EAAE7B,WAAW,CAACgD;QAAI;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpF7C,OAAA;UAAGW,SAAS,EAAC,6CAA6C;UAAA4B,QAAA,EAAE7B,WAAW,CAACiD;QAAW;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CACN,eAGD7C,OAAA;QAAK+C,GAAG,EAAEjC;MAAe;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGN7C,OAAA;MAAKW,SAAS,EAAC,yDAAyD;MAAA4B,QAAA,eACtEvC,OAAA,CAACF,YAAY;QACXK,aAAa,EAAEA,aAAc;QAC7BC,YAAY,EAAEA,YAAa;QAC3BwD,WAAW,EAAC;MAAiB;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CA9JIZ,UAAU;AAAA4D,EAAA,GAAV5D,UAAU;AAgKhB,eAAeA,UAAU;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}