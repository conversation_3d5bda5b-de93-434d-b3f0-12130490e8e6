(base) PS E:\cursor\ppt\TikTodo-aippt\backend> python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
INFO:     Will watch for changes in these directories: ['E:\\cursor\\ppt\\TikTodo-aippt\\backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [28688] using WatchFiles
2025-06-18 22:53:07,508 [INFO] [root:81] - Loaded environment variables from E:\cursor\ppt\TikTodo-aippt\backend\.env
2025-06-18 22:53:07,508 [INFO] [root:81] - Settings initialized and all required environment variables are present.        
2025-06-18 22:53:07,509 [INFO] [root:81] - Project storage directory is set to: E:\cursor\ppt\TikTodo-aippt\project_storage
2025-06-18 22:53:07,509 [INFO] [root:81] - Loaded 5 Gemini API Key(s).
Debug: Added E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages to sys.path for prioritization.
2025-06-18 22:53:07,535 [INFO] [root:81] - PYTHONIOENCODING before set: None
2025-06-18 22:53:07,535 [INFO] [root:81] - PYTHONIOENCODING after set: utf-8
2025-06-18 22:53:07,535 [INFO] [root:81] - HTTP_PROXY from env: http://127.0.0.1:7897
2025-06-18 22:53:07,535 [INFO] [root:81] - HTTPS_PROXY from env: http://127.0.0.1:7897
2025-06-18 22:53:07,535 [INFO] [root:81] - Using proxy configuration from environment variables
2025-06-18 22:53:07,615 [INFO] [app.services.prompt_manager:12] - PromptManager: Base directory for prompts set to: E:\cursor\ppt\TikTodo-aippt\backend\app\prompts
2025-06-18 22:53:07,616 [INFO] [app.services.prompt_manager:19] - PromptManagerSingleton initialized with base path: E:\cursor\ppt\TikTodo-aippt\backend\app\prompts
2025-06-18 22:53:08,837 [INFO] [app.utils.api_key_manager:36] - ApiKeyManager initialized with 5 keys. Rate limit: 5 RPM and 1 RPS per key.
2025-06-18 22:53:10,865 [INFO] [root:81] - [MainApp Pre-Init] Using project-specific chat logging instead of error log handler.
2025-06-18 22:53:10,869 [INFO] [root:81] - [MainApp] Skipping global Gemini API configuration. API keys will be passed per-request.
INFO:     Started server process [27400]
INFO:     Waiting for application startup.
2025-06-18 22:53:10,938 [INFO] [root:81] - [MainApp Startup] Application startup event triggered.
2025-06-18 22:53:10,938 [INFO] [root:81] - [MainApp Startup] Initializing database and creating tables if they don't exist...
2025-06-18 22:53:10,948 [INFO] [root:81] - [MainApp Startup] Database tables checked/created successfully.
2025-06-18 22:53:10,948 [INFO] [root:81] - [MainApp Pre-Init] Attempting to instantiate PresentationWorkflow...
2025-06-18 22:53:10,955 [INFO] [app.agents.user_intent_agent:20] - UserIntentAgent initialized using gemini-2.5-flash and PromptManager for prompts.ompts.
2025-06-18 22:53:10,960 [INFO] [app.agents.visual_style_agent:30] - VisualStyleAgent initialized using gemini-2.5-flash.
2025-06-18 22:53:10,965 [INFO] [app.agents.slide_detailer_agent:40] - SlideDetailerAgent initialized using gemini-2.5-flash.
2025-06-18 22:53:10,965 [INFO] [app.agents.slide_generator_agent:27] - SlideGeneratorAgent initialized as 'Faithful Engineer'.
2025-06-18 22:53:10,965 [INFO] [app.agents.presentation_workflow:56] - PresentationWorkflow初始化完成
2025-06-18 22:53:10,965 [INFO] [root:81] - [MainApp Pre-Init] PresentationWorkflow instantiated successfully.
2025-06-18 22:53:10,965 [INFO] [root:81] - [MainApp Startup] Workflow instance created.
2025-06-18 22:53:10,965 [INFO] [root:81] - [MainApp Startup] Lifespan setup complete. Application is ready to accept requests.
2025-06-18 22:53:10,966 [INFO] [root:81] - [MainApp Health Check] Database connection test successful.
INFO:     Application startup complete.
INFO:     127.0.0.1:2825 - "OPTIONS /api/v1/projects/history HTTP/1.1" 200 OK
INFO:     127.0.0.1:2824 - "OPTIONS /api/v1/projects/history HTTP/1.1" 200 OK
INFO:     127.0.0.1:2824 - "GET /api/v1/projects/history HTTP/1.1" 200 OK
INFO:     127.0.0.1:2825 - "GET /api/v1/projects/history HTTP/1.1" 200 OK
INFO:     127.0.0.1:2825 - "OPTIONS /api/v1/projects/8bdba947-a0d2-486e-aff4-5d578234a7f6/details HTTP/1.1" 200 OK
INFO:     127.0.0.1:2825 - "GET /api/v1/projects/8bdba947-a0d2-486e-aff4-5d578234a7f6/details HTTP/1.1" 200 OK
INFO:     127.0.0.1:2825 - "GET /api/v1/projects/8bdba947-a0d2-486e-aff4-5d578234a7f6/details HTTP/1.1" 200 OK
INFO:     127.0.0.1:2825 - "GET /api/v1/projects/history HTTP/1.1" 200 OK
INFO:     127.0.0.1:2824 - "GET /api/v1/projects/history HTTP/1.1" 200 OK
INFO:     127.0.0.1:2824 - "OPTIONS /generate_presentation/ HTTP/1.1" 200 OK
2025-06-18 22:58:57,633 [INFO] [root:81] - API层为新项目生成临时ID: proj_04ed68d5
INFO:     127.0.0.1:2824 - "POST /generate_presentation/ HTTP/1.1" 200 OK
2025-06-18 22:58:57,634 [INFO] [root:81] - [ID管理] 等待 1.0 秒，以便前端建立SSE连接...
2025-06-18 22:58:57,640 [INFO] [app.apis.v1.slides_router:317] - [SSE Connect] 客户端请求连接 project_id: proj_04ed68d5
2025-06-18 22:58:57,640 [INFO] [app.apis.v1.slides_router:327] - [SSE Connect] 客户端已添加到项目 proj_04ed68d5 的监听队列，当前此ID有 1 个监 
听者
2025-06-18 22:58:57,640 [INFO] [app.apis.v1.slides_router:328] - [SSE Connect] 当前 connected_clients 包含的项目ID: ['proj_04ed68d5']
INFO:     127.0.0.1:2824 - "GET /api/v1/presentation_updates/proj_04ed68d5 HTTP/1.1" 200 OK
2025-06-18 22:58:57,641 [INFO] [app.apis.v1.slides_router:339] - [SSE Connect] 已发送连接确认给 proj_04ed68d5
2025-06-18 22:58:58,649 [INFO] [root:81] - [ID管理] 新项目（API临时ID proj_04ed68d5），在DB中创建记录
2025-06-18 22:58:58,656 [INFO] [root:81] - [ID管理] 新项目DB记录创建成功，真实DB ID: 6c188575-6e6a-4aaf-aff8-424f37b36fb3
2025-06-18 22:58:58,760 [INFO] [root:81] - [ID管理] 需要ID映射: API临时ID proj_04ed68d5 -> DB真实ID 6c188575-6e6a-4aaf-aff8-424f37b36fb3
2025-06-18 22:58:58,760 [INFO] [app.apis.v1.slides_router:112] - [SSE Push START] Attempting to push for progress.project_id: proj_04ed68d5, status: id_mapping
2025-06-18 22:58:58,761 [INFO] [app.apis.v1.slides_router:113] - [SSE Push START] current connected_clients: ['proj_04ed68d5']
2025-06-18 22:58:58,761 [INFO] [app.apis.v1.slides_router:114] - [SSE Push START] current temp_id_to_actual_id_map: {}
2025-06-18 22:58:58,761 [INFO] [app.apis.v1.slides_router:122] - [SSE Push ID_MAP_PROCESS] ID Mapping Request: TempID proj_04ed68d5 -> ActualID 6c188575-6e6a-4aaf-aff8-424f37b36fb3
2025-06-18 22:58:58,761 [INFO] [app.apis.v1.slides_router:131] - [SSE Push ID_MAP_PROCESS] Global map updated: proj_04ed68d5 -> 6c188575-6e6a-4aaf-aff8-424f37b36fb3
2025-06-18 22:58:58,761 [INFO] [app.apis.v1.slides_router:140] - [SSE Push ID_MAP_PROCESS] Moved 1 client queue(s) from TempID proj_04ed68d5 to ActualID 6c188575-6e6a-4aaf-aff8-424f37b36fb3
2025-06-18 22:58:58,761 [INFO] [app.apis.v1.slides_router:162] - [SSE Push ID_MAP_PROCESS] Prepared 'id_mapped_to_client' message for ActualID 6c188575-6e6a-4aaf-aff8-424f37b36fb3.
2025-06-18 22:58:58,761 [INFO] [app.apis.v1.slides_router:179] - [SSE Push SUCCESS] Pushed to 1 client(s) for ID 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: id_mapped_to_client
INFO:     127.0.0.1:2825 - "GET /api/v1/projects/history HTTP/1.1" 200 OK
INFO:     127.0.0.1:2825 - "OPTIONS /api/v1/projects/6c188575-6e6a-4aaf-aff8-424f37b36fb3/details HTTP/1.1" 200 OK
INFO:     127.0.0.1:2825 - "GET /api/v1/projects/6c188575-6e6a-4aaf-aff8-424f37b36fb3/details HTTP/1.1" 200 OK
INFO:     127.0.0.1:2825 - "GET /api/v1/projects/history HTTP/1.1" 200 OK
INFO:     127.0.0.1:2828 - "GET /api/v1/projects/history HTTP/1.1" 200 OK
2025-06-18 22:58:59,274 [INFO] [root:81] - [ID管理] ID映射消息已发送，临时等待0.5秒
2025-06-18 22:58:59,274 [INFO] [app.apis.v1.slides_router:112] - [SSE Push START] Attempting to push for progress.project_id: 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: preparing
2025-06-18 22:58:59,275 [INFO] [app.apis.v1.slides_router:113] - [SSE Push START] current connected_clients: ['6c188575-6e6a-4aaf-aff8-424f37b36fb3']
2025-06-18 22:58:59,275 [INFO] [app.apis.v1.slides_router:114] - [SSE Push START] current temp_id_to_actual_id_map: {'proj_04ed68d5': '6c188575-6e6a-4aaf-aff8-424f37b36fb3'}
2025-06-18 22:58:59,275 [INFO] [app.apis.v1.slides_router:179] - [SSE Push SUCCESS] Pushed to 1 client(s) for ID 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: preparing
2025-06-18 22:58:59,275 [INFO] [root:81] - [ID管理] 开始使用真实DB ID 6c188575-6e6a-4aaf-aff8-424f37b36fb3 调用工作流
2025-06-18 22:58:59,275 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:77] - 开始重构后的演示文稿生成流程，项目ID: 6c188575-6e6a-4aaf-aff8-424f37b36fb3
2025-06-18 22:58:59,281 [INFO] [app.agents.presentation_workflow:537] - 聊天消息已保存到数据库: user - 介绍珍珠港前因后果，需要引人入胜，震撼 
人心...
2025-06-18 22:58:59,281 [INFO] [app.apis.v1.slides_router:112] - [SSE Push START] Attempting to push for progress.project_id: 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: analyzing_intent
2025-06-18 22:58:59,281 [INFO] [app.apis.v1.slides_router:113] - [SSE Push START] current connected_clients: ['6c188575-6e6a-4aaf-aff8-424f37b36fb3']
2025-06-18 22:58:59,282 [INFO] [app.apis.v1.slides_router:114] - [SSE Push START] current temp_id_to_actual_id_map: {'proj_04ed68d5': '6c188575-6e6a-4aaf-aff8-424f37b36fb3'}
2025-06-18 22:58:59,282 [INFO] [app.apis.v1.slides_router:179] - [SSE Push SUCCESS] Pushed to 1 client(s) for ID 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: analyzing_intent
2025-06-18 22:58:59,286 [INFO] [app.agents.presentation_workflow:537] - 聊天消息已保存到数据库: ai - 🔍 正在分析您的需求......
2025-06-18 22:58:59,286 [INFO] [app.agents.user_intent_agent:29] - [UserIntentAgent][IntentAnalysis_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3] Processing user query with Instructor...
2025-06-18 22:58:59,293 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...Wk0A'. Usage in last 60s: 1/5.
2025-06-18 22:58:59,442 [INFO] [app.agents.base_agent:816] - [UserIntentAgent] 尝试 1/5 使用密钥 '...Wk0A'
2025-06-18 22:58:59,448 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:59:01,657 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-06-18 22:59:01,658 [INFO] [google_genai.models:6061] - AFC remote call 1 is done.
2025-06-18 22:59:01,667 [INFO] [app.agents.user_intent_agent:68] - [UserIntentAgent][IntentAnalysis_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3] Successfully parsed user intent with Instructor. Topic: '珍珠港前因后果', Slides: 8, Lang: zh-CN
2025-06-18 22:59:01,667 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:307] - 用户意图分析成功: 珍珠港前因后果, 8张幻灯片    
2025-06-18 22:59:01,668 [INFO] [app.apis.v1.slides_router:112] - [SSE Push START] Attempting to push for progress.project_id: 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: intent_analyzed
2025-06-18 22:59:01,668 [INFO] [app.apis.v1.slides_router:113] - [SSE Push START] current connected_clients: ['6c188575-6e6a-4aaf-aff8-424f37b36fb3']
2025-06-18 22:59:01,668 [INFO] [app.apis.v1.slides_router:114] - [SSE Push START] current temp_id_to_actual_id_map: {'proj_04ed68d5': '6c188575-6e6a-4aaf-aff8-424f37b36fb3'}
2025-06-18 22:59:01,668 [INFO] [app.apis.v1.slides_router:179] - [SSE Push SUCCESS] Pushed to 1 client(s) for ID 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: intent_analyzed
2025-06-18 22:59:01,673 [INFO] [app.agents.presentation_workflow:537] - 聊天消息已保存到数据库: ai - ✅ 已理解: 珍珠港前因后果, 8张幻灯片...   
2025-06-18 22:59:01,673 [INFO] [app.apis.v1.slides_router:112] - [SSE Push START] Attempting to push for progress.project_id: 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: generating_outline_and_style
2025-06-18 22:59:01,673 [INFO] [app.apis.v1.slides_router:113] - [SSE Push START] current connected_clients: ['6c188575-6e6a-4aaf-aff8-424f37b36fb3']
2025-06-18 22:59:01,674 [INFO] [app.apis.v1.slides_router:114] - [SSE Push START] current temp_id_to_actual_id_map: {'proj_04ed68d5': '6c188575-6e6a-4aaf-aff8-424f37b36fb3'}
2025-06-18 22:59:01,674 [INFO] [app.apis.v1.slides_router:179] - [SSE Push SUCCESS] Pushed to 1 client(s) for ID 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: generating_outline_and_style
2025-06-18 22:59:01,678 [INFO] [app.agents.presentation_workflow:537] - 聊天消息已保存到数据库: ai - 🎨 设计整体风格和大纲......
2025-06-18 22:59:01,679 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:319] - 开始调用VisualStyleAgent.process，参数: topic= 
珍珠港前因后果, num_slides=8
2025-06-18 22:59:01,679 [INFO] [app.agents.visual_style_agent:52] - [VisualStyleAgent][VisualStyleGen_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3] Generating visual style and outlines with Instructor...
2025-06-18 22:59:01,680 [INFO] [app.agents.visual_style_agent:83] - [VisualStyleAgent][VisualStyleGen_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3] 使用完整的StructuredPresentationStyleSchema...
2025-06-18 22:59:01,686 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...eCPQ'. Usage in last 60s: 1/5.
2025-06-18 22:59:01,802 [INFO] [app.agents.base_agent:816] - [VisualStyleAgent] 尝试 1/5 使用密钥 '...eCPQ'
2025-06-18 22:59:01,811 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:59:29,784 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-06-18 22:59:29,786 [INFO] [google_genai.models:6061] - AFC remote call 1 is done.
2025-06-18 22:59:29,795 [INFO] [app.agents.visual_style_agent:91] - [VisualStyleAgent][VisualStyleGen_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3] 完整设计系统生成成功！
2025-06-18 22:59:29,795 [INFO] [app.agents.visual_style_agent:92] - 设计系统摘要: 一种融合了深沉历史感与现代视觉冲击力的风格。采用深色调为主，
通过强烈的对比色和有力的字体来表现事件的严肃性和震撼力，旨在引导观众沉浸于历史的厚重与命运的回响。...
2025-06-18 22:59:29,795 [INFO] [app.agents.visual_style_agent:93] - 生成了 8 个幻灯片大纲
2025-06-18 22:59:29,795 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:326] - VisualStyleAgent.process调用完成，llm_log_id=aadceef4-ec7e-4e44-99ab-de12be1ce557
2025-06-18 22:59:29,795 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:329] - 风格和大纲生成成功，返回类型: <class 'app.models.presentation_model.StructuredPresentationStyleSchema'>
2025-06-18 22:59:29,796 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:330] - 大纲数量: 8
2025-06-18 22:59:29,796 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:175] - 整体风格和大纲生成成功，共 8 张幻灯片
2025-06-18 22:59:29,796 [INFO] [app.apis.v1.slides_router:112] - [SSE Push START] Attempting to push for progress.project_id: 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: outline_and_style_generated
2025-06-18 22:59:29,796 [INFO] [app.apis.v1.slides_router:113] - [SSE Push START] current connected_clients: ['6c188575-6e6a-4aaf-aff8-424f37b36fb3']
2025-06-18 22:59:29,796 [INFO] [app.apis.v1.slides_router:114] - [SSE Push START] current temp_id_to_actual_id_map: {'proj_04ed68d5': '6c188575-6e6a-4aaf-aff8-424f37b36fb3'}
2025-06-18 22:59:29,796 [INFO] [app.apis.v1.slides_router:179] - [SSE Push SUCCESS] Pushed to 1 client(s) for ID 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: outline_and_style_generated
2025-06-18 22:59:29,801 [INFO] [app.agents.presentation_workflow:537] - 聊天消息已保存到数据库: ai - ✅ 设计完成，共 8 张幻灯片...
2025-06-18 22:59:29,802 [INFO] [app.apis.v1.slides_router:112] - [SSE Push START] Attempting to push for progress.project_id: 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: starting_parallel_generation
2025-06-18 22:59:29,802 [INFO] [app.apis.v1.slides_router:113] - [SSE Push START] current connected_clients: ['6c188575-6e6a-4aaf-aff8-424f37b36fb3']
2025-06-18 22:59:29,802 [INFO] [app.apis.v1.slides_router:114] - [SSE Push START] current temp_id_to_actual_id_map: {'proj_04ed68d5': '6c188575-6e6a-4aaf-aff8-424f37b36fb3'}
2025-06-18 22:59:29,803 [INFO] [app.apis.v1.slides_router:179] - [SSE Push SUCCESS] Pushed to 1 client(s) for ID 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: starting_parallel_generation
2025-06-18 22:59:29,810 [INFO] [app.agents.presentation_workflow:537] - 聊天消息已保存到数据库: ai - 🚀 开始并行生成幻灯片......
2025-06-18 22:59:29,810 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:212] - 开始细化第 1 张幻灯片蓝图...
2025-06-18 22:59:29,811 [INFO] [app.agents.slide_detailer_agent:155] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_1] 正 
在根据大纲细化第 1 张幻灯片的详细蓝图...
2025-06-18 22:59:29,819 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...VJGU'. Usage in last 60s: 1/5.
2025-06-18 22:59:29,932 [INFO] [app.agents.base_agent:816] - [SlideDetailerAgent] 尝试 1/5 使用密钥 '...VJGU'
2025-06-18 22:59:29,936 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:59:30,723 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 400 Bad Request"
2025-06-18 22:59:30,724 [ERROR] [app.agents.base_agent:892] - [SlideDetailerAgent] Instructor调用失败，出现不可重试的错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 168, in retry_sync
    response = func(*args, **kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client_genai.py", line 66, in sync_wrapper
    return client.models.generate_content(*args, **kwargs)  # type:ignore
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 6058, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 5007, in _generate_content
    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 927, in request
    response = self._request(http_request, stream=False)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 793, in _request
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\errors.py", line 104, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 163, in retry_sync
    for attempt in max_retries:
                   ^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 445, in __iter__
    do = self.iter(retry_state=retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 421, in exc_check
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x17d3b37df90 state=finished raised ClientError>]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\agents\base_agent.py", line 817, in _call_llm_with_instructor
    response_model = await asyncio.to_thread(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "D:\miniconda3\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\miniconda3\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client.py", line 366, in create
    return self.create_fn(
           ~~~~~~~~~~~~~~^
        response_model=response_model,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\patch.py", line 193, in new_create_sync
    response = retry_sync(
        func=func,  # type: ignore
    ...<7 lines>...
        mode=mode,
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 194, in retry_sync
    raise InstructorRetryException(
    ...<9 lines>...
    ) from e
instructor.exceptions.InstructorRetryException: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
2025-06-18 22:59:30,821 [ERROR] [app.agents.slide_detailer_agent:186] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_1] 详
细蓝图生成失败，LLM返回空响应或Pydantic验证失败。
2025-06-18 22:59:30,821 [ERROR] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:417] - 第 1 张幻灯片蓝图细化失败
2025-06-18 22:59:30,821 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:212] - 开始细化第 2 张幻灯片蓝图...
2025-06-18 22:59:30,821 [INFO] [app.agents.slide_detailer_agent:155] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_2] 正 
在根据大纲细化第 2 张幻灯片的详细蓝图...
2025-06-18 22:59:30,827 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...5iK0'. Usage in last 60s: 1/5.
2025-06-18 22:59:30,938 [INFO] [app.agents.base_agent:816] - [SlideDetailerAgent] 尝试 1/5 使用密钥 '...5iK0'
2025-06-18 22:59:30,942 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:59:31,708 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 400 Bad Request"
2025-06-18 22:59:31,709 [ERROR] [app.agents.base_agent:892] - [SlideDetailerAgent] Instructor调用失败，出现不可重试的错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 168, in retry_sync
    response = func(*args, **kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client_genai.py", line 66, in sync_wrapper
    return client.models.generate_content(*args, **kwargs)  # type:ignore
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 6058, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 5007, in _generate_content
    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 927, in request
    response = self._request(http_request, stream=False)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 793, in _request
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\errors.py", line 104, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 163, in retry_sync
    for attempt in max_retries:
                   ^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 445, in __iter__
    do = self.iter(retry_state=retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 421, in exc_check
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x17d3b599810 state=finished raised ClientError>]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\agents\base_agent.py", line 817, in _call_llm_with_instructor
    response_model = await asyncio.to_thread(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "D:\miniconda3\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\miniconda3\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client.py", line 366, in create
    return self.create_fn(
           ~~~~~~~~~~~~~~^
        response_model=response_model,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\patch.py", line 193, in new_create_sync
    response = retry_sync(
        func=func,  # type: ignore
    ...<7 lines>...
        mode=mode,
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 194, in retry_sync
    raise InstructorRetryException(
    ...<9 lines>...
    ) from e
instructor.exceptions.InstructorRetryException: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
2025-06-18 22:59:31,723 [ERROR] [app.agents.slide_detailer_agent:186] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_2] 详
细蓝图生成失败，LLM返回空响应或Pydantic验证失败。
2025-06-18 22:59:31,723 [ERROR] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:417] - 第 2 张幻灯片蓝图细化失败
2025-06-18 22:59:31,835 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:212] - 开始细化第 3 张幻灯片蓝图...
2025-06-18 22:59:31,835 [INFO] [app.agents.slide_detailer_agent:155] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_3] 正
在根据大纲细化第 3 张幻灯片的详细蓝图...
2025-06-18 22:59:31,841 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...ChBw'. Usage in last 60s: 1/5.
2025-06-18 22:59:31,953 [INFO] [app.agents.base_agent:816] - [SlideDetailerAgent] 尝试 1/5 使用密钥 '...ChBw'
2025-06-18 22:59:31,957 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:59:32,715 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 400 Bad Request"
2025-06-18 22:59:32,716 [ERROR] [app.agents.base_agent:892] - [SlideDetailerAgent] Instructor调用失败，出现不可重试的错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 168, in retry_sync
    response = func(*args, **kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client_genai.py", line 66, in sync_wrapper
    return client.models.generate_content(*args, **kwargs)  # type:ignore
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 6058, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 5007, in _generate_content
    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 927, in request
    response = self._request(http_request, stream=False)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 793, in _request
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\errors.py", line 104, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 163, in retry_sync
    for attempt in max_retries:
                   ^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 445, in __iter__
    do = self.iter(retry_state=retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 421, in exc_check
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x17d3b59b230 state=finished raised ClientError>]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\agents\base_agent.py", line 817, in _call_llm_with_instructor
    response_model = await asyncio.to_thread(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "D:\miniconda3\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\miniconda3\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client.py", line 366, in create
    return self.create_fn(
           ~~~~~~~~~~~~~~^
        response_model=response_model,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\patch.py", line 193, in new_create_sync
    response = retry_sync(
        func=func,  # type: ignore
    ...<7 lines>...
        mode=mode,
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 194, in retry_sync
    raise InstructorRetryException(
    ...<9 lines>...
    ) from e
instructor.exceptions.InstructorRetryException: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
2025-06-18 22:59:32,750 [ERROR] [app.agents.slide_detailer_agent:186] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_3] 详
细蓝图生成失败，LLM返回空响应或Pydantic验证失败。
2025-06-18 22:59:32,750 [ERROR] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:417] - 第 3 张幻灯片蓝图细化失败
2025-06-18 22:59:32,847 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:212] - 开始细化第 4 张幻灯片蓝图...
2025-06-18 22:59:32,847 [INFO] [app.agents.slide_detailer_agent:155] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_4] 正
在根据大纲细化第 4 张幻灯片的详细蓝图...
2025-06-18 22:59:32,853 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...Wk0A'. Usage in last 60s: 2/5.
2025-06-18 22:59:32,966 [INFO] [app.agents.base_agent:816] - [SlideDetailerAgent] 尝试 1/5 使用密钥 '...Wk0A'
2025-06-18 22:59:32,970 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:59:33,719 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 400 Bad Request"
2025-06-18 22:59:33,720 [ERROR] [app.agents.base_agent:892] - [SlideDetailerAgent] Instructor调用失败，出现不可重试的错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 168, in retry_sync
    response = func(*args, **kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client_genai.py", line 66, in sync_wrapper
    return client.models.generate_content(*args, **kwargs)  # type:ignore
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 6058, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 5007, in _generate_content
    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 927, in request
    response = self._request(http_request, stream=False)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 793, in _request
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\errors.py", line 104, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 163, in retry_sync
    for attempt in max_retries:
                   ^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 445, in __iter__
    do = self.iter(retry_state=retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 421, in exc_check
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x17d3b590dd0 state=finished raised ClientError>]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\agents\base_agent.py", line 817, in _call_llm_with_instructor
    response_model = await asyncio.to_thread(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "D:\miniconda3\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\miniconda3\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client.py", line 366, in create
    return self.create_fn(
           ~~~~~~~~~~~~~~^
        response_model=response_model,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\patch.py", line 193, in new_create_sync
    response = retry_sync(
        func=func,  # type: ignore
    ...<7 lines>...
        mode=mode,
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 194, in retry_sync
    raise InstructorRetryException(
    ...<9 lines>...
    ) from e
instructor.exceptions.InstructorRetryException: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
2025-06-18 22:59:33,730 [ERROR] [app.agents.slide_detailer_agent:186] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_4] 详
细蓝图生成失败，LLM返回空响应或Pydantic验证失败。
2025-06-18 22:59:33,731 [ERROR] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:417] - 第 4 张幻灯片蓝图细化失败
2025-06-18 22:59:33,856 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:212] - 开始细化第 5 张幻灯片蓝图...
2025-06-18 22:59:33,856 [INFO] [app.agents.slide_detailer_agent:155] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_5] 正
在根据大纲细化第 5 张幻灯片的详细蓝图...
2025-06-18 22:59:33,863 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...eCPQ'. Usage in last 60s: 2/5.
2025-06-18 22:59:33,981 [INFO] [app.agents.base_agent:816] - [SlideDetailerAgent] 尝试 1/5 使用密钥 '...eCPQ'
2025-06-18 22:59:33,984 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:59:34,812 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 400 Bad Request"
2025-06-18 22:59:34,813 [ERROR] [app.agents.base_agent:892] - [SlideDetailerAgent] Instructor调用失败，出现不可重试的错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 168, in retry_sync
    response = func(*args, **kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client_genai.py", line 66, in sync_wrapper
    return client.models.generate_content(*args, **kwargs)  # type:ignore
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 6058, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 5007, in _generate_content
    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 927, in request
    response = self._request(http_request, stream=False)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 793, in _request
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\errors.py", line 104, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 163, in retry_sync
    for attempt in max_retries:
                   ^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 445, in __iter__
    do = self.iter(retry_state=retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 421, in exc_check
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x17d3b5aa7a0 state=finished raised ClientError>]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\agents\base_agent.py", line 817, in _call_llm_with_instructor
    response_model = await asyncio.to_thread(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "D:\miniconda3\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\miniconda3\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client.py", line 366, in create
    return self.create_fn(
           ~~~~~~~~~~~~~~^
        response_model=response_model,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\patch.py", line 193, in new_create_sync
    response = retry_sync(
        func=func,  # type: ignore
    ...<7 lines>...
        mode=mode,
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 194, in retry_sync
    raise InstructorRetryException(
    ...<9 lines>...
    ) from e
instructor.exceptions.InstructorRetryException: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
2025-06-18 22:59:34,823 [ERROR] [app.agents.slide_detailer_agent:186] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_5] 详
细蓝图生成失败，LLM返回空响应或Pydantic验证失败。
2025-06-18 22:59:34,823 [ERROR] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:417] - 第 5 张幻灯片蓝图细化失败
2025-06-18 22:59:34,862 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:212] - 开始细化第 6 张幻灯片蓝图...
2025-06-18 22:59:34,862 [INFO] [app.agents.slide_detailer_agent:155] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_6] 正
在根据大纲细化第 6 张幻灯片的详细蓝图...
2025-06-18 22:59:34,868 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...VJGU'. Usage in last 60s: 2/5.
2025-06-18 22:59:34,983 [INFO] [app.agents.base_agent:816] - [SlideDetailerAgent] 尝试 1/5 使用密钥 '...VJGU'
2025-06-18 22:59:34,987 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:59:35,800 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 400 Bad Request"
2025-06-18 22:59:35,801 [ERROR] [app.agents.base_agent:892] - [SlideDetailerAgent] Instructor调用失败，出现不可重试的错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 168, in retry_sync
    response = func(*args, **kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client_genai.py", line 66, in sync_wrapper
    return client.models.generate_content(*args, **kwargs)  # type:ignore
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 6058, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 5007, in _generate_content
    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 927, in request
    response = self._request(http_request, stream=False)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 793, in _request
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\errors.py", line 104, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 163, in retry_sync
    for attempt in max_retries:
                   ^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 445, in __iter__
    do = self.iter(retry_state=retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 421, in exc_check
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x17d3b5d98c0 state=finished raised ClientError>]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\agents\base_agent.py", line 817, in _call_llm_with_instructor
    response_model = await asyncio.to_thread(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "D:\miniconda3\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\miniconda3\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client.py", line 366, in create
    return self.create_fn(
           ~~~~~~~~~~~~~~^
        response_model=response_model,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\patch.py", line 193, in new_create_sync
    response = retry_sync(
        func=func,  # type: ignore
    ...<7 lines>...
        mode=mode,
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 194, in retry_sync
    raise InstructorRetryException(
    ...<9 lines>...
    ) from e
instructor.exceptions.InstructorRetryException: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
2025-06-18 22:59:35,811 [ERROR] [app.agents.slide_detailer_agent:186] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_6] 详
细蓝图生成失败，LLM返回空响应或Pydantic验证失败。
2025-06-18 22:59:35,811 [ERROR] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:417] - 第 6 张幻灯片蓝图细化失败
2025-06-18 22:59:35,875 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:212] - 开始细化第 7 张幻灯片蓝图...
2025-06-18 22:59:35,876 [INFO] [app.agents.slide_detailer_agent:155] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_7] 正
在根据大纲细化第 7 张幻灯片的详细蓝图...
2025-06-18 22:59:35,882 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...5iK0'. Usage in last 60s: 2/5.
2025-06-18 22:59:35,994 [INFO] [app.agents.base_agent:816] - [SlideDetailerAgent] 尝试 1/5 使用密钥 '...5iK0'
2025-06-18 22:59:35,997 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:59:36,791 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 400 Bad Request"
2025-06-18 22:59:36,792 [ERROR] [app.agents.base_agent:892] - [SlideDetailerAgent] Instructor调用失败，出现不可重试的错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 168, in retry_sync
    response = func(*args, **kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client_genai.py", line 66, in sync_wrapper
    return client.models.generate_content(*args, **kwargs)  # type:ignore
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 6058, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 5007, in _generate_content
    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 927, in request
    response = self._request(http_request, stream=False)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 793, in _request
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\errors.py", line 104, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 163, in retry_sync
    for attempt in max_retries:
                   ^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 445, in __iter__
    do = self.iter(retry_state=retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 421, in exc_check
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x17d3b44fe50 state=finished raised ClientError>]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\agents\base_agent.py", line 817, in _call_llm_with_instructor
    response_model = await asyncio.to_thread(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "D:\miniconda3\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\miniconda3\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client.py", line 366, in create
    return self.create_fn(
           ~~~~~~~~~~~~~~^
        response_model=response_model,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\patch.py", line 193, in new_create_sync
    response = retry_sync(
        func=func,  # type: ignore
    ...<7 lines>...
        mode=mode,
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 194, in retry_sync
    raise InstructorRetryException(
    ...<9 lines>...
    ) from e
instructor.exceptions.InstructorRetryException: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
2025-06-18 22:59:36,801 [ERROR] [app.agents.slide_detailer_agent:186] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_7] 详
细蓝图生成失败，LLM返回空响应或Pydantic验证失败。
2025-06-18 22:59:36,802 [ERROR] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:417] - 第 7 张幻灯片蓝图细化失败
2025-06-18 22:59:36,885 [INFO] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:212] - 开始细化第 8 张幻灯片蓝图...
2025-06-18 22:59:36,885 [INFO] [app.agents.slide_detailer_agent:155] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_8] 正
在根据大纲细化第 8 张幻灯片的详细蓝图...
2025-06-18 22:59:36,891 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...ChBw'. Usage in last 60s: 2/5.
2025-06-18 22:59:37,004 [INFO] [app.agents.base_agent:816] - [SlideDetailerAgent] 尝试 1/5 使用密钥 '...ChBw'
2025-06-18 22:59:37,008 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:59:37,918 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 400 Bad Request"
2025-06-18 22:59:37,918 [ERROR] [app.agents.base_agent:892] - [SlideDetailerAgent] Instructor调用失败，出现不可重试的错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 168, in retry_sync
    response = func(*args, **kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client_genai.py", line 66, in sync_wrapper
    return client.models.generate_content(*args, **kwargs)  # type:ignore
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 6058, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 5007, in _generate_content
    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 927, in request
    response = self._request(http_request, stream=False)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 793, in _request
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\errors.py", line 104, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 163, in retry_sync
    for attempt in max_retries:
                   ^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 445, in __iter__
    do = self.iter(retry_state=retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 421, in exc_check
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x17d3b2fbe50 state=finished raised ClientError>]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\agents\base_agent.py", line 817, in _call_llm_with_instructor
    response_model = await asyncio.to_thread(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "D:\miniconda3\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\miniconda3\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client.py", line 366, in create
    return self.create_fn(
           ~~~~~~~~~~~~~~^
        response_model=response_model,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\patch.py", line 193, in new_create_sync
    response = retry_sync(
        func=func,  # type: ignore
    ...<7 lines>...
        mode=mode,
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 194, in retry_sync
    raise InstructorRetryException(
    ...<9 lines>...
    ) from e
instructor.exceptions.InstructorRetryException: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties["final_chart_js_config"].properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
2025-06-18 22:59:37,927 [ERROR] [app.agents.slide_detailer_agent:186] - [DetailBlueprint_Proj_6c188575-6e6a-4aaf-aff8-424f37b36fb3_Slide_8] 详
细蓝图生成失败，LLM返回空响应或Pydantic验证失败。
2025-06-18 22:59:37,928 [ERROR] [project_generation.6c188575-6e6a-4aaf-aff8-424f37b36fb3:417] - 第 8 张幻灯片蓝图细化失败
2025-06-18 22:59:37,928 [INFO] [app.apis.v1.slides_router:112] - [SSE Push START] Attempting to push for progress.project_id: 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: completed_with_errors
2025-06-18 22:59:37,929 [INFO] [app.apis.v1.slides_router:113] - [SSE Push START] current connected_clients: ['6c188575-6e6a-4aaf-aff8-424f37b36fb3']
2025-06-18 22:59:37,929 [INFO] [app.apis.v1.slides_router:114] - [SSE Push START] current temp_id_to_actual_id_map: {'proj_04ed68d5': '6c188575-6e6a-4aaf-aff8-424f37b36fb3'}
2025-06-18 22:59:37,929 [INFO] [app.apis.v1.slides_router:179] - [SSE Push SUCCESS] Pushed to 1 client(s) for ID 6c188575-6e6a-4aaf-aff8-424f37b36fb3, status: completed_with_errors
2025-06-18 22:59:37,933 [INFO] [app.agents.presentation_workflow:537] - 聊天消息已保存到数据库: ai - ⚠️ 部分完成：0/8 张幻灯片生成成功...
INFO:     Shutting down
INFO:     Waiting for connections to close. (CTRL+C to force quit)
