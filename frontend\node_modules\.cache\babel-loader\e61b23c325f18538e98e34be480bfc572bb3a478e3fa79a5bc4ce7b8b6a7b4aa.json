{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\views\\\\ChatView.js\",\n  _s = $RefreshSig$();\n// frontend/src/views/ChatView.js\nimport React, { useState, useRef, useEffect } from 'react';\nimport MessageInput from '../components/MessageInput';\nimport ChatMessage from '../components/ChatMessage';\nimport apiService from '../services/api';\nimport { FaPlus, FaComments } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatView = ({\n  currentChatId,\n  initialMessages = []\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  // 使用initialMessages初始化聊天记录\n  useEffect(() => {\n    if (initialMessages && initialMessages.length > 0) {\n      setMessages(initialMessages);\n    }\n  }, [initialMessages]);\n\n  // 自动滚动到最新消息\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages]);\n  const handleSendMessage = async messageData => {\n    // 兼容旧版本调用（直接传字符串）和新版本（传对象）\n    const messageText = typeof messageData === 'string' ? messageData : messageData.message;\n    const files = typeof messageData === 'object' ? messageData.files : [];\n    if (!(messageText !== null && messageText !== void 0 && messageText.trim()) && (!files || files.length === 0) || isGenerating) return;\n\n    // 添加用户消息\n    const userMessage = {\n      id: `user-${Date.now()}`,\n      sender: 'user',\n      text: messageText || '发送了文件',\n      files: files || [],\n      timestamp: new Date().toISOString()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setIsGenerating(true);\n    try {\n      // 开始流式AI响应\n      await apiService.streamChatResponse(messageText, progressData => {\n        setMessages(prevMessages => {\n          const newMessages = [...prevMessages];\n\n          // 处理系统消息 - 总是作为新的、独立的系统消息添加\n          if (progressData.sender === 'system') {\n            newMessages.push({\n              ...progressData,\n              type: 'system-info',\n              timestamp: progressData.timestamp || new Date().toISOString()\n            });\n            return newMessages;\n          }\n\n          // 处理AI消息 (sender === 'ai')\n          if (progressData.sender === 'ai') {\n            // 查找具有相同ID的现有AI消息\n            const aiMessageIndex = newMessages.findIndex(msg => msg.id === progressData.id);\n            if (aiMessageIndex !== -1) {\n              // 找到现有消息\n              const existingMessage = newMessages[aiMessageIndex];\n              if (progressData.is_append) {\n                // 如果是追加内容，将文本追加到现有消息\n                newMessages[aiMessageIndex] = {\n                  ...existingMessage,\n                  text: (existingMessage.text || '') + (progressData.text || ''),\n                  is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,\n                  timestamp: progressData.timestamp || existingMessage.timestamp\n                };\n              } else {\n                // 如果不是追加，更新整个消息状态\n                newMessages[aiMessageIndex] = {\n                  ...existingMessage,\n                  text: progressData.text !== undefined ? progressData.text : existingMessage.text,\n                  is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,\n                  stream_complete: progressData.stream_complete !== undefined ? progressData.stream_complete : existingMessage.stream_complete,\n                  timestamp: progressData.timestamp || existingMessage.timestamp\n                };\n              }\n            } else {\n              // 如果未找到现有AI消息，作为新消息添加\n              newMessages.push({\n                ...progressData,\n                type: 'ai',\n                timestamp: progressData.timestamp || new Date().toISOString()\n              });\n            }\n          }\n          return newMessages;\n        });\n      }, error => {\n        console.error('Chat streaming error:', error);\n        setIsGenerating(false);\n        setMessages(prev => [...prev, {\n          id: `error-${Date.now()}`,\n          sender: 'system',\n          type: 'ai_error',\n          text: `发生错误: ${error.message}`,\n          timestamp: new Date().toISOString()\n        }]);\n      }, () => {\n        // 完成回调\n        setIsGenerating(false);\n      }, currentChatId // 传递当前聊天ID\n      );\n    } catch (apiError) {\n      console.error('API call failed to start:', apiError);\n      setIsGenerating(false);\n      setMessages(prev => [...prev, {\n        id: `error-api-${Date.now()}`,\n        sender: 'system',\n        type: 'ai_error',\n        text: `无法连接到AI聊天服务: ${apiError.message}`,\n        timestamp: new Date().toISOString()\n      }]);\n    }\n  };\n  const handleNewChat = () => {\n    setMessages([]);\n    setIsGenerating(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-screen bg-gray-50 flex-1\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-16 px-3 border-b border-gray-200 bg-white flex items-center sticky top-0 z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleNewChat,\n        className: \"p-2 mr-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100\",\n        title: \"\\u65B0\\u804A\\u5929\",\n        children: /*#__PURE__*/_jsxDEV(FaPlus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 text-base font-medium text-gray-800\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"py-1 px-2 rounded\",\n          children: \"AI \\u804A\\u5929\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4 space-y-5 custom-scrollbar\",\n      children: [messages.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center h-full text-gray-400 p-8 min-h-[400px]\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-6xl mb-6 text-gray-300\",\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-2xl font-medium\",\n          children: \"\\u5F00\\u59CB\\u4F60\\u7684AI\\u5BF9\\u8BDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 mt-2 text-center max-w-md\",\n          children: \"\\u5728\\u8FD9\\u91CC\\u4E0EAI\\u81EA\\u7531\\u4EA4\\u6D41\\uFF0C\\u63D0\\u51FA\\u95EE\\u9898\\u6216\\u5BFB\\u6C42\\u5E2E\\u52A9\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 21\n      }, this), messages.map((msg, index) => /*#__PURE__*/_jsxDEV(ChatMessage, {\n        message: msg\n      }, msg.id || index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 21\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2 bg-gray-50 sticky bottom-0 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(MessageInput, {\n        onSendMessage: handleSendMessage,\n        isGenerating: isGenerating,\n        placeholder: \"\\u8F93\\u5165\\u4F60\\u7684\\u6D88\\u606F...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 9\n  }, this);\n};\n_s(ChatView, \"YQnbCWhOpv4/t3VosIUcwuR7wW4=\");\n_c = ChatView;\nexport default ChatView;\nvar _c;\n$RefreshReg$(_c, \"ChatView\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "MessageInput", "ChatMessage", "apiService", "FaPlus", "FaComments", "jsxDEV", "_jsxDEV", "ChatView", "currentChatId", "initialMessages", "_s", "messages", "setMessages", "isGenerating", "setIsGenerating", "messagesEndRef", "length", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "messageData", "messageText", "message", "files", "trim", "userMessage", "id", "Date", "now", "sender", "text", "timestamp", "toISOString", "prev", "streamChatResponse", "progressData", "prevMessages", "newMessages", "push", "type", "aiMessageIndex", "findIndex", "msg", "existingMessage", "is_append", "is_streaming", "undefined", "stream_complete", "error", "console", "apiError", "handleNewChat", "className", "children", "onClick", "title", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "index", "ref", "onSendMessage", "placeholder", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/views/ChatView.js"], "sourcesContent": ["// frontend/src/views/ChatView.js\nimport React, { useState, useRef, useEffect } from 'react';\nimport MessageInput from '../components/MessageInput';\nimport ChatMessage from '../components/ChatMessage';\nimport apiService from '../services/api';\nimport { FaPlus, FaComments } from 'react-icons/fa';\n\nconst ChatView = ({ currentChatId, initialMessages = [] }) => {\n    const [messages, setMessages] = useState([]);\n    const [isGenerating, setIsGenerating] = useState(false);\n    const messagesEndRef = useRef(null);\n\n    // 使用initialMessages初始化聊天记录\n    useEffect(() => {\n        if (initialMessages && initialMessages.length > 0) {\n            setMessages(initialMessages);\n        }\n    }, [initialMessages]);\n\n    // 自动滚动到最新消息\n    useEffect(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n    }, [messages]);\n\n    const handleSendMessage = async (messageData) => {\n        // 兼容旧版本调用（直接传字符串）和新版本（传对象）\n        const messageText = typeof messageData === 'string' ? messageData : messageData.message;\n        const files = typeof messageData === 'object' ? messageData.files : [];\n        \n        if ((!messageText?.trim() && (!files || files.length === 0)) || isGenerating) return;\n\n        // 添加用户消息\n        const userMessage = {\n            id: `user-${Date.now()}`,\n            sender: 'user',\n            text: messageText || '发送了文件',\n            files: files || [],\n            timestamp: new Date().toISOString()\n        };\n        setMessages(prev => [...prev, userMessage]);\n        setIsGenerating(true);\n\n        try {\n            // 开始流式AI响应\n            await apiService.streamChatResponse(\n                messageText,\n                (progressData) => {\n                    setMessages(prevMessages => {\n                        const newMessages = [...prevMessages];\n\n                        // 处理系统消息 - 总是作为新的、独立的系统消息添加\n                        if (progressData.sender === 'system') {\n                            newMessages.push({\n                                ...progressData,\n                                type: 'system-info',\n                                timestamp: progressData.timestamp || new Date().toISOString()\n                            });\n                            return newMessages;\n                        }\n\n                        // 处理AI消息 (sender === 'ai')\n                        if (progressData.sender === 'ai') {\n                            // 查找具有相同ID的现有AI消息\n                            const aiMessageIndex = newMessages.findIndex(msg => msg.id === progressData.id);\n\n                            if (aiMessageIndex !== -1) {\n                                // 找到现有消息\n                                const existingMessage = newMessages[aiMessageIndex];\n                                \n                                if (progressData.is_append) {\n                                    // 如果是追加内容，将文本追加到现有消息\n                                    newMessages[aiMessageIndex] = {\n                                        ...existingMessage,\n                                        text: (existingMessage.text || '') + (progressData.text || ''),\n                                        is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,\n                                        timestamp: progressData.timestamp || existingMessage.timestamp\n                                    };\n                                } else {\n                                    // 如果不是追加，更新整个消息状态\n                                    newMessages[aiMessageIndex] = {\n                                        ...existingMessage,\n                                        text: progressData.text !== undefined ? progressData.text : existingMessage.text,\n                                        is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,\n                                        stream_complete: progressData.stream_complete !== undefined ? progressData.stream_complete : existingMessage.stream_complete,\n                                        timestamp: progressData.timestamp || existingMessage.timestamp\n                                    };\n                                }\n                            } else {\n                                // 如果未找到现有AI消息，作为新消息添加\n                                newMessages.push({\n                                    ...progressData,\n                                    type: 'ai',\n                                    timestamp: progressData.timestamp || new Date().toISOString()\n                                });\n                            }\n                        }\n                        return newMessages;\n                    });\n                },\n                (error) => {\n                    console.error('Chat streaming error:', error);\n                    setIsGenerating(false);\n                    setMessages(prev => [...prev, {\n                        id: `error-${Date.now()}`,\n                        sender: 'system',\n                        type: 'ai_error',\n                        text: `发生错误: ${error.message}`,\n                        timestamp: new Date().toISOString()\n                    }]);\n                },\n                () => {\n                    // 完成回调\n                    setIsGenerating(false);\n                },\n                currentChatId // 传递当前聊天ID\n            );\n        } catch (apiError) {\n            console.error('API call failed to start:', apiError);\n            setIsGenerating(false);\n            setMessages(prev => [...prev, {\n                id: `error-api-${Date.now()}`,\n                sender: 'system',\n                type: 'ai_error',\n                text: `无法连接到AI聊天服务: ${apiError.message}`,\n                timestamp: new Date().toISOString()\n            }]);\n        }\n    };\n\n    const handleNewChat = () => {\n        setMessages([]);\n        setIsGenerating(false);\n    };\n\n    return (\n        <div className=\"flex flex-col h-screen bg-gray-50 flex-1\">\n            {/* Header for Chat View */}\n            <div className=\"h-16 px-3 border-b border-gray-200 bg-white flex items-center sticky top-0 z-10\">\n                <button \n                  onClick={handleNewChat}\n                  className=\"p-2 mr-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100\"\n                  title=\"新聊天\"\n                >\n                    <FaPlus size={16} />\n                </button>\n                <div className=\"flex-1 text-base font-medium text-gray-800\">\n                    <span className=\"py-1 px-2 rounded\">AI 聊天</span>\n                </div>\n            </div>\n\n            {/* Chat Messages Area */}\n            <div className=\"flex-1 overflow-y-auto p-4 space-y-5 custom-scrollbar\">\n                {messages.length === 0 && (\n                    <div className=\"flex flex-col items-center justify-center h-full text-gray-400 p-8 min-h-[400px]\">\n                        <span className=\"text-6xl mb-6 text-gray-300\">💬</span>\n                        <span className=\"text-2xl font-medium\">开始你的AI对话</span>\n                        <p className=\"text-sm text-gray-400 mt-2 text-center max-w-md\">\n                            在这里与AI自由交流，提出问题或寻求帮助。\n                        </p>\n                    </div>\n                )}\n                {messages.map((msg, index) => (\n                    <ChatMessage key={msg.id || index} message={msg} /> \n                ))}\n                <div ref={messagesEndRef} />\n            </div>\n\n            {/* Message Input Area */}\n            <div className=\"p-2 bg-gray-50 sticky bottom-0 border-t border-gray-200\">\n                <MessageInput \n                    onSendMessage={handleSendMessage} \n                    isGenerating={isGenerating}\n                    placeholder=\"输入你的消息...\"\n                />\n            </div>\n        </div>\n    );\n};\n\nexport default ChatView;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,MAAM,EAAEC,UAAU,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,aAAa;EAAEC,eAAe,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAMkB,cAAc,GAAGjB,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACAC,SAAS,CAAC,MAAM;IACZ,IAAIU,eAAe,IAAIA,eAAe,CAACO,MAAM,GAAG,CAAC,EAAE;MAC/CJ,WAAW,CAACH,eAAe,CAAC;IAChC;EACJ,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACAV,SAAS,CAAC,MAAM;IAAA,IAAAkB,qBAAA;IACZ,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAClE,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;EAEd,MAAMU,iBAAiB,GAAG,MAAOC,WAAW,IAAK;IAC7C;IACA,MAAMC,WAAW,GAAG,OAAOD,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGA,WAAW,CAACE,OAAO;IACvF,MAAMC,KAAK,GAAG,OAAOH,WAAW,KAAK,QAAQ,GAAGA,WAAW,CAACG,KAAK,GAAG,EAAE;IAEtE,IAAK,EAACF,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEG,IAAI,CAAC,CAAC,MAAK,CAACD,KAAK,IAAIA,KAAK,CAACT,MAAM,KAAK,CAAC,CAAC,IAAKH,YAAY,EAAE;;IAE9E;IACA,MAAMc,WAAW,GAAG;MAChBC,EAAE,EAAE,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACxBC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAET,WAAW,IAAI,OAAO;MAC5BE,KAAK,EAAEA,KAAK,IAAI,EAAE;MAClBQ,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACtC,CAAC;IACDtB,WAAW,CAACuB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAER,WAAW,CAAC,CAAC;IAC3Cb,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACA;MACA,MAAMZ,UAAU,CAACkC,kBAAkB,CAC/Bb,WAAW,EACVc,YAAY,IAAK;QACdzB,WAAW,CAAC0B,YAAY,IAAI;UACxB,MAAMC,WAAW,GAAG,CAAC,GAAGD,YAAY,CAAC;;UAErC;UACA,IAAID,YAAY,CAACN,MAAM,KAAK,QAAQ,EAAE;YAClCQ,WAAW,CAACC,IAAI,CAAC;cACb,GAAGH,YAAY;cACfI,IAAI,EAAE,aAAa;cACnBR,SAAS,EAAEI,YAAY,CAACJ,SAAS,IAAI,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;YAChE,CAAC,CAAC;YACF,OAAOK,WAAW;UACtB;;UAEA;UACA,IAAIF,YAAY,CAACN,MAAM,KAAK,IAAI,EAAE;YAC9B;YACA,MAAMW,cAAc,GAAGH,WAAW,CAACI,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAChB,EAAE,KAAKS,YAAY,CAACT,EAAE,CAAC;YAE/E,IAAIc,cAAc,KAAK,CAAC,CAAC,EAAE;cACvB;cACA,MAAMG,eAAe,GAAGN,WAAW,CAACG,cAAc,CAAC;cAEnD,IAAIL,YAAY,CAACS,SAAS,EAAE;gBACxB;gBACAP,WAAW,CAACG,cAAc,CAAC,GAAG;kBAC1B,GAAGG,eAAe;kBAClBb,IAAI,EAAE,CAACa,eAAe,CAACb,IAAI,IAAI,EAAE,KAAKK,YAAY,CAACL,IAAI,IAAI,EAAE,CAAC;kBAC9De,YAAY,EAAEV,YAAY,CAACU,YAAY,KAAKC,SAAS,GAAGX,YAAY,CAACU,YAAY,GAAGF,eAAe,CAACE,YAAY;kBAChHd,SAAS,EAAEI,YAAY,CAACJ,SAAS,IAAIY,eAAe,CAACZ;gBACzD,CAAC;cACL,CAAC,MAAM;gBACH;gBACAM,WAAW,CAACG,cAAc,CAAC,GAAG;kBAC1B,GAAGG,eAAe;kBAClBb,IAAI,EAAEK,YAAY,CAACL,IAAI,KAAKgB,SAAS,GAAGX,YAAY,CAACL,IAAI,GAAGa,eAAe,CAACb,IAAI;kBAChFe,YAAY,EAAEV,YAAY,CAACU,YAAY,KAAKC,SAAS,GAAGX,YAAY,CAACU,YAAY,GAAGF,eAAe,CAACE,YAAY;kBAChHE,eAAe,EAAEZ,YAAY,CAACY,eAAe,KAAKD,SAAS,GAAGX,YAAY,CAACY,eAAe,GAAGJ,eAAe,CAACI,eAAe;kBAC5HhB,SAAS,EAAEI,YAAY,CAACJ,SAAS,IAAIY,eAAe,CAACZ;gBACzD,CAAC;cACL;YACJ,CAAC,MAAM;cACH;cACAM,WAAW,CAACC,IAAI,CAAC;gBACb,GAAGH,YAAY;gBACfI,IAAI,EAAE,IAAI;gBACVR,SAAS,EAAEI,YAAY,CAACJ,SAAS,IAAI,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;cAChE,CAAC,CAAC;YACN;UACJ;UACA,OAAOK,WAAW;QACtB,CAAC,CAAC;MACN,CAAC,EACAW,KAAK,IAAK;QACPC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CpC,eAAe,CAAC,KAAK,CAAC;QACtBF,WAAW,CAACuB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC1BP,EAAE,EAAE,SAASC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UACzBC,MAAM,EAAE,QAAQ;UAChBU,IAAI,EAAE,UAAU;UAChBT,IAAI,EAAE,SAASkB,KAAK,CAAC1B,OAAO,EAAE;UAC9BS,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;QACtC,CAAC,CAAC,CAAC;MACP,CAAC,EACD,MAAM;QACF;QACApB,eAAe,CAAC,KAAK,CAAC;MAC1B,CAAC,EACDN,aAAa,CAAC;MAClB,CAAC;IACL,CAAC,CAAC,OAAO4C,QAAQ,EAAE;MACfD,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEE,QAAQ,CAAC;MACpDtC,eAAe,CAAC,KAAK,CAAC;MACtBF,WAAW,CAACuB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC1BP,EAAE,EAAE,aAAaC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAC7BC,MAAM,EAAE,QAAQ;QAChBU,IAAI,EAAE,UAAU;QAChBT,IAAI,EAAE,gBAAgBoB,QAAQ,CAAC5B,OAAO,EAAE;QACxCS,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;MACtC,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EAED,MAAMmB,aAAa,GAAGA,CAAA,KAAM;IACxBzC,WAAW,CAAC,EAAE,CAAC;IACfE,eAAe,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,oBACIR,OAAA;IAAKgD,SAAS,EAAC,0CAA0C;IAAAC,QAAA,gBAErDjD,OAAA;MAAKgD,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAC5FjD,OAAA;QACEkD,OAAO,EAAEH,aAAc;QACvBC,SAAS,EAAC,+EAA+E;QACzFG,KAAK,EAAC,oBAAK;QAAAF,QAAA,eAETjD,OAAA,CAACH,MAAM;UAACuD,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACTxD,OAAA;QAAKgD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACvDjD,OAAA;UAAMgD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNxD,OAAA;MAAKgD,SAAS,EAAC,uDAAuD;MAAAC,QAAA,GACjE5C,QAAQ,CAACK,MAAM,KAAK,CAAC,iBAClBV,OAAA;QAAKgD,SAAS,EAAC,kFAAkF;QAAAC,QAAA,gBAC7FjD,OAAA;UAAMgD,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvDxD,OAAA;UAAMgD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtDxD,OAAA;UAAGgD,SAAS,EAAC,iDAAiD;UAAAC,QAAA,EAAC;QAE/D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,EACAnD,QAAQ,CAACoD,GAAG,CAAC,CAACnB,GAAG,EAAEoB,KAAK,kBACrB1D,OAAA,CAACL,WAAW;QAAuBuB,OAAO,EAAEoB;MAAI,GAA9BA,GAAG,CAAChB,EAAE,IAAIoC,KAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CACrD,CAAC,eACFxD,OAAA;QAAK2D,GAAG,EAAElD;MAAe;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAGNxD,OAAA;MAAKgD,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACpEjD,OAAA,CAACN,YAAY;QACTkE,aAAa,EAAE7C,iBAAkB;QACjCR,YAAY,EAAEA,YAAa;QAC3BsD,WAAW,EAAC;MAAW;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACpD,EAAA,CA1KIH,QAAQ;AAAA6D,EAAA,GAAR7D,QAAQ;AA4Kd,eAAeA,QAAQ;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}