"use strict";(self.webpackChunktiktodo_slides_clone=self.webpackChunktiktodo_slides_clone||[]).push([[102],{8102:(e,t,o)=>{o.r(t),o.d(t,{default:()=>a});var l=o(9379),n=o(5043),r=o(2774),s=o(579);const a=e=>{let{initialText:t,initialStyle:o,onSave:a,onCancel:i,position:c}=e;const[d,u]=(0,n.useState)(t),[x,b]=(0,n.useState)(o||{}),g=(0,n.useRef)(null);(0,n.useEffect)((()=>{u(t),b(o||{})}),[t,o]),(0,n.useEffect)((()=>{if(g.current){const e=g.current.querySelector("textarea");e&&(e.focus(),e.select())}}),[c]);const h=(e,t)=>{b((o=>(0,l.A)((0,l.A)({},o),{},{[e]:t})))};return c?(0,s.jsxs)("div",{ref:g,className:"absolute bg-white shadow-2xl rounded-lg p-3 z-50 border border-gray-300",style:{top:c.y,left:c.x,minWidth:"250px"},onClick:e=>e.stopPropagation(),children:[(0,s.jsx)("textarea",{value:d,onChange:e=>u(e.target.value),className:"w-full p-2 border border-gray-200 rounded-md mb-2 focus:ring-tiktodo-blue focus:border-tiktodo-blue outline-none resize-none text-sm",rows:3}),(0,s.jsxs)("div",{className:"flex items-center space-x-1 mb-2",children:[(0,s.jsx)("button",{title:"Bold",onClick:()=>h("fontWeight","bold"===x.fontWeight?"normal":"bold"),className:"p-1.5 rounded hover:bg-gray-200 ".concat("bold"===x.fontWeight?"bg-gray-200":""),children:(0,s.jsx)(r.ljE,{})}),(0,s.jsx)("button",{title:"Italic",onClick:()=>h("fontStyle","italic"===x.fontStyle?"normal":"italic"),className:"p-1.5 rounded hover:bg-gray-200 ".concat("italic"===x.fontStyle?"bg-gray-200":""),children:(0,s.jsx)(r._Me,{})}),(0,s.jsx)("button",{title:"Underline",onClick:()=>h("textDecoration","underline"===x.textDecoration?"none":"underline"),className:"p-1.5 rounded hover:bg-gray-200 ".concat("underline"===x.textDecoration?"bg-gray-200":""),children:(0,s.jsx)(r.INR,{})}),(0,s.jsxs)("div",{className:"relative p-1.5 rounded hover:bg-gray-200",children:[(0,s.jsx)(r.IFh,{}),(0,s.jsx)("input",{type:"color",value:x.color||"#000000",onChange:e=>h("color",e.target.value),className:"absolute top-0 left-0 w-full h-full opacity-0 cursor-pointer",title:"Text Color"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)("button",{onClick:i,className:"px-3 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300",children:"\u53d6\u6d88"}),(0,s.jsx)("button",{onClick:()=>{a(d,x)},className:"px-3 py-1 text-xs bg-tiktodo-blue text-white rounded hover:bg-blue-700",children:"\u4fdd\u5b58"})]})]}):null}}}]);
//# sourceMappingURL=102.e0c3ac76.chunk.js.map