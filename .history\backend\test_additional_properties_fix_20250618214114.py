#!/usr/bin/env python3
"""
验证additionalProperties修复的测试脚本
"""

import sys
import os
import json

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.models.presentation_model import (
    DetailedSlideBlueprintSchema,
    TextElementSchema,
    KpiCardSchema,
    ChartBlueprintSchema,
    ImageElementSchema,
    ChartConfig
)

def test_schema_generation():
    """测试Schema生成，确保没有additionalProperties"""
    
    print("🔧 测试Schema JSON生成（检查additionalProperties）")
    print("=" * 50)
    
    schemas_to_test = [
        ("TextElementSchema", TextElementSchema),
        ("KpiCardSchema", KpiCardSchema),
        ("ChartBlueprintSchema", ChartBlueprintSchema),
        ("ImageElementSchema", ImageElementSchema),
        ("DetailedSlideBlueprintSchema", DetailedSlideBlueprintSchema)
    ]
    
    for schema_name, schema_class in schemas_to_test:
        try:
            # 生成JSON Schema
            json_schema = schema_class.model_json_schema()
            
            # 检查是否包含additionalProperties
            schema_str = json.dumps(json_schema, indent=2)
            has_additional_properties = "additionalProperties" in schema_str
            
            if has_additional_properties:
                print(f"❌ {schema_name}: 仍包含 additionalProperties")
                # 查找具体位置
                lines = schema_str.split('\n')
                for i, line in enumerate(lines):
                    if "additionalProperties" in line:
                        print(f"   在第 {i+1} 行: {line.strip()}")
            else:
                print(f"✅ {schema_name}: 未包含 additionalProperties")
                
        except Exception as e:
            print(f"❌ {schema_name}: 生成Schema失败 - {e}")
    
    print("\n" + "=" * 50)

def test_instructor_compatibility():
    """测试与Instructor的兼容性"""
    
    print("🔧 测试DetailedSlideBlueprintSchema的基本验证")
    print("=" * 50)
    
    # 测试数据
    test_blueprint = {
        "slide_number": 1,
        "layout_template_name": "ContentSlideLayout",
        "background_style_description": "linear-gradient(135deg, #0A1931 0%, #1E293B 100%)",
        "key_elements": [
            {
                "type": "title",
                "content": "珍珠港事件：历史的转折点",
                "target_area": "title_area",
                "animation_style": "fade-in-up"
            },
            {
                "type": "kpi_card",
                "title": "伤亡人数",
                "value": "2,403人",
                "change": "↑100%",
                "icon_fontawesome_class": "fa-solid fa-exclamation-triangle",
                "target_area": "kpi_card_1",
                "animation_style": "slide-in-left"
            }
        ],
        "speaker_notes": "这张幻灯片介绍了珍珠港事件的基本概况。珍珠港事件发生在1941年12月7日，是太平洋战争的导火索。此次攻击造成了重大伤亡，彻底改变了美国的中立立场，促使美国正式参与第二次世界大战。演讲时应强调这一事件的历史重要性，以及它如何成为20世纪历史的重要转折点。"
    }
    
    try:
        # 创建Schema实例
        blueprint = DetailedSlideBlueprintSchema.model_validate(test_blueprint)
        print("✅ DetailedSlideBlueprintSchema 验证成功")
        
        # 测试get_typed_elements方法
        typed_elements = blueprint.get_typed_elements()
        print(f"✅ get_typed_elements 成功返回 {len(typed_elements)} 个元素")
        
        for i, element in enumerate(typed_elements):
            print(f"   元素 {i+1}: {type(element).__name__} (type={element.type})")
        
        # 测试JSON序列化
        json_data = blueprint.model_dump_json()
        print("✅ JSON序列化成功")
        
        # 测试JSON反序列化
        blueprint_from_json = DetailedSlideBlueprintSchema.model_validate_json(json_data)
        print("✅ JSON反序列化成功")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_schema_generation()
    print()
    test_instructor_compatibility() 