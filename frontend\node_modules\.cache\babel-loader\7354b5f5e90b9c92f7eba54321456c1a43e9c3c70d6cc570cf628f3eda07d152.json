{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\PdfExport.js\",\n  _s = $RefreshSig$();\nimport React, { forwardRef, useImperativeHandle, useState } from 'react';\n// Import dependencies for PDF generation\n// Note: You'll need to run: npm install jspdf html2canvas\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PdfExport = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s((props, ref) => {\n  _s();\n  const [isExporting, setIsExporting] = useState(false);\n  useImperativeHandle(ref, () => ({\n    exportToPdf: async slides => {\n      return new Promise((resolve, reject) => {\n        try {\n          setIsExporting(true);\n          console.log('Started PDF export process for', slides.length, 'slides');\n\n          // In a real implementation, we would:\n          // 1. Dynamically import jsPDF and html2canvas (for code splitting)\n          // 2. Create temporary DOM elements for each slide\n          // 3. Use html2canvas to capture each slide as an image\n          // 4. Add each image to a jsPDF document\n          // 5. Save the PDF\n\n          // This simulates the PDF creation process\n          setTimeout(() => {\n            import('jspdf').then(({\n              default: jsPDF\n            }) => {\n              import('html2canvas').then(({\n                default: html2canvas\n              }) => {\n                // This is a simplified example - in a real app, you'd need to handle:\n                // - Rendering slides with correct dimensions\n                // - Waiting for all content (including images/charts) to load\n                // - Proper error handling for each slide\n\n                // Create new PDF document (A4 size by default)\n                const pdf = new jsPDF({\n                  orientation: 'landscape',\n                  // Slides are typically landscape\n                  unit: 'mm'\n                });\n                const exportSlides = async () => {\n                  try {\n                    // Mock HTML to Canvas conversion\n                    console.log('Simulating PDF export of slides');\n\n                    // For demo purposes - would actually render & convert slides\n                    for (let i = 0; i < slides.length; i++) {\n                      if (i > 0) pdf.addPage();\n\n                      // In a real implementation:\n                      // 1. Create a temporary container for the slide\n                      // 2. Insert the slide HTML\n                      // 3. Wait for content to load (images, charts)\n                      // 4. Use html2canvas to capture the slide\n                      // 5. Add the image to the PDF\n\n                      // Mock - would be an actual HTML canvas element\n                      const mockCanvas = {\n                        width: 1280,\n                        height: 720,\n                        toDataURL: () => 'data:image/png;base64,mockImageData'\n                      };\n\n                      // Add slide image to PDF\n                      const imgData = mockCanvas.toDataURL('image/png');\n                      pdf.addImage(imgData, 'PNG', 0, 0, pdf.internal.pageSize.getWidth(), pdf.internal.pageSize.getHeight());\n\n                      // Update progress (in a real app, you might show a progress indicator)\n                      console.log(`Processed slide ${i + 1}/${slides.length}`);\n                    }\n\n                    // Save the PDF\n                    pdf.save(`${props.filename || 'presentation'}.pdf`);\n                    console.log('PDF export completed');\n                    setIsExporting(false);\n                    resolve();\n                  } catch (error) {\n                    console.error('Error during PDF export:', error);\n                    setIsExporting(false);\n                    reject(error);\n                  }\n                };\n\n                // Start the export process\n                exportSlides();\n              }).catch(err => {\n                console.error('Failed to load html2canvas:', err);\n                setIsExporting(false);\n                reject(err);\n              });\n            }).catch(err => {\n              console.error('Failed to load jsPDF:', err);\n              setIsExporting(false);\n              reject(err);\n            });\n          }, 500); // Simulate dynamic import delay\n        } catch (error) {\n          console.error('PDF export error:', error);\n          setIsExporting(false);\n          reject(error);\n        }\n      });\n    }\n  }));\n\n  // Render a loading indicator when exporting (optional)\n  if (isExporting) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 right-4 bg-white p-3 rounded-lg shadow-lg z-50 flex items-center space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm text-gray-700\",\n        children: \"\\u6B63\\u5728\\u5BFC\\u51FA PDF...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this);\n  }\n\n  // This component doesn't render anything when not exporting\n  return null;\n}, \"GSLHyZOEWEUo+HXvlQ8sxP8vwq8=\")), \"GSLHyZOEWEUo+HXvlQ8sxP8vwq8=\");\n_c2 = PdfExport;\nPdfExport.displayName = 'PdfExport';\nexport default PdfExport;\nvar _c, _c2;\n$RefreshReg$(_c, \"PdfExport$forwardRef\");\n$RefreshReg$(_c2, \"PdfExport\");", "map": {"version": 3, "names": ["React", "forwardRef", "useImperativeHandle", "useState", "jsxDEV", "_jsxDEV", "PdfExport", "_s", "_c", "props", "ref", "isExporting", "setIsExporting", "exportToPdf", "slides", "Promise", "resolve", "reject", "console", "log", "length", "setTimeout", "then", "default", "jsPDF", "html2canvas", "pdf", "orientation", "unit", "exportSlides", "i", "addPage", "mockCanvas", "width", "height", "toDataURL", "imgData", "addImage", "internal", "pageSize", "getWidth", "getHeight", "save", "filename", "error", "catch", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/PdfExport.js"], "sourcesContent": ["import React, { forwardRef, useImperativeHandle, useState } from 'react';\n// Import dependencies for PDF generation\n// Note: You'll need to run: npm install jspdf html2canvas\n\nconst PdfExport = forwardRef((props, ref) => {\n  const [isExporting, setIsExporting] = useState(false);\n\n  useImperativeHandle(ref, () => ({\n    exportToPdf: async (slides) => {\n      return new Promise((resolve, reject) => {\n        try {\n          setIsExporting(true);\n          console.log('Started PDF export process for', slides.length, 'slides');\n          \n          // In a real implementation, we would:\n          // 1. Dynamically import jsPDF and html2canvas (for code splitting)\n          // 2. Create temporary DOM elements for each slide\n          // 3. Use html2canvas to capture each slide as an image\n          // 4. Add each image to a jsPDF document\n          // 5. Save the PDF\n\n          // This simulates the PDF creation process\n          setTimeout(() => {\n            import('jspdf')\n              .then(({ default: jsPDF }) => {\n                import('html2canvas')\n                  .then(({ default: html2canvas }) => {\n                    // This is a simplified example - in a real app, you'd need to handle:\n                    // - Rendering slides with correct dimensions\n                    // - Waiting for all content (including images/charts) to load\n                    // - Proper error handling for each slide\n\n                    // Create new PDF document (A4 size by default)\n                    const pdf = new jsPDF({\n                      orientation: 'landscape', // Slides are typically landscape\n                      unit: 'mm',\n                    });\n\n                    const exportSlides = async () => {\n                      try {\n                        // Mock HTML to Canvas conversion\n                        console.log('Simulating PDF export of slides');\n                        \n                        // For demo purposes - would actually render & convert slides\n                        for (let i = 0; i < slides.length; i++) {\n                          if (i > 0) pdf.addPage();\n                          \n                          // In a real implementation:\n                          // 1. Create a temporary container for the slide\n                          // 2. Insert the slide HTML\n                          // 3. Wait for content to load (images, charts)\n                          // 4. Use html2canvas to capture the slide\n                          // 5. Add the image to the PDF\n                          \n                          // Mock - would be an actual HTML canvas element\n                          const mockCanvas = { \n                            width: 1280, \n                            height: 720,\n                            toDataURL: () => 'data:image/png;base64,mockImageData' \n                          };\n                          \n                          // Add slide image to PDF\n                          const imgData = mockCanvas.toDataURL('image/png');\n                          pdf.addImage(\n                            imgData, \n                            'PNG', \n                            0, 0, \n                            pdf.internal.pageSize.getWidth(), \n                            pdf.internal.pageSize.getHeight()\n                          );\n                          \n                          // Update progress (in a real app, you might show a progress indicator)\n                          console.log(`Processed slide ${i+1}/${slides.length}`);\n                        }\n                        \n                        // Save the PDF\n                        pdf.save(`${props.filename || 'presentation'}.pdf`);\n                        console.log('PDF export completed');\n                        setIsExporting(false);\n                        resolve();\n                      } catch (error) {\n                        console.error('Error during PDF export:', error);\n                        setIsExporting(false);\n                        reject(error);\n                      }\n                    };\n                    \n                    // Start the export process\n                    exportSlides();\n                  })\n                  .catch(err => {\n                    console.error('Failed to load html2canvas:', err);\n                    setIsExporting(false);\n                    reject(err);\n                  });\n              })\n              .catch(err => {\n                console.error('Failed to load jsPDF:', err);\n                setIsExporting(false);\n                reject(err);\n              });\n          }, 500); // Simulate dynamic import delay\n          \n        } catch (error) {\n          console.error('PDF export error:', error);\n          setIsExporting(false);\n          reject(error);\n        }\n      });\n    }\n  }));\n\n  // Render a loading indicator when exporting (optional)\n  if (isExporting) {\n    return (\n      <div className=\"fixed bottom-4 right-4 bg-white p-3 rounded-lg shadow-lg z-50 flex items-center space-x-2\">\n        <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full\"></div>\n        <span className=\"text-sm text-gray-700\">正在导出 PDF...</span>\n      </div>\n    );\n  }\n\n  // This component doesn't render anything when not exporting\n  return null;\n});\n\nPdfExport.displayName = 'PdfExport';\n\nexport default PdfExport; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,QAAQ,QAAQ,OAAO;AACxE;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,SAAS,gBAAAC,EAAA,cAAGN,UAAU,CAAAO,EAAA,GAAAD,EAAA,CAAC,CAACE,KAAK,EAAEC,GAAG,KAAK;EAAAH,EAAA;EAC3C,MAAM,CAACI,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAErDD,mBAAmB,CAACQ,GAAG,EAAE,OAAO;IAC9BG,WAAW,EAAE,MAAOC,MAAM,IAAK;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,IAAI;UACFL,cAAc,CAAC,IAAI,CAAC;UACpBM,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEL,MAAM,CAACM,MAAM,EAAE,QAAQ,CAAC;;UAEtE;UACA;UACA;UACA;UACA;UACA;;UAEA;UACAC,UAAU,CAAC,MAAM;YACf,MAAM,CAAC,OAAO,CAAC,CACZC,IAAI,CAAC,CAAC;cAAEC,OAAO,EAAEC;YAAM,CAAC,KAAK;cAC5B,MAAM,CAAC,aAAa,CAAC,CAClBF,IAAI,CAAC,CAAC;gBAAEC,OAAO,EAAEE;cAAY,CAAC,KAAK;gBAClC;gBACA;gBACA;gBACA;;gBAEA;gBACA,MAAMC,GAAG,GAAG,IAAIF,KAAK,CAAC;kBACpBG,WAAW,EAAE,WAAW;kBAAE;kBAC1BC,IAAI,EAAE;gBACR,CAAC,CAAC;gBAEF,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;kBAC/B,IAAI;oBACF;oBACAX,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;;oBAE9C;oBACA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,MAAM,CAACM,MAAM,EAAEU,CAAC,EAAE,EAAE;sBACtC,IAAIA,CAAC,GAAG,CAAC,EAAEJ,GAAG,CAACK,OAAO,CAAC,CAAC;;sBAExB;sBACA;sBACA;sBACA;sBACA;sBACA;;sBAEA;sBACA,MAAMC,UAAU,GAAG;wBACjBC,KAAK,EAAE,IAAI;wBACXC,MAAM,EAAE,GAAG;wBACXC,SAAS,EAAEA,CAAA,KAAM;sBACnB,CAAC;;sBAED;sBACA,MAAMC,OAAO,GAAGJ,UAAU,CAACG,SAAS,CAAC,WAAW,CAAC;sBACjDT,GAAG,CAACW,QAAQ,CACVD,OAAO,EACP,KAAK,EACL,CAAC,EAAE,CAAC,EACJV,GAAG,CAACY,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,CAAC,EAChCd,GAAG,CAACY,QAAQ,CAACC,QAAQ,CAACE,SAAS,CAAC,CAClC,CAAC;;sBAED;sBACAvB,OAAO,CAACC,GAAG,CAAC,mBAAmBW,CAAC,GAAC,CAAC,IAAIhB,MAAM,CAACM,MAAM,EAAE,CAAC;oBACxD;;oBAEA;oBACAM,GAAG,CAACgB,IAAI,CAAC,GAAGjC,KAAK,CAACkC,QAAQ,IAAI,cAAc,MAAM,CAAC;oBACnDzB,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;oBACnCP,cAAc,CAAC,KAAK,CAAC;oBACrBI,OAAO,CAAC,CAAC;kBACX,CAAC,CAAC,OAAO4B,KAAK,EAAE;oBACd1B,OAAO,CAAC0B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;oBAChDhC,cAAc,CAAC,KAAK,CAAC;oBACrBK,MAAM,CAAC2B,KAAK,CAAC;kBACf;gBACF,CAAC;;gBAED;gBACAf,YAAY,CAAC,CAAC;cAChB,CAAC,CAAC,CACDgB,KAAK,CAACC,GAAG,IAAI;gBACZ5B,OAAO,CAAC0B,KAAK,CAAC,6BAA6B,EAAEE,GAAG,CAAC;gBACjDlC,cAAc,CAAC,KAAK,CAAC;gBACrBK,MAAM,CAAC6B,GAAG,CAAC;cACb,CAAC,CAAC;YACN,CAAC,CAAC,CACDD,KAAK,CAACC,GAAG,IAAI;cACZ5B,OAAO,CAAC0B,KAAK,CAAC,uBAAuB,EAAEE,GAAG,CAAC;cAC3ClC,cAAc,CAAC,KAAK,CAAC;cACrBK,MAAM,CAAC6B,GAAG,CAAC;YACb,CAAC,CAAC;UACN,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAEX,CAAC,CAAC,OAAOF,KAAK,EAAE;UACd1B,OAAO,CAAC0B,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;UACzChC,cAAc,CAAC,KAAK,CAAC;UACrBK,MAAM,CAAC2B,KAAK,CAAC;QACf;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;;EAEH;EACA,IAAIjC,WAAW,EAAE;IACf,oBACEN,OAAA;MAAK0C,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBACxG3C,OAAA;QAAK0C,SAAS,EAAC;MAAiF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvG/C,OAAA;QAAM0C,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAEV;;EAEA;EACA,OAAO,IAAI;AACb,CAAC,kCAAC;AAACC,GAAA,GAxHG/C,SAAS;AA0HfA,SAAS,CAACgD,WAAW,GAAG,WAAW;AAEnC,eAAehD,SAAS;AAAC,IAAAE,EAAA,EAAA6C,GAAA;AAAAE,YAAA,CAAA/C,EAAA;AAAA+C,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}