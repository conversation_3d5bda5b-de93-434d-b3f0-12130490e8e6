{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\views\\\\ChatViewPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport LeftNav from '../components/LeftNav';\nimport ChatView from './ChatView';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatViewPage = () => {\n  _s();\n  const [currentChatId, setCurrentChatId] = useState(null);\n  const [isTaskListOpen, setIsTaskListOpen] = useState(false);\n  const handleSelectChat = chatId => {\n    setCurrentChatId(chatId);\n    console.log('选择聊天:', chatId);\n    // 这里可以加载对应的聊天内容\n  };\n  const toggleTaskList = () => {\n    setIsTaskListOpen(!isTaskListOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-1 overflow-hidden h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(LeftNav, {\n      isTaskListOpen: isTaskListOpen,\n      onToggleTaskList: toggleTaskList,\n      onSelectChat: handleSelectChat\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChatView, {\n      currentChatId: currentChatId\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatViewPage, \"rhdhkSRqUf17uGdn9b+bhVEJsBY=\");\n_c = ChatViewPage;\nexport default ChatViewPage;\nvar _c;\n$RefreshReg$(_c, \"ChatViewPage\");", "map": {"version": 3, "names": ["React", "useState", "LeftNav", "ChatView", "jsxDEV", "_jsxDEV", "ChatViewPage", "_s", "currentChatId", "setCurrentChatId", "isTaskListOpen", "setIsTaskListOpen", "handleSelectChat", "chatId", "console", "log", "toggleTaskList", "className", "children", "onToggleTaskList", "onSelectChat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/views/ChatViewPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport LeftNav from '../components/LeftNav';\nimport ChatView from './ChatView';\n\nconst ChatViewPage = () => {\n  const [currentChatId, setCurrentChatId] = useState(null);\n  const [isTaskListOpen, setIsTaskListOpen] = useState(false);\n\n  const handleSelectChat = (chatId) => {\n    setCurrentChatId(chatId);\n    console.log('选择聊天:', chatId);\n    // 这里可以加载对应的聊天内容\n  };\n\n  const toggleTaskList = () => {\n    setIsTaskListOpen(!isTaskListOpen);\n  };\n\n  return (\n    <div className=\"flex flex-1 overflow-hidden h-screen\">\n      <LeftNav \n        isTaskListOpen={isTaskListOpen} \n        onToggleTaskList={toggleTaskList}\n        onSelectChat={handleSelectChat}\n      />\n      <ChatView currentChatId={currentChatId} />\n    </div>\n  );\n};\n\nexport default ChatViewPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACS,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAMW,gBAAgB,GAAIC,MAAM,IAAK;IACnCJ,gBAAgB,CAACI,MAAM,CAAC;IACxBC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,MAAM,CAAC;IAC5B;EACF,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3BL,iBAAiB,CAAC,CAACD,cAAc,CAAC;EACpC,CAAC;EAED,oBACEL,OAAA;IAAKY,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBACnDb,OAAA,CAACH,OAAO;MACNQ,cAAc,EAAEA,cAAe;MAC/BS,gBAAgB,EAAEH,cAAe;MACjCI,YAAY,EAAER;IAAiB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,eACFnB,OAAA,CAACF,QAAQ;MAACK,aAAa,EAAEA;IAAc;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CAAC;AAEV,CAAC;AAACjB,EAAA,CAxBID,YAAY;AAAAmB,EAAA,GAAZnB,YAAY;AA0BlB,eAAeA,YAAY;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}