#!/usr/bin/env python3
"""
测试additionalProperties修复的验证脚本

验证DetailedSlideBlueprintSchema不再产生additionalProperties错误
"""

import sys
import os
import asyncio

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.models.presentation_model import DetailedSlideBlueprintSchema, SlideElementSchema
from app.agents.slide_detailer_agent import SlideDetailerAgent
from pydantic import ValidationError

def test_slide_element_schema():
    """测试SlideElementSchema能否正确工作"""
    print("🧪 测试 SlideElementSchema...")
    
    # 测试创建一个基本的幻灯片元素
    try:
        element = SlideElementSchema(
            type="title",
            content="珍珠港事件的历史背景",
            target_area="title_area"
        )
        print(f"✅ SlideElementSchema 创建成功: {element.type}")
        print(f"   内容: {element.content}")
        
        # 测试KPI卡片元素
        kpi_element = SlideElementSchema(
            type="kpi_card",
            title="美军伤亡",
            value="2,400人",
            change="+100%",
            target_area="kpi_area_1"
        )
        print(f"✅ KPI元素创建成功: {kpi_element.type} - {kpi_element.value}")
        
        # 测试图表元素
        chart_element = SlideElementSchema(
            type="chart",
            chart_type="bar",
            chart_data='{"labels": ["战舰", "巡洋舰"], "data": [8, 3]}',
            target_area="chart_area"
        )
        print(f"✅ 图表元素创建成功: {chart_element.chart_type}")
        
        return True
    except Exception as e:
        print(f"❌ SlideElementSchema 测试失败: {e}")
        return False

def test_detailed_slide_blueprint_schema():
    """测试DetailedSlideBlueprintSchema能否正确工作"""
    print("\n🧪 测试 DetailedSlideBlueprintSchema...")
    
    try:
        # 创建测试元素列表
        elements = [
            SlideElementSchema(
                type="title",
                content="1941年12月7日：珍珠港事件",
                target_area="title_area"
            ),
            SlideElementSchema(
                type="subtitle", 
                content="太平洋战争的导火索",
                target_area="subtitle_area"
            ),
            SlideElementSchema(
                type="kpi_card",
                title="美军伤亡",
                value="2,400人",
                change="+∞",
                kpi_trend="up",
                target_area="kpi_area_1"
            )
        ]
        
        # 创建详细蓝图
        blueprint = DetailedSlideBlueprintSchema(
            slide_number=1,
            layout_template_name="TitleSlideLayout",
            background_style_description="linear-gradient(135deg, #1A2C3D 0%, #2D4A5C 100%)",
            key_elements=elements,
            speaker_notes="这是珍珠港事件的开篇幻灯片，重点介绍了事件发生的具体时间和严重性。通过KPI卡片突出了美军的巨大伤亡，为后续详细讲解奠定了沉重的历史基调。"
        )
        
        print(f"✅ DetailedSlideBlueprintSchema 创建成功!")
        print(f"   幻灯片编号: {blueprint.slide_number}")
        print(f"   布局模板: {blueprint.layout_template_name}")
        print(f"   元素数量: {len(blueprint.key_elements)}")
        print(f"   演讲备注长度: {len(blueprint.speaker_notes)}")
        
        # 验证每个元素
        for i, element in enumerate(blueprint.key_elements):
            print(f"   元素 {i+1}: {element.type} - {element.content or element.title or '无内容'}")
        
        return True
    except ValidationError as e:
        print(f"❌ DetailedSlideBlueprintSchema 验证失败: {e}")
        return False
    except Exception as e:
        print(f"❌ DetailedSlideBlueprintSchema 测试失败: {e}")
        return False

async def test_slide_detailer_agent_schema_compatibility():
    """测试SlideDetailerAgent与新Schema的兼容性"""
    print("\n🧪 测试 SlideDetailerAgent Schema 兼容性...")
    
    try:
        agent = SlideDetailerAgent()
        print("✅ SlideDetailerAgent 初始化成功")
        
        # 检查schema是否可以生成JSON Schema而不产生additionalProperties错误
        schema_dict = DetailedSlideBlueprintSchema.model_json_schema()
        
        # 检查是否包含additionalProperties
        def check_for_additional_properties(obj, path=""):
            if isinstance(obj, dict):
                if "additionalProperties" in obj:
                    return f"发现additionalProperties在路径: {path}"
                for key, value in obj.items():
                    result = check_for_additional_properties(value, f"{path}.{key}")
                    if result:
                        return result
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    result = check_for_additional_properties(item, f"{path}[{i}]")
                    if result:
                        return result
            return None
        
        additional_props_found = check_for_additional_properties(schema_dict)
        
        if additional_props_found:
            print(f"❌ 仍然存在additionalProperties: {additional_props_found}")
            return False
        else:
            print("✅ 未发现additionalProperties，Schema兼容Gemini API")
            return True
            
    except Exception as e:
        print(f"❌ Schema兼容性测试失败: {e}")
        return False

async def main():
    """运行所有测试"""
    print("🚀 开始测试 additionalProperties 修复...")
    
    results = []
    
    # 测试1: SlideElementSchema基本功能
    results.append(test_slide_element_schema())
    
    # 测试2: DetailedSlideBlueprintSchema基本功能
    results.append(test_detailed_slide_blueprint_schema())
    
    # 测试3: Schema与Gemini API兼容性
    results.append(await test_slide_detailer_agent_schema_compatibility())
    
    # 总结结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果总结:")
    print(f"   ✅ 通过: {passed}/{total}")
    print(f"   ❌ 失败: {total-passed}/{total}")
    
    if passed == total:
        print("\n🎉 所有测试都通过了！additionalProperties 问题已修复！")
        return True
    else:
        print(f"\n⚠️ 仍有 {total-passed} 个测试失败，需要进一步修复。")
        return False

if __name__ == "__main__":
    asyncio.run(main()) 