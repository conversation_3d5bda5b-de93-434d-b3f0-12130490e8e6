{"version": 3, "file": "static/js/102.e0c3ac76.chunk.js", "mappings": "oMAOA,MAiGA,EAjGyBA,IAAgE,IAA/D,YAAEC,EAAW,aAAEC,EAAY,OAAEC,EAAM,SAAEC,EAAQ,SAAEC,GAAUL,EACjF,MAAOM,EAAMC,IAAWC,EAAAA,EAAAA,UAASP,IAC1BQ,EAAOC,IAAYF,EAAAA,EAAAA,UAASN,GAAgB,CAAC,GAC9CS,GAAYC,EAAAA,EAAAA,QAAO,OAEzBC,EAAAA,EAAAA,YAAU,KACRN,EAAQN,GACRS,EAASR,GAAgB,CAAC,KACzB,CAACD,EAAaC,KAEjBW,EAAAA,EAAAA,YAAU,KAER,GAAIF,EAAUG,QAAS,CACrB,MAAMC,EAAWJ,EAAUG,QAAQE,cAAc,YAC7CD,IACFA,EAASE,QACTF,EAASG,SAEb,IACC,CAACb,IAGJ,MAAMc,EAAoBA,CAACC,EAAUC,KACnCX,GAASY,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACF,GAAWC,OAO3C,OAAKhB,GAGHmB,EAAAA,EAAAA,MAAA,OACEC,IAAKd,EACLe,UAAU,0EACVjB,MAAO,CAAEkB,IAAKtB,EAASuB,EAAGC,KAAMxB,EAASyB,EAAGC,SAAU,SACtDC,QAAUC,GAAMA,EAAEC,kBAAmBC,SAAA,EAErCC,EAAAA,EAAAA,KAAA,YACEf,MAAOf,EACP+B,SAAWJ,GAAM1B,EAAQ0B,EAAEK,OAAOjB,OAClCK,UAAU,uIACVa,KAAM,KAERf,EAAAA,EAAAA,MAAA,OAAKE,UAAU,mCAAkCS,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,UACEI,MAAM,OACNR,QAASA,IAAMb,EAAkB,aAAmC,SAArBV,EAAMgC,WAAwB,SAAW,QACxFf,UAAS,mCAAAgB,OAA0D,SAArBjC,EAAMgC,WAAwB,cAAgB,IAAKN,UAEjGC,EAAAA,EAAAA,KAACO,EAAAA,IAAM,OAETP,EAAAA,EAAAA,KAAA,UACEI,MAAM,SACNR,QAASA,IAAMb,EAAkB,YAAiC,WAApBV,EAAMmC,UAAyB,SAAW,UACxFlB,UAAS,mCAAAgB,OAAyD,WAApBjC,EAAMmC,UAAyB,cAAgB,IAAKT,UAElGC,EAAAA,EAAAA,KAACS,EAAAA,IAAQ,OAEXT,EAAAA,EAAAA,KAAA,UACEI,MAAM,YACNR,QAASA,IAAMb,EAAkB,iBAA2C,cAAzBV,EAAMqC,eAAiC,OAAS,aACnGpB,UAAS,mCAAAgB,OAA8D,cAAzBjC,EAAMqC,eAAiC,cAAgB,IAAKX,UAE1GC,EAAAA,EAAAA,KAACW,EAAAA,IAAW,OAGdvB,EAAAA,EAAAA,MAAA,OAAKE,UAAU,2CAA0CS,SAAA,EACrDC,EAAAA,EAAAA,KAACY,EAAAA,IAAY,KACbZ,EAAAA,EAAAA,KAAA,SAAOa,KAAK,QACL5B,MAAOZ,EAAMyC,OAAS,UACtBb,SAAWJ,GAAMd,EAAkB,QAASc,EAAEK,OAAOjB,OACrDK,UAAU,+DACVc,MAAM,sBAKnBhB,EAAAA,EAAAA,MAAA,OAAKE,UAAU,6BAA4BS,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEJ,QAAS5B,EACTsB,UAAU,wEAAuES,SAClF,kBAGDC,EAAAA,EAAAA,KAAA,UACEJ,QA7DWmB,KACjBhD,EAAOG,EAAMG,IA6DPiB,UAAU,yEAAwES,SACnF,uBA3De,K", "sources": ["components/InlineTextEditor.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport {\r\n  FaBold, FaItalic, FaUnderline, FaPaintBrush\r\n} from 'react-icons/fa';\r\n// TinyColor for color picker, or use native input type=\"color\"\r\n// import { SketchPicker } from 'react-color'; // Example, can be heavy. Let's use native for simplicity first.\r\n\r\nconst InlineTextEditor = ({ initialText, initialStyle, onSave, onCancel, position }) => {\r\n  const [text, setText] = useState(initialText);\r\n  const [style, setStyle] = useState(initialStyle || {}); // e.g., { fontWeight: 'bold', color: '#000000' }\r\n  const editorRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    setText(initialText);\r\n    setStyle(initialStyle || {});\r\n  }, [initialText, initialStyle]);\r\n\r\n  useEffect(() => {\r\n    // Focus the textarea when editor becomes visible\r\n    if (editorRef.current) {\r\n      const textarea = editorRef.current.querySelector('textarea');\r\n      if (textarea) {\r\n        textarea.focus();\r\n        textarea.select();\r\n      }\r\n    }\r\n  }, [position]);\r\n\r\n\r\n  const handleStyleChange = (property, value) => {\r\n    setStyle(prev => ({ ...prev, [property]: value }));\r\n  };\r\n\r\n  const handleSave = () => {\r\n    onSave(text, style);\r\n  };\r\n\r\n  if (!position) return null;\r\n\r\n  return (\r\n    <div\r\n      ref={editorRef}\r\n      className=\"absolute bg-white shadow-2xl rounded-lg p-3 z-50 border border-gray-300\"\r\n      style={{ top: position.y, left: position.x, minWidth: '250px' }}\r\n      onClick={(e) => e.stopPropagation()} // Prevent click from propagating to slide elements\r\n    >\r\n      <textarea\r\n        value={text}\r\n        onChange={(e) => setText(e.target.value)}\r\n        className=\"w-full p-2 border border-gray-200 rounded-md mb-2 focus:ring-tiktodo-blue focus:border-tiktodo-blue outline-none resize-none text-sm\"\r\n        rows={3}\r\n      />\r\n      <div className=\"flex items-center space-x-1 mb-2\">\r\n        <button\r\n          title=\"Bold\"\r\n          onClick={() => handleStyleChange('fontWeight', style.fontWeight === 'bold' ? 'normal' : 'bold')}\r\n          className={`p-1.5 rounded hover:bg-gray-200 ${style.fontWeight === 'bold' ? 'bg-gray-200' : ''}`}\r\n        >\r\n          <FaBold />\r\n        </button>\r\n        <button\r\n          title=\"Italic\"\r\n          onClick={() => handleStyleChange('fontStyle', style.fontStyle === 'italic' ? 'normal' : 'italic')}\r\n          className={`p-1.5 rounded hover:bg-gray-200 ${style.fontStyle === 'italic' ? 'bg-gray-200' : ''}`}\r\n        >\r\n          <FaItalic />\r\n        </button>\r\n        <button\r\n          title=\"Underline\"\r\n          onClick={() => handleStyleChange('textDecoration', style.textDecoration === 'underline' ? 'none' : 'underline')}\r\n          className={`p-1.5 rounded hover:bg-gray-200 ${style.textDecoration === 'underline' ? 'bg-gray-200' : ''}`}\r\n        >\r\n          <FaUnderline />\r\n        </button>\r\n        {/* Basic Color Picker */}\r\n        <div className=\"relative p-1.5 rounded hover:bg-gray-200\">\r\n            <FaPaintBrush />\r\n            <input type=\"color\"\r\n                   value={style.color || '#000000'}\r\n                   onChange={(e) => handleStyleChange('color', e.target.value)}\r\n                   className=\"absolute top-0 left-0 w-full h-full opacity-0 cursor-pointer\"\r\n                   title=\"Text Color\"\r\n            />\r\n        </div>\r\n        {/* Add more controls: font size, family, alignment etc. later */}\r\n      </div>\r\n      <div className=\"flex justify-end space-x-2\">\r\n        <button\r\n          onClick={onCancel}\r\n          className=\"px-3 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300\"\r\n        >\r\n          取消\r\n        </button>\r\n        <button\r\n          onClick={handleSave}\r\n          className=\"px-3 py-1 text-xs bg-tiktodo-blue text-white rounded hover:bg-blue-700\"\r\n        >\r\n          保存\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InlineTextEditor; "], "names": ["_ref", "initialText", "initialStyle", "onSave", "onCancel", "position", "text", "setText", "useState", "style", "setStyle", "editor<PERSON><PERSON>", "useRef", "useEffect", "current", "textarea", "querySelector", "focus", "select", "handleStyleChange", "property", "value", "prev", "_objectSpread", "_jsxs", "ref", "className", "top", "y", "left", "x", "min<PERSON><PERSON><PERSON>", "onClick", "e", "stopPropagation", "children", "_jsx", "onChange", "target", "rows", "title", "fontWeight", "concat", "FaBold", "fontStyle", "FaItalic", "textDecoration", "FaUnderline", "FaPaintBrush", "type", "color", "handleSave"], "sourceRoot": ""}