2025-06-18 14:40:11 [INFO] === LLM REQUEST START ===
2025-06-18 14:40:11 [INFO] Agent: TestAgent
2025-06-18 14:40:11 [INFO] Model: gemini-2.5-flash-preview
2025-06-18 14:40:11 [INFO] Temperature: 0.7
2025-06-18 14:40:11 [INFO] Context: test_request
2025-06-18 14:40:11 [INFO] Expected Response Type: text
2025-06-18 14:40:11 [INFO] Prompt Length: 39 characters
2025-06-18 14:40:11 [INFO] --- FULL PROMPT ---
2025-06-18 14:40:11 [INFO] 这是一个测试提示词，用于验证日志记录功能是否正常工作。请回答：什么是人工智能？
2025-06-18 14:40:11 [INFO] --- END PROMPT ---
2025-06-18 14:40:11 [INFO] === LLM REQUEST END ===

2025-06-18 14:40:11 [INFO] === LLM RESPONSE START ===
2025-06-18 14:40:11 [INFO] Agent: TestAgent
2025-06-18 14:40:11 [INFO] Context: test_request
2025-06-18 14:40:11 [INFO] Duration: 1500ms
2025-06-18 14:40:11 [INFO] Success: True
2025-06-18 14:40:11 [INFO] --- THINKING PROCESS ---
2025-06-18 14:40:11 [INFO] 用户询问人工智能的定义，我需要提供一个准确、简洁的解释。
2025-06-18 14:40:11 [INFO] --- END THINKING ---
2025-06-18 14:40:11 [INFO] Response Length: 68 characters
2025-06-18 14:40:11 [INFO] --- RESPONSE CONTENT ---
2025-06-18 14:40:11 [INFO] 人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。
2025-06-18 14:40:11 [INFO] --- END RESPONSE ---
2025-06-18 14:40:11 [INFO] --- RAW RESPONSE DATA ---
2025-06-18 14:40:11 [INFO] {
  "success": true,
  "model": "gemini-2.5-flash-preview"
}
2025-06-18 14:40:11 [INFO] --- END RAW RESPONSE ---
2025-06-18 14:40:11 [INFO] === LLM RESPONSE END ===

2025-06-18 14:40:11 [INFO] === PARSING ATTEMPT START ===
2025-06-18 14:40:11 [INFO] Agent: TestAgent
2025-06-18 14:40:11 [INFO] Context: test_parsing
2025-06-18 14:40:11 [INFO] Expected Schema: DefinitionSchema
2025-06-18 14:40:11 [INFO] Validation Success: True
2025-06-18 14:40:11 [INFO] --- ORIGINAL RESPONSE ---
2025-06-18 14:40:11 [INFO] {"type": "definition", "content": "AI是计算机科学的分支"}
2025-06-18 14:40:11 [INFO] --- END ORIGINAL RESPONSE ---
2025-06-18 14:40:11 [INFO] --- PARSED RESULT ---
2025-06-18 14:40:11 [INFO] {"type": "definition", "content": "AI是计算机科学的分支"}
2025-06-18 14:40:11 [INFO] --- END PARSED RESULT ---
2025-06-18 14:40:11 [INFO] === PARSING ATTEMPT END ===

2025-06-18 14:40:11 [INFO] === LLM RESPONSE START ===
2025-06-18 14:40:11 [INFO] Agent: TestAgent
2025-06-18 14:40:11 [INFO] Context: test_error
2025-06-18 14:40:11 [INFO] Duration: 500ms
2025-06-18 14:40:11 [INFO] Success: False
2025-06-18 14:40:11 [INFO] Error: API密钥达到速率限制
2025-06-18 14:40:11 [INFO] --- RAW RESPONSE DATA ---
2025-06-18 14:40:11 [INFO] {
  "error": "rate_limit",
  "type": "ResourceExhausted"
}
2025-06-18 14:40:11 [INFO] --- END RAW RESPONSE ---
2025-06-18 14:40:11 [INFO] === LLM RESPONSE END ===

2025-06-18 14:40:11 [INFO] === PARSING ATTEMPT START ===
2025-06-18 14:40:11 [INFO] Agent: TestAgent
2025-06-18 14:40:11 [INFO] Context: test_parsing_error
2025-06-18 14:40:11 [INFO] Expected Schema: JsonSchema
2025-06-18 14:40:11 [INFO] Validation Success: False
2025-06-18 14:40:11 [INFO] Validation Error: JSONDecodeError: 期望值在第1行第1列（字符0）
2025-06-18 14:40:11 [INFO] --- ORIGINAL RESPONSE ---
2025-06-18 14:40:11 [INFO] 这不是有效的JSON格式
2025-06-18 14:40:11 [INFO] --- END ORIGINAL RESPONSE ---
2025-06-18 14:40:11 [INFO] === PARSING ATTEMPT END ===

