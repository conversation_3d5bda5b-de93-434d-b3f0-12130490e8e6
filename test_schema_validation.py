#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的schema验证
验证ChartBlueprintSchema和DetailedSlideBlueprintSchema是否能正确生成JSON schema
"""

import json
import sys
import os

# 设置输出编码
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())

# 添加backend路径到sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from app.models.presentation_model import (
    ChartBlueprintSchema, 
    DetailedSlideBlueprintSchema,
    ChartConfig
)

def test_chart_config_schema():
    """测试ChartConfig的JSON schema生成"""
    print("=== 测试ChartConfig JSON Schema ===")
    try:
        schema = ChartConfig.model_json_schema()
        print("[OK] ChartConfig schema生成成功")

        # 检查是否有必需的字段
        required_fields = schema.get('required', [])
        print(f"必需字段: {required_fields}")

        # 检查properties是否非空
        properties = schema.get('properties', {})
        if properties:
            print(f"[OK] Properties非空，包含 {len(properties)} 个字段")
            print(f"字段列表: {list(properties.keys())}")
        else:
            print("[ERROR] Properties为空")
            return False

        return True
    except Exception as e:
        print(f"[ERROR] ChartConfig schema生成失败: {e}")
        return False

def test_chart_blueprint_schema():
    """测试ChartBlueprintSchema的JSON schema生成"""
    print("\n=== 测试ChartBlueprintSchema JSON Schema ===")
    try:
        schema = ChartBlueprintSchema.model_json_schema()
        print("✅ ChartBlueprintSchema schema生成成功")
        
        # 检查final_chart_js_config字段
        properties = schema.get('properties', {})
        final_chart_config = properties.get('final_chart_js_config')
        
        if final_chart_config:
            print("✅ final_chart_js_config字段存在")
            config_properties = final_chart_config.get('properties', {})
            if config_properties:
                print(f"✅ final_chart_js_config有非空properties，包含 {len(config_properties)} 个字段")
                print(f"字段列表: {list(config_properties.keys())}")
            else:
                print("❌ final_chart_js_config的properties为空")
                return False
        else:
            print("❌ final_chart_js_config字段不存在")
            return False
            
        return True
    except Exception as e:
        print(f"❌ ChartBlueprintSchema schema生成失败: {e}")
        return False

def test_detailed_slide_blueprint_schema():
    """测试DetailedSlideBlueprintSchema的JSON schema生成"""
    print("\n=== 测试DetailedSlideBlueprintSchema JSON Schema ===")
    try:
        schema = DetailedSlideBlueprintSchema.model_json_schema()
        print("✅ DetailedSlideBlueprintSchema schema生成成功")
        
        # 检查key_elements字段
        properties = schema.get('properties', {})
        key_elements = properties.get('key_elements')
        
        if key_elements:
            print("✅ key_elements字段存在")
            items = key_elements.get('items', {})
            if items:
                item_properties = items.get('properties', {})
                final_chart_config = item_properties.get('final_chart_js_config')
                
                if final_chart_config:
                    print("✅ key_elements.items中包含final_chart_js_config")
                    config_properties = final_chart_config.get('properties', {})
                    if config_properties:
                        print(f"✅ final_chart_js_config有非空properties，包含 {len(config_properties)} 个字段")
                        print(f"字段列表: {list(config_properties.keys())}")
                        
                        # 检查required字段
                        required = final_chart_config.get('required', [])
                        if required:
                            print(f"✅ final_chart_js_config有required字段: {required}")
                        else:
                            print("⚠️ final_chart_js_config没有required字段")
                            
                    else:
                        print("❌ final_chart_js_config的properties为空")
                        return False
                else:
                    print("❌ key_elements.items中不包含final_chart_js_config")
                    return False
            else:
                print("❌ key_elements的items为空")
                return False
        else:
            print("❌ key_elements字段不存在")
            return False
            
        return True
    except Exception as e:
        print(f"❌ DetailedSlideBlueprintSchema schema生成失败: {e}")
        return False

def test_chart_config_creation():
    """测试ChartConfig对象创建"""
    print("\n=== 测试ChartConfig对象创建 ===")
    try:
        # 创建一个完整的ChartConfig对象
        chart_config = {
            "chart_canvas_id": "chart_test_123",
            "chart_type": "bar",
            "chart_js_data": {
                "labels": ["2022", "2023", "2024"],
                "datasets": [{
                    "label": "销售额",
                    "data": [100, 150, 200],
                    "backgroundColor": "var(--chart-color-1)",
                    "borderColor": "var(--chart-color-1)"
                }]
            },
            "chart_js_options": {
                "responsive": True,
                "maintainAspectRatio": False,
                "plugins": {
                    "title": {
                        "display": True,
                        "text": "测试图表"
                    }
                }
            },
            "chart_title": "测试图表",
            "data_source_description": "测试数据"
        }
        
        # 验证ChartConfig
        validated_config = ChartConfig.model_validate(chart_config)
        print("✅ ChartConfig对象创建和验证成功")
        print(f"图表类型: {validated_config.chart_type}")
        print(f"图表标题: {validated_config.chart_title}")
        
        return True
    except Exception as e:
        print(f"❌ ChartConfig对象创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的schema验证...")
    
    tests = [
        test_chart_config_schema,
        test_chart_blueprint_schema,
        test_detailed_slide_blueprint_schema,
        test_chart_config_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print("测试失败，停止执行")
            break
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！Schema修复成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
