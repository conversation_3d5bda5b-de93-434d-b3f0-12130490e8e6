{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\ChatHistoryList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { FaEllipsisV, FaComment, FaShare, FaEdit, FaArchive, FaTrash } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatHistoryList = ({\n  currentChatId,\n  onSelectChat,\n  className = \"\"\n}) => {\n  _s();\n  const [chatList, setChatList] = useState([{\n    id: 'chat-1',\n    title: '关于人工智能的发展前景讨论',\n    lastMessage: '请分析一下当前AI技术的发展趋势...',\n    timestamp: '2025-01-16 15:30',\n    unreadCount: 0\n  }, {\n    id: 'chat-2',\n    title: '房地产市场数据分析',\n    lastMessage: '根据这些数据，房价走势如何？',\n    timestamp: '2025-01-16 14:22',\n    unreadCount: 2\n  }, {\n    id: 'chat-3',\n    title: '产品营销策略规划',\n    lastMessage: '我们需要制定一个完整的营销计划...',\n    timestamp: '2025-01-15 16:45',\n    unreadCount: 0\n  }, {\n    id: 'chat-4',\n    title: '技术文档翻译项目',\n    lastMessage: '请帮我翻译这份API文档',\n    timestamp: '2025-01-15 11:20',\n    unreadCount: 1\n  }, {\n    id: 'chat-5',\n    title: '财务报表数据整理',\n    lastMessage: '这些数字需要重新核算',\n    timestamp: '2025-01-14 09:15',\n    unreadCount: 0\n  }]);\n  const [hoveredChatId, setHoveredChatId] = useState(null);\n  const [dropdownOpen, setDropdownOpen] = useState(null);\n  const dropdownRef = useRef(null);\n\n  // 点击外部关闭下拉菜单\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setDropdownOpen(null);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n  const formatTimestamp = timestamp => {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));\n    if (diffInHours < 1) {\n      return '刚刚';\n    } else if (diffInHours < 24) {\n      return `${diffInHours}小时前`;\n    } else if (diffInHours < 48) {\n      return '昨天';\n    } else {\n      return date.toLocaleDateString('zh-CN', {\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n  };\n  const truncateText = (text, maxLength = 30) => {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  const handleDropdownAction = (action, chatId) => {\n    setDropdownOpen(null);\n    switch (action) {\n      case 'share':\n        console.log('分享聊天:', chatId);\n        // 实现分享功能\n        break;\n      case 'rename':\n        console.log('重命名聊天:', chatId);\n        // 实现重命名功能\n        const newTitle = prompt('请输入新的聊天名称:');\n        if (newTitle && newTitle.trim()) {\n          setChatList(prev => prev.map(chat => chat.id === chatId ? {\n            ...chat,\n            title: newTitle.trim()\n          } : chat));\n        }\n        break;\n      case 'archive':\n        console.log('归档聊天:', chatId);\n        // 实现归档功能\n        setChatList(prev => prev.filter(chat => chat.id !== chatId));\n        break;\n      case 'delete':\n        console.log('删除聊天:', chatId);\n        // 实现删除功能\n        if (window.confirm('确定要删除这个聊天吗？此操作无法撤销。')) {\n          setChatList(prev => prev.filter(chat => chat.id !== chatId));\n        }\n        break;\n      default:\n        break;\n    }\n  };\n  const handleChatClick = chatId => {\n    onSelectChat === null || onSelectChat === void 0 ? void 0 : onSelectChat(chatId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-1 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between px-3 py-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-gray-700 flex items-center\",\n        children: /*#__PURE__*/_jsxDEV(FaComment, {\n          className: \"mr-2\",\n          size: 14\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-xs text-gray-500\",\n        children: chatList.length\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-1 max-h-96 overflow-y-auto custom-scrollbar\",\n      children: chatList.map(chat => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `relative group flex items-center px-3 py-3 rounded-md cursor-pointer transition-colors duration-150 ${currentChatId === chat.id ? 'bg-blue-50 border-r-2 border-blue-500' : 'hover:bg-gray-50'}`,\n        onMouseEnter: () => setHoveredChatId(chat.id),\n        onMouseLeave: () => setHoveredChatId(null),\n        onClick: () => handleChatClick(chat.id),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: `text-sm font-medium truncate ${currentChatId === chat.id ? 'text-blue-700' : 'text-gray-900'}`,\n              children: truncateText(chat.title)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs text-gray-500 ml-2 flex-shrink-0\",\n              children: formatTimestamp(chat.timestamp)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-600 truncate\",\n            children: truncateText(chat.lastMessage)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), chat.unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex items-center justify-center px-2 py-1 text-xs font-medium text-white bg-blue-600 rounded-full mt-1\",\n            children: chat.unreadCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), (hoveredChatId === chat.id || dropdownOpen === chat.id) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative ml-2\",\n          ref: dropdownOpen === chat.id ? dropdownRef : null,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.stopPropagation();\n              setDropdownOpen(dropdownOpen === chat.id ? null : chat.id);\n            },\n            className: \"p-1 text-gray-400 hover:text-gray-600 rounded transition-colors\",\n            title: \"\\u66F4\\u591A\\u64CD\\u4F5C\",\n            children: /*#__PURE__*/_jsxDEV(FaEllipsisV, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 17\n          }, this), dropdownOpen === chat.id && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute right-0 top-6 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: e => {\n                e.stopPropagation();\n                handleDropdownAction('share', chat.id);\n              },\n              className: \"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(FaShare, {\n                size: 12,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 23\n              }, this), \"\\u5171\\u4EAB\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: e => {\n                e.stopPropagation();\n                handleDropdownAction('rename', chat.id);\n              },\n              className: \"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(FaEdit, {\n                size: 12,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 23\n              }, this), \"\\u91CD\\u547D\\u540D\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: e => {\n                e.stopPropagation();\n                handleDropdownAction('archive', chat.id);\n              },\n              className: \"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(FaArchive, {\n                size: 12,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 23\n              }, this), \"\\u5F52\\u6863\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: e => {\n                e.stopPropagation();\n                handleDropdownAction('delete', chat.id);\n              },\n              className: \"flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\",\n              children: [/*#__PURE__*/_jsxDEV(FaTrash, {\n                size: 12,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 23\n              }, this), \"\\u5220\\u9664\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 15\n        }, this)]\n      }, chat.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), chatList.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(FaComment, {\n        size: 24,\n        className: \"mx-auto mb-2 opacity-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm\",\n        children: \"\\u6682\\u65E0\\u804A\\u5929\\u8BB0\\u5F55\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatHistoryList, \"l0NfUywYALq5WlyI2l1ju9UoHJs=\");\n_c = ChatHistoryList;\nexport default ChatHistoryList;\nvar _c;\n$RefreshReg$(_c, \"ChatHistoryList\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "FaEllipsisV", "FaComment", "FaShare", "FaEdit", "FaArchive", "FaTrash", "jsxDEV", "_jsxDEV", "ChatHistoryList", "currentChatId", "onSelectChat", "className", "_s", "chatList", "setChatList", "id", "title", "lastMessage", "timestamp", "unreadCount", "hoveredChatId", "setHoveredChatId", "dropdownOpen", "setDropdownOpen", "dropdownRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "formatTimestamp", "date", "Date", "now", "diffInHours", "Math", "floor", "toLocaleDateString", "month", "day", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "length", "substring", "handleDropdownAction", "action", "chatId", "console", "log", "newTitle", "prompt", "trim", "prev", "map", "chat", "filter", "window", "confirm", "handleChatClick", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMouseEnter", "onMouseLeave", "onClick", "ref", "e", "stopPropagation", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/ChatHistoryList.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { FaEllipsisV, FaComment, FaShare, FaEdit, FaArchive, FaTrash } from 'react-icons/fa';\r\n\r\nconst ChatHistoryList = ({ currentChatId, onSelectChat, className = \"\" }) => {\r\n  const [chatList, setChatList] = useState([\r\n    {\r\n      id: 'chat-1',\r\n      title: '关于人工智能的发展前景讨论',\r\n      lastMessage: '请分析一下当前AI技术的发展趋势...',\r\n      timestamp: '2025-01-16 15:30',\r\n      unreadCount: 0\r\n    },\r\n    {\r\n      id: 'chat-2', \r\n      title: '房地产市场数据分析',\r\n      lastMessage: '根据这些数据，房价走势如何？',\r\n      timestamp: '2025-01-16 14:22',\r\n      unreadCount: 2\r\n    },\r\n    {\r\n      id: 'chat-3',\r\n      title: '产品营销策略规划',\r\n      lastMessage: '我们需要制定一个完整的营销计划...',\r\n      timestamp: '2025-01-15 16:45',\r\n      unreadCount: 0\r\n    },\r\n    {\r\n      id: 'chat-4',\r\n      title: '技术文档翻译项目',\r\n      lastMessage: '请帮我翻译这份API文档',\r\n      timestamp: '2025-01-15 11:20',\r\n      unreadCount: 1\r\n    },\r\n    {\r\n      id: 'chat-5',\r\n      title: '财务报表数据整理',\r\n      lastMessage: '这些数字需要重新核算',\r\n      timestamp: '2025-01-14 09:15',\r\n      unreadCount: 0\r\n    }\r\n  ]);\r\n  \r\n  const [hoveredChatId, setHoveredChatId] = useState(null);\r\n  const [dropdownOpen, setDropdownOpen] = useState(null);\r\n  const dropdownRef = useRef(null);\r\n\r\n  // 点击外部关闭下拉菜单\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\r\n        setDropdownOpen(null);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const formatTimestamp = (timestamp) => {\r\n    const date = new Date(timestamp);\r\n    const now = new Date();\r\n    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));\r\n    \r\n    if (diffInHours < 1) {\r\n      return '刚刚';\r\n    } else if (diffInHours < 24) {\r\n      return `${diffInHours}小时前`;\r\n    } else if (diffInHours < 48) {\r\n      return '昨天';\r\n    } else {\r\n      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });\r\n    }\r\n  };\r\n\r\n  const truncateText = (text, maxLength = 30) => {\r\n    if (text.length <= maxLength) return text;\r\n    return text.substring(0, maxLength) + '...';\r\n  };\r\n\r\n  const handleDropdownAction = (action, chatId) => {\r\n    setDropdownOpen(null);\r\n    \r\n    switch (action) {\r\n      case 'share':\r\n        console.log('分享聊天:', chatId);\r\n        // 实现分享功能\r\n        break;\r\n      case 'rename':\r\n        console.log('重命名聊天:', chatId);\r\n        // 实现重命名功能\r\n        const newTitle = prompt('请输入新的聊天名称:');\r\n        if (newTitle && newTitle.trim()) {\r\n          setChatList(prev => prev.map(chat => \r\n            chat.id === chatId ? { ...chat, title: newTitle.trim() } : chat\r\n          ));\r\n        }\r\n        break;\r\n      case 'archive':\r\n        console.log('归档聊天:', chatId);\r\n        // 实现归档功能\r\n        setChatList(prev => prev.filter(chat => chat.id !== chatId));\r\n        break;\r\n      case 'delete':\r\n        console.log('删除聊天:', chatId);\r\n        // 实现删除功能\r\n        if (window.confirm('确定要删除这个聊天吗？此操作无法撤销。')) {\r\n          setChatList(prev => prev.filter(chat => chat.id !== chatId));\r\n        }\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  };\r\n\r\n  const handleChatClick = (chatId) => {\r\n    onSelectChat?.(chatId);\r\n  };\r\n\r\n  return (\r\n    <div className={`space-y-1 ${className}`}>\r\n      {/* 标题 */}\r\n      <div className=\"flex items-center justify-between px-3 py-2\">\r\n        <h3 className=\"text-sm font-medium text-gray-700 flex items-center\">\r\n          <FaComment className=\"mr-2\" size={14} />\r\n        </h3>\r\n        <span className=\"text-xs text-gray-500\">\r\n          {chatList.length}\r\n        </span>\r\n      </div>\r\n\r\n      {/* 聊天列表 */}\r\n      <div className=\"space-y-1 max-h-96 overflow-y-auto custom-scrollbar\">\r\n        {chatList.map((chat) => (\r\n          <div\r\n            key={chat.id}\r\n            className={`relative group flex items-center px-3 py-3 rounded-md cursor-pointer transition-colors duration-150 ${\r\n              currentChatId === chat.id\r\n                ? 'bg-blue-50 border-r-2 border-blue-500'\r\n                : 'hover:bg-gray-50'\r\n            }`}\r\n            onMouseEnter={() => setHoveredChatId(chat.id)}\r\n            onMouseLeave={() => setHoveredChatId(null)}\r\n            onClick={() => handleChatClick(chat.id)}\r\n          >\r\n            {/* 聊天内容 */}\r\n            <div className=\"flex-1 min-w-0\">\r\n              <div className=\"flex items-center justify-between mb-1\">\r\n                <h4 className={`text-sm font-medium truncate ${\r\n                  currentChatId === chat.id ? 'text-blue-700' : 'text-gray-900'\r\n                }`}>\r\n                  {truncateText(chat.title)}\r\n                </h4>\r\n                <span className=\"text-xs text-gray-500 ml-2 flex-shrink-0\">\r\n                  {formatTimestamp(chat.timestamp)}\r\n                </span>\r\n              </div>\r\n              \r\n              <p className=\"text-xs text-gray-600 truncate\">\r\n                {truncateText(chat.lastMessage)}\r\n              </p>\r\n              \r\n              {/* 未读消息计数 */}\r\n              {chat.unreadCount > 0 && (\r\n                <div className=\"inline-flex items-center justify-center px-2 py-1 text-xs font-medium text-white bg-blue-600 rounded-full mt-1\">\r\n                  {chat.unreadCount}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 三点菜单按钮 */}\r\n            {(hoveredChatId === chat.id || dropdownOpen === chat.id) && (\r\n              <div className=\"relative ml-2\" ref={dropdownOpen === chat.id ? dropdownRef : null}>\r\n                <button\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    setDropdownOpen(dropdownOpen === chat.id ? null : chat.id);\r\n                  }}\r\n                  className=\"p-1 text-gray-400 hover:text-gray-600 rounded transition-colors\"\r\n                  title=\"更多操作\"\r\n                >\r\n                  <FaEllipsisV size={12} />\r\n                </button>\r\n\r\n                {/* 下拉菜单 */}\r\n                {dropdownOpen === chat.id && (\r\n                  <div className=\"absolute right-0 top-6 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50\">\r\n                    <button\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        handleDropdownAction('share', chat.id);\r\n                      }}\r\n                      className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                    >\r\n                      <FaShare size={12} className=\"mr-2\" />\r\n                      共享\r\n                    </button>\r\n                    <button\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        handleDropdownAction('rename', chat.id);\r\n                      }}\r\n                      className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                    >\r\n                      <FaEdit size={12} className=\"mr-2\" />\r\n                      重命名\r\n                    </button>\r\n                    <button\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        handleDropdownAction('archive', chat.id);\r\n                      }}\r\n                      className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                    >\r\n                      <FaArchive size={12} className=\"mr-2\" />\r\n                      归档\r\n                    </button>\r\n                    <button\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        handleDropdownAction('delete', chat.id);\r\n                      }}\r\n                      className=\"flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\"\r\n                    >\r\n                      <FaTrash size={12} className=\"mr-2\" />\r\n                      删除\r\n                    </button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* 空状态 */}\r\n      {chatList.length === 0 && (\r\n        <div className=\"text-center py-8 text-gray-500\">\r\n          <FaComment size={24} className=\"mx-auto mb-2 opacity-50\" />\r\n          <p className=\"text-sm\">暂无聊天记录</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatHistoryList; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7F,MAAMC,eAAe,GAAGA,CAAC;EAAEC,aAAa;EAAEC,YAAY;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,CACvC;IACEkB,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,qBAAqB;IAClCC,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,gBAAgB;IAC7BC,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,oBAAoB;IACjCC,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,cAAc;IAC3BC,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE;EACf,CAAC,EACD;IACEJ,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,YAAY;IACzBC,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE;EACf,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM2B,WAAW,GAAG1B,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACAC,SAAS,CAAC,MAAM;IACd,MAAM0B,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIF,WAAW,CAACG,OAAO,IAAI,CAACH,WAAW,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtEN,eAAe,CAAC,IAAI,CAAC;MACvB;IACF,CAAC;IAEDO,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,eAAe,GAAIf,SAAS,IAAK;IACrC,MAAMgB,IAAI,GAAG,IAAIC,IAAI,CAACjB,SAAS,CAAC;IAChC,MAAMkB,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,GAAGF,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE/D,IAAIG,WAAW,GAAG,CAAC,EAAE;MACnB,OAAO,IAAI;IACb,CAAC,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAO,GAAGA,WAAW,KAAK;IAC5B,CAAC,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAOH,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAU,CAAC,CAAC;IAC7E;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEC,SAAS,GAAG,EAAE,KAAK;IAC7C,IAAID,IAAI,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,IAAI;IACzC,OAAOA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;EAED,MAAMG,oBAAoB,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;IAC/C3B,eAAe,CAAC,IAAI,CAAC;IAErB,QAAQ0B,MAAM;MACZ,KAAK,OAAO;QACVE,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,MAAM,CAAC;QAC5B;QACA;MACF,KAAK,QAAQ;QACXC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEF,MAAM,CAAC;QAC7B;QACA,MAAMG,QAAQ,GAAGC,MAAM,CAAC,YAAY,CAAC;QACrC,IAAID,QAAQ,IAAIA,QAAQ,CAACE,IAAI,CAAC,CAAC,EAAE;UAC/BzC,WAAW,CAAC0C,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,IAAI,IAC/BA,IAAI,CAAC3C,EAAE,KAAKmC,MAAM,GAAG;YAAE,GAAGQ,IAAI;YAAE1C,KAAK,EAAEqC,QAAQ,CAACE,IAAI,CAAC;UAAE,CAAC,GAAGG,IAC7D,CAAC,CAAC;QACJ;QACA;MACF,KAAK,SAAS;QACZP,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,MAAM,CAAC;QAC5B;QACApC,WAAW,CAAC0C,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC3C,EAAE,KAAKmC,MAAM,CAAC,CAAC;QAC5D;MACF,KAAK,QAAQ;QACXC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,MAAM,CAAC;QAC5B;QACA,IAAIU,MAAM,CAACC,OAAO,CAAC,qBAAqB,CAAC,EAAE;UACzC/C,WAAW,CAAC0C,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACD,IAAI,IAAIA,IAAI,CAAC3C,EAAE,KAAKmC,MAAM,CAAC,CAAC;QAC9D;QACA;MACF;QACE;IACJ;EACF,CAAC;EAED,MAAMY,eAAe,GAAIZ,MAAM,IAAK;IAClCxC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAGwC,MAAM,CAAC;EACxB,CAAC;EAED,oBACE3C,OAAA;IAAKI,SAAS,EAAE,aAAaA,SAAS,EAAG;IAAAoD,QAAA,gBAEvCxD,OAAA;MAAKI,SAAS,EAAC,6CAA6C;MAAAoD,QAAA,gBAC1DxD,OAAA;QAAII,SAAS,EAAC,qDAAqD;QAAAoD,QAAA,eACjExD,OAAA,CAACN,SAAS;UAACU,SAAS,EAAC,MAAM;UAACqD,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACL7D,OAAA;QAAMI,SAAS,EAAC,uBAAuB;QAAAoD,QAAA,EACpClD,QAAQ,CAACiC;MAAM;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN7D,OAAA;MAAKI,SAAS,EAAC,qDAAqD;MAAAoD,QAAA,EACjElD,QAAQ,CAAC4C,GAAG,CAAEC,IAAI,iBACjBnD,OAAA;QAEEI,SAAS,EAAE,uGACTF,aAAa,KAAKiD,IAAI,CAAC3C,EAAE,GACrB,uCAAuC,GACvC,kBAAkB,EACrB;QACHsD,YAAY,EAAEA,CAAA,KAAMhD,gBAAgB,CAACqC,IAAI,CAAC3C,EAAE,CAAE;QAC9CuD,YAAY,EAAEA,CAAA,KAAMjD,gBAAgB,CAAC,IAAI,CAAE;QAC3CkD,OAAO,EAAEA,CAAA,KAAMT,eAAe,CAACJ,IAAI,CAAC3C,EAAE,CAAE;QAAAgD,QAAA,gBAGxCxD,OAAA;UAAKI,SAAS,EAAC,gBAAgB;UAAAoD,QAAA,gBAC7BxD,OAAA;YAAKI,SAAS,EAAC,wCAAwC;YAAAoD,QAAA,gBACrDxD,OAAA;cAAII,SAAS,EAAE,gCACbF,aAAa,KAAKiD,IAAI,CAAC3C,EAAE,GAAG,eAAe,GAAG,eAAe,EAC5D;cAAAgD,QAAA,EACApB,YAAY,CAACe,IAAI,CAAC1C,KAAK;YAAC;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACL7D,OAAA;cAAMI,SAAS,EAAC,0CAA0C;cAAAoD,QAAA,EACvD9B,eAAe,CAACyB,IAAI,CAACxC,SAAS;YAAC;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN7D,OAAA;YAAGI,SAAS,EAAC,gCAAgC;YAAAoD,QAAA,EAC1CpB,YAAY,CAACe,IAAI,CAACzC,WAAW;UAAC;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,EAGHV,IAAI,CAACvC,WAAW,GAAG,CAAC,iBACnBZ,OAAA;YAAKI,SAAS,EAAC,gHAAgH;YAAAoD,QAAA,EAC5HL,IAAI,CAACvC;UAAW;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL,CAAChD,aAAa,KAAKsC,IAAI,CAAC3C,EAAE,IAAIO,YAAY,KAAKoC,IAAI,CAAC3C,EAAE,kBACrDR,OAAA;UAAKI,SAAS,EAAC,eAAe;UAAC6D,GAAG,EAAElD,YAAY,KAAKoC,IAAI,CAAC3C,EAAE,GAAGS,WAAW,GAAG,IAAK;UAAAuC,QAAA,gBAChFxD,OAAA;YACEgE,OAAO,EAAGE,CAAC,IAAK;cACdA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnBnD,eAAe,CAACD,YAAY,KAAKoC,IAAI,CAAC3C,EAAE,GAAG,IAAI,GAAG2C,IAAI,CAAC3C,EAAE,CAAC;YAC5D,CAAE;YACFJ,SAAS,EAAC,iEAAiE;YAC3EK,KAAK,EAAC,0BAAM;YAAA+C,QAAA,eAEZxD,OAAA,CAACP,WAAW;cAACgE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EAGR9C,YAAY,KAAKoC,IAAI,CAAC3C,EAAE,iBACvBR,OAAA;YAAKI,SAAS,EAAC,4FAA4F;YAAAoD,QAAA,gBACzGxD,OAAA;cACEgE,OAAO,EAAGE,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB1B,oBAAoB,CAAC,OAAO,EAAEU,IAAI,CAAC3C,EAAE,CAAC;cACxC,CAAE;cACFJ,SAAS,EAAC,4EAA4E;cAAAoD,QAAA,gBAEtFxD,OAAA,CAACL,OAAO;gBAAC8D,IAAI,EAAE,EAAG;gBAACrD,SAAS,EAAC;cAAM;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7D,OAAA;cACEgE,OAAO,EAAGE,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB1B,oBAAoB,CAAC,QAAQ,EAAEU,IAAI,CAAC3C,EAAE,CAAC;cACzC,CAAE;cACFJ,SAAS,EAAC,4EAA4E;cAAAoD,QAAA,gBAEtFxD,OAAA,CAACJ,MAAM;gBAAC6D,IAAI,EAAE,EAAG;gBAACrD,SAAS,EAAC;cAAM;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7D,OAAA;cACEgE,OAAO,EAAGE,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB1B,oBAAoB,CAAC,SAAS,EAAEU,IAAI,CAAC3C,EAAE,CAAC;cAC1C,CAAE;cACFJ,SAAS,EAAC,4EAA4E;cAAAoD,QAAA,gBAEtFxD,OAAA,CAACH,SAAS;gBAAC4D,IAAI,EAAE,EAAG;gBAACrD,SAAS,EAAC;cAAM;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7D,OAAA;cACEgE,OAAO,EAAGE,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB1B,oBAAoB,CAAC,QAAQ,EAAEU,IAAI,CAAC3C,EAAE,CAAC;cACzC,CAAE;cACFJ,SAAS,EAAC,yEAAyE;cAAAoD,QAAA,gBAEnFxD,OAAA,CAACF,OAAO;gBAAC2D,IAAI,EAAE,EAAG;gBAACrD,SAAS,EAAC;cAAM;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA,GA/FIV,IAAI,CAAC3C,EAAE;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgGT,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGLvD,QAAQ,CAACiC,MAAM,KAAK,CAAC,iBACpBvC,OAAA;MAAKI,SAAS,EAAC,gCAAgC;MAAAoD,QAAA,gBAC7CxD,OAAA,CAACN,SAAS;QAAC+D,IAAI,EAAE,EAAG;QAACrD,SAAS,EAAC;MAAyB;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3D7D,OAAA;QAAGI,SAAS,EAAC,SAAS;QAAAoD,QAAA,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxD,EAAA,CAlPIJ,eAAe;AAAAmE,EAAA,GAAfnE,eAAe;AAoPrB,eAAeA,eAAe;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}