from pydantic import BaseModel, Field
from typing import List

class SimplifiedStyleSchema(BaseModel):
    """简化的样式Schema，基于Gemini API最佳实践"""
    
    # 基本信息
    style_summary: str = Field(description="整体风格总结，2-3句话")
    theme_name: str = Field(description="主题风格名称，例如'商务专业风格'")
    
    # 色彩方案 - 简化为必需字段
    primary_color: str = Field(description="主色调HEX值", pattern="^#[0-9A-Fa-f]{6}$")
    secondary_color: str = Field(description="辅色调HEX值", pattern="^#[0-9A-Fa-f]{6}$")
    accent_color: str = Field(description="强调色HEX值", pattern="^#[0-9A-Fa-f]{6}$")
    background_color: str = Field(description="背景色HEX值", pattern="^#[0-9A-Fa-f]{6}$")
    text_color: str = Field(description="文本色HEX值", pattern="^#[0-9A-Fa-f]{6}$")
    
    # 字体设置
    primary_font: str = Field(default="Microsoft YaHei", description="主字体")
    heading_font: str = Field(default="Microsoft YaHei", description="标题字体")
    
    # 幻灯片大纲
    slide_outlines: List[str] = Field(description="幻灯片标题列表，每个元素是一张幻灯片的标题") 