#!/usr/bin/env python3
"""
修复additionalProperties问题的脚本
"""

import re

def fix_model_configs():
    """为主要模型添加extra='forbid'配置"""
    
    file_path = "app/models/presentation_model.py"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 需要修复的模型列表
    models_to_fix = [
        "TextElementSchema",
        "KpiCardSchema", 
        "ChartBlueprintSchema",
        "ImageElementSchema"
    ]
    
    for model_name in models_to_fix:
        # 查找模型定义，在类定义后面添加model_config
        pattern = rf'(class {model_name}\(BaseModel\):.*?)(animation_style.*?\n    \))'
        
        def add_config(match):
            return match.group(1) + match.group(2) + "\n    \n    model_config = ConfigDict(extra='forbid')"
        
        content = re.sub(pattern, add_config, content, flags=re.DOTALL)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已为主要模型添加extra='forbid'配置")

if __name__ == "__main__":
    fix_model_configs() 