#!/usr/bin/env python3
"""
文件处理服务
支持音频、视频、图片、文档等多种文件类型的上传和处理
"""

import os
import mimetypes
import time
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path
import logging

from google import genai
from google.genai import types

logger = logging.getLogger(__name__)

class FileProcessor:
    """文件处理器，支持多种文件类型"""
    
    # 支持的文件类型
    SUPPORTED_MIME_TYPES = {
        # 图片
        'image/png': {'category': 'image', 'max_size': 7 * 1024 * 1024},  # 7MB
        'image/jpeg': {'category': 'image', 'max_size': 7 * 1024 * 1024},
        'image/webp': {'category': 'image', 'max_size': 7 * 1024 * 1024},
        
        # 音频
        'audio/x-aac': {'category': 'audio', 'max_size': 50 * 1024 * 1024},  # 50MB
        'audio/flac': {'category': 'audio', 'max_size': 50 * 1024 * 1024},
        'audio/mp3': {'category': 'audio', 'max_size': 50 * 1024 * 1024},
        'audio/m4a': {'category': 'audio', 'max_size': 50 * 1024 * 1024},
        'audio/mpeg': {'category': 'audio', 'max_size': 50 * 1024 * 1024},
        'audio/mpga': {'category': 'audio', 'max_size': 50 * 1024 * 1024},
        'audio/mp4': {'category': 'audio', 'max_size': 50 * 1024 * 1024},
        'audio/opus': {'category': 'audio', 'max_size': 50 * 1024 * 1024},
        'audio/pcm': {'category': 'audio', 'max_size': 50 * 1024 * 1024},
        'audio/wav': {'category': 'audio', 'max_size': 50 * 1024 * 1024},
        'audio/webm': {'category': 'audio', 'max_size': 50 * 1024 * 1024},
        
        # 视频
        'video/x-flv': {'category': 'video', 'max_size': 100 * 1024 * 1024},  # 100MB
        'video/quicktime': {'category': 'video', 'max_size': 100 * 1024 * 1024},
        'video/mpeg': {'category': 'video', 'max_size': 100 * 1024 * 1024},
        'video/mpegs': {'category': 'video', 'max_size': 100 * 1024 * 1024},
        'video/mpg': {'category': 'video', 'max_size': 100 * 1024 * 1024},
        'video/mp4': {'category': 'video', 'max_size': 100 * 1024 * 1024},
        'video/webm': {'category': 'video', 'max_size': 100 * 1024 * 1024},
        'video/wmv': {'category': 'video', 'max_size': 100 * 1024 * 1024},
        'video/3gpp': {'category': 'video', 'max_size': 100 * 1024 * 1024},
        
        # 文档
        'application/pdf': {'category': 'document', 'max_size': 50 * 1024 * 1024},  # 50MB
        'text/plain': {'category': 'document', 'max_size': 10 * 1024 * 1024},  # 10MB
    }
    
    def __init__(self, api_key: str):
        """初始化文件处理器"""
        self.client = genai.Client(api_key=api_key)
        
    def detect_file_type(self, file_path: str) -> Tuple[str, str]:
        """
        检测文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            (mime_type, category) 元组
        """
        # 首先使用mimetypes库检测
        mime_type, _ = mimetypes.guess_type(file_path)
        
        if not mime_type:
            # 如果检测失败，根据文件扩展名手动判断
            ext = Path(file_path).suffix.lower()
            ext_to_mime = {
                '.png': 'image/png',
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.webp': 'image/webp',
                '.mp3': 'audio/mp3',
                '.wav': 'audio/wav',
                '.m4a': 'audio/m4a',
                '.flac': 'audio/flac',
                '.mp4': 'video/mp4',
                '.webm': 'video/webm',
                '.mov': 'video/quicktime',
                '.avi': 'video/mpeg',
                '.pdf': 'application/pdf',
                '.txt': 'text/plain',
            }
            mime_type = ext_to_mime.get(ext, 'application/octet-stream')
        
        # 获取文件类别
        if mime_type in self.SUPPORTED_MIME_TYPES:
            category = self.SUPPORTED_MIME_TYPES[mime_type]['category']
            return mime_type, category
        else:
            return mime_type, 'unknown'
    
    def validate_file(self, file_path: str) -> Dict[str, Any]:
        """
        验证文件是否可以处理
        
        Args:
            file_path: 文件路径
            
        Returns:
            验证结果字典
        """
        result = {
            'valid': False,
            'mime_type': None,
            'category': None,
            'size': 0,
            'error': None
        }
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            result['error'] = f"文件不存在: {file_path}"
            return result
        
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        result['size'] = file_size
        
        # 检测文件类型
        mime_type, category = self.detect_file_type(file_path)
        result['mime_type'] = mime_type
        result['category'] = category
        
        # 检查是否支持该文件类型
        if mime_type not in self.SUPPORTED_MIME_TYPES:
            result['error'] = f"不支持的文件类型: {mime_type}"
            return result
        
        # 检查文件大小
        max_size = self.SUPPORTED_MIME_TYPES[mime_type]['max_size']
        if file_size > max_size:
            result['error'] = f"文件过大: {file_size} bytes (最大: {max_size} bytes)"
            return result
        
        result['valid'] = True
        return result
    
    def upload_file(self, file_path: str, display_name: Optional[str] = None) -> Dict[str, Any]:
        """
        上传文件到Gemini File API
        
        Args:
            file_path: 文件路径
            display_name: 显示名称
            
        Returns:
            上传结果字典
        """
        result = {
            'success': False,
            'file_info': None,
            'error': None
        }
        
        try:
            # 验证文件
            validation = self.validate_file(file_path)
            if not validation['valid']:
                result['error'] = validation['error']
                return result
            
            # 设置显示名称
            if not display_name:
                display_name = Path(file_path).stem
            
            logger.info(f"正在上传文件: {file_path} (类型: {validation['category']})")
            
            # 上传文件
            uploaded_file = self.client.files.upload(
                file=file_path,
                config={'display_name': display_name}
            )
            
            # 等待文件处理完成（特别是音频和视频文件）
            if validation['category'] in ['audio', 'video']:
                uploaded_file = self._wait_for_processing(uploaded_file)
            
            result['success'] = True
            result['file_info'] = {
                'name': uploaded_file.name,
                'uri': uploaded_file.uri,
                'mime_type': uploaded_file.mime_type,
                'display_name': uploaded_file.display_name,
                'size_bytes': getattr(uploaded_file, 'size_bytes', 0),
                'state': getattr(uploaded_file, 'state', 'ACTIVE'),
                'category': validation['category']
            }
            
            logger.info(f"文件上传成功: {uploaded_file.uri}")
            
        except Exception as e:
            result['error'] = f"上传失败: {str(e)}"
            logger.error(f"文件上传失败: {str(e)}")
        
        return result
    
    def _wait_for_processing(self, uploaded_file, max_wait_time: int = 300) -> Any:
        """
        等待文件处理完成
        
        Args:
            uploaded_file: 上传的文件对象
            max_wait_time: 最大等待时间（秒）
            
        Returns:
            处理完成的文件对象
        """
        start_time = time.time()
        
        while hasattr(uploaded_file, 'state') and uploaded_file.state.name == "PROCESSING":
            if time.time() - start_time > max_wait_time:
                raise TimeoutError(f"文件处理超时 (>{max_wait_time}秒)")
            
            logger.info("等待文件处理完成...")
            time.sleep(5)
            uploaded_file = self.client.files.get(name=uploaded_file.name)
        
        if hasattr(uploaded_file, 'state') and uploaded_file.state.name == "FAILED":
            raise ValueError(f"文件处理失败: {uploaded_file.state.name}")
        
        return uploaded_file
    
    def analyze_file(self, file_uri: str, mime_type: str, category: str, 
                    custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        分析文件内容
        
        Args:
            file_uri: 文件URI
            mime_type: MIME类型
            category: 文件类别
            custom_prompt: 自定义提示词
            
        Returns:
            分析结果字典
        """
        result = {
            'success': False,
            'analysis': None,
            'error': None
        }
        
        try:
            # 根据文件类型选择合适的提示词
            if custom_prompt:
                prompt = custom_prompt
            else:
                prompt = self._get_default_prompt(category)
            
            # 构建内容
            contents = [
                prompt,
                types.Part(
                    file_data=types.FileData(
                        file_uri=file_uri,
                        mime_type=mime_type
                    )
                )
            ]
            
            logger.info(f"正在分析文件: {file_uri} (类型: {category})")
            
            # 生成内容
            response = self.client.models.generate_content(
                model="gemini-2.0-flash",
                contents=contents,
                config=types.GenerateContentConfig(
                    temperature=0.3,
                    max_output_tokens=4096
                )
            )
            
            result['success'] = True
            result['analysis'] = response.text
            
            logger.info(f"文件分析完成: {file_uri}")
            
        except Exception as e:
            result['error'] = f"分析失败: {str(e)}"
            logger.error(f"文件分析失败: {str(e)}")
        
        return result
    
    def _get_default_prompt(self, category: str) -> str:
        """获取默认的分析提示词"""
        prompts = {
            'image': '请详细描述这张图片的内容，包括主要对象、场景、颜色、构图等要素。',
            'audio': '请分析这个音频文件的内容，包括语音内容（如果有）、音乐类型、音质特征等。',
            'video': '请分析这个视频的内容，包括主要场景、人物活动、视觉效果、时长等信息。',
            'document': '请总结这个文档的主要内容，包括关键信息、结构和重点。'
        }
        return prompts.get(category, '请分析这个文件的内容。')
    
    def delete_file(self, file_name: str) -> Dict[str, Any]:
        """
        删除已上传的文件
        
        Args:
            file_name: 文件名称
            
        Returns:
            删除结果字典
        """
        result = {
            'success': False,
            'error': None
        }
        
        try:
            self.client.files.delete(name=file_name)
            result['success'] = True
            logger.info(f"文件删除成功: {file_name}")
            
        except Exception as e:
            result['error'] = f"删除失败: {str(e)}"
            logger.error(f"文件删除失败: {str(e)}")
        
        return result
    
    def list_files(self) -> Dict[str, Any]:
        """
        列出所有已上传的文件
        
        Returns:
            文件列表字典
        """
        result = {
            'success': False,
            'files': [],
            'error': None
        }
        
        try:
            files = self.client.files.list()
            result['files'] = [
                {
                    'name': file.name,
                    'uri': file.uri,
                    'display_name': file.display_name,
                    'mime_type': file.mime_type,
                    'size_bytes': getattr(file, 'size_bytes', 0),
                    'state': getattr(file, 'state', 'ACTIVE')
                }
                for file in files
            ]
            result['success'] = True
            
        except Exception as e:
            result['error'] = f"获取文件列表失败: {str(e)}"
            logger.error(f"获取文件列表失败: {str(e)}")
        
        return result
    
    def get_file_info(self, file_name: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_name: 文件名称
            
        Returns:
            文件信息字典
        """
        result = {
            'success': False,
            'file_info': None,
            'error': None
        }
        
        try:
            file_obj = self.client.files.get(name=file_name)
            result['file_info'] = {
                'name': file_obj.name,
                'uri': file_obj.uri,
                'display_name': file_obj.display_name,
                'mime_type': file_obj.mime_type,
                'size_bytes': getattr(file_obj, 'size_bytes', 0),
                'state': getattr(file_obj, 'state', 'ACTIVE')
            }
            result['success'] = True
            
        except Exception as e:
            result['error'] = f"获取文件信息失败: {str(e)}"
            logger.error(f"获取文件信息失败: {str(e)}")
        
        return result 