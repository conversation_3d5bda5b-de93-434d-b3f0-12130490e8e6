{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\UploadedFiles.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadedFiles = ({\n  files,\n  onRemoveFile,\n  className = \"\"\n}) => {\n  if (!files || files.length === 0) {\n    return null;\n  }\n  const getFileIcon = (category, mimeType) => {\n    switch (category) {\n      case 'image':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-4 h-4 text-green-600\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this);\n      case 'audio':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-4 h-4 text-purple-600\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this);\n      case 'video':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-4 h-4 text-red-600\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-4 h-4 text-blue-600\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\n  };\n  const getFileName = filename => {\n    // 如果文件名太长，显示前面部分...后缀\n    if (filename.length > 25) {\n      const ext = filename.split('.').pop();\n      const name = filename.substring(0, filename.lastIndexOf('.'));\n      return name.substring(0, 20) + '...' + (ext ? '.' + ext : '');\n    }\n    return filename;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-2 ${className}`,\n    children: files.map(file => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-3 p-3 bg-gray-50 rounded-lg border\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0\",\n        children: getFileIcon(file.category, file.mime_type)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 min-w-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm font-medium text-gray-900 truncate\",\n          children: getFileName(file.filename)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-500\",\n          children: [formatFileSize(file.size), \" \\u2022 \", file.category]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onRemoveFile === null || onRemoveFile === void 0 ? void 0 : onRemoveFile(file.file_id),\n        className: \"flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors\",\n        title: \"\\u79FB\\u9664\\u6587\\u4EF6\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-4 h-4\",\n          fill: \"none\",\n          stroke: \"currentColor\",\n          viewBox: \"0 0 24 24\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M6 18L18 6M6 6l12 12\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this)]\n    }, file.file_id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_c = UploadedFiles;\nexport default UploadedFiles;\nvar _c;\n$RefreshReg$(_c, \"UploadedFiles\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "UploadedFiles", "files", "onRemoveFile", "className", "length", "getFileIcon", "category", "mimeType", "fill", "stroke", "viewBox", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "getFileName", "filename", "ext", "split", "pop", "name", "substring", "lastIndexOf", "map", "file", "mime_type", "size", "onClick", "file_id", "title", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/UploadedFiles.js"], "sourcesContent": ["import React from 'react';\r\n\r\nconst UploadedFiles = ({ files, onRemoveFile, className = \"\" }) => {\r\n  if (!files || files.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  const getFileIcon = (category, mimeType) => {\r\n    switch (category) {\r\n      case 'image':\r\n        return (\r\n          <svg className=\"w-4 h-4 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n          </svg>\r\n        );\r\n      case 'audio':\r\n        return (\r\n          <svg className=\"w-4 h-4 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3\" />\r\n          </svg>\r\n        );\r\n      case 'video':\r\n        return (\r\n          <svg className=\"w-4 h-4 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\r\n          </svg>\r\n        );\r\n      default:\r\n        return (\r\n          <svg className=\"w-4 h-4 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n          </svg>\r\n        );\r\n    }\r\n  };\r\n\r\n  const formatFileSize = (bytes) => {\r\n    if (bytes === 0) return '0 B';\r\n    const k = 1024;\r\n    const sizes = ['B', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\r\n  };\r\n\r\n  const getFileName = (filename) => {\r\n    // 如果文件名太长，显示前面部分...后缀\r\n    if (filename.length > 25) {\r\n      const ext = filename.split('.').pop();\r\n      const name = filename.substring(0, filename.lastIndexOf('.'));\r\n      return name.substring(0, 20) + '...' + (ext ? '.' + ext : '');\r\n    }\r\n    return filename;\r\n  };\r\n\r\n  return (\r\n    <div className={`space-y-2 ${className}`}>\r\n      {files.map((file) => (\r\n        <div\r\n          key={file.file_id}\r\n          className=\"flex items-center gap-3 p-3 bg-gray-50 rounded-lg border\"\r\n        >\r\n          {/* 文件图标 */}\r\n          <div className=\"flex-shrink-0\">\r\n            {getFileIcon(file.category, file.mime_type)}\r\n          </div>\r\n          \r\n          {/* 文件信息 */}\r\n          <div className=\"flex-1 min-w-0\">\r\n            <div className=\"text-sm font-medium text-gray-900 truncate\">\r\n              {getFileName(file.filename)}\r\n            </div>\r\n            <div className=\"text-xs text-gray-500\">\r\n              {formatFileSize(file.size)} • {file.category}\r\n            </div>\r\n          </div>\r\n          \r\n          {/* 删除按钮 */}\r\n          <button\r\n            onClick={() => onRemoveFile?.(file.file_id)}\r\n            className=\"flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors\"\r\n            title=\"移除文件\"\r\n          >\r\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UploadedFiles; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,KAAK;EAAEC,YAAY;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EACjE,IAAI,CAACF,KAAK,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;IAChC,OAAO,IAAI;EACb;EAEA,MAAMC,WAAW,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,QAAQD,QAAQ;MACd,KAAK,OAAO;QACV,oBACEP,OAAA;UAAKI,SAAS,EAAC,wBAAwB;UAACK,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC3FZ,OAAA;YAAMa,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAA2J;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChO,CAAC;MAEV,KAAK,OAAO;QACV,oBACEpB,OAAA;UAAKI,SAAS,EAAC,yBAAyB;UAACK,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC5FZ,OAAA;YAAMa,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAqJ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1N,CAAC;MAEV,KAAK,OAAO;QACV,oBACEpB,OAAA;UAAKI,SAAS,EAAC,sBAAsB;UAACK,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eACzFZ,OAAA;YAAMa,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAoI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzM,CAAC;MAEV;QACE,oBACEpB,OAAA;UAAKI,SAAS,EAAC,uBAAuB;UAACK,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC1FZ,OAAA;YAAMa,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAsH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3L,CAAC;IAEZ;EACF,CAAC;EAED,MAAMC,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;IAC7B,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACN,KAAK,CAAC,GAAGI,IAAI,CAACE,GAAG,CAACL,CAAC,CAAC,CAAC;IACnD,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAG,CAACP,CAAC,EAAEE,CAAC,CAAC,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;EAED,MAAMO,WAAW,GAAIC,QAAQ,IAAK;IAChC;IACA,IAAIA,QAAQ,CAAC5B,MAAM,GAAG,EAAE,EAAE;MACxB,MAAM6B,GAAG,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MACrC,MAAMC,IAAI,GAAGJ,QAAQ,CAACK,SAAS,CAAC,CAAC,EAAEL,QAAQ,CAACM,WAAW,CAAC,GAAG,CAAC,CAAC;MAC7D,OAAOF,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,IAAIJ,GAAG,GAAG,GAAG,GAAGA,GAAG,GAAG,EAAE,CAAC;IAC/D;IACA,OAAOD,QAAQ;EACjB,CAAC;EAED,oBACEjC,OAAA;IAAKI,SAAS,EAAE,aAAaA,SAAS,EAAG;IAAAQ,QAAA,EACtCV,KAAK,CAACsC,GAAG,CAAEC,IAAI,iBACdzC,OAAA;MAEEI,SAAS,EAAC,0DAA0D;MAAAQ,QAAA,gBAGpEZ,OAAA;QAAKI,SAAS,EAAC,eAAe;QAAAQ,QAAA,EAC3BN,WAAW,CAACmC,IAAI,CAAClC,QAAQ,EAAEkC,IAAI,CAACC,SAAS;MAAC;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAGNpB,OAAA;QAAKI,SAAS,EAAC,gBAAgB;QAAAQ,QAAA,gBAC7BZ,OAAA;UAAKI,SAAS,EAAC,4CAA4C;UAAAQ,QAAA,EACxDoB,WAAW,CAACS,IAAI,CAACR,QAAQ;QAAC;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACNpB,OAAA;UAAKI,SAAS,EAAC,uBAAuB;UAAAQ,QAAA,GACnCS,cAAc,CAACoB,IAAI,CAACE,IAAI,CAAC,EAAC,UAAG,EAACF,IAAI,CAAClC,QAAQ;QAAA;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpB,OAAA;QACE4C,OAAO,EAAEA,CAAA,KAAMzC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAGsC,IAAI,CAACI,OAAO,CAAE;QAC5CzC,SAAS,EAAC,sEAAsE;QAChF0C,KAAK,EAAC,0BAAM;QAAAlC,QAAA,eAEZZ,OAAA;UAAKI,SAAS,EAAC,SAAS;UAACK,IAAI,EAAC,MAAM;UAACC,MAAM,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC5EZ,OAAA;YAAMa,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,WAAW,EAAE,CAAE;YAACC,CAAC,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA,GA3BJqB,IAAI,CAACI,OAAO;MAAA5B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA4Bd,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC2B,EAAA,GAxFI9C,aAAa;AA0FnB,eAAeA,aAAa;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}