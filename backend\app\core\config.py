# backend/app/core/config.py
import os
from dotenv import load_dotenv
from typing import Optional, List
from pathlib import Path

# --- (核心修复) 使用相对路径和 Pathlib 来定位 .env ---
# __file__ 是当前文件 (config.py) 的路径
# .parent 是父目录 (core)
# .parent.parent 是祖父目录 (app)
# .parent.parent.parent 是曾祖父目录 (backend)
# 最终，我们想要 backend 目录下的 .env 文件
backend_dir = Path(__file__).resolve().parent.parent.parent
env_path = backend_dir / ".env"

# 导入 log_once，使用相对于 backend 目录的路径
from app.utils.log_util import log_once

if env_path.exists():
    load_dotenv(dotenv_path=env_path)
    log_once(f"Loaded environment variables from {env_path}")
else:
    # 尝试在项目根目录查找（上一级）
    root_env_path = backend_dir.parent / ".env"
    if root_env_path.exists():
        load_dotenv(dotenv_path=root_env_path)
        log_once(f"Loaded environment variables from project root: {root_env_path}")
    else:
        log_once(f"Warning: .env file not found at {env_path} or {root_env_path}. Using environment variables.")


class Settings:
    PROJECT_NAME: str = "TikTodo AI Slides"
    PROJECT_VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    SERVER_HOST: str = os.getenv("SERVER_HOST", "0.0.0.0")
    SERVER_PORT: int = int(os.getenv("SERVER_PORT", 8000))
    DEV_MODE: bool = os.getenv("DEV_MODE", "true").lower() in ("true", "1", "t")

    GEMINI_DEFAULT_TIMEOUT: int = int(os.getenv("GEMINI_DEFAULT_TIMEOUT", 120))

    GOOGLE_CUSTOM_SEARCH_API_KEY: Optional[str] = os.getenv("GOOGLE_CUSTOM_SEARCH_API_KEY")
    GOOGLE_CUSTOM_SEARCH_CX_ID: Optional[str] = os.getenv("GOOGLE_CUSTOM_SEARCH_CX_ID")

    MIN_SLIDES_PER_PRESENTATION: int = int(os.getenv("MIN_SLIDES_PER_PRESENTATION", 3))
    DEFAULT_SLIDES_PER_PRESENTATION: int = int(os.getenv("DEFAULT_SLIDES_PER_PRESENTATION", 8))
    MAX_SLIDES_PER_PRESENTATION: int = int(os.getenv("MAX_SLIDES_PER_PRESENTATION", 20))

    GEMINI_API_KEYS_STR: str = os.getenv("GEMINI_API_KEY", "")
    GEMINI_API_KEYS: List[str] = [key.strip() for key in GEMINI_API_KEYS_STR.split(',') if key.strip()]
    
    GEMINI_MODEL_NAME: str = os.getenv("GEMINI_MODEL_NAME", "gemini-2.5-flash-preview-05-20")
    GEMINI_IMAGE_MODEL_NAME: str = os.getenv("GEMINI_IMAGE_MODEL_NAME", "gemini-1.5-flash")
    
    DATABASE_URL: str = os.getenv("DATABASE_URL")
    
    SECRET_KEY: str = os.getenv("SECRET_KEY", "a_very_default_secret_key_please_change_me")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # --- (核心修复) 确保 project_storage 路径正确 ---
    # 它应该在 backend 目录的同级，即项目根目录
    BASE_PROJECT_STORAGE_DIR: str = str(backend_dir.parent / "project_storage")

    def __init__(self):
        if not self.GEMINI_API_KEYS:
            raise ValueError("CRITICAL: GEMINI_API_KEY must be set in the .env file with at least one key.")
        
        # 模型名称现在有默认值，不需要强制要求设置
        if not self.DATABASE_URL:
             raise ValueError("CRITICAL: DATABASE_URL must be set in the .env file.")
        
        os.makedirs(self.BASE_PROJECT_STORAGE_DIR, exist_ok=True)
        log_once("Settings initialized and all required environment variables are present.")
        log_once(f"Project storage directory is set to: {self.BASE_PROJECT_STORAGE_DIR}")
        log_once(f"Loaded {len(self.GEMINI_API_KEYS)} Gemini API Key(s).")


settings = Settings()