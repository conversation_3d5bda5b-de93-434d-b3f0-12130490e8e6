# Core Web Framework
fastapi==0.115.12
uvicorn[standard]==0.34.2
python-dotenv==1.1.0

# Database & ORM
sqlalchemy==2.0.31
pymysql==1.1.0

# Google AI - Official New SDK (recommended by Google)
google-genai>=1.20.0
google-api-python-client==2.170.0

# AI Libraries
instructor>=1.8.3
jsonref>=1.0.0  # Required by instructor for GENAI_TOOLS mode
pydantic==2.11.5
pydantic-settings==2.7.0

# Authentication & Security
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
email-validator>=2.0.0
python-multipart==0.0.20

# Browser Automation & HTML/Image Processing
beautifulsoup4==4.13.4
pillow==11.2.1
playwright==1.49.1
aiohttp==3.12.13
aiofiles==24.1.0

# HTTP Client & Networking
httpx==0.28.1
anyio==4.9.0
starlette==0.46.2
h11==0.16.0

# Date & Time Utilities
python-dateutil==2.9.0.post0

# File Processing
python-docx==1.1.2
openpyxl==3.1.5

# Development & Testing
pytest==8.3.4
pytest-asyncio==0.24.0

# Utilities
typing-extensions==4.13.2
rich==13.9.4

# Indirect Dependencies (retained for clarity and stability)
annotated-types==0.7.0
bcrypt==4.1.2
cachetools==5.5.2
certifi==2025.4.26
charset-normalizer==3.4.2
click==8.2.1
docstring_parser==0.16
google-ai-generativelanguage==0.6.15
google-api-core==2.25.0rc1
google-auth==2.40.2
google-auth-httplib2==0.2.0
googleapis-common-protos==1.70.0
grpcio==1.71.0
httpcore==1.0.9
httplib2==0.22.0
idna==3.10
jsonschema>=4.0.0
packaging==25.0
proto-plus==1.26.1
protobuf==5.29.4
pyasn1==0.6.1
pyasn1_modules==0.4.2
pydantic_core==2.33.2
pyparsing==3.2.3
rsa==4.9.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
typing-inspection==0.4.1
uritemplate==4.1.1
urllib3==2.4.0

# CORS
fastapi-cors==0.0.6