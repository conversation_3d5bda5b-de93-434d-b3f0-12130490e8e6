from sqlalchemy.orm import Session
from typing import Optional, List
from app.db import models as db_models
from app.models import user_model as pydantic_models
from app.core.security import get_password_hash, verify_password


def get_user_by_email(db: Session, email: str) -> Optional[db_models.User]:
    """根据邮箱获取用户"""
    return db.query(db_models.User).filter(db_models.User.email == email).first()


def get_user(db: Session, user_id: str) -> Optional[db_models.User]:
    """根据ID获取用户"""
    return db.query(db_models.User).filter(db_models.User.id == user_id).first()


def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[db_models.User]:
    """获取用户列表，带分页功能"""
    return db.query(db_models.User).offset(skip).limit(limit).all()


def create_user(db: Session, user: pydantic_models.UserCreate) -> db_models.User:
    """创建新用户"""
    hashed_password = get_password_hash(user.password)
    db_user = db_models.User(
        email=user.email,
        hashed_password=hashed_password,
        # id 会由 default=generate_uuid_str 生成
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def update_user(db: Session, user_id: str, user_update: pydantic_models.UserUpdate) -> Optional[db_models.User]:
    """更新用户信息"""
    db_user = get_user(db, user_id)
    if not db_user:
        return None

    update_data = user_update.dict(exclude_unset=True)
    
    # 如果更新中包含密码，需要哈希处理
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data.pop("password"))
    
    for field, value in update_data.items():
        setattr(db_user, field, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user


def authenticate_user(db: Session, email: str, password: str) -> Optional[db_models.User]:
    """验证用户身份"""
    user = get_user_by_email(db, email=email)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user


def delete_user(db: Session, user_id: str) -> bool:
    """删除用户"""
    db_user = get_user(db, user_id)
    if not db_user:
        return False
    
    db.delete(db_user)
    db.commit()
    return True 