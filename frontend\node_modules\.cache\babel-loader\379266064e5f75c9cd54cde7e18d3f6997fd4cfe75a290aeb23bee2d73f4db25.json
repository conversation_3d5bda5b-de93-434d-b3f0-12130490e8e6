{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\ChatMessage.js\";\n// frontend/src/components/ChatMessage.js\nimport React from 'react';\nimport { FaUser, FaRobot } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatMessage = ({\n  message\n}) => {\n  // 从 message 对象中解构出需要的字段\n  const {\n    sender,\n    text,\n    icon,\n    is_streaming,\n    thinking,\n    files\n  } = message;\n\n  // 根据发送者确定消息样式\n  const isUser = sender === 'user';\n  const isSystem = sender === 'system';\n  const isThinking = thinking === true;\n\n  // 思考消息样式（特殊的思考状态显示）\n  if (isThinking) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-start mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0 h-8 w-8 rounded-full bg-purple-500 flex items-center justify-center text-white mr-2\",\n        children: \"\\uD83E\\uDDE0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-purple-50 border border-purple-200 text-purple-800 rounded-lg px-4 py-2 max-w-[80%] text-sm italic\",\n        children: [icon && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mr-2\",\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 20\n        }, this), text]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 系统消息样式（警告、通知等）\n  if (isSystem) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center my-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-100 text-gray-700 rounded-lg px-4 py-2 max-w-[80%] text-sm\",\n        children: [icon && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mr-2\",\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 20\n        }, this), text]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`,\n    children: [!isUser && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-shrink-0 h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white mr-2\",\n      children: /*#__PURE__*/_jsxDEV(FaRobot, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `px-4 py-2 rounded-lg max-w-[80%] ${isUser ? 'bg-tiktodo-blue text-white' : 'bg-white border border-gray-200 text-gray-800'}`,\n      children: [files && files.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3 space-y-2\",\n        children: files.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center p-2 bg-gray-50 rounded border text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 mr-2\",\n            children: [file.category === 'image' && '🖼️', file.category === 'audio' && '🎵', file.category === 'video' && '🎬', file.category === 'document' && '📄']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium text-gray-900 truncate\",\n              children: file.filename\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: [Math.round(file.size / 1024), \" KB \\u2022 \", file.category]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this), text && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"whitespace-pre-wrap\",\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this), is_streaming && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-block w-2 h-4 bg-blue-400 animate-pulse ml-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), isUser && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-shrink-0 h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center text-white ml-2\",\n      children: /*#__PURE__*/_jsxDEV(FaUser, {\n        size: 14\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_c = ChatMessage;\nexport default ChatMessage;\nvar _c;\n$RefreshReg$(_c, \"ChatMessage\");", "map": {"version": 3, "names": ["React", "FaUser", "FaRobot", "jsxDEV", "_jsxDEV", "ChatMessage", "message", "sender", "text", "icon", "is_streaming", "thinking", "files", "isUser", "isSystem", "isThinking", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "length", "map", "file", "index", "category", "filename", "Math", "round", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/ChatMessage.js"], "sourcesContent": ["// frontend/src/components/ChatMessage.js\nimport React from 'react';\nimport { FaUser, FaRobot } from 'react-icons/fa';\n\nconst ChatMessage = ({ message }) => {\n  // 从 message 对象中解构出需要的字段\n  const { sender, text, icon, is_streaming, thinking, files } = message;\n  \n  // 根据发送者确定消息样式\n  const isUser = sender === 'user';\n  const isSystem = sender === 'system';\n  const isThinking = thinking === true;\n  \n  // 思考消息样式（特殊的思考状态显示）\n  if (isThinking) {\n    return (\n      <div className=\"flex justify-start mb-2\">\n        <div className=\"flex-shrink-0 h-8 w-8 rounded-full bg-purple-500 flex items-center justify-center text-white mr-2\">\n          🧠\n        </div>\n        <div className=\"bg-purple-50 border border-purple-200 text-purple-800 rounded-lg px-4 py-2 max-w-[80%] text-sm italic\">\n          {icon && <span className=\"mr-2\">{icon}</span>}\n          {text}\n        </div>\n      </div>\n    );\n  }\n\n  // 系统消息样式（警告、通知等）\n  if (isSystem) {\n    return (\n      <div className=\"flex justify-center my-4\">\n        <div className=\"bg-gray-100 text-gray-700 rounded-lg px-4 py-2 max-w-[80%] text-sm\">\n          {icon && <span className=\"mr-2\">{icon}</span>}\n          {text}\n        </div>\n      </div>\n    );\n  }\n\n    return (\n    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>\n      {/* AI头像 */}\n      {!isUser && (\n        <div className=\"flex-shrink-0 h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white mr-2\">\n          <FaRobot size={16} />\n        </div>\n      )}\n      \n      {/* 消息内容 */}\n      <div \n        className={`px-4 py-2 rounded-lg max-w-[80%] ${\n          isUser \n            ? 'bg-tiktodo-blue text-white' \n            : 'bg-white border border-gray-200 text-gray-800'\n        }`} \n      >\n        {/* 文件列表 */}\n        {files && files.length > 0 && (\n          <div className=\"mb-3 space-y-2\">\n            {files.map((file, index) => (\n              <div key={index} className=\"flex items-center p-2 bg-gray-50 rounded border text-sm\">\n                <div className=\"flex-shrink-0 mr-2\">\n                  {file.category === 'image' && '🖼️'}\n                  {file.category === 'audio' && '🎵'}\n                  {file.category === 'video' && '🎬'}\n                  {file.category === 'document' && '📄'}\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"font-medium text-gray-900 truncate\">\n                    {file.filename}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    {Math.round(file.size / 1024)} KB • {file.category}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n        \n        {/* 只显示文本内容，保持简洁 */}\n        {text && (\n          <div className=\"whitespace-pre-wrap\">\n            {text}\n          </div>\n        )}\n        \n        {/* 流式输出指示器 */}\n        {is_streaming && (\n          <div className=\"inline-block w-2 h-4 bg-blue-400 animate-pulse ml-1\"></div>\n        )}\n      </div>\n      \n      {/* 用户头像 */}\n      {isUser && (\n        <div className=\"flex-shrink-0 h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center text-white ml-2\">\n          <FaUser size={14} />\n        </div>\n      )}\n      </div>\n    );\n};\n\nexport default ChatMessage; "], "mappings": ";AAAA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EACnC;EACA,MAAM;IAAEC,MAAM;IAAEC,IAAI;IAAEC,IAAI;IAAEC,YAAY;IAAEC,QAAQ;IAAEC;EAAM,CAAC,GAAGN,OAAO;;EAErE;EACA,MAAMO,MAAM,GAAGN,MAAM,KAAK,MAAM;EAChC,MAAMO,QAAQ,GAAGP,MAAM,KAAK,QAAQ;EACpC,MAAMQ,UAAU,GAAGJ,QAAQ,KAAK,IAAI;;EAEpC;EACA,IAAII,UAAU,EAAE;IACd,oBACEX,OAAA;MAAKY,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCb,OAAA;QAAKY,SAAS,EAAC,mGAAmG;QAAAC,QAAA,EAAC;MAEnH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNjB,OAAA;QAAKY,SAAS,EAAC,uGAAuG;QAAAC,QAAA,GACnHR,IAAI,iBAAIL,OAAA;UAAMY,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAER;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC5Cb,IAAI;MAAA;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIP,QAAQ,EAAE;IACZ,oBACEV,OAAA;MAAKY,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvCb,OAAA;QAAKY,SAAS,EAAC,oEAAoE;QAAAC,QAAA,GAChFR,IAAI,iBAAIL,OAAA;UAAMY,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAER;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAC5Cb,IAAI;MAAA;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEE,oBACAjB,OAAA;IAAKY,SAAS,EAAE,QAAQH,MAAM,GAAG,aAAa,GAAG,eAAe,OAAQ;IAAAI,QAAA,GAErE,CAACJ,MAAM,iBACNT,OAAA;MAAKY,SAAS,EAAC,iGAAiG;MAAAC,QAAA,eAC9Gb,OAAA,CAACF,OAAO;QAACoB,IAAI,EAAE;MAAG;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CACN,eAGDjB,OAAA;MACEY,SAAS,EAAE,oCACTH,MAAM,GACF,4BAA4B,GAC5B,+CAA+C,EAClD;MAAAI,QAAA,GAGFL,KAAK,IAAIA,KAAK,CAACW,MAAM,GAAG,CAAC,iBACxBnB,OAAA;QAAKY,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BL,KAAK,CAACY,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBtB,OAAA;UAAiBY,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBAClFb,OAAA;YAAKY,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAChCQ,IAAI,CAACE,QAAQ,KAAK,OAAO,IAAI,KAAK,EAClCF,IAAI,CAACE,QAAQ,KAAK,OAAO,IAAI,IAAI,EACjCF,IAAI,CAACE,QAAQ,KAAK,OAAO,IAAI,IAAI,EACjCF,IAAI,CAACE,QAAQ,KAAK,UAAU,IAAI,IAAI;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACNjB,OAAA;YAAKY,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7Bb,OAAA;cAAKY,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAChDQ,IAAI,CAACG;YAAQ;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACNjB,OAAA;cAAKY,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GACnCY,IAAI,CAACC,KAAK,CAACL,IAAI,CAACH,IAAI,GAAG,IAAI,CAAC,EAAC,aAAM,EAACG,IAAI,CAACE,QAAQ;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAdEK,KAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAb,IAAI,iBACHJ,OAAA;QAAKY,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EACjCT;MAAI;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,EAGAX,YAAY,iBACXN,OAAA;QAAKY,SAAS,EAAC;MAAqD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAC3E;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLR,MAAM,iBACLT,OAAA;MAAKY,SAAS,EAAC,iGAAiG;MAAAC,QAAA,eAC9Gb,OAAA,CAACH,MAAM;QAACqB,IAAI,EAAE;MAAG;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEZ,CAAC;AAACU,EAAA,GAlGI1B,WAAW;AAoGjB,eAAeA,WAAW;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}