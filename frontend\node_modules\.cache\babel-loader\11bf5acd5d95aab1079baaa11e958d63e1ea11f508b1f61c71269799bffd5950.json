{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\SlideThumbnails.js\";\nimport React from 'react';\nimport SlideRenderer from './SlideRenderer'; // 确保路径正确\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SlideThumbnails = ({\n  slides,\n  currentSlideIndex,\n  onThumbnailClick\n}) => {\n  const THUMBNAIL_TARGET_WIDTH = 213; // 缩略图容器的宽度\n  const THUMBNAIL_TARGET_HEIGHT = 120; // 缩略图容器的高度 (16:9 比例)\n\n  if (!slides || slides.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-full flex items-center justify-center text-xs text-gray-400\",\n      children: \"\\u6CA1\\u6709\\u7F29\\u7565\\u56FE\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-full rounded-lg bg-gray-100 border border-gray-300 shadow-inner\",\n    style: {\n      overflow: 'hidden',\n      // 防止内部滚动容器溢出\n      display: 'flex',\n      alignItems: 'center' // 垂直居中滚动容器，如果它更小\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex overflow-x-auto py-2 px-2 space-x-3 custom-scrollbar\",\n      style: {\n        width: '100%' /* 占据全部宽度 */\n      },\n      children: [slides.map((slide, index) => {\n        const slideId = slide.id || `thumbnail-item-${index}`; // 获取幻灯片ID或生成一个\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `thumbnail-item-wrapper flex-shrink-0 cursor-pointer rounded-md overflow-hidden shadow-sm transition-all duration-200 border-2\n                        ${index === currentSlideIndex ? 'border-blue-500 ring-1 ring-blue-400' : 'border-gray-300 hover:border-gray-400'}`,\n          style: {\n            width: `${THUMBNAIL_TARGET_WIDTH}px`,\n            height: `${THUMBNAIL_TARGET_HEIGHT}px`,\n            backgroundColor: '#fff',\n            // 为透明幻灯片提供一个背景颜色\n            position: 'relative' // 用于幻灯片编号的定位\n          },\n          onClick: () => onThumbnailClick(index) // 点击缩略图时设置当前幻灯片\n          ,\n          title: `显示幻灯片 ${index + 1}` // 鼠标悬停提示\n          ,\n          children: [/*#__PURE__*/_jsxDEV(SlideRenderer, {\n            slideId: `thumb-renderer-${slide.id || index}` // 为缩略图渲染器提供唯一ID\n            ,\n            slideFullHtml: slide.html // 幻灯片的完整 HTML 内容\n            ,\n            isThumbnail: true // 标记为缩略图视图\n            // isAppEditingMode 对于缩略图不相关，因此始终为 false\n            ,\n            isAppEditingMode: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white text-xs py-0.5 px-1 text-center z-10\",\n            style: {\n              pointerEvents: 'none'\n            } // 防止数字捕获点击事件，使其可穿透\n            ,\n            children: [index + 1, \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this)]\n        }, slideId, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 13\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flexShrink: 0,\n          width: '1px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c = SlideThumbnails;\nexport default SlideThumbnails;\nvar _c;\n$RefreshReg$(_c, \"SlideThumbnails\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "SlideThumbnails", "slides", "currentSlideIndex", "onThumbnailClick", "THUMBNAIL_TARGET_WIDTH", "THUMBNAIL_TARGET_HEIGHT", "length", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "overflow", "display", "alignItems", "width", "map", "slide", "index", "slideId", "id", "height", "backgroundColor", "position", "onClick", "title", "slideFullHtml", "html", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isAppEditingMode", "pointerEvents", "flexShrink", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/SlideThumbnails.js"], "sourcesContent": ["import React from 'react';\nimport Slide<PERSON>enderer from './SlideRenderer'; // 确保路径正确\n\nconst SlideThumbnails = ({\n  slides,\n  currentSlideIndex,\n  onThumbnailClick\n}) => {\n  const THUMBNAIL_TARGET_WIDTH = 213;  // 缩略图容器的宽度\n  const THUMBNAIL_TARGET_HEIGHT = 120; // 缩略图容器的高度 (16:9 比例)\n\n  if (!slides || slides.length === 0) {\n    return (\n      <div className=\"h-full flex items-center justify-center text-xs text-gray-400\">\n        没有缩略图。\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className=\"h-full rounded-lg bg-gray-100 border border-gray-300 shadow-inner\"\n      style={{\n        overflow: 'hidden', // 防止内部滚动容器溢出\n        display: 'flex',\n        alignItems: 'center', // 垂直居中滚动容器，如果它更小\n      }}\n    >\n      <div\n        className=\"flex overflow-x-auto py-2 px-2 space-x-3 custom-scrollbar\"\n        style={{ width: '100%' /* 占据全部宽度 */ }}\n      >\n        {slides.map((slide, index) => {\n          const slideId = slide.id || `thumbnail-item-${index}`; // 获取幻灯片ID或生成一个\n          return (\n            <div\n              key={slideId}\n              className={`thumbnail-item-wrapper flex-shrink-0 cursor-pointer rounded-md overflow-hidden shadow-sm transition-all duration-200 border-2\n                        ${index === currentSlideIndex ? 'border-blue-500 ring-1 ring-blue-400' : 'border-gray-300 hover:border-gray-400'}`}\n              style={{\n                width: `${THUMBNAIL_TARGET_WIDTH}px`,\n                height: `${THUMBNAIL_TARGET_HEIGHT}px`,\n                backgroundColor: '#fff', // 为透明幻灯片提供一个背景颜色\n                position: 'relative',    // 用于幻灯片编号的定位\n              }}\n              onClick={() => onThumbnailClick(index)} // 点击缩略图时设置当前幻灯片\n              title={`显示幻灯片 ${index + 1}`} // 鼠标悬停提示\n            >\n              {/* SlideRenderer 现在会适应这个包装器的大小 */}\n              <SlideRenderer\n                slideId={`thumb-renderer-${slide.id || index}`} // 为缩略图渲染器提供唯一ID\n                slideFullHtml={slide.html} // 幻灯片的完整 HTML 内容\n                isThumbnail={true} // 标记为缩略图视图\n                // isAppEditingMode 对于缩略图不相关，因此始终为 false\n                isAppEditingMode={false}\n              />\n              <div\n                className=\"absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 text-white text-xs py-0.5 px-1 text-center z-10\"\n                style={{ pointerEvents: 'none' }} // 防止数字捕获点击事件，使其可穿透\n              >\n                {index + 1} {/* 显示幻灯片编号 */}\n              </div>\n            </div>\n          );\n        })}\n        <div style={{ flexShrink: 0, width: '1px' }}></div> {/* 结尾的小缓冲区，防止最后一个缩略图贴边 */}\n      </div>\n    </div>\n  );\n};\n\nexport default SlideThumbnails;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,iBAAiB,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,eAAe,GAAGA,CAAC;EACvBC,MAAM;EACNC,iBAAiB;EACjBC;AACF,CAAC,KAAK;EACJ,MAAMC,sBAAsB,GAAG,GAAG,CAAC,CAAE;EACrC,MAAMC,uBAAuB,GAAG,GAAG,CAAC,CAAC;;EAErC,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAACK,MAAM,KAAK,CAAC,EAAE;IAClC,oBACEP,OAAA;MAAKQ,SAAS,EAAC,+DAA+D;MAAAC,QAAA,EAAC;IAE/E;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACEb,OAAA;IACEQ,SAAS,EAAC,mEAAmE;IAC7EM,KAAK,EAAE;MACLC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ,CAAE;IACxB,CAAE;IAAAR,QAAA,eAEFT,OAAA;MACEQ,SAAS,EAAC,2DAA2D;MACrEM,KAAK,EAAE;QAAEI,KAAK,EAAE,MAAM,CAAC;MAAa,CAAE;MAAAT,QAAA,GAErCP,MAAM,CAACiB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAC5B,MAAMC,OAAO,GAAGF,KAAK,CAACG,EAAE,IAAI,kBAAkBF,KAAK,EAAE,CAAC,CAAC;QACvD,oBACErB,OAAA;UAEEQ,SAAS,EAAE;AACzB,0BAA0Ba,KAAK,KAAKlB,iBAAiB,GAAG,sCAAsC,GAAG,uCAAuC,EAAG;UAC7HW,KAAK,EAAE;YACLI,KAAK,EAAE,GAAGb,sBAAsB,IAAI;YACpCmB,MAAM,EAAE,GAAGlB,uBAAuB,IAAI;YACtCmB,eAAe,EAAE,MAAM;YAAE;YACzBC,QAAQ,EAAE,UAAU,CAAK;UAC3B,CAAE;UACFC,OAAO,EAAEA,CAAA,KAAMvB,gBAAgB,CAACiB,KAAK,CAAE,CAAC;UAAA;UACxCO,KAAK,EAAE,SAASP,KAAK,GAAG,CAAC,EAAG,CAAC;UAAA;UAAAZ,QAAA,gBAG7BT,OAAA,CAACF,aAAa;YACZwB,OAAO,EAAE,kBAAkBF,KAAK,CAACG,EAAE,IAAIF,KAAK,EAAG,CAAC;YAAA;YAChDQ,aAAa,EAAET,KAAK,CAACU,IAAK,CAAC;YAAA;YAC3BC,WAAW,EAAE,IAAK,CAAC;YACnB;YAAA;YACAC,gBAAgB,EAAE;UAAM;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACFb,OAAA;YACEQ,SAAS,EAAC,yGAAyG;YACnHM,KAAK,EAAE;cAAEmB,aAAa,EAAE;YAAO,CAAE,CAAC;YAAA;YAAAxB,QAAA,GAEjCY,KAAK,GAAG,CAAC,EAAC,GAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,GAzBDS,OAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0BT,CAAC;MAEV,CAAC,CAAC,eACFb,OAAA;QAAKc,KAAK,EAAE;UAAEoB,UAAU,EAAE,CAAC;UAAEhB,KAAK,EAAE;QAAM;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,KAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACsB,EAAA,GAlEIlC,eAAe;AAoErB,eAAeA,eAAe;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}