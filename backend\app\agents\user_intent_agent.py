# backend/app/agents/user_intent_agent.py
import logging
from typing import Optional, Dict, Any, Tuple 

from app.agents.base_agent import BaseAgent
from app.models.presentation_model import UserIntentSchema
from app.core.config import settings
from app.services.prompt_manager import format_prompt_section

logger = logging.getLogger(__name__)

class UserIntentAgent(BaseAgent):
    def __init__(self, model_name_override: Optional[str] = None):
        super().__init__(
            agent_name="UserIntentAgent", 
            system_prompt_name="system",
            agent_prompt_subdir="user_intent",
            model_name_override=model_name_override
        ) # 【删除】 system_prompt_version="v1"
        logger.info(f"UserIntentAgent initialized using {self.model_name_description} and PromptManager for prompts.")

    async def process(
        self,
        user_query: str,
        project_id: str
    ) -> Tuple[Optional[UserIntentSchema], Optional[str]]: 

        context_name = f"IntentAnalysis_Proj_{project_id}"
        logger.info(f"[{self.agent_name}][{context_name}] Processing user query with Instructor...")

        # 准备 prompt 参数
        prompt_args = {
            "user_query": user_query,
            "min_slides": settings.MIN_SLIDES_PER_PRESENTATION,
            "default_min_slides": settings.DEFAULT_SLIDES_PER_PRESENTATION - 2,
            "default_max_slides": settings.DEFAULT_SLIDES_PER_PRESENTATION + 2,
            "max_slides": settings.MAX_SLIDES_PER_PRESENTATION
        }

        # 格式化 prompt
        prompt = format_prompt_section(
            agent_name="user_intent_agent",
            section_name="task:main",
            **prompt_args
        )
        
        if not prompt:
            logger.error(f"[{self.agent_name}][{context_name}] Failed to format prompt.")
            return None, None
        
        # 调用 LLM
        response_model, llm_log_id = await self._call_llm_with_instructor(
            prompt=prompt,
            pydantic_schema=UserIntentSchema,
            project_id_for_logging=project_id
        )

        if not response_model:
            logger.error(f"[{self.agent_name}][{context_name}] Failed to get valid UserIntentSchema.")
            return None, llm_log_id
        
        # 这里可以继续执行您的业务逻辑验证
        response_model.suggested_slide_count = max(
            settings.MIN_SLIDES_PER_PRESENTATION,
            min(response_model.suggested_slide_count, settings.MAX_SLIDES_PER_PRESENTATION)
        )

        logger.info(f"[{self.agent_name}][{context_name}] Successfully parsed user intent with Instructor. Topic: '{response_model.topic}', Slides: {response_model.suggested_slide_count}, Lang: {response_model.detected_language}")
        return response_model, llm_log_id