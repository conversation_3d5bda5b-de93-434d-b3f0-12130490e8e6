from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from datetime import datetime

class FeedbackBase(BaseModel):
    """
    Base model for feedback with common fields.
    """
    rating_type: str = Field(
        default="user_overall_rating_slide", 
        description="Type of rating, e.g., 'user_visual_rating', 'system_html_score'"
    )
    rating_value: Optional[float] = Field(
        None, 
        description="User rating (e.g., 1-5) or system score (e.g., 0-10)"
    )
    comment: Optional[str] = Field(
        None, 
        description="User or system comments regarding the feedback"
    )
    feedback_source: str = Field(
        default="user", 
        description="Source of the feedback, e.g., 'user', 'system_html_checker'"
    )

class FeedbackCreate(FeedbackBase):
    """
    Model for creating new feedback entries.
    llm_interaction_log_id and user_id are handled at the service/crud layer.
    """
    pass

class FeedbackInDB(FeedbackBase):
    """
    Model for feedback entries as stored in the database.
    """
    id: str
    llm_interaction_log_id: str
    user_id: Optional[str] = None
    timestamp: datetime

    model_config = ConfigDict(from_attributes=True) 