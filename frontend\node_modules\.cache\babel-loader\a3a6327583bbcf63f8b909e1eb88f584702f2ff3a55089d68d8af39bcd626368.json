{"ast": null, "code": "// frontend/src/config.js\n// 应用程序配置\n\n// 获取环境变量或使用默认值\nconst getEnvValue = (key, defaultValue) => {\n  const value = process.env[`REACT_APP_${key}`];\n  return value !== undefined ? value : defaultValue;\n};\n\n// 检测后端API地址\nconst determineApiUrl = () => {\n  // 首先尝试从环境变量获取\n  const envBaseUrl = getEnvValue('API_BASE_URL', '');\n  if (envBaseUrl) return envBaseUrl;\n\n  // 如果环境变量未设置，则使用基于当前域名的推断\n  const protocol = window.location.protocol;\n  const hostname = window.location.hostname;\n\n  // 在开发环境中使用固定地址\n  if (process.env.NODE_ENV === 'development') {\n    console.log('[Config] 开发环境，使用固定后端地址: http://localhost:8000');\n    return 'http://localhost:8000';\n  }\n\n  // 在生产环境中尝试使用相同域名但不同端口\n  console.log(`[Config] 生产环境，使用同域API地址: ${protocol}//${hostname}:8000`);\n  return `${protocol}//${hostname}:8000`;\n};\nconst API_BASE_URL = determineApiUrl();\nconst config = {\n  api: {\n    baseUrl: `${API_BASE_URL}/api/v1`,\n    rootUrl: API_BASE_URL\n  },\n  app: {\n    name: 'TikTodo AI Slides',\n    version: '0.2.0'\n  },\n  debug: process.env.NODE_ENV === 'development',\n  // Feature flags\n  features: {\n    useStreamingApi: true,\n    // Whether to use the streaming API or the regular API\n    enableImageGeneration: false // Whether to enable image generation\n  }\n};\n\n// 在开发环境中记录配置\nif (config.debug) {\n  console.log('[Config] 应用配置:', config);\n}\nexport default config;", "map": {"version": 3, "names": ["getEnvValue", "key", "defaultValue", "value", "process", "env", "undefined", "determineApiUrl", "envBaseUrl", "protocol", "window", "location", "hostname", "NODE_ENV", "console", "log", "API_BASE_URL", "config", "api", "baseUrl", "rootUrl", "app", "name", "version", "debug", "features", "useStreamingApi", "enableImageGeneration"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/config.js"], "sourcesContent": ["// frontend/src/config.js\r\n// 应用程序配置\r\n\r\n// 获取环境变量或使用默认值\r\nconst getEnvValue = (key, defaultValue) => {\r\n  const value = process.env[`REACT_APP_${key}`];\r\n  return value !== undefined ? value : defaultValue;\r\n};\r\n\r\n// 检测后端API地址\r\nconst determineApiUrl = () => {\r\n  // 首先尝试从环境变量获取\r\n  const envBaseUrl = getEnvValue('API_BASE_URL', '');\r\n  if (envBaseUrl) return envBaseUrl;\r\n\r\n  // 如果环境变量未设置，则使用基于当前域名的推断\r\n  const protocol = window.location.protocol;\r\n  const hostname = window.location.hostname;\r\n  \r\n  // 在开发环境中使用固定地址\r\n  if (process.env.NODE_ENV === 'development') {\r\n    console.log('[Config] 开发环境，使用固定后端地址: http://localhost:8000');\r\n    return 'http://localhost:8000';\r\n  }\r\n  \r\n  // 在生产环境中尝试使用相同域名但不同端口\r\n  console.log(`[Config] 生产环境，使用同域API地址: ${protocol}//${hostname}:8000`);\r\n  return `${protocol}//${hostname}:8000`;\r\n};\r\n\r\nconst API_BASE_URL = determineApiUrl();\r\n\r\nconst config = {\r\n  api: {\r\n    baseUrl: `${API_BASE_URL}/api/v1`,\r\n    rootUrl: API_BASE_URL,\r\n  },\r\n  app: {\r\n    name: 'TikTodo AI Slides',\r\n    version: '0.2.0',\r\n  },\r\n  debug: process.env.NODE_ENV === 'development',\r\n  \r\n  // Feature flags\r\n  features: {\r\n    useStreamingApi: true, // Whether to use the streaming API or the regular API\r\n    enableImageGeneration: false, // Whether to enable image generation\r\n  }\r\n};\r\n\r\n// 在开发环境中记录配置\r\nif (config.debug) {\r\n  console.log('[Config] 应用配置:', config);\r\n}\r\n\r\nexport default config; "], "mappings": "AAAA;AACA;;AAEA;AACA,MAAMA,WAAW,GAAGA,CAACC,GAAG,EAAEC,YAAY,KAAK;EACzC,MAAMC,KAAK,GAAGC,OAAO,CAACC,GAAG,CAAC,aAAaJ,GAAG,EAAE,CAAC;EAC7C,OAAOE,KAAK,KAAKG,SAAS,GAAGH,KAAK,GAAGD,YAAY;AACnD,CAAC;;AAED;AACA,MAAMK,eAAe,GAAGA,CAAA,KAAM;EAC5B;EACA,MAAMC,UAAU,GAAGR,WAAW,CAAC,cAAc,EAAE,EAAE,CAAC;EAClD,IAAIQ,UAAU,EAAE,OAAOA,UAAU;;EAEjC;EACA,MAAMC,QAAQ,GAAGC,MAAM,CAACC,QAAQ,CAACF,QAAQ;EACzC,MAAMG,QAAQ,GAAGF,MAAM,CAACC,QAAQ,CAACC,QAAQ;;EAEzC;EACA,IAAIR,OAAO,CAACC,GAAG,CAACQ,QAAQ,KAAK,aAAa,EAAE;IAC1CC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC5D,OAAO,uBAAuB;EAChC;;EAEA;EACAD,OAAO,CAACC,GAAG,CAAC,4BAA4BN,QAAQ,KAAKG,QAAQ,OAAO,CAAC;EACrE,OAAO,GAAGH,QAAQ,KAAKG,QAAQ,OAAO;AACxC,CAAC;AAED,MAAMI,YAAY,GAAGT,eAAe,CAAC,CAAC;AAEtC,MAAMU,MAAM,GAAG;EACbC,GAAG,EAAE;IACHC,OAAO,EAAE,GAAGH,YAAY,SAAS;IACjCI,OAAO,EAAEJ;EACX,CAAC;EACDK,GAAG,EAAE;IACHC,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE;EACX,CAAC;EACDC,KAAK,EAAEpB,OAAO,CAACC,GAAG,CAACQ,QAAQ,KAAK,aAAa;EAE7C;EACAY,QAAQ,EAAE;IACRC,eAAe,EAAE,IAAI;IAAE;IACvBC,qBAAqB,EAAE,KAAK,CAAE;EAChC;AACF,CAAC;;AAED;AACA,IAAIV,MAAM,CAACO,KAAK,EAAE;EAChBV,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEE,MAAM,CAAC;AACvC;AAEA,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}