# backend/app/agents/base_agent.py
import json
import re
import time
import asyncio
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, Any, Tuple, Union, Type, List, cast

# OLD: from google.ai import generativelanguage_v1beta as glm
# OLD: from google.auth import credentials as ga_credentials

# ====== NEW IMPORTS for google-genai ======
import google.genai as genai  # 使用 genai 作为别名
from google.genai import types  # 用于 GenerateContentConfig, ThinkingConfig, HarmCategory, HarmBlockThreshold
# ==========================================

from pydantic import BaseModel, ValidationError, parse_obj_as, create_model, Field
import instructor
# OLD: from tenacity import Retrying, stop_after_attempt, wait_exponential, retry_if_exception_type
from google.api_core import exceptions as google_exceptions

from app.core.config import settings
from app.db.session import SessionLocal
from app.crud.crud_llm_interaction_log import create_llm_interaction_log, update_llm_interaction_log
from app.services.prompt_manager import get_prompt as pm_get_prompt
from app.services.prompt_manager import get_prompt_section, format_prompt_section
from app.utils.api_key_manager import key_manager # 导入我们的新Key管理器单例
from app.utils.llm_communication_logger import llm_comm_logger # 导入LLM通信日志记录器

logger = logging.getLogger(__name__)

class BaseAgent:
    LLM_CALL_BASE_TIMEOUT_SECONDS = settings.GEMINI_DEFAULT_TIMEOUT
    MAX_LLM_CALL_RETRIES = 2
    MIN_VALID_JSON_LENGTH = 10
    
    def __init__(
        self,
        agent_name: str = "BaseAgent",
        system_prompt_name: Optional[str] = None,
        agent_prompt_subdir: Optional[str] = None, 
        principled_prompt_name: str = "principled_prompt",
        model_name_override: Optional[str] = None,
        requires_llm: bool = True
    ):
        """
        初始化Agent基类。
        
        Args:
            agent_name: Agent的名称，用于日志和提示词加载
            system_prompt_name: 系统提示的名称，默认为None
            agent_prompt_subdir: Agent提示词的子目录，默认为None
            principled_prompt_name: 原则提示词的名称，默认为"principled_prompt"
            model_name_override: 可选的模型名称覆盖，用于日志记录
            requires_llm: 是否需要LLM模型，默认为True
        """
        self.agent_name = agent_name
        self.system_prompt_name = system_prompt_name
        self.agent_prompt_subdir = agent_prompt_subdir or agent_name
        self.principled_prompt_name = principled_prompt_name
        self.model_name_description = model_name_override or settings.GEMINI_MODEL_NAME
        self.requires_llm = requires_llm
        
        # 加载系统提示和原则提示
        self._system_prompt = None
        
        if self.system_prompt_name:
            # 统一的 agent prompt 文件名，例如 "user_intent_agent_prompt.md"
            unified_agent_prompt_filename = f"{self.agent_prompt_subdir}_agent"
            # 要加载的部分，例如 "system"
            self._system_prompt = get_prompt_section(
                agent_name=unified_agent_prompt_filename,
                section_name=self.system_prompt_name
            )

    def _build_full_prompt(self, task_prompt_content: str, use_principled_prompt: bool = True) -> str:
        parts = []
        if self._system_prompt:
            parts.append(self._system_prompt)
        parts.append(task_prompt_content)
        return "\n\n".join(parts)

    async def _call_gemini_api(
        self,
        prompt_content: str,
        temperature: float,
        expected_response_mime_type: Optional[str] = None,
        expected_pydantic_schema_for_api: Optional[Type[BaseModel]] = None, # Not directly used in API call config, but for logging
        llm_interaction_log_id: Optional[str] = None,
        project_id_for_logging: Optional[str] = None,  # 新增项目ID参数用于日志记录
        call_context_name: Optional[str] = None  # 新增调用上下文参数
    ) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
        start_time = time.perf_counter()
        response_text: Optional[str] = None
        raw_response_or_error: Optional[Dict[str, Any]] = None
        db_for_log_update = SessionLocal()
        
        # 记录LLM请求到项目日志文件
        llm_comm_logger.log_llm_request(
            project_id=project_id_for_logging,
            agent_name=self.agent_name,
            prompt_content=prompt_content,
            temperature=temperature,
            model_name=self.model_name_description,
            expected_response_type=expected_pydantic_schema_for_api.__name__ if expected_pydantic_schema_for_api else expected_response_mime_type,
            context=call_context_name
        )
        
        try:
            # 1. 从管理器获取一个可用的API Key
            api_key = await key_manager.get_next_key_async()
            
            # 2. 使用最新的google-genai Client API
            client = genai.Client(api_key=api_key)
            
            # 3. 配置安全设置（使用最新的types）
            safety_settings = [
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HATE_SPEECH, 
                    threshold=types.HarmBlockThreshold.BLOCK_NONE
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, 
                    threshold=types.HarmBlockThreshold.BLOCK_NONE
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, 
                    threshold=types.HarmBlockThreshold.BLOCK_NONE
                ),
                types.SafetySetting(
                    category=types.HarmCategory.HARM_CATEGORY_HARASSMENT, 
                    threshold=types.HarmBlockThreshold.BLOCK_NONE
                )
            ]

            # 4. 构建生成配置
            config_kwargs = {
                "temperature": temperature,
                "max_output_tokens": 65536,
                "safety_settings": safety_settings
            }

            # 5. 处理结构化输出（根据您的建议进行修改）
            # --- START OF MODIFICATION ---
            # 始终启用思考配置，这对复杂任务（包括结构化输出）的质量至关重要
            config_kwargs["thinking_config"] = types.ThinkingConfig(
                include_thoughts=True,
                thinking_budget=24576
            )
            
            # 如果期望结构化输出，额外设置响应MIME类型
            if expected_pydantic_schema_for_api:
                # 即使启用了思考模式，设置此MIME类型也能强烈指示模型输出JSON
                config_kwargs["response_mime_type"] = "application/json"
                logger.debug(f"Agent '{self.agent_name}': 使用结构化输出（JSON），模式: {expected_pydantic_schema_for_api.__name__}")
            # --- END OF MODIFICATION ---
            
            gen_config = types.GenerateContentConfig(**config_kwargs)
            
            logger.debug(f"Agent '{self.agent_name}': 发送提示词 (前300字符): {prompt_content[:300]}...")
            
            # 6. 调用API（使用异步方法）
            api_response = await client.aio.models.generate_content(
                model=self.model_name_description,
                contents=[prompt_content],
                config=gen_config
            )
            
            duration_ms = int((time.perf_counter() - start_time) * 1000)

            # 7. 处理响应
            thinking_summary = ""
            if api_response.prompt_feedback and api_response.prompt_feedback.block_reason:
                error_msg = f"安全过滤器触发: {api_response.prompt_feedback.block_reason}"
                response_text = error_msg
                raw_response_or_error = {
                    "error": "safety_filter", 
                    "reason": str(api_response.prompt_feedback.block_reason), 
                    "message": error_msg, 
                    "type": "SafetyFilterError"
                }
            elif not api_response.candidates:
                error_msg = "API调用未返回候选响应"
                response_text = error_msg
                raw_response_or_error = {
                    "error": "no_candidates", 
                    "message": error_msg, 
                    "type": "LLMError"
                }
            else:
                # 提取响应内容
                candidate = api_response.candidates[0]
                response_content = ""
                
                if candidate.content and candidate.content.parts:
                    for part in candidate.content.parts:
                        if hasattr(part, 'text') and part.text:
                            if hasattr(part, 'thought') and part.thought:
                                thinking_summary += part.text
                            else:
                                response_content += part.text
                    
                    # 根据是否期望结构化输出来处理响应
                    if expected_pydantic_schema_for_api:
                        response_text = response_content.strip()
                    else:
                        if thinking_summary.strip():
                            if response_content.strip():
                                response_text = f"思考过程:\n{thinking_summary.strip()}\n\n回答:\n{response_content.strip()}"
                            else:
                                response_text = thinking_summary.strip()
                        else:
                            response_text = response_content.strip() if response_content.strip() else None
                
                # 备用处理
                if not response_text and hasattr(api_response, 'text') and api_response.text:
                    response_text = api_response.text.strip()

                if not response_text:
                    error_msg = "API响应为空"
                    response_text = error_msg
                    raw_response_or_error = {
                        "error": "empty_response", 
                        "message": error_msg, 
                        "type": "LLMError"
                    }
                else:
                    raw_response_or_error = {
                        "success": True,
                        "thinking_summary_length": len(thinking_summary) if thinking_summary else 0
                    }
            
            # 记录LLM响应到项目日志文件
            llm_comm_logger.log_llm_response(
                project_id=project_id_for_logging,
                agent_name=self.agent_name,
                response_text=response_text,
                raw_response=raw_response_or_error,
                duration_ms=duration_ms,
                success=raw_response_or_error.get("success", False) if raw_response_or_error else False,
                error_message=raw_response_or_error.get("message") if raw_response_or_error and "error" in raw_response_or_error else None,
                thinking_summary=thinking_summary if thinking_summary.strip() else None,
                context=call_context_name
            )

            # 8. 更新日志
            if llm_interaction_log_id:
                log_update_data = {
                    "response_timestamp": datetime.now(timezone.utc), 
                    "duration_ms": duration_ms,
                    "response_payload_preview": response_text[:1000] if response_text else None
                }
                if raw_response_or_error and "error" in raw_response_or_error:
                    log_update_data["error_message"] = raw_response_or_error.get("message", "未知错误")[:1000]
                update_llm_interaction_log(db_for_log_update, llm_interaction_log_id, log_update_data)

            return response_text, raw_response_or_error

        except Exception as e:
            duration_ms = int((time.perf_counter() - start_time) * 1000)
            error_str = str(e)
            error_type_name = type(e).__name__
            
            # 处理特定的API错误
            if "429" in error_str or "quota" in error_str.lower():
                # 强制冷却该密钥
                key_manager.force_cooldown(api_key)
                logger.warning(f"API密钥达到速率限制，已强制冷却: {api_key[-4:]}")
            
            logger.error(f"Agent '{self.agent_name}': Gemini API调用错误: {error_type_name}: {error_str}", exc_info=True)
            response_text = None
            raw_response_or_error = {
                "error": "api_exception", 
                "message": error_str, 
                "type": error_type_name
            }
            
            # 记录异常到项目日志文件
            llm_comm_logger.log_llm_response(
                project_id=project_id_for_logging,
                agent_name=self.agent_name,
                response_text=None,
                raw_response=raw_response_or_error,
                duration_ms=duration_ms,
                success=False,
                error_message=error_str,
                context=call_context_name
            )
            
            if llm_interaction_log_id:
                update_llm_interaction_log(db_for_log_update, llm_interaction_log_id, {
                    "response_timestamp": datetime.now(timezone.utc), 
                    "duration_ms": duration_ms,
                    "error_message": error_str[:1000]
                })
            
            return response_text, raw_response_or_error
        finally:
            db_for_log_update.close()

    async def call_llm(
        self,
        task_prompt_name: str,
        prompt_format_args: Dict[str, Any],
        call_context_name: str,
        project_id_for_logging: Optional[str] = None,
        slide_id_for_logging: Optional[str] = None,
        temperature: float = 0.7,
        expected_response_mime_type: Optional[str] = None,
        expected_pydantic_schema: Optional[Type[BaseModel]] = None,
        max_retries_override: Optional[int] = None,
        use_principled_protocol: bool = True,
        section_name: Optional[str] = None
    ) -> Tuple[Optional[Union[str, Dict[str, Any], BaseModel]], Optional[str]]:

        actual_max_retries = max_retries_override if max_retries_override is not None else self.MAX_LLM_CALL_RETRIES
        log_prefix = f"[{self.agent_name}][{call_context_name.upper()}]"
        if project_id_for_logging: log_prefix += f" [PROJ:{project_id_for_logging}]"
        if slide_id_for_logging: log_prefix += f" [SLIDE_DB_ID:{slide_id_for_logging}]"

        assignment_identifier = project_id_for_logging or f"anon_user_{int(time.time())}"
        
        prompt_version_tag_for_log: str = ""
        task_prompt_template: Optional[str] = None
        
        # 优先使用新的统一提示词系统（如果指定了section_name）
        if hasattr(self, 'agent_prompt_subdir') and self.agent_prompt_subdir:
            agent_name_for_pm_load = self.agent_prompt_subdir
        else:
            agent_name_for_pm_load = self.agent_name.lower().replace("agent", "")

        if section_name:
            # 使用新的统一提示词文件系统
            agent_name_for_unified = agent_name_for_pm_load + "_agent"
            task_prompt_template = get_prompt_section(agent_name_for_unified, section_name)
            prompt_version_tag_for_log = f"{agent_name_for_unified}/{section_name}"
            logger.info(f"{log_prefix} Using unified prompt system: {agent_name_for_unified}/{section_name}")
        else:
            # 旧逻辑，现在 agent_name_for_pm_load 肯定有值了
            task_prompt_template = pm_get_prompt(
                prompt_name=task_prompt_name,
                agent_name=agent_name_for_pm_load
            )
            prompt_version_tag_for_log = f"{agent_name_for_pm_load}/{task_prompt_name}"

        logger.info(f"{log_prefix} Using task prompt: '{prompt_version_tag_for_log}'.")

        if not task_prompt_template:
            if section_name:
                error_msg_template = f"CRITICAL: Failed to load prompt section '{section_name}' for agent '{self.agent_name}' from unified prompt file."
            else:
                error_msg_template = f"CRITICAL: Failed to load task prompt '{prompt_version_tag_for_log}' for agent '{self.agent_name}'."
            logger.error(f"{log_prefix} {error_msg_template} Aborting call.")
            db_log_fail_session = SessionLocal()
            try:
                # Log ID will be None in this case, so we create a log entry for the failure
                failed_log_entry = create_llm_interaction_log(
                    db_log_fail_session, project_id=project_id_for_logging, slide_id=slide_id_for_logging,
                    agent_name=self.agent_name, prompt_name=task_prompt_name,
                    prompt_version_tag=prompt_version_tag_for_log,
                    error_message=error_msg_template,
                    request_timestamp=datetime.now(timezone.utc), response_timestamp=datetime.now(timezone.utc)
                )
                return None, failed_log_entry.id
            except Exception as log_e_template_fail:
                logger.error(f"{log_prefix} Failed to create error log for template loading failure: {log_e_template_fail}", exc_info=True)
                return None, None
            finally:
                db_log_fail_session.close()

        try:
            if section_name:
                agent_name_for_unified = f"{self.agent_prompt_subdir}_agent" if hasattr(self, 'agent_prompt_subdir') and self.agent_prompt_subdir else self.agent_name.lower()
                original_semantic_task_prompt = format_prompt_section(agent_name_for_unified, section_name, **prompt_format_args)
                if original_semantic_task_prompt is None:
                    raise KeyError(f"Failed to format section '{section_name}' for agent '{agent_name_for_unified}'")
            else:
                original_semantic_task_prompt = task_prompt_template.format(**prompt_format_args)
        except KeyError as e:
            logger.error(f"{log_prefix} KeyError during template formatting for '{prompt_version_tag_for_log}': {e}. This indicates a placeholder in the template that's missing from prompt_format_args. Available keys in prompt_format_args: {list(prompt_format_args.keys())}")
            error_msg_format = f"CRITICAL: KeyError during template formatting for '{prompt_version_tag_for_log}': {e}."
            db_log_fail_session = SessionLocal()
            try:
                failed_log_entry = create_llm_interaction_log(
                    db_log_fail_session, project_id=project_id_for_logging, slide_id=slide_id_for_logging,
                    agent_name=self.agent_name, prompt_name=task_prompt_name,
                    prompt_version_tag=prompt_version_tag_for_log,
                    error_message=error_msg_format,
                    request_timestamp=datetime.now(timezone.utc), response_timestamp=datetime.now(timezone.utc)
                )
                return None, failed_log_entry.id
            except Exception as log_e_fmt_fail:
                logger.error(f"{log_prefix} Failed to create error log for prompt formatting failure: {log_e_fmt_fail}", exc_info=True)
                return None, None
            finally:
                db_log_fail_session.close()

        last_error_details_for_retry: Optional[Dict[str, Any]] = None
        final_llm_interaction_log_id: Optional[str] = None

        # 只保留基础的重试，用于处理网络等瞬时故障，而非内容修正
        for attempt in range(1, actual_max_retries + 2): 
            full_prompt_for_llm = self._build_full_prompt(original_semantic_task_prompt, use_principled_protocol)
            
            llm_interaction_log_id_for_this_attempt: Optional[str] = None
            current_prompt_name_for_log = task_prompt_name

            db_log_create_session = SessionLocal()
            try:
                log_entry = create_llm_interaction_log(
                    db_log_create_session, project_id=project_id_for_logging, slide_id=slide_id_for_logging,
                    agent_name=self.agent_name, prompt_name=current_prompt_name_for_log,
                    prompt_version_tag=prompt_version_tag_for_log, 
                    request_payload_preview=full_prompt_for_llm, # 记录完整请求
                    request_timestamp=datetime.now(timezone.utc)
                )
                llm_interaction_log_id_for_this_attempt = log_entry.id
                final_llm_interaction_log_id = llm_interaction_log_id_for_this_attempt 
            except Exception as log_e:
                logger.error(f"{log_prefix} Failed to create LLM interaction log: {log_e}", exc_info=True)
            finally:
                db_log_create_session.close()

            response_text, raw_api_details = await self._call_gemini_api(
                prompt_content=full_prompt_for_llm, temperature=temperature,
                expected_response_mime_type=expected_response_mime_type,
                expected_pydantic_schema_for_api=expected_pydantic_schema, # Pass this parameter even if not directly used in generate_content_async config
                llm_interaction_log_id=llm_interaction_log_id_for_this_attempt,
                project_id_for_logging=project_id_for_logging,
                call_context_name=call_context_name
            )
            last_error_details_for_retry = raw_api_details if not response_text or \
                                           (isinstance(response_text, str) and (response_text.startswith("SAFETY_FILTER_TRIGGERED:") or
                                            response_text in ["LLM_NO_CANDIDATES", "LLM_EMPTY_RESPONSE_TEXT", "LLM_TIMEOUT_ERROR"])) else None

            if not response_text or (isinstance(response_text, str) and (response_text.startswith("SAFETY_FILTER_TRIGGERED:") or
                                       response_text in ["LLM_NO_CANDIDATES", "LLM_EMPTY_RESPONSE_TEXT", "LLM_TIMEOUT_ERROR"])):
                logger.error(f"{log_prefix} LLM call failed or content issue on attempt {attempt}. Reason: {response_text or 'No response'}. Details: {last_error_details_for_retry}")
                if attempt >= actual_max_retries + 1: break
                await asyncio.sleep(1.0 * attempt)
                continue

            if expected_pydantic_schema:
                try:
                    # 由于启用了原生JSON模式，response_text本身就是有效的JSON字符串。
                    # 我们可以直接用Pydantic v2的parse_obj_as进行解析和验证。
                    validated_model = parse_obj_as(expected_pydantic_schema, json.loads(response_text))
                    
                    # 记录成功的解析尝试
                    llm_comm_logger.log_llm_parsing_attempt(
                        project_id=project_id_for_logging,
                        agent_name=self.agent_name,
                        response_text=response_text,
                        expected_schema=expected_pydantic_schema.__name__,
                        validation_success=True,
                        parsed_result=validated_model.model_dump_json(indent=2),
                        context=call_context_name
                    )
                    
                    if llm_interaction_log_id_for_this_attempt:
                        db_log_update_session_valid = SessionLocal()
                        try:
                            update_llm_interaction_log(db_log_update_session_valid, llm_interaction_log_id_for_this_attempt, {"pydantic_validated": True})
                        finally:
                            db_log_update_session_valid.close()

                    logger.info(f"{log_prefix} Successfully received and validated response against {expected_pydantic_schema.__name__}.")
                    return cast(BaseModel, validated_model), llm_interaction_log_id_for_this_attempt
                
                except (json.JSONDecodeError, ValidationError) as e:
                    error_msg_validation = f"Native JSON mode output failed validation: {str(e)[:500]}"
                    logger.error(f"{log_prefix} {error_msg_validation} on attempt {attempt}. 完整的LLM响应内容如下:\n{response_text}")
                    
                    # 记录失败的解析尝试
                    llm_comm_logger.log_llm_parsing_attempt(
                        project_id=project_id_for_logging,
                        agent_name=self.agent_name,
                        response_text=response_text,
                        expected_schema=expected_pydantic_schema.__name__,
                        validation_success=False,
                        validation_error=error_msg_validation,
                        context=call_context_name
                    )
                    
                    last_error_details_for_retry = {
                        "error": "validation_failed", "type": type(e).__name__,
                        "message": error_msg_validation, "response_preview": response_text[:200]
                    }
                    if llm_interaction_log_id_for_this_attempt:
                        db_log_update_session_pydantic = SessionLocal()
                        try:
                            update_llm_interaction_log(db_log_update_session_pydantic, llm_interaction_log_id_for_this_attempt, {
                                "error_message": error_msg_validation[:1000],
                                "pydantic_validated": False
                            })
                        finally:
                            db_log_update_session_pydantic.close()
                    if attempt >= actual_max_retries + 1: break
                    continue # 进入下一轮重试
            else: 
                logger.info(f"{log_prefix} Successfully received text response from LLM.")
                return response_text, llm_interaction_log_id_for_this_attempt 

        logger.error(f"{log_prefix} All {actual_max_retries + 1} LLM call attempts failed.")
        return None, final_llm_interaction_log_id
    
    async def process(self, *args, **kwargs) -> Any:
        """
        Agent的通用处理方法，需要由子类实现。
        """
        raise NotImplementedError("子类必须实现process()方法")

    async def call_llm_with_instructor(
        self,
        task_prompt_name: str,
        section_name: str,
        prompt_format_args: Dict[str, Any],
        response_model: Type[BaseModel],
        call_context_name: str,
        project_id_for_logging: Optional[str] = None,
        slide_id_for_logging: Optional[str] = None,
        image_data: Optional[str] = None,
        max_retries: int = 3
    ) -> Tuple[Optional[BaseModel], Optional[str]]:
        """
        使用Instructor库调用LLM，并将结果解析为Pydantic模型，支持图像输入。
        
        Args:
            task_prompt_name: 任务提示词名称
            section_name: 提示词段落名称
            prompt_format_args: 提示词格式化参数
            response_model: Pydantic模型类
            call_context_name: 调用上下文名称
            project_id_for_logging: 项目ID，用于日志记录
            slide_id_for_logging: 幻灯片ID，用于日志记录
            image_data: Base64编码的图像数据（可选）
            max_retries: 最大重试次数
            
        Returns:
            Tuple[Optional[BaseModel], Optional[str]]: (解析后的Pydantic模型或None, LLM交互日志ID或None)
        """
        # 获取并格式化提示词
        prompt_content = format_prompt_section(
            agent_name=self.agent_prompt_subdir,
            section_name=section_name,
            format_args=prompt_format_args
        )
        
        full_prompt = self._build_full_prompt(prompt_content)
        
        # 记录LLM请求到项目日志文件
        llm_comm_logger.log_llm_request(
            project_id=project_id_for_logging,
            agent_name=self.agent_name,
            prompt_content=full_prompt,
            temperature=0.7,  # instructor默认温度
            model_name=self.model_name_description,
            expected_response_type=response_model.__name__,
            context=call_context_name
        )
        llm_log_id = None
        db_session = SessionLocal()
        
        try:
            llm_log = create_llm_interaction_log(
                db_session,
                agent_name=self.agent_name,
                model_name=self.model_name_description,
                request_payload_preview=full_prompt[:1000],  # 限制长度
                project_id=project_id_for_logging,
                slide_id=slide_id_for_logging
            )
            llm_log_id = str(llm_log.id)
        except Exception as log_e:
            logger.error(f"创建LLM日志失败: {log_e}", exc_info=True)
        finally:
            db_session.close()

        # 将重试次数与API Key数量关联，确保每个Key都有机会
        max_attempts = len(settings.GEMINI_API_KEYS)
        last_exception = None

        for attempt in range(max_attempts):
            try:
                # 1. 从管理器获取一个可用的API Key
                api_key = await key_manager.get_next_key_async()
                
                # 2. 使用新的google-genai API创建客户端
                client = genai.Client(api_key=api_key)
                
                # 3. 用新的genai客户端初始化 instructor
                instructor_client_for_call = instructor.from_genai(
                    client,
                    mode=instructor.Mode.GENAI_STRUCTURED_OUTPUTS
                )

                # 4. 构建消息内容
                if image_data:
                    # 包含图像的消息
                    message_content = [
                        {"type": "text", "text": full_prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_data}"
                            }
                        }
                    ]
                    messages = [{"role": "user", "content": message_content}]
                else:
                    # 纯文本消息
                    messages = [{"role": "user", "content": full_prompt}]

                # 5. 使用动态创建的客户端进行调用
                logger.info(f"[{self.agent_name}] 尝试 {attempt + 1}/{max_attempts} 使用密钥 '...{api_key[-4:]}'")
                response_model_instance = await asyncio.to_thread(
                    instructor_client_for_call.chat.completions.create,
                    response_model=response_model,
                    max_retries=1,
                    model=self.model_name_description,
                    messages=messages,
                )
                
                # 记录成功响应到项目日志
                response_json = response_model_instance.model_dump_json(indent=2)
                llm_comm_logger.log_llm_response(
                    project_id=project_id_for_logging,
                    agent_name=self.agent_name,
                    response_text=response_json,
                    raw_response={"success": True, "instructor_mode": True},
                    duration_ms=0,  # instructor内部处理时间
                    success=True,
                    context=call_context_name
                )
                
                llm_comm_logger.log_llm_parsing_attempt(
                    project_id=project_id_for_logging,
                    agent_name=self.agent_name,
                    response_text=response_json,
                    expected_schema=response_model.__name__,
                    validation_success=True,
                    parsed_result=response_json,
                    context=call_context_name
                )
                
                # 更新成功日志
                if llm_log_id:
                    db_update = SessionLocal()
                    try:
                        update_llm_interaction_log(
                            db_update, 
                            llm_log_id, 
                            {
                                "response_payload_preview": response_model_instance.model_dump_json(indent=2)[:1000],
                                "pydantic_validated": True
                            }
                        )
                    finally:
                        db_update.close()

                return response_model_instance, llm_log_id

            except google_exceptions.ResourceExhausted as e:
                last_exception = e
                warning_msg = (
                    f"[{self.agent_name}] 密钥 '...{api_key if api_key else 'N/A'}' 达到速率限制 (429)，尝试 {attempt + 1}。"
                    f"强制冷却并尝试下一个密钥。错误: {e}"
                )
                logger.warning(warning_msg)
                
                # 记录速率限制错误到项目日志
                llm_comm_logger.log_llm_response(
                    project_id=project_id_for_logging,
                    agent_name=self.agent_name,
                    response_text=None,
                    raw_response={"error": "rate_limit", "attempt": attempt + 1, "api_key_suffix": api_key[-4:] if api_key else "N/A"},
                    duration_ms=0,
                    success=False,
                    error_message=warning_msg,
                    context=call_context_name
                )
                
                # 手动让这个失败的key进入冷却，然后继续循环
                if api_key:
                    key_manager.force_cooldown(api_key)
                # 记录这次失败的尝试
                if llm_log_id:
                    db_err_update = SessionLocal()
                    try:
                        update_llm_interaction_log(db_err_update, llm_log_id, {"error_message": f"RateLimitError on key ...{api_key[-4:]} (Attempt {attempt + 1}): {e}"})
                    finally:
                        db_err_update.close()
                continue 

            except (ValidationError, Exception) as e:
                last_exception = e
                logger.error(f"[{self.agent_name}] Instructor调用失败，出现不可重试的错误: {e}", exc_info=True)
                
                # 记录错误到项目日志
                llm_comm_logger.log_llm_response(
                    project_id=project_id_for_logging,
                    agent_name=self.agent_name,
                    response_text=None,
                    raw_response={"error": "instructor_error", "type": type(e).__name__},
                    duration_ms=0,
                    success=False,
                    error_message=str(e),
                    context=call_context_name
                )
                
                # 更新失败日志
                if llm_log_id:
                    db_err_update = SessionLocal()
                    try:
                        update_llm_interaction_log(db_err_update, llm_log_id, {"error_message": str(e)})
                    finally:
                        db_err_update.close()
                return None, llm_log_id

        # 如果所有尝试都失败了
        final_error_msg = f"[{self.agent_name}] 所有 {max_attempts} 次尝试都失败了。最后错误: {last_exception}"
        logger.error(final_error_msg, exc_info=True)
        
        # 记录最终失败到项目日志
        llm_comm_logger.log_llm_response(
            project_id=project_id_for_logging,
            agent_name=self.agent_name,
            response_text=None,
            raw_response={"error": "all_attempts_failed", "total_attempts": max_attempts, "last_error": str(last_exception)},
            duration_ms=0,
            success=False,
            error_message=final_error_msg,
            context=call_context_name
        )
        
        if llm_log_id:
            db_err_update = SessionLocal()
            try:
                update_llm_interaction_log(db_err_update, llm_log_id, {"error_message": f"所有尝试都失败。最后错误: {last_exception}"})
            finally:
                db_err_update.close()
        return None, llm_log_id

    async def _call_llm_with_instructor(
        self,
        prompt: str,
        pydantic_schema: Type[BaseModel],
        max_retries: int = 3,
        project_id_for_logging: Optional[str] = None,
        slide_id_for_logging: Optional[str] = None
    ) -> Tuple[Optional[BaseModel], Optional[str]]:
        """
        使用Instructor库调用LLM，并将结果解析为Pydantic模型。
        
        Args:
            prompt: 提示词
            pydantic_schema: Pydantic模型类
            max_retries: 最大重试次数
            project_id_for_logging: 项目ID，用于日志记录
            slide_id_for_logging: 幻灯片ID，用于日志记录
            
        Returns:
            Tuple[Optional[BaseModel], Optional[str]]: (解析后的Pydantic模型或None, LLM交互日志ID或None)
        """
        full_prompt = self._build_full_prompt(prompt)
        llm_log_id = None
        db_session = SessionLocal()
        try:
            llm_log = create_llm_interaction_log(
                db_session,
                agent_name=self.agent_name,
                model_name=self.model_name_description,
                request_payload_preview=full_prompt, # 记录完整请求
                project_id=project_id_for_logging,
                slide_id=slide_id_for_logging
            )
            llm_log_id = str(llm_log.id)
        except Exception as log_e:
            logger.error(f"创建LLM日志失败: {log_e}", exc_info=True)
        finally:
            db_session.close()

        # 将重试次数与API Key数量关联，确保每个Key都有机会
        # 总尝试次数等于Key的数量，确保每个Key最多试一次
        max_attempts = len(settings.GEMINI_API_KEYS)
        last_exception = None

        for attempt in range(max_attempts):
            try:
                # 1. 从管理器获取一个可用的API Key
                api_key = await key_manager.get_next_key_async()
                
                # 2. 使用新的google-genai API创建客户端
                client = genai.Client(api_key=api_key)
                
                # 3. 用新的genai客户端初始化 instructor
                instructor_client_for_call = instructor.from_genai(
                    client,
                    mode=instructor.Mode.GENAI_STRUCTURED_OUTPUTS # 使用结构化输出模式，更稳定
                )

                # 4. 使用动态创建的客户端进行调用
                logger.info(f"[{self.agent_name}] 尝试 {attempt + 1}/{max_attempts} 使用密钥 '...{api_key[-4:]}'")
                response_model = await asyncio.to_thread(
                    instructor_client_for_call.chat.completions.create,
                    response_model=pydantic_schema,
                    max_retries=1,
                    model=self.model_name_description,
                    messages=[{"role": "user", "content": full_prompt}],
                )
                
                # 更新成功日志
                if llm_log_id:
                    db_update = SessionLocal()
                    try:
                        update_llm_interaction_log(db_update, llm_log_id, {"response_payload_preview": response_model.model_dump_json(indent=2), "pydantic_validated": True}) # 记录完整响应
                    finally:
                        db_update.close()

                return response_model, llm_log_id

            except google_exceptions.ResourceExhausted as e:
                last_exception = e
                warning_msg = (
                    f"[{self.agent_name}] 密钥 '...{api_key if api_key else 'N/A'}' 达到速率限制 (429)，尝试 {attempt + 1}。"
                    f"强制冷却并尝试下一个密钥。错误: {e}"
                )
                logger.warning(warning_msg)
                
                # 手动让这个失败的key进入冷却，然后继续循环
                if api_key:
                    key_manager.force_cooldown(api_key)
                # 记录这次失败的尝试
                if llm_log_id:
                    db_err_update = SessionLocal()
                    try:
                        update_llm_interaction_log(db_err_update, llm_log_id, {"error_message": f"RateLimitError on key ...{api_key[-4:]} (Attempt {attempt + 1}): {e}"})
                    finally:
                        db_err_update.close()
                # 使用 continue 直接进入下一次循环，获取新key
                continue 

            except (ValidationError, Exception) as e:
                last_exception = e
                logger.error(f"[{self.agent_name}] Instructor调用失败，出现不可重试的错误: {e}", exc_info=True)
                # 更新失败日志
                if llm_log_id:
                    db_err_update = SessionLocal()
                    try:
                        update_llm_interaction_log(db_err_update, llm_log_id, {"error_message": str(e)})
                    finally:
                        db_err_update.close()
                # 对于其他类型的错误，直接失败并返回
                return None, llm_log_id

        # 如果所有尝试都失败了
        final_error_msg = f"[{self.agent_name}] 所有 {max_attempts} 次尝试都失败了。最后错误: {last_exception}"
        logger.error(final_error_msg, exc_info=True)
        if llm_log_id:
            db_err_update = SessionLocal()
            try:
                update_llm_interaction_log(db_err_update, llm_log_id, {"error_message": f"所有尝试都失败。最后错误: {last_exception}"})
            finally:
                db_err_update.close()
        return None, llm_log_id