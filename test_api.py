#!/usr/bin/env python3
"""
测试API是否正常工作
"""
import requests
import json
import time

def test_api_health():
    """测试API健康状态"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("SUCCESS: API健康检查通过")
            return True
        else:
            print(f"ERROR: API健康检查失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"ERROR: 无法连接到API: {e}")
        return False

def test_projects_history():
    """测试项目历史API"""
    try:
        response = requests.get("http://localhost:8000/api/v1/projects/history", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"SUCCESS: 项目历史API正常，返回 {len(data)} 个项目")
            return True
        else:
            print(f"ERROR: 项目历史API失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"ERROR: 项目历史API请求失败: {e}")
        return False

def test_generate_presentation():
    """测试生成演示文稿API（不实际生成，只测试接口）"""
    try:
        # 只测试OPTIONS请求，不实际生成
        response = requests.options("http://localhost:8000/generate_presentation/", timeout=5)
        if response.status_code == 200:
            print("SUCCESS: 生成演示文稿API接口可访问")
            return True
        else:
            print(f"WARNING: 生成演示文稿API OPTIONS请求状态码: {response.status_code}")
            return True  # OPTIONS可能返回其他状态码，但不一定是错误
    except Exception as e:
        print(f"ERROR: 生成演示文稿API请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试API...")
    
    tests = [
        ("API健康检查", test_api_health),
        ("项目历史API", test_projects_history),
        ("生成演示文稿API", test_generate_presentation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n=== {test_name} ===")
        if test_func():
            passed += 1
        time.sleep(1)  # 避免请求过快
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("SUCCESS: 所有API测试通过！")
        return True
    else:
        print("WARNING: 部分API测试失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
