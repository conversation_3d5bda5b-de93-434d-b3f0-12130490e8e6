// frontend/src/views/ChatView.js
import React, { useState, useRef, useEffect } from 'react';
import MessageInput from '../components/MessageInput';
import ChatMessage from '../components/ChatMessage';
import apiService from '../services/api';
import { FaPlus, FaComments, FaBolt } from 'react-icons/fa';

const ChatView = ({ currentChatId, initialMessages = [], onChatComplete }) => {
    const [messages, setMessages] = useState([]);
    const [isGenerating, setIsGenerating] = useState(false);
    const [turboMode, setTurboMode] = useState(false); // 添加极速模式状态
    const messagesEndRef = useRef(null);

    // 使用initialMessages初始化聊天记录
    useEffect(() => {
        if (initialMessages && initialMessages.length > 0) {
            setMessages(initialMessages);
        }
    }, [initialMessages]);

    // 自动滚动到最新消息
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    const handleSendMessage = async (messageData) => {
        // 兼容旧版本调用（直接传字符串）和新版本（传对象）
        const messageText = typeof messageData === 'string' ? messageData : messageData.message;
        const files = typeof messageData === 'object' ? messageData.files : [];
        
        if ((!messageText?.trim() && (!files || files.length === 0)) || isGenerating) return;

        // 添加用户消息
        const userMessage = {
            id: `user-${Date.now()}`,
            sender: 'user',
            text: messageText || '发送了文件',
            files: files || [],
            timestamp: new Date().toISOString()
        };
        setMessages(prev => [...prev, userMessage]);
        setIsGenerating(true);

        try {
            // 开始流式AI响应
            await apiService.streamChatResponse(
                messageText,
                (progressData) => {
                    setMessages(prevMessages => {
                        const newMessages = [...prevMessages];

                        // 处理系统消息 - 总是作为新的、独立的系统消息添加
                        if (progressData.sender === 'system') {
                            newMessages.push({
                                ...progressData,
                                type: 'system-info',
                                timestamp: progressData.timestamp || new Date().toISOString()
                            });
                            return newMessages;
                        }

                        // 处理AI消息 (sender === 'ai')
                        if (progressData.sender === 'ai') {
                            // 查找具有相同ID的现有AI消息
                            const aiMessageIndex = newMessages.findIndex(msg => msg.id === progressData.id);

                            if (aiMessageIndex !== -1) {
                                // 找到现有消息
                                const existingMessage = newMessages[aiMessageIndex];
                                
                                if (progressData.is_append) {
                                    // 如果是追加内容，将文本追加到现有消息
                                    newMessages[aiMessageIndex] = {
                                        ...existingMessage,
                                        text: (existingMessage.text || '') + (progressData.text || ''),
                                        is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,
                                        timestamp: progressData.timestamp || existingMessage.timestamp
                                    };
                                } else {
                                    // 如果不是追加，更新整个消息状态
                                    newMessages[aiMessageIndex] = {
                                        ...existingMessage,
                                        text: progressData.text !== undefined ? progressData.text : existingMessage.text,
                                        is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,
                                        stream_complete: progressData.stream_complete !== undefined ? progressData.stream_complete : existingMessage.stream_complete,
                                        timestamp: progressData.timestamp || existingMessage.timestamp
                                    };
                                }
                            } else {
                                // 如果未找到现有AI消息，作为新消息添加
                                newMessages.push({
                                    ...progressData,
                                    type: 'ai',
                                    timestamp: progressData.timestamp || new Date().toISOString()
                                });
                            }
                        }
                        return newMessages;
                    });
                },
                (error) => {
                    console.error('Chat streaming error:', error);
                    setIsGenerating(false);
                    setMessages(prev => [...prev, {
                        id: `error-${Date.now()}`,
                        sender: 'system',
                        type: 'ai_error',
                        text: `发生错误: ${error.message}`,
                        timestamp: new Date().toISOString()
                    }]);
                },
                () => {
                    // 完成回调
                    setIsGenerating(false);
                    // 通知父组件聊天完成，可能需要刷新聊天历史
                    if (onChatComplete) {
                        onChatComplete();
                    }
                },
                currentChatId, // 传递当前聊天ID
                turboMode // 传递极速模式状态
            );
        } catch (apiError) {
            console.error('API call failed to start:', apiError);
            setIsGenerating(false);
            setMessages(prev => [...prev, {
                id: `error-api-${Date.now()}`,
                sender: 'system',
                type: 'ai_error',
                text: `无法连接到AI聊天服务: ${apiError.message}`,
                timestamp: new Date().toISOString()
            }]);
        }
    };

    const handleNewChat = () => {
        setMessages([]);
        setIsGenerating(false);
        // 导航到新的聊天页面，清除URL中的chatId参数
        window.location.href = '/chat';
    };

    // 切换极速模式
    const toggleTurboMode = () => {
        setTurboMode(prev => !prev);
    };

    return (
        <div className="flex flex-col h-screen bg-gray-50 flex-1">
            {/* Header for Chat View */}
            <div className="h-16 px-3 border-b border-gray-200 bg-white flex items-center sticky top-0 z-10">
                <button 
                  onClick={handleNewChat}
                  className="p-2 mr-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100"
                  title="新聊天"
                >
                    <FaPlus size={16} />
                </button>
                <div className="flex-1 text-base font-medium text-gray-800">
                    <span className="py-1 px-2 rounded">AI 聊天</span>
                </div>
                <button 
                  onClick={toggleTurboMode}
                  className={`p-2 ml-2 rounded-full ${turboMode 
                    ? 'bg-yellow-500 text-white' 
                    : 'text-gray-500 hover:text-yellow-500 hover:bg-gray-100'}`}
                  title={turboMode ? "关闭极速模式" : "开启极速模式"}
                >
                    <FaBolt size={16} />
                    {turboMode && <span className="ml-1 text-xs">极速</span>}
                </button>
            </div>

            {/* Chat Messages Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-5 custom-scrollbar">
                {messages.length === 0 && (
                    <div className="flex flex-col items-center justify-center h-full text-gray-400 p-8 min-h-[400px]">
                        <span className="text-6xl mb-6 text-gray-300">💬</span>
                        <span className="text-2xl font-medium">开始你的AI对话</span>
                        <p className="text-sm text-gray-400 mt-2 text-center max-w-md">
                            在这里与AI自由交流，提出问题或寻求帮助。
                            {turboMode && <span className="block mt-1 font-medium text-yellow-500">已开启极速模式，响应更快但可能不使用搜索和深度思考</span>}
                        </p>
                    </div>
                )}
                {messages.map((msg, index) => (
                    <ChatMessage key={msg.id || index} message={msg} /> 
                ))}
                <div ref={messagesEndRef} />
            </div>

            {/* Message Input Area */}
            <div className="p-2 bg-gray-50 sticky bottom-0 border-t border-gray-200">
                <MessageInput 
                    onSendMessage={handleSendMessage} 
                    isGenerating={isGenerating}
                    placeholder={turboMode ? "极速模式已开启，输入你的消息..." : "输入你的消息..."}
                />
            </div>
        </div>
    );
};

export default ChatView;