{"ast": null, "code": "/**\r\n * Format a Date object into a user-friendly string\r\n * @param {Date} date - The date to format\r\n * @returns {string} Formatted date string\r\n */\nconst formatDate = date => {\n  if (!date || isNaN(date.getTime())) return '未知时间';\n  const now = new Date();\n  const diffMs = now - date;\n  const diffSecs = Math.floor(diffMs / 1000);\n  const diffMins = Math.floor(diffSecs / 60);\n  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n\n  // Today, show time only\n  if (diffDays === 0) {\n    if (diffMins < 1) {\n      return '刚刚';\n    }\n    if (diffMins < 60) {\n      return `${diffMins} 分钟前`;\n    }\n    return `${Math.floor(diffMins / 60)}小时前`;\n  }\n\n  // Yesterday\n  if (diffDays === 1) {\n    return '昨天';\n  }\n\n  // Within 7 days\n  if (diffDays < 7) {\n    return `${diffDays}天前`;\n  }\n\n  // Same year, show month and day\n  const year = date.getFullYear();\n  const month = date.getMonth() + 1;\n  const day = date.getDate();\n  if (year === now.getFullYear()) {\n    return `${month}月${day}日`;\n  }\n\n  // Different year, show year-month-day\n  return `${year}年${month}月${day}日`;\n};\nexport default formatDate;", "map": {"version": 3, "names": ["formatDate", "date", "isNaN", "getTime", "now", "Date", "diffMs", "diffSecs", "Math", "floor", "diffMins", "diffDays", "year", "getFullYear", "month", "getMonth", "day", "getDate"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/utils/formatDate.js"], "sourcesContent": ["/**\r\n * Format a Date object into a user-friendly string\r\n * @param {Date} date - The date to format\r\n * @returns {string} Formatted date string\r\n */\r\nconst formatDate = (date) => {\r\n  if (!date || isNaN(date.getTime())) return '未知时间';\r\n\r\n  const now = new Date();\r\n  const diffMs = now - date;\r\n  const diffSecs = Math.floor(diffMs / 1000);\r\n  const diffMins = Math.floor(diffSecs / 60);\r\n  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\r\n  \r\n  // Today, show time only\r\n  if (diffDays === 0) {\r\n    if (diffMins < 1) {\r\n      return '刚刚';\r\n    }\r\n    if (diffMins < 60) {\r\n      return `${diffMins} 分钟前`;\r\n    }\r\n    return `${Math.floor(diffMins / 60)}小时前`;\r\n  }\r\n  \r\n  // Yesterday\r\n  if (diffDays === 1) {\r\n    return '昨天';\r\n  }\r\n  \r\n  // Within 7 days\r\n  if (diffDays < 7) {\r\n    return `${diffDays}天前`;\r\n  }\r\n  \r\n  // Same year, show month and day\r\n  const year = date.getFullYear();\r\n  const month = date.getMonth() + 1;\r\n  const day = date.getDate();\r\n  if (year === now.getFullYear()) {\r\n    return `${month}月${day}日`;\r\n  }\r\n  \r\n  // Different year, show year-month-day\r\n  return `${year}年${month}月${day}日`;\r\n};\r\n\r\nexport default formatDate; "], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,MAAMA,UAAU,GAAIC,IAAI,IAAK;EAC3B,IAAI,CAACA,IAAI,IAAIC,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,MAAM;EAEjD,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;EACtB,MAAMC,MAAM,GAAGF,GAAG,GAAGH,IAAI;EACzB,MAAMM,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,MAAM,GAAG,IAAI,CAAC;EAC1C,MAAMI,QAAQ,GAAGF,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAC;EAC1C,MAAMI,QAAQ,GAAGH,IAAI,CAACC,KAAK,CAACH,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;;EAE3D;EACA,IAAIK,QAAQ,KAAK,CAAC,EAAE;IAClB,IAAID,QAAQ,GAAG,CAAC,EAAE;MAChB,OAAO,IAAI;IACb;IACA,IAAIA,QAAQ,GAAG,EAAE,EAAE;MACjB,OAAO,GAAGA,QAAQ,MAAM;IAC1B;IACA,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE,CAAC,KAAK;EAC1C;;EAEA;EACA,IAAIC,QAAQ,KAAK,CAAC,EAAE;IAClB,OAAO,IAAI;EACb;;EAEA;EACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;IAChB,OAAO,GAAGA,QAAQ,IAAI;EACxB;;EAEA;EACA,MAAMC,IAAI,GAAGX,IAAI,CAACY,WAAW,CAAC,CAAC;EAC/B,MAAMC,KAAK,GAAGb,IAAI,CAACc,QAAQ,CAAC,CAAC,GAAG,CAAC;EACjC,MAAMC,GAAG,GAAGf,IAAI,CAACgB,OAAO,CAAC,CAAC;EAC1B,IAAIL,IAAI,KAAKR,GAAG,CAACS,WAAW,CAAC,CAAC,EAAE;IAC9B,OAAO,GAAGC,KAAK,IAAIE,GAAG,GAAG;EAC3B;;EAEA;EACA,OAAO,GAAGJ,IAAI,IAAIE,KAAK,IAAIE,GAAG,GAAG;AACnC,CAAC;AAED,eAAehB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}