"""
内容优化工具 - 确保幻灯片内容适配1280x720空间限制
"""
import re
from typing import Dict, List, Any, Tuple
import logging

logger = logging.getLogger(__name__)

class ContentOptimizer:
    """内容优化器，确保幻灯片内容适配空间限制"""
    
    # 针对1280x720的空间限制常量（更严格的控制）
    MAX_TITLE_LENGTH = 18  # 主标题最大字符数（考虑大字号显示）
    MAX_SUBTITLE_LENGTH = 25  # 副标题最大字符数
    MAX_PARAGRAPH_LENGTH = 60  # 段落最大字符数（更严格）
    MAX_TOTAL_CHARS = 150  # 每张幻灯片总字符数限制（更严格）
    MAX_ELEMENTS = 5  # 最大元素数量（更严格，确保不拥挤）
    MAX_BULLET_POINTS = 4  # 最大要点数量
    MAX_KPI_CARDS = 3  # 最大KPI卡片数量
    
    # 中文字符宽度因子（中文字符通常比英文字符宽1.5-2倍）
    CJK_WIDTH_FACTOR = 1.8
    
    @staticmethod
    def calculate_visual_width(text: str) -> float:
        """
        计算文本的视觉宽度（考虑中英文字符差异）
        
        Args:
            text: 文本内容
            
        Returns:
            视觉宽度值
        """
        if not text:
            return 0.0
            
        cjk_chars = 0
        ascii_chars = 0
        
        for char in text:
            # 检测中日韩字符
            if '\u4e00' <= char <= '\u9fff' or '\u3400' <= char <= '\u4dbf' or \
               '\u3040' <= char <= '\u309f' or '\u30a0' <= char <= '\u30ff':
                cjk_chars += 1
            else:
                ascii_chars += 1
                
        # 计算视觉宽度
        visual_width = cjk_chars * ContentOptimizer.CJK_WIDTH_FACTOR + ascii_chars
        return visual_width
    
    @staticmethod
    def optimize_text_for_visual_width(text: str, max_visual_width: float, preserve_key_info: bool = True) -> str:
        """
        基于视觉宽度优化文本内容
        
        Args:
            text: 原始文本
            max_visual_width: 最大视觉宽度
            preserve_key_info: 是否保留关键信息
            
        Returns:
            优化后的文本
        """
        if ContentOptimizer.calculate_visual_width(text) <= max_visual_width:
            return text
            
        if preserve_key_info:
            # 保留关键信息的智能截断
            sentences = re.split(r'[。！？；]', text)
            result = ""
            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue
                    
                test_result = result + sentence + "。"
                if ContentOptimizer.calculate_visual_width(test_result) <= max_visual_width - 3:
                    result = test_result
                else:
                    break
            
            if ContentOptimizer.calculate_visual_width(result) < max_visual_width - 3:
                result = result.rstrip("。") + "..."
            return result
        else:
            # 简单截断，确保视觉宽度不超限
            current_width = 0
            result = ""
            for char in text:
                char_width = ContentOptimizer.CJK_WIDTH_FACTOR if '\u4e00' <= char <= '\u9fff' else 1
                if current_width + char_width > max_visual_width - 3:
                    break
                result += char
                current_width += char_width
            return result + "..."
    
    @staticmethod
    def optimize_text_content(text: str, max_length: int, preserve_key_info: bool = True) -> str:
        """
        优化文本内容，确保不超过长度限制
        
        Args:
            text: 原始文本
            max_length: 最大长度限制
            preserve_key_info: 是否保留关键信息
            
        Returns:
            优化后的文本
        """
        # 使用视觉宽度优化
        max_visual_width = max_length * 1.2  # 给予一些宽度余量
        return ContentOptimizer.optimize_text_for_visual_width(text, max_visual_width, preserve_key_info)
    
    @staticmethod
    def split_long_paragraph(text: str, max_length: int = 45) -> List[str]:
        """
        将长段落分割为多个短段落（更严格的长度控制）
        
        Args:
            text: 原始段落
            max_length: 每段最大长度
            
        Returns:
            分割后的段落列表
        """
        max_visual_width = max_length * 1.2
        
        if ContentOptimizer.calculate_visual_width(text) <= max_visual_width:
            return [text]
            
        # 按句子分割
        sentences = re.split(r'[。！？；]', text)
        paragraphs = []
        current_paragraph = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            test_paragraph = current_paragraph + sentence + "。"
            if ContentOptimizer.calculate_visual_width(test_paragraph) <= max_visual_width:
                current_paragraph = test_paragraph
            else:
                if current_paragraph:
                    paragraphs.append(current_paragraph.rstrip("。"))
                current_paragraph = sentence + "。"
        
        if current_paragraph:
            paragraphs.append(current_paragraph.rstrip("。"))
            
        return paragraphs
    
    @staticmethod
    def extract_key_points(text: str, max_points: int = 3) -> List[str]:
        """
        从长文本中提取关键要点
        
        Args:
            text: 原始文本
            max_points: 最大要点数量
            
        Returns:
            关键要点列表
        """
        # 按句子分割
        sentences = re.split(r'[。！？；]', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # 优先选择包含数字、百分比、关键词的句子
        key_sentences = []
        regular_sentences = []
        
        for sentence in sentences:
            if re.search(r'[\d%]|增长|下降|提升|改善|挑战|机遇|风险|重要|关键|核心|主要', sentence):
                key_sentences.append(sentence)
            else:
                regular_sentences.append(sentence)
        
        # 组合结果
        result = key_sentences[:max_points]
        if len(result) < max_points:
            result.extend(regular_sentences[:max_points - len(result)])
            
        # 优化每个要点的长度
        optimized_result = []
        for point in result[:max_points]:
            optimized_point = ContentOptimizer.optimize_text_content(point, 35, True)
            optimized_result.append(optimized_point)
            
        return optimized_result
    
    @staticmethod
    def optimize_blueprint_content(blueprint: Dict[str, Any]) -> Dict[str, Any]:
        """
        优化蓝图内容，确保适配1280x720空间限制
        
        Args:
            blueprint: 原始蓝图数据
            
        Returns:
            优化后的蓝图数据
        """
        optimized = blueprint.copy()
        
        try:
            # 优化标题
            if 'slide_title' in optimized:
                optimized['slide_title'] = ContentOptimizer.optimize_text_content(
                    optimized['slide_title'], 
                    ContentOptimizer.MAX_TITLE_LENGTH
                )
            
            # 优化副标题
            if 'slide_subtitle' in optimized:
                optimized['slide_subtitle'] = ContentOptimizer.optimize_text_content(
                    optimized['slide_subtitle'], 
                    ContentOptimizer.MAX_SUBTITLE_LENGTH
                )
            
            # 优化关键元素
            if 'key_elements' in optimized and isinstance(optimized['key_elements'], list):
                # 限制元素数量
                optimized['key_elements'] = optimized['key_elements'][:ContentOptimizer.MAX_ELEMENTS]
                
                kpi_card_count = 0
                bullet_point_count = 0
                
                for i, element in enumerate(optimized['key_elements']):
                    if isinstance(element, dict):
                        element_type = element.get('type', '')
                        
                        # 优化文本元素
                        if element_type == 'text' and 'content' in element:
                            original_content = element['content']
                            if ContentOptimizer.calculate_visual_width(original_content) > ContentOptimizer.MAX_PARAGRAPH_LENGTH * 1.2:
                                # 分割为要点
                                key_points = ContentOptimizer.extract_key_points(original_content, ContentOptimizer.MAX_BULLET_POINTS)
                                element['content'] = '；'.join(key_points)
                        
                        # 优化KPI卡片
                        elif element_type == 'kpi_card':
                            kpi_card_count += 1
                            if kpi_card_count > ContentOptimizer.MAX_KPI_CARDS:
                                # 移除多余的KPI卡片
                                optimized['key_elements'] = optimized['key_elements'][:i]
                                break
                                
                            if 'title' in element:
                                element['title'] = ContentOptimizer.optimize_text_content(
                                    element['title'], 12
                                )
                            if 'description' in element:
                                element['description'] = ContentOptimizer.optimize_text_content(
                                    element['description'], 15
                                )
                            if 'value' in element and isinstance(element['value'], str):
                                element['value'] = ContentOptimizer.optimize_text_content(
                                    element['value'], 8
                                )
                        
                        # 优化要点列表
                        elif element_type == 'bullet_points' and 'items' in element:
                            if isinstance(element['items'], list):
                                # 限制要点数量
                                element['items'] = element['items'][:ContentOptimizer.MAX_BULLET_POINTS]
                                # 优化每个要点的长度
                                optimized_items = []
                                for item in element['items']:
                                    if isinstance(item, str):
                                        optimized_item = ContentOptimizer.optimize_text_content(item, 25)
                                        optimized_items.append(optimized_item)
                                    else:
                                        optimized_items.append(item)
                                element['items'] = optimized_items
            
            # 检查总字符数
            total_chars = ContentOptimizer._count_total_characters(optimized)
            if total_chars > ContentOptimizer.MAX_TOTAL_CHARS:
                logger.warning(f"蓝图总字符数 {total_chars} 超过限制 {ContentOptimizer.MAX_TOTAL_CHARS}，进行进一步优化")
                optimized = ContentOptimizer._reduce_content_density(optimized)
                
        except Exception as e:
            logger.error(f"优化蓝图内容时发生错误: {e}")
            return blueprint  # 返回原始蓝图
            
        return optimized
    
    @staticmethod
    def _count_total_characters(blueprint: Dict[str, Any]) -> int:
        """计算蓝图中的总字符数"""
        total = 0
        
        # 计算标题字符数
        if 'slide_title' in blueprint:
            total += len(blueprint['slide_title'])
        if 'slide_subtitle' in blueprint:
            total += len(blueprint['slide_subtitle'])
            
        # 计算关键元素字符数
        if 'key_elements' in blueprint and isinstance(blueprint['key_elements'], list):
            for element in blueprint['key_elements']:
                if isinstance(element, dict):
                    # 文本内容
                    for key in ['content', 'title', 'description', 'text', 'value']:
                        if key in element and isinstance(element[key], str):
                            total += len(element[key])
                    
                    # 要点列表
                    if 'items' in element and isinstance(element['items'], list):
                        for item in element['items']:
                            if isinstance(item, str):
                                total += len(item)
                            
        return total
    
    @staticmethod
    def _reduce_content_density(blueprint: Dict[str, Any]) -> Dict[str, Any]:
        """进一步减少内容密度"""
        optimized = blueprint.copy()
        
        # 进一步缩短文本内容
        if 'key_elements' in optimized and isinstance(optimized['key_elements'], list):
            for element in optimized['key_elements']:
                if isinstance(element, dict):
                    element_type = element.get('type', '')
                    
                    # 进一步压缩文本元素
                    if element_type == 'text' and 'content' in element:
                        if ContentOptimizer.calculate_visual_width(element['content']) > 30 * 1.2:
                            # 提取最关键的信息
                            key_points = ContentOptimizer.extract_key_points(element['content'], 2)
                            element['content'] = '；'.join(key_points)
                    
                    # 压缩要点列表
                    elif element_type == 'bullet_points' and 'items' in element:
                        if isinstance(element['items'], list) and len(element['items']) > 3:
                            element['items'] = element['items'][:3]  # 最多保留3个要点
        
        # 如果还是太长，减少元素数量
        total_chars = ContentOptimizer._count_total_characters(optimized)
        if total_chars > ContentOptimizer.MAX_TOTAL_CHARS:
            if 'key_elements' in optimized:
                optimized['key_elements'] = optimized['key_elements'][:3]  # 进一步减少到3个元素
                
        return optimized

    @staticmethod
    def validate_space_constraints(blueprint: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证蓝图是否符合空间限制
        
        Args:
            blueprint: 蓝图数据
            
        Returns:
            Tuple of (是否通过验证, 问题列表)
        """
        issues = []
        
        # 检查标题长度
        if 'slide_title' in blueprint:
            title_width = ContentOptimizer.calculate_visual_width(blueprint['slide_title'])
            if title_width > ContentOptimizer.MAX_TITLE_LENGTH * 1.2:
                issues.append(f"标题视觉宽度过长: {title_width:.1f} > {ContentOptimizer.MAX_TITLE_LENGTH * 1.2}")
            
        # 检查副标题长度
        if 'slide_subtitle' in blueprint:
            subtitle_width = ContentOptimizer.calculate_visual_width(blueprint['slide_subtitle'])
            if subtitle_width > ContentOptimizer.MAX_SUBTITLE_LENGTH * 1.2:
                issues.append(f"副标题视觉宽度过长: {subtitle_width:.1f} > {ContentOptimizer.MAX_SUBTITLE_LENGTH * 1.2}")
            
        # 检查元素数量
        if 'key_elements' in blueprint and isinstance(blueprint['key_elements'], list):
            if len(blueprint['key_elements']) > ContentOptimizer.MAX_ELEMENTS:
                issues.append(f"元素过多: {len(blueprint['key_elements'])} > {ContentOptimizer.MAX_ELEMENTS}")
                
        # 检查总字符数
        total_chars = ContentOptimizer._count_total_characters(blueprint)
        if total_chars > ContentOptimizer.MAX_TOTAL_CHARS:
            issues.append(f"总字符数过多: {total_chars} > {ContentOptimizer.MAX_TOTAL_CHARS}")
            
        return len(issues) == 0, issues
        
    @staticmethod
    def estimate_required_height(blueprint: Dict[str, Any]) -> int:
        """
        估算蓝图内容需要的高度（像素）
        
        Args:
            blueprint: 蓝图数据
            
        Returns:
            估算的高度（像素）
        """
        height = 0
        
        # 标题区域高度
        if 'slide_title' in blueprint:
            height += 80  # 主标题高度
        if 'slide_subtitle' in blueprint:
            height += 40  # 副标题高度
            
        # 内容区域高度
        if 'key_elements' in blueprint and isinstance(blueprint['key_elements'], list):
            for element in blueprint['key_elements']:
                if isinstance(element, dict):
                    element_type = element.get('type', '')
                    
                    if element_type == 'text':
                        height += 60  # 文本段落高度
                    elif element_type == 'kpi_card':
                        height += 100  # KPI卡片高度
                    elif element_type == 'bullet_points':
                        items_count = len(element.get('items', []))
                        height += 30 + items_count * 25  # 要点列表高度
                    elif element_type == 'chart':
                        height += 300  # 图表高度
                    else:
                        height += 50  # 其他元素默认高度
        
        # 内边距和间距
        height += 60  # 总体内边距
        
        return height 