{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\LeftNav.js\",\n  _s = $RefreshSig$();\n// frontend/src/components/LeftNav.js\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom'; // Import useNavigate and useLocation\nimport { FaBrain, FaFilePowerpoint, FaTable, FaComments, FaImage, FaVideo, FaThLarge, FaCloud, FaUserCircle, FaCog } from 'react-icons/fa'; // 移除未使用的图标\nimport Logo from './Logo';\nimport ChatHistoryList from './ChatHistoryList';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LeftNav = ({\n  isTaskListOpen,\n  onToggleTaskList,\n  onSelectHistoryItem,\n  onSelectChat\n}) => {\n  _s();\n  const [activeItem, setActiveItem] = useState('');\n  const [currentChatId, setCurrentChatId] = useState(null);\n  const navigate = useNavigate(); // Initialize useNavigate\n  const location = useLocation(); // Get current location\n\n  const navItems = [\n  // { id: '首页', name: '首页', icon: <FaHome size={20} />, path: '/' }, // Removed, main app handles project history\n  {\n    id: '超级智能体',\n    name: '超级智能体',\n    icon: /*#__PURE__*/_jsxDEV(FaBrain, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 41\n    }, this)\n  }, {\n    id: 'AI幻灯片',\n    name: 'AI幻灯片',\n    icon: /*#__PURE__*/_jsxDEV(FaFilePowerpoint, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 41\n    }, this),\n    path: '/'\n  },\n  // Updated path to root\n  {\n    id: 'AI聊天',\n    name: 'AI聊天',\n    icon: /*#__PURE__*/_jsxDEV(FaComments, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 39\n    }, this),\n    path: '/chat'\n  },\n  // Updated path\n  {\n    id: 'AI表格',\n    name: 'AI表格',\n    icon: /*#__PURE__*/_jsxDEV(FaTable, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 39\n    }, this)\n  }, {\n    id: '图片工作室',\n    name: '图片工作室',\n    icon: /*#__PURE__*/_jsxDEV(FaImage, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 41\n    }, this)\n  }, {\n    id: '视频生成',\n    name: '视频生成',\n    icon: /*#__PURE__*/_jsxDEV(FaVideo, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 39\n    }, this)\n  }, {\n    id: '所有智能体',\n    name: '所有智能体',\n    icon: /*#__PURE__*/_jsxDEV(FaThLarge, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 41\n    }, this)\n  }, {\n    id: 'AI云盘',\n    name: 'AI 云盘',\n    icon: /*#__PURE__*/_jsxDEV(FaCloud, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 40\n    }, this)\n  }, {\n    id: '我',\n    name: '我',\n    icon: /*#__PURE__*/_jsxDEV(FaUserCircle, {\n      size: 20\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 33\n    }, this)\n  }];\n\n  // 根据当前路径设置活动项\n  useEffect(() => {\n    const currentPath = location.pathname;\n    const currentItem = navItems.find(item => item.path === currentPath);\n    if (currentItem) {\n      setActiveItem(currentItem.id);\n    } else if (currentPath === '/') {\n      setActiveItem('AI幻灯片');\n    }\n  }, [location.pathname]);\n  const handleNavigate = (path, itemId) => {\n    setActiveItem(itemId);\n    navigate(path);\n  };\n  const handleSelectChat = chatId => {\n    setCurrentChatId(chatId);\n    onSelectChat === null || onSelectChat === void 0 ? void 0 : onSelectChat(chatId);\n  };\n  const tasks = [{\n    id: 1,\n    name: '中国房地产市场2025年趋势分析'\n  }, {\n    id: 2,\n    name: '季度营销报告'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen flex flex-col bg-white border-r border-gray-200 w-60\",\n    children: [isTaskListOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-0 left-0 w-60 h-full bg-white shadow-xl z-30 p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-700\",\n          children: \"\\u5386\\u53F2\\u4EFB\\u52A1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onToggleTaskList,\n          className: \"text-gray-500 hover:text-gray-700\",\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: tasks.map(task => /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"p-2 hover:bg-gray-100 rounded cursor-pointer text-sm text-gray-600\",\n          children: task.name\n        }, task.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center h-16 px-4 border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center pl-4\",\n        children: /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/\",\n          className: \"flex items-center\",\n          onClick: e => {\n            e.preventDefault();\n            navigate('/');\n          },\n          children: [/*#__PURE__*/_jsxDEV(Logo, {\n            size: 32\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-bold text-2xl text-gray-800 ml-2\",\n            children: \"TikTodo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"flex-grow p-2 space-y-1 custom-scrollbar overflow-y-auto\",\n      children: [navItems.map(item => /*#__PURE__*/_jsxDEV(\"a\", {\n        href: item.path || '#' // Use item.path if available, otherwise '#'\n        ,\n        onClick: e => {\n          e.preventDefault(); // Prevent default link behavior\n          if (item.path) {\n            handleNavigate(item.path, item.id);\n          } else {\n            setActiveItem(item.id); // For items without a path\n          }\n        },\n        className: `flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors duration-150\n              ${activeItem === item.id ? 'bg-tiktodo-blue-light text-tiktodo-blue' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}\n            `,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mr-3\",\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this), item.name]\n      }, item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-200 my-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), (location.pathname === '/chat' || location.pathname === '/') && /*#__PURE__*/_jsxDEV(ChatHistoryList, {\n        currentChatId: currentChatId,\n        onSelectChat: handleSelectChat\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"flex items-center px-3 py-2.5 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150 w-full\",\n        children: [/*#__PURE__*/_jsxDEV(FaCog, {\n          size: 20,\n          className: \"mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), \"\\u8BBE\\u7F6E\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(LeftNav, \"o+WbXE+vBz3hS/LDcMljPUXLWI0=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = LeftNav;\nexport default LeftNav;\nvar _c;\n$RefreshReg$(_c, \"LeftNav\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "FaBrain", "FaFilePowerpoint", "FaTable", "FaComments", "FaImage", "FaVideo", "FaThLarge", "FaCloud", "FaUserCircle", "FaCog", "Logo", "ChatHistoryList", "jsxDEV", "_jsxDEV", "LeftNav", "isTaskListOpen", "onToggleTaskList", "onSelectHistoryItem", "onSelectChat", "_s", "activeItem", "setActiveItem", "currentChatId", "setCurrentChatId", "navigate", "location", "navItems", "id", "name", "icon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "currentPath", "pathname", "currentItem", "find", "item", "handleNavigate", "itemId", "handleSelectChat", "chatId", "tasks", "className", "children", "onClick", "map", "task", "href", "e", "preventDefault", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/LeftNav.js"], "sourcesContent": ["// frontend/src/components/LeftNav.js\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom'; // Import useNavigate and useLocation\nimport {\n  FaBrain, FaFilePowerpoint, FaTable, FaComments, FaImage, FaVideo, FaThLarge, FaCloud, FaUserCircle, FaCog\n} from 'react-icons/fa'; // 移除未使用的图标\nimport Logo from './Logo';\nimport ChatHistoryList from './ChatHistoryList';\n\nconst LeftNav = ({ isTaskListOpen, onToggleTaskList, onSelectHistoryItem, onSelectChat }) => {\n  const [activeItem, setActiveItem] = useState('');\n  const [currentChatId, setCurrentChatId] = useState(null);\n  const navigate = useNavigate(); // Initialize useNavigate\n  const location = useLocation(); // Get current location\n\n  const navItems = [\n    // { id: '首页', name: '首页', icon: <FaHome size={20} />, path: '/' }, // Removed, main app handles project history\n    { id: '超级智能体', name: '超级智能体', icon: <FaBrain size={20} /> },\n    { id: 'AI幻灯片', name: 'AI幻灯片', icon: <FaFilePowerpoint size={20} />, path: '/' }, // Updated path to root\n    { id: 'AI聊天', name: 'AI聊天', icon: <FaComments size={20} />, path: '/chat' }, // Updated path\n    { id: 'AI表格', name: 'AI表格', icon: <FaTable size={20} /> },\n    { id: '图片工作室', name: '图片工作室', icon: <FaImage size={20} /> },\n    { id: '视频生成', name: '视频生成', icon: <FaVideo size={20} /> },\n    { id: '所有智能体', name: '所有智能体', icon: <FaThLarge size={20} /> },\n    { id: 'AI云盘', name: 'AI 云盘', icon: <FaCloud size={20} /> },\n    { id: '我', name: '我', icon: <FaUserCircle size={20} /> },\n  ];\n\n  // 根据当前路径设置活动项\n  useEffect(() => {\n    const currentPath = location.pathname;\n    const currentItem = navItems.find(item => item.path === currentPath);\n    if (currentItem) {\n      setActiveItem(currentItem.id);\n    } else if (currentPath === '/') {\n      setActiveItem('AI幻灯片');\n    }\n  }, [location.pathname]);\n\n  const handleNavigate = (path, itemId) => {\n    setActiveItem(itemId);\n    navigate(path);\n  };\n\n  const handleSelectChat = (chatId) => {\n    setCurrentChatId(chatId);\n    onSelectChat?.(chatId);\n  };\n\n  const tasks = [\n    { id: 1, name: '中国房地产市场2025年趋势分析' },\n    { id: 2, name: '季度营销报告' },\n  ];\n\n  return (\n    <div className=\"h-screen flex flex-col bg-white border-r border-gray-200 w-60\">\n      {/* Task List Overlay - Conditional Rendering */}\n      {isTaskListOpen && (\n        <div className=\"absolute top-0 left-0 w-60 h-full bg-white shadow-xl z-30 p-4\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <h2 className=\"text-lg font-semibold text-gray-700\">历史任务</h2>\n            <button onClick={onToggleTaskList} className=\"text-gray-500 hover:text-gray-700\">\n              ✕\n            </button>\n          </div>\n          <ul>\n            {tasks.map(task => (\n              <li key={task.id} className=\"p-2 hover:bg-gray-100 rounded cursor-pointer text-sm text-gray-600\">\n                {task.name}\n              </li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      {/* Main Left Navigation */}\n      <div className=\"flex items-center h-16 px-4 border-b border-gray-200\">\n        {/* TikTodo Logo - Clickable to navigate to home page */}\n        <div className=\"flex items-center pl-4\">\n          <a href=\"/\" className=\"flex items-center\" onClick={(e) => { e.preventDefault(); navigate('/'); }}>\n            <Logo size={32} />\n            <span className=\"font-bold text-2xl text-gray-800 ml-2\">TikTodo</span>\n          </a>\n        </div>\n      </div>\n      <nav className=\"flex-grow p-2 space-y-1 custom-scrollbar overflow-y-auto\">\n        {navItems.map((item) => (\n          <a\n            key={item.id}\n            href={item.path || '#'} // Use item.path if available, otherwise '#'\n            onClick={(e) => {\n                e.preventDefault(); // Prevent default link behavior\n                if (item.path) {\n                    handleNavigate(item.path, item.id);\n                } else {\n                    setActiveItem(item.id); // For items without a path\n                }\n            }}\n            className={`flex items-center px-3 py-2.5 rounded-md text-sm font-medium transition-colors duration-150\n              ${\n                activeItem === item.id\n                  ? 'bg-tiktodo-blue-light text-tiktodo-blue'\n                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'\n              }\n            `}\n          >\n            <span className=\"mr-3\">{item.icon}</span>\n            {item.name}\n          </a>\n        ))}\n        \n        {/* 在导航和聊天历史记录之间添加分割线 */}\n        <div className=\"border-t border-gray-200 my-2\"></div>\n        \n        {/* 在首页和聊天页面都显示历史记录 */}\n        {(location.pathname === '/chat' || location.pathname === '/') && (\n          <ChatHistoryList \n            currentChatId={currentChatId}\n            onSelectChat={handleSelectChat}\n          />\n        )}\n      </nav>\n      <div className=\"p-4 border-t border-gray-200\">\n        <button\n          className=\"flex items-center px-3 py-2.5 rounded-md text-sm font-medium text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150 w-full\"\n        >\n          <FaCog size={20} className=\"mr-3\" />\n          设置\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default LeftNav; "], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB,CAAC,CAAC;AAC7D,SACEC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEC,YAAY,EAAEC,KAAK,QACpG,gBAAgB,CAAC,CAAC;AACzB,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,OAAO,GAAGA,CAAC;EAAEC,cAAc;EAAEC,gBAAgB;EAAEC,mBAAmB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC3F,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM4B,QAAQ,GAAG1B,WAAW,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM2B,QAAQ,GAAG1B,WAAW,CAAC,CAAC,CAAC,CAAC;;EAEhC,MAAM2B,QAAQ,GAAG;EACf;EACA;IAAEC,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEhB,OAAA,CAACb,OAAO;MAAC8B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC3D;IAAEP,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEhB,OAAA,CAACZ,gBAAgB;MAAC6B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAI,CAAC;EAAE;EACjF;IAAER,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,eAAEhB,OAAA,CAACV,UAAU;MAAC2B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAQ,CAAC;EAAE;EAC7E;IAAER,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,eAAEhB,OAAA,CAACX,OAAO;MAAC4B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACzD;IAAEP,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEhB,OAAA,CAACT,OAAO;MAAC0B,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC3D;IAAEP,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,eAAEhB,OAAA,CAACR,OAAO;MAACyB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACzD;IAAEP,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEhB,OAAA,CAACP,SAAS;MAACwB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC7D;IAAEP,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEhB,OAAA,CAACN,OAAO;MAACuB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC1D;IAAEP,EAAE,EAAE,GAAG;IAAEC,IAAI,EAAE,GAAG;IAAEC,IAAI,eAAEhB,OAAA,CAACL,YAAY;MAACsB,IAAI,EAAE;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CACzD;;EAED;EACArC,SAAS,CAAC,MAAM;IACd,MAAMuC,WAAW,GAAGX,QAAQ,CAACY,QAAQ;IACrC,MAAMC,WAAW,GAAGZ,QAAQ,CAACa,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACL,IAAI,KAAKC,WAAW,CAAC;IACpE,IAAIE,WAAW,EAAE;MACfjB,aAAa,CAACiB,WAAW,CAACX,EAAE,CAAC;IAC/B,CAAC,MAAM,IAAIS,WAAW,KAAK,GAAG,EAAE;MAC9Bf,aAAa,CAAC,OAAO,CAAC;IACxB;EACF,CAAC,EAAE,CAACI,QAAQ,CAACY,QAAQ,CAAC,CAAC;EAEvB,MAAMI,cAAc,GAAGA,CAACN,IAAI,EAAEO,MAAM,KAAK;IACvCrB,aAAa,CAACqB,MAAM,CAAC;IACrBlB,QAAQ,CAACW,IAAI,CAAC;EAChB,CAAC;EAED,MAAMQ,gBAAgB,GAAIC,MAAM,IAAK;IACnCrB,gBAAgB,CAACqB,MAAM,CAAC;IACxB1B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAG0B,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,KAAK,GAAG,CACZ;IAAElB,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAmB,CAAC,EACnC;IAAED,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAS,CAAC,CAC1B;EAED,oBACEf,OAAA;IAAKiC,SAAS,EAAC,+DAA+D;IAAAC,QAAA,GAE3EhC,cAAc,iBACbF,OAAA;MAAKiC,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAC5ElC,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDlC,OAAA;UAAIiC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAI;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DrB,OAAA;UAAQmC,OAAO,EAAEhC,gBAAiB;UAAC8B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAEjF;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNrB,OAAA;QAAAkC,QAAA,EACGF,KAAK,CAACI,GAAG,CAACC,IAAI,iBACbrC,OAAA;UAAkBiC,SAAS,EAAC,oEAAoE;UAAAC,QAAA,EAC7FG,IAAI,CAACtB;QAAI,GADHsB,IAAI,CAACvB,EAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEZ,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eAGDrB,OAAA;MAAKiC,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eAEnElC,OAAA;QAAKiC,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrClC,OAAA;UAAGsC,IAAI,EAAC,GAAG;UAACL,SAAS,EAAC,mBAAmB;UAACE,OAAO,EAAGI,CAAC,IAAK;YAAEA,CAAC,CAACC,cAAc,CAAC,CAAC;YAAE7B,QAAQ,CAAC,GAAG,CAAC;UAAE,CAAE;UAAAuB,QAAA,gBAC/FlC,OAAA,CAACH,IAAI;YAACoB,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClBrB,OAAA;YAAMiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAO;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNrB,OAAA;MAAKiC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,GACtErB,QAAQ,CAACuB,GAAG,CAAET,IAAI,iBACjB3B,OAAA;QAEEsC,IAAI,EAAEX,IAAI,CAACL,IAAI,IAAI,GAAI,CAAC;QAAA;QACxBa,OAAO,EAAGI,CAAC,IAAK;UACZA,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;UACpB,IAAIb,IAAI,CAACL,IAAI,EAAE;YACXM,cAAc,CAACD,IAAI,CAACL,IAAI,EAAEK,IAAI,CAACb,EAAE,CAAC;UACtC,CAAC,MAAM;YACHN,aAAa,CAACmB,IAAI,CAACb,EAAE,CAAC,CAAC,CAAC;UAC5B;QACJ,CAAE;QACFmB,SAAS,EAAE;AACvB,gBACgB1B,UAAU,KAAKoB,IAAI,CAACb,EAAE,GAClB,yCAAyC,GACzC,qDAAqD;AACzE,aACc;QAAAoB,QAAA,gBAEFlC,OAAA;UAAMiC,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAEP,IAAI,CAACX;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACxCM,IAAI,CAACZ,IAAI;MAAA,GAnBLY,IAAI,CAACb,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBX,CACJ,CAAC,eAGFrB,OAAA;QAAKiC,SAAS,EAAC;MAA+B;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAGpD,CAACT,QAAQ,CAACY,QAAQ,KAAK,OAAO,IAAIZ,QAAQ,CAACY,QAAQ,KAAK,GAAG,kBAC1DxB,OAAA,CAACF,eAAe;QACdW,aAAa,EAAEA,aAAc;QAC7BJ,YAAY,EAAEyB;MAAiB;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNrB,OAAA;MAAKiC,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3ClC,OAAA;QACEiC,SAAS,EAAC,wJAAwJ;QAAAC,QAAA,gBAElKlC,OAAA,CAACJ,KAAK;UAACqB,IAAI,EAAE,EAAG;UAACgB,SAAS,EAAC;QAAM;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEtC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACf,EAAA,CA3HIL,OAAO;EAAA,QAGMhB,WAAW,EACXC,WAAW;AAAA;AAAAuD,EAAA,GAJxBxC,OAAO;AA6Hb,eAAeA,OAAO;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}