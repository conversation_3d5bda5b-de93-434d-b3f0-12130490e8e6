{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\FileUpload.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FileUpload = ({\n  onFileUploaded,\n  onError,\n  className = \"\"\n}) => {\n  _s();\n  const [uploading, setUploading] = useState(false);\n  const [dragOver, setDragOver] = useState(false);\n  const fileInputRef = useRef(null);\n  const supportedTypes = ['image/png', 'image/jpeg', 'image/webp', 'image/gif', 'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/x-aac', 'audio/flac', 'video/mp4', 'video/mpeg', 'video/mov', 'video/avi', 'video/webm', 'text/plain', 'text/csv', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];\n  const maxFileSize = 50 * 1024 * 1024; // 50MB\n\n  const validateFile = file => {\n    if (!file) {\n      return {\n        valid: false,\n        error: '请选择文件'\n      };\n    }\n    if (file.size > maxFileSize) {\n      return {\n        valid: false,\n        error: '文件大小不能超过50MB'\n      };\n    }\n    if (!supportedTypes.includes(file.type)) {\n      return {\n        valid: false,\n        error: `不支持的文件类型: ${file.type}`\n      };\n    }\n    return {\n      valid: true\n    };\n  };\n  const uploadFile = async file => {\n    const validation = validateFile(file);\n    if (!validation.valid) {\n      onError === null || onError === void 0 ? void 0 : onError(validation.error);\n      return;\n    }\n    setUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('session_id', sessionStorage.getItem('chat_session_id') || '');\n      const response = await fetch('/api/upload/file', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '文件上传失败');\n      }\n      const result = await response.json();\n      if (result.success) {\n        onFileUploaded === null || onFileUploaded === void 0 ? void 0 : onFileUploaded(result);\n      } else {\n        throw new Error(result.error || '文件上传失败');\n      }\n    } catch (error) {\n      console.error('文件上传错误:', error);\n      onError === null || onError === void 0 ? void 0 : onError(error.message || '文件上传失败');\n    } finally {\n      setUploading(false);\n    }\n  };\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      uploadFile(file);\n    }\n    // 清空input，允许重复选择同一文件\n    event.target.value = '';\n  };\n  const handleDragOver = event => {\n    event.preventDefault();\n    setDragOver(true);\n  };\n  const handleDragLeave = event => {\n    event.preventDefault();\n    setDragOver(false);\n  };\n  const handleDrop = event => {\n    event.preventDefault();\n    setDragOver(false);\n    const files = event.dataTransfer.files;\n    if (files.length > 0) {\n      uploadFile(files[0]);\n    }\n  };\n  const handleClick = () => {\n    var _fileInputRef$current;\n    (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n  };\n  const getFileIcon = () => {\n    if (uploading) {\n      return /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"w-6 h-6 animate-spin\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: 2,\n          d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-6 h-6\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      ref: fileInputRef,\n      type: \"file\",\n      onChange: handleFileSelect,\n      className: \"hidden\",\n      accept: supportedTypes.join(','),\n      disabled: uploading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleClick,\n      onDragOver: handleDragOver,\n      onDragLeave: handleDragLeave,\n      onDrop: handleDrop,\n      disabled: uploading,\n      className: `\n          flex items-center gap-2 px-3 py-2 rounded-lg border-2 border-dashed\n          transition-all duration-200 ease-in-out\n          ${dragOver ? 'border-blue-400 bg-blue-50 text-blue-600' : 'border-gray-300 hover:border-gray-400 text-gray-600 hover:text-gray-700'}\n          ${uploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-gray-50'}\n        `,\n      title: \"\\u652F\\u6301\\u56FE\\u7247\\u3001\\u97F3\\u9891\\u3001\\u89C6\\u9891\\u3001\\u6587\\u6863\\u7B49\\u683C\\u5F0F\\uFF0C\\u6700\\u592750MB\",\n      children: [getFileIcon(), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-sm font-medium\",\n        children: uploading ? '上传中...' : '附件'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-full left-0 mt-1 text-xs text-gray-500 whitespace-nowrap\",\n      children: \"\\u652F\\u6301\\u56FE\\u7247\\u3001\\u97F3\\u9891\\u3001\\u89C6\\u9891\\u3001\\u6587\\u6863 (\\u6700\\u592750MB)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_s(FileUpload, \"8z4+Tvn4vGUAYPdMB+aw5ClA6bI=\");\n_c = FileUpload;\nexport default FileUpload;\nvar _c;\n$RefreshReg$(_c, \"FileUpload\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "jsxDEV", "_jsxDEV", "FileUpload", "onFileUploaded", "onError", "className", "_s", "uploading", "setUploading", "dragOver", "setDragOver", "fileInputRef", "supportedTypes", "maxFileSize", "validateFile", "file", "valid", "error", "size", "includes", "type", "uploadFile", "validation", "formData", "FormData", "append", "sessionStorage", "getItem", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "result", "success", "console", "message", "handleFileSelect", "event", "target", "files", "value", "handleDragOver", "preventDefault", "handleDragLeave", "handleDrop", "dataTransfer", "length", "handleClick", "_fileInputRef$current", "current", "click", "getFileIcon", "fill", "stroke", "viewBox", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onChange", "accept", "join", "disabled", "onClick", "onDragOver", "onDragLeave", "onDrop", "title", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/FileUpload.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\r\n\r\nconst FileUpload = ({ onFileUploaded, onError, className = \"\" }) => {\r\n  const [uploading, setUploading] = useState(false);\r\n  const [dragOver, setDragOver] = useState(false);\r\n  const fileInputRef = useRef(null);\r\n\r\n  const supportedTypes = [\r\n    'image/png', 'image/jpeg', 'image/webp', 'image/gif',\r\n    'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/x-aac', 'audio/flac',\r\n    'video/mp4', 'video/mpeg', 'video/mov', 'video/avi', 'video/webm',\r\n    'text/plain', 'text/csv', 'application/pdf',\r\n    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\r\n    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n  ];\r\n\r\n  const maxFileSize = 50 * 1024 * 1024; // 50MB\r\n\r\n  const validateFile = (file) => {\r\n    if (!file) {\r\n      return { valid: false, error: '请选择文件' };\r\n    }\r\n\r\n    if (file.size > maxFileSize) {\r\n      return { valid: false, error: '文件大小不能超过50MB' };\r\n    }\r\n\r\n    if (!supportedTypes.includes(file.type)) {\r\n      return { valid: false, error: `不支持的文件类型: ${file.type}` };\r\n    }\r\n\r\n    return { valid: true };\r\n  };\r\n\r\n  const uploadFile = async (file) => {\r\n    const validation = validateFile(file);\r\n    if (!validation.valid) {\r\n      onError?.(validation.error);\r\n      return;\r\n    }\r\n\r\n    setUploading(true);\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      formData.append('session_id', sessionStorage.getItem('chat_session_id') || '');\r\n\r\n      const response = await fetch('/api/upload/file', {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.detail || '文件上传失败');\r\n      }\r\n\r\n      const result = await response.json();\r\n      \r\n      if (result.success) {\r\n        onFileUploaded?.(result);\r\n      } else {\r\n        throw new Error(result.error || '文件上传失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('文件上传错误:', error);\r\n      onError?.(error.message || '文件上传失败');\r\n    } finally {\r\n      setUploading(false);\r\n    }\r\n  };\r\n\r\n  const handleFileSelect = (event) => {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      uploadFile(file);\r\n    }\r\n    // 清空input，允许重复选择同一文件\r\n    event.target.value = '';\r\n  };\r\n\r\n  const handleDragOver = (event) => {\r\n    event.preventDefault();\r\n    setDragOver(true);\r\n  };\r\n\r\n  const handleDragLeave = (event) => {\r\n    event.preventDefault();\r\n    setDragOver(false);\r\n  };\r\n\r\n  const handleDrop = (event) => {\r\n    event.preventDefault();\r\n    setDragOver(false);\r\n    \r\n    const files = event.dataTransfer.files;\r\n    if (files.length > 0) {\r\n      uploadFile(files[0]);\r\n    }\r\n  };\r\n\r\n  const handleClick = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  const getFileIcon = () => {\r\n    if (uploading) {\r\n      return (\r\n        <svg className=\"w-6 h-6 animate-spin\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n        </svg>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\" />\r\n      </svg>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      <input\r\n        ref={fileInputRef}\r\n        type=\"file\"\r\n        onChange={handleFileSelect}\r\n        className=\"hidden\"\r\n        accept={supportedTypes.join(',')}\r\n        disabled={uploading}\r\n      />\r\n      \r\n      <button\r\n        onClick={handleClick}\r\n        onDragOver={handleDragOver}\r\n        onDragLeave={handleDragLeave}\r\n        onDrop={handleDrop}\r\n        disabled={uploading}\r\n        className={`\r\n          flex items-center gap-2 px-3 py-2 rounded-lg border-2 border-dashed\r\n          transition-all duration-200 ease-in-out\r\n          ${dragOver \r\n            ? 'border-blue-400 bg-blue-50 text-blue-600' \r\n            : 'border-gray-300 hover:border-gray-400 text-gray-600 hover:text-gray-700'\r\n          }\r\n          ${uploading \r\n            ? 'opacity-50 cursor-not-allowed' \r\n            : 'cursor-pointer hover:bg-gray-50'\r\n          }\r\n        `}\r\n        title=\"支持图片、音频、视频、文档等格式，最大50MB\"\r\n      >\r\n        {getFileIcon()}\r\n        <span className=\"text-sm font-medium\">\r\n          {uploading ? '上传中...' : '附件'}\r\n        </span>\r\n      </button>\r\n\r\n      {/* 文件类型提示 */}\r\n      <div className=\"absolute top-full left-0 mt-1 text-xs text-gray-500 whitespace-nowrap\">\r\n        支持图片、音频、视频、文档 (最大50MB)\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUpload; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,cAAc;EAAEC,OAAO;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMa,YAAY,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMa,cAAc,GAAG,CACrB,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EACpD,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EACnE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EACjE,YAAY,EAAE,UAAU,EAAE,iBAAiB,EAC3C,oBAAoB,EAAE,yEAAyE,EAC/F,0BAA0B,EAAE,mEAAmE,CAChG;EAED,MAAMC,WAAW,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;EAEtC,MAAMC,YAAY,GAAIC,IAAI,IAAK;IAC7B,IAAI,CAACA,IAAI,EAAE;MACT,OAAO;QAAEC,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAQ,CAAC;IACzC;IAEA,IAAIF,IAAI,CAACG,IAAI,GAAGL,WAAW,EAAE;MAC3B,OAAO;QAAEG,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAe,CAAC;IAChD;IAEA,IAAI,CAACL,cAAc,CAACO,QAAQ,CAACJ,IAAI,CAACK,IAAI,CAAC,EAAE;MACvC,OAAO;QAAEJ,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE,aAAaF,IAAI,CAACK,IAAI;MAAG,CAAC;IAC1D;IAEA,OAAO;MAAEJ,KAAK,EAAE;IAAK,CAAC;EACxB,CAAC;EAED,MAAMK,UAAU,GAAG,MAAON,IAAI,IAAK;IACjC,MAAMO,UAAU,GAAGR,YAAY,CAACC,IAAI,CAAC;IACrC,IAAI,CAACO,UAAU,CAACN,KAAK,EAAE;MACrBZ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGkB,UAAU,CAACL,KAAK,CAAC;MAC3B;IACF;IAEAT,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMe,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEV,IAAI,CAAC;MAC7BQ,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEC,cAAc,CAACC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;MAE9E,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kBAAkB,EAAE;QAC/CC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAER;MACR,CAAC,CAAC;MAEF,IAAI,CAACK,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,QAAQ,CAAC;MAC/C;MAEA,MAAMC,MAAM,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAEpC,IAAIG,MAAM,CAACC,OAAO,EAAE;QAClBnC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGkC,MAAM,CAAC;MAC1B,CAAC,MAAM;QACL,MAAM,IAAIF,KAAK,CAACE,MAAM,CAACpB,KAAK,IAAI,QAAQ,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdsB,OAAO,CAACtB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/Bb,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGa,KAAK,CAACuB,OAAO,IAAI,QAAQ,CAAC;IACtC,CAAC,SAAS;MACRhC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMiC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAM3B,IAAI,GAAG2B,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI7B,IAAI,EAAE;MACRM,UAAU,CAACN,IAAI,CAAC;IAClB;IACA;IACA2B,KAAK,CAACC,MAAM,CAACE,KAAK,GAAG,EAAE;EACzB,CAAC;EAED,MAAMC,cAAc,GAAIJ,KAAK,IAAK;IAChCA,KAAK,CAACK,cAAc,CAAC,CAAC;IACtBrC,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMsC,eAAe,GAAIN,KAAK,IAAK;IACjCA,KAAK,CAACK,cAAc,CAAC,CAAC;IACtBrC,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMuC,UAAU,GAAIP,KAAK,IAAK;IAC5BA,KAAK,CAACK,cAAc,CAAC,CAAC;IACtBrC,WAAW,CAAC,KAAK,CAAC;IAElB,MAAMkC,KAAK,GAAGF,KAAK,CAACQ,YAAY,CAACN,KAAK;IACtC,IAAIA,KAAK,CAACO,MAAM,GAAG,CAAC,EAAE;MACpB9B,UAAU,CAACuB,KAAK,CAAC,CAAC,CAAC,CAAC;IACtB;EACF,CAAC;EAED,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IACxB,CAAAA,qBAAA,GAAA1C,YAAY,CAAC2C,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;EAC/B,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIjD,SAAS,EAAE;MACb,oBACEN,OAAA;QAAKI,SAAS,EAAC,sBAAsB;QAACoD,IAAI,EAAC,MAAM;QAACC,MAAM,EAAC,cAAc;QAACC,OAAO,EAAC,WAAW;QAAAC,QAAA,eACzF3D,OAAA;UAAM4D,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAACC,WAAW,EAAE,CAAE;UAACC,CAAC,EAAC;QAA6G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClL,CAAC;IAEV;IAEA,oBACEnE,OAAA;MAAKI,SAAS,EAAC,SAAS;MAACoD,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC5E3D,OAAA;QAAM4D,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAsH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3L,CAAC;EAEV,CAAC;EAED,oBACEnE,OAAA;IAAKI,SAAS,EAAE,YAAYA,SAAS,EAAG;IAAAuD,QAAA,gBACtC3D,OAAA;MACEoE,GAAG,EAAE1D,YAAa;MAClBS,IAAI,EAAC,MAAM;MACXkD,QAAQ,EAAE7B,gBAAiB;MAC3BpC,SAAS,EAAC,QAAQ;MAClBkE,MAAM,EAAE3D,cAAc,CAAC4D,IAAI,CAAC,GAAG,CAAE;MACjCC,QAAQ,EAAElE;IAAU;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAEFnE,OAAA;MACEyE,OAAO,EAAEtB,WAAY;MACrBuB,UAAU,EAAE7B,cAAe;MAC3B8B,WAAW,EAAE5B,eAAgB;MAC7B6B,MAAM,EAAE5B,UAAW;MACnBwB,QAAQ,EAAElE,SAAU;MACpBF,SAAS,EAAE;AACnB;AACA;AACA,YAAYI,QAAQ,GACN,0CAA0C,GAC1C,yEAAyE;AACvF,YACYF,SAAS,GACP,+BAA+B,GAC/B,iCAAiC;AAC/C,SACU;MACFuE,KAAK,EAAC,wHAAyB;MAAAlB,QAAA,GAE9BJ,WAAW,CAAC,CAAC,eACdvD,OAAA;QAAMI,SAAS,EAAC,qBAAqB;QAAAuD,QAAA,EAClCrD,SAAS,GAAG,QAAQ,GAAG;MAAI;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGTnE,OAAA;MAAKI,SAAS,EAAC,uEAAuE;MAAAuD,QAAA,EAAC;IAEvF;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAnKIJ,UAAU;AAAA6E,EAAA,GAAV7E,UAAU;AAqKhB,eAAeA,UAAU;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}