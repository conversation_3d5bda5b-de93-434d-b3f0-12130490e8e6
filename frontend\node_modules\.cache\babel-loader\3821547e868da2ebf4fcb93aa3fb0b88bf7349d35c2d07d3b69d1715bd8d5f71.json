{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\ChatHistoryList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { FaEllipsisV, FaComment, FaShare, FaEdit, FaArchive, FaTrash } from 'react-icons/fa';\nimport apiService from '../services/api';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatHistoryList = ({\n  currentChatId,\n  onSelectChat,\n  className = \"\"\n}) => {\n  _s();\n  const [chatList, setChatList] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [hoveredChatId, setHoveredChatId] = useState(null);\n  const [dropdownOpen, setDropdownOpen] = useState(null);\n  const dropdownRef = useRef(null);\n  const navigate = useNavigate();\n\n  // 获取聊天历史的函数\n  const fetchChatHistory = async () => {\n    try {\n      setIsLoading(true);\n      // 使用与获取项目历史相同的API，因为它们是同一个数据源\n      const history = await apiService.getProjectsHistory();\n\n      // 格式化API返回的数据\n      const formattedHistory = history.map(item => ({\n        id: item.id,\n        title: item.title || `未命名对话`,\n        lastMessage: item.last_message || '无消息内容',\n        timestamp: item.last_modified || item.created_at,\n        unreadCount: 0,\n        // 暂时不处理未读消息\n        projectType: item.project_type || 'presentation' // 获取项目类型\n      }));\n      setChatList(formattedHistory);\n    } catch (error) {\n      console.error('获取聊天历史失败:', error);\n      setError('加载聊天历史失败');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 页面加载时获取聊天历史\n  useEffect(() => {\n    fetchChatHistory();\n  }, []);\n\n  // 监听刷新事件\n  useEffect(() => {\n    const handleRefresh = () => {\n      fetchChatHistory();\n    };\n    window.addEventListener('refreshChatHistory', handleRefresh);\n    return () => {\n      window.removeEventListener('refreshChatHistory', handleRefresh);\n    };\n  }, []);\n\n  // 点击外部关闭下拉菜单\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setDropdownOpen(null);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n  const formatTimestamp = timestamp => {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));\n    if (diffInHours < 1) {\n      return '刚刚';\n    } else if (diffInHours < 24) {\n      return `${diffInHours}小时前`;\n    } else if (diffInHours < 48) {\n      return '昨天';\n    } else {\n      return date.toLocaleDateString('zh-CN', {\n        month: 'short',\n        day: 'numeric'\n      });\n    }\n  };\n  const truncateText = (text, maxLength = 30) => {\n    if (!text) return '';\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  const handleDropdownAction = async (action, chatId) => {\n    setDropdownOpen(null);\n    switch (action) {\n      case 'share':\n        console.log('分享聊天:', chatId);\n        // 实现分享功能\n        break;\n      case 'rename':\n        console.log('重命名聊天:', chatId);\n        // 实现重命名功能\n        const newTitle = prompt('请输入新的聊天名称:');\n        if (newTitle && newTitle.trim()) {\n          try {\n            await apiService.updateProject(chatId, {\n              title: newTitle.trim()\n            });\n            setChatList(prev => prev.map(chat => chat.id === chatId ? {\n              ...chat,\n              title: newTitle.trim()\n            } : chat));\n          } catch (error) {\n            console.error('重命名失败:', error);\n          }\n        }\n        break;\n      case 'archive':\n        console.log('归档聊天:', chatId);\n        // 实现归档功能 (临时还是删除)\n        try {\n          await apiService.deleteProject(chatId);\n          setChatList(prev => prev.filter(chat => chat.id !== chatId));\n        } catch (error) {\n          console.error('归档失败:', error);\n        }\n        break;\n      case 'delete':\n        console.log('删除聊天:', chatId);\n        // 直接删除，无需确认\n        try {\n          await apiService.deleteProject(chatId);\n          setChatList(prev => prev.filter(chat => chat.id !== chatId));\n        } catch (error) {\n          console.error('删除失败:', error);\n        }\n        break;\n      default:\n        break;\n    }\n  };\n  const handleChatClick = (chatId, projectType, title) => {\n    // 触发父组件的onSelectChat回调\n    onSelectChat === null || onSelectChat === void 0 ? void 0 : onSelectChat(chatId);\n\n    // 根据项目类型导航到对应页面\n    // 首先尝试通过标题检测是否是聊天项目\n    const isChatByTitle = title && (title.toLowerCase().includes('chat') || title.toLowerCase().includes('聊天') || title.toLowerCase().includes('对话'));\n    if (projectType === 'chat' || isChatByTitle) {\n      // 如果是聊天项目，导航到聊天页面并使用正确的查询参数\n      navigate(`/chat?chatId=${chatId}`);\n      // 强制页面刷新以确保数据加载 - 如果当前已在聊天页面\n      if (window.location.pathname === '/chat') {\n        window.location.reload();\n      }\n    } else {\n      // 如果是幻灯片项目，使用projectId参数导航\n      navigate(`/?projectId=${chatId}`);\n      // 强制页面刷新以确保数据加载 - 如果当前已在幻灯片页面\n      if (window.location.pathname === '/') {\n        window.location.reload();\n      }\n    }\n  };\n\n  // 显示加载状态\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `space-y-1 ${className} p-3 text-center`,\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-500\",\n        children: \"\\u52A0\\u8F7D\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 显示错误信息\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `space-y-1 ${className} p-3 text-center`,\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-red-500\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: className,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-1\",\n      children: chatList.map(chat => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `relative group flex items-center px-3 py-3 rounded-md cursor-pointer transition-colors duration-150 ${currentChatId === chat.id ? 'bg-blue-50 border-r-2 border-blue-500' : 'hover:bg-gray-50'}`,\n        onMouseEnter: () => setHoveredChatId(chat.id),\n        onMouseLeave: () => setHoveredChatId(null),\n        onClick: () => handleChatClick(chat.id, chat.projectType, chat.title),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-1\",\n            children: /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: `text-sm font-medium truncate ${currentChatId === chat.id ? 'text-blue-700' : 'text-gray-900'}`,\n              children: truncateText(chat.title)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-600 truncate\",\n            children: truncateText(chat.lastMessage)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), (hoveredChatId === chat.id || dropdownOpen === chat.id) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative ml-2\",\n          ref: dropdownOpen === chat.id ? dropdownRef : null,\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.stopPropagation();\n              setDropdownOpen(dropdownOpen === chat.id ? null : chat.id);\n            },\n            className: \"p-1 text-gray-400 hover:text-gray-600 rounded transition-colors\",\n            title: \"\\u66F4\\u591A\\u64CD\\u4F5C\",\n            children: /*#__PURE__*/_jsxDEV(FaEllipsisV, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 17\n          }, this), dropdownOpen === chat.id && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute right-0 top-6 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50 translate-x-[80px]\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: e => {\n                e.stopPropagation();\n                handleDropdownAction('share', chat.id);\n              },\n              className: \"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(FaShare, {\n                size: 12,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 23\n              }, this), \"\\u5171\\u4EAB\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: e => {\n                e.stopPropagation();\n                handleDropdownAction('rename', chat.id);\n              },\n              className: \"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(FaEdit, {\n                size: 12,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 23\n              }, this), \"\\u91CD\\u547D\\u540D\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: e => {\n                e.stopPropagation();\n                handleDropdownAction('archive', chat.id);\n              },\n              className: \"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(FaArchive, {\n                size: 12,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 23\n              }, this), \"\\u5F52\\u6863\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: e => {\n                e.stopPropagation();\n                handleDropdownAction('delete', chat.id);\n              },\n              className: \"flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\",\n              children: [/*#__PURE__*/_jsxDEV(FaTrash, {\n                size: 12,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 23\n              }, this), \"\\u5220\\u9664\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 15\n        }, this)]\n      }, chat.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), chatList.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: [/*#__PURE__*/_jsxDEV(FaComment, {\n        size: 24,\n        className: \"mx-auto mb-2 opacity-50\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm\",\n        children: \"\\u6682\\u65E0\\u804A\\u5929\\u8BB0\\u5F55\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatHistoryList, \"imyvQ+QRQ5YBr4JACais2TCTRWE=\", false, function () {\n  return [useNavigate];\n});\n_c = ChatHistoryList;\nexport default ChatHistoryList;\nvar _c;\n$RefreshReg$(_c, \"ChatHistoryList\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "FaEllipsisV", "FaComment", "FaShare", "FaEdit", "FaArchive", "FaTrash", "apiService", "useNavigate", "jsxDEV", "_jsxDEV", "ChatHistoryList", "currentChatId", "onSelectChat", "className", "_s", "chatList", "setChatList", "isLoading", "setIsLoading", "error", "setError", "hoveredChatId", "setHoveredChatId", "dropdownOpen", "setDropdownOpen", "dropdownRef", "navigate", "fetchChatHistory", "history", "getProjectsHistory", "formattedHistory", "map", "item", "id", "title", "lastMessage", "last_message", "timestamp", "last_modified", "created_at", "unreadCount", "projectType", "project_type", "console", "handleRefresh", "window", "addEventListener", "removeEventListener", "handleClickOutside", "event", "current", "contains", "target", "document", "formatTimestamp", "date", "Date", "now", "diffInHours", "Math", "floor", "toLocaleDateString", "month", "day", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "length", "substring", "handleDropdownAction", "action", "chatId", "log", "newTitle", "prompt", "trim", "updateProject", "prev", "chat", "deleteProject", "filter", "handleChatClick", "isChatByTitle", "toLowerCase", "includes", "location", "pathname", "reload", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMouseEnter", "onMouseLeave", "onClick", "ref", "e", "stopPropagation", "size", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/ChatHistoryList.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { FaEllipsisV, FaComment, FaShare, FaEdit, FaArchive, FaTrash } from 'react-icons/fa';\nimport apiService from '../services/api';\nimport { useNavigate } from 'react-router-dom';\n\nconst ChatHistoryList = ({ currentChatId, onSelectChat, className = \"\" }) => {\n  const [chatList, setChatList] = useState([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  \n  const [hoveredChatId, setHoveredChatId] = useState(null);\n  const [dropdownOpen, setDropdownOpen] = useState(null);\n  const dropdownRef = useRef(null);\n\n  const navigate = useNavigate();\n\n  // 获取聊天历史的函数\n  const fetchChatHistory = async () => {\n    try {\n      setIsLoading(true);\n      // 使用与获取项目历史相同的API，因为它们是同一个数据源\n      const history = await apiService.getProjectsHistory();\n      \n      // 格式化API返回的数据\n      const formattedHistory = history.map(item => ({\n        id: item.id,\n        title: item.title || `未命名对话`,\n        lastMessage: item.last_message || '无消息内容',\n        timestamp: item.last_modified || item.created_at,\n        unreadCount: 0, // 暂时不处理未读消息\n        projectType: item.project_type || 'presentation' // 获取项目类型\n      }));\n      \n      setChatList(formattedHistory);\n    } catch (error) {\n      console.error('获取聊天历史失败:', error);\n      setError('加载聊天历史失败');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 页面加载时获取聊天历史\n  useEffect(() => {\n    fetchChatHistory();\n  }, []);\n\n  // 监听刷新事件\n  useEffect(() => {\n    const handleRefresh = () => {\n      fetchChatHistory();\n    };\n\n    window.addEventListener('refreshChatHistory', handleRefresh);\n    return () => {\n      window.removeEventListener('refreshChatHistory', handleRefresh);\n    };\n  }, []);\n\n  // 点击外部关闭下拉菜单\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setDropdownOpen(null);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const formatTimestamp = (timestamp) => {\n    if (!timestamp) return '';\n    \n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));\n    \n    if (diffInHours < 1) {\n      return '刚刚';\n    } else if (diffInHours < 24) {\n      return `${diffInHours}小时前`;\n    } else if (diffInHours < 48) {\n      return '昨天';\n    } else {\n      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });\n    }\n  };\n\n  const truncateText = (text, maxLength = 30) => {\n    if (!text) return '';\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n\n  const handleDropdownAction = async (action, chatId) => {\n    setDropdownOpen(null);\n    \n    switch (action) {\n      case 'share':\n        console.log('分享聊天:', chatId);\n        // 实现分享功能\n        break;\n      case 'rename':\n        console.log('重命名聊天:', chatId);\n        // 实现重命名功能\n        const newTitle = prompt('请输入新的聊天名称:');\n        if (newTitle && newTitle.trim()) {\n          try {\n            await apiService.updateProject(chatId, { title: newTitle.trim() });\n            setChatList(prev => prev.map(chat => \n              chat.id === chatId ? { ...chat, title: newTitle.trim() } : chat\n            ));\n          } catch (error) {\n            console.error('重命名失败:', error);\n          }\n        }\n        break;\n      case 'archive':\n        console.log('归档聊天:', chatId);\n        // 实现归档功能 (临时还是删除)\n        try {\n          await apiService.deleteProject(chatId);\n          setChatList(prev => prev.filter(chat => chat.id !== chatId));\n        } catch (error) {\n          console.error('归档失败:', error);\n        }\n        break;\n      case 'delete':\n        console.log('删除聊天:', chatId);\n        // 直接删除，无需确认\n        try {\n          await apiService.deleteProject(chatId);\n          setChatList(prev => prev.filter(chat => chat.id !== chatId));\n        } catch (error) {\n          console.error('删除失败:', error);\n        }\n        break;\n      default:\n        break;\n    }\n  };\n\n  const handleChatClick = (chatId, projectType, title) => {\n    // 触发父组件的onSelectChat回调\n    onSelectChat?.(chatId);\n    \n    // 根据项目类型导航到对应页面\n    // 首先尝试通过标题检测是否是聊天项目\n    const isChatByTitle = title && (\n      title.toLowerCase().includes('chat') || \n      title.toLowerCase().includes('聊天') ||\n      title.toLowerCase().includes('对话')\n    );\n    \n    if (projectType === 'chat' || isChatByTitle) {\n      // 如果是聊天项目，导航到聊天页面并使用正确的查询参数\n      navigate(`/chat?chatId=${chatId}`);\n      // 强制页面刷新以确保数据加载 - 如果当前已在聊天页面\n      if (window.location.pathname === '/chat') {\n        window.location.reload();\n      }\n    } else {\n      // 如果是幻灯片项目，使用projectId参数导航\n      navigate(`/?projectId=${chatId}`);\n      // 强制页面刷新以确保数据加载 - 如果当前已在幻灯片页面\n      if (window.location.pathname === '/') {\n        window.location.reload();\n      }\n    }\n  };\n\n  // 显示加载状态\n  if (isLoading) {\n    return (\n      <div className={`space-y-1 ${className} p-3 text-center`}>\n        <p className=\"text-sm text-gray-500\">加载中...</p>\n      </div>\n    );\n  }\n\n  // 显示错误信息\n  if (error) {\n    return (\n      <div className={`space-y-1 ${className} p-3 text-center`}>\n        <p className=\"text-sm text-red-500\">{error}</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className={className}>\n      {/* 聊天列表 */}\n      <div className=\"space-y-1\">\n        {chatList.map((chat) => (\n          <div\n            key={chat.id}\n            className={`relative group flex items-center px-3 py-3 rounded-md cursor-pointer transition-colors duration-150 ${\n              currentChatId === chat.id\n                ? 'bg-blue-50 border-r-2 border-blue-500'\n                : 'hover:bg-gray-50'\n            }`}\n            onMouseEnter={() => setHoveredChatId(chat.id)}\n            onMouseLeave={() => setHoveredChatId(null)}\n            onClick={() => handleChatClick(chat.id, chat.projectType, chat.title)}\n          >\n            {/* 聊天内容 */}\n            <div className=\"flex-1 min-w-0\">\n              <div className=\"flex items-center justify-between mb-1\">\n                <h4 className={`text-sm font-medium truncate ${\n                  currentChatId === chat.id ? 'text-blue-700' : 'text-gray-900'\n                }`}>\n                  {truncateText(chat.title)}\n                </h4>\n              </div>\n              \n              <p className=\"text-xs text-gray-600 truncate\">\n                {truncateText(chat.lastMessage)}\n              </p>\n            </div>\n\n            {/* 三点菜单按钮 */}\n            {(hoveredChatId === chat.id || dropdownOpen === chat.id) && (\n              <div className=\"relative ml-2\" ref={dropdownOpen === chat.id ? dropdownRef : null}>\n                <button\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    setDropdownOpen(dropdownOpen === chat.id ? null : chat.id);\n                  }}\n                  className=\"p-1 text-gray-400 hover:text-gray-600 rounded transition-colors\"\n                  title=\"更多操作\"\n                >\n                  <FaEllipsisV size={12} />\n                </button>\n\n                {/* 下拉菜单 */}\n                {dropdownOpen === chat.id && (\n                  <div className=\"absolute right-0 top-6 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50 translate-x-[80px]\">\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleDropdownAction('share', chat.id);\n                      }}\n                      className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      <FaShare size={12} className=\"mr-2\" />\n                      共享\n                    </button>\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleDropdownAction('rename', chat.id);\n                      }}\n                      className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      <FaEdit size={12} className=\"mr-2\" />\n                      重命名\n                    </button>\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleDropdownAction('archive', chat.id);\n                      }}\n                      className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      <FaArchive size={12} className=\"mr-2\" />\n                      归档\n                    </button>\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleDropdownAction('delete', chat.id);\n                      }}\n                      className=\"flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\"\n                    >\n                      <FaTrash size={12} className=\"mr-2\" />\n                      删除\n                    </button>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* 空状态 */}\n      {chatList.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <FaComment size={24} className=\"mx-auto mb-2 opacity-50\" />\n          <p className=\"text-sm\">暂无聊天记录</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ChatHistoryList; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,gBAAgB;AAC5F,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,eAAe,GAAGA,CAAC;EAAEC,aAAa;EAAEC,YAAY;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM4B,WAAW,GAAG3B,MAAM,CAAC,IAAI,CAAC;EAEhC,MAAM4B,QAAQ,GAAGnB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMoB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFT,YAAY,CAAC,IAAI,CAAC;MAClB;MACA,MAAMU,OAAO,GAAG,MAAMtB,UAAU,CAACuB,kBAAkB,CAAC,CAAC;;MAErD;MACA,MAAMC,gBAAgB,GAAGF,OAAO,CAACG,GAAG,CAACC,IAAI,KAAK;QAC5CC,EAAE,EAAED,IAAI,CAACC,EAAE;QACXC,KAAK,EAAEF,IAAI,CAACE,KAAK,IAAI,OAAO;QAC5BC,WAAW,EAAEH,IAAI,CAACI,YAAY,IAAI,OAAO;QACzCC,SAAS,EAAEL,IAAI,CAACM,aAAa,IAAIN,IAAI,CAACO,UAAU;QAChDC,WAAW,EAAE,CAAC;QAAE;QAChBC,WAAW,EAAET,IAAI,CAACU,YAAY,IAAI,cAAc,CAAC;MACnD,CAAC,CAAC,CAAC;MAEH1B,WAAW,CAACc,gBAAgB,CAAC;IAC/B,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCC,QAAQ,CAAC,UAAU,CAAC;IACtB,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACAnB,SAAS,CAAC,MAAM;IACd4B,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5B,SAAS,CAAC,MAAM;IACd,MAAM6C,aAAa,GAAGA,CAAA,KAAM;MAC1BjB,gBAAgB,CAAC,CAAC;IACpB,CAAC;IAEDkB,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAEF,aAAa,CAAC;IAC5D,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,oBAAoB,EAAEH,aAAa,CAAC;IACjE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7C,SAAS,CAAC,MAAM;IACd,MAAMiD,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIxB,WAAW,CAACyB,OAAO,IAAI,CAACzB,WAAW,CAACyB,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtE5B,eAAe,CAAC,IAAI,CAAC;MACvB;IACF,CAAC;IAED6B,QAAQ,CAACP,gBAAgB,CAAC,WAAW,EAAEE,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXK,QAAQ,CAACN,mBAAmB,CAAC,WAAW,EAAEC,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,eAAe,GAAIjB,SAAS,IAAK;IACrC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMkB,IAAI,GAAG,IAAIC,IAAI,CAACnB,SAAS,CAAC;IAChC,MAAMoB,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,GAAG,GAAGF,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE/D,IAAIG,WAAW,GAAG,CAAC,EAAE;MACnB,OAAO,IAAI;IACb,CAAC,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAO,GAAGA,WAAW,KAAK;IAC5B,CAAC,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAOH,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAU,CAAC,CAAC;IAC7E;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEC,SAAS,GAAG,EAAE,KAAK;IAC7C,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;IACpB,IAAIA,IAAI,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,IAAI;IACzC,OAAOA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;EAED,MAAMG,oBAAoB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,MAAM,KAAK;IACrD/C,eAAe,CAAC,IAAI,CAAC;IAErB,QAAQ8C,MAAM;MACZ,KAAK,OAAO;QACV3B,OAAO,CAAC6B,GAAG,CAAC,OAAO,EAAED,MAAM,CAAC;QAC5B;QACA;MACF,KAAK,QAAQ;QACX5B,OAAO,CAAC6B,GAAG,CAAC,QAAQ,EAAED,MAAM,CAAC;QAC7B;QACA,MAAME,QAAQ,GAAGC,MAAM,CAAC,YAAY,CAAC;QACrC,IAAID,QAAQ,IAAIA,QAAQ,CAACE,IAAI,CAAC,CAAC,EAAE;UAC/B,IAAI;YACF,MAAMrE,UAAU,CAACsE,aAAa,CAACL,MAAM,EAAE;cAAErC,KAAK,EAAEuC,QAAQ,CAACE,IAAI,CAAC;YAAE,CAAC,CAAC;YAClE3D,WAAW,CAAC6D,IAAI,IAAIA,IAAI,CAAC9C,GAAG,CAAC+C,IAAI,IAC/BA,IAAI,CAAC7C,EAAE,KAAKsC,MAAM,GAAG;cAAE,GAAGO,IAAI;cAAE5C,KAAK,EAAEuC,QAAQ,CAACE,IAAI,CAAC;YAAE,CAAC,GAAGG,IAC7D,CAAC,CAAC;UACJ,CAAC,CAAC,OAAO3D,KAAK,EAAE;YACdwB,OAAO,CAACxB,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;UAChC;QACF;QACA;MACF,KAAK,SAAS;QACZwB,OAAO,CAAC6B,GAAG,CAAC,OAAO,EAAED,MAAM,CAAC;QAC5B;QACA,IAAI;UACF,MAAMjE,UAAU,CAACyE,aAAa,CAACR,MAAM,CAAC;UACtCvD,WAAW,CAAC6D,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACF,IAAI,IAAIA,IAAI,CAAC7C,EAAE,KAAKsC,MAAM,CAAC,CAAC;QAC9D,CAAC,CAAC,OAAOpD,KAAK,EAAE;UACdwB,OAAO,CAACxB,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC/B;QACA;MACF,KAAK,QAAQ;QACXwB,OAAO,CAAC6B,GAAG,CAAC,OAAO,EAAED,MAAM,CAAC;QAC5B;QACA,IAAI;UACF,MAAMjE,UAAU,CAACyE,aAAa,CAACR,MAAM,CAAC;UACtCvD,WAAW,CAAC6D,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACF,IAAI,IAAIA,IAAI,CAAC7C,EAAE,KAAKsC,MAAM,CAAC,CAAC;QAC9D,CAAC,CAAC,OAAOpD,KAAK,EAAE;UACdwB,OAAO,CAACxB,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC/B;QACA;MACF;QACE;IACJ;EACF,CAAC;EAED,MAAM8D,eAAe,GAAGA,CAACV,MAAM,EAAE9B,WAAW,EAAEP,KAAK,KAAK;IACtD;IACAtB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAG2D,MAAM,CAAC;;IAEtB;IACA;IACA,MAAMW,aAAa,GAAGhD,KAAK,KACzBA,KAAK,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,IACpClD,KAAK,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,IAClClD,KAAK,CAACiD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,CACnC;IAED,IAAI3C,WAAW,KAAK,MAAM,IAAIyC,aAAa,EAAE;MAC3C;MACAxD,QAAQ,CAAC,gBAAgB6C,MAAM,EAAE,CAAC;MAClC;MACA,IAAI1B,MAAM,CAACwC,QAAQ,CAACC,QAAQ,KAAK,OAAO,EAAE;QACxCzC,MAAM,CAACwC,QAAQ,CAACE,MAAM,CAAC,CAAC;MAC1B;IACF,CAAC,MAAM;MACL;MACA7D,QAAQ,CAAC,eAAe6C,MAAM,EAAE,CAAC;MACjC;MACA,IAAI1B,MAAM,CAACwC,QAAQ,CAACC,QAAQ,KAAK,GAAG,EAAE;QACpCzC,MAAM,CAACwC,QAAQ,CAACE,MAAM,CAAC,CAAC;MAC1B;IACF;EACF,CAAC;;EAED;EACA,IAAItE,SAAS,EAAE;IACb,oBACER,OAAA;MAAKI,SAAS,EAAE,aAAaA,SAAS,kBAAmB;MAAA2E,QAAA,eACvD/E,OAAA;QAAGI,SAAS,EAAC,uBAAuB;QAAA2E,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC;EAEV;;EAEA;EACA,IAAIzE,KAAK,EAAE;IACT,oBACEV,OAAA;MAAKI,SAAS,EAAE,aAAaA,SAAS,kBAAmB;MAAA2E,QAAA,eACvD/E,OAAA;QAAGI,SAAS,EAAC,sBAAsB;QAAA2E,QAAA,EAAErE;MAAK;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC;EAEV;EAEA,oBACEnF,OAAA;IAAKI,SAAS,EAAEA,SAAU;IAAA2E,QAAA,gBAExB/E,OAAA;MAAKI,SAAS,EAAC,WAAW;MAAA2E,QAAA,EACvBzE,QAAQ,CAACgB,GAAG,CAAE+C,IAAI,iBACjBrE,OAAA;QAEEI,SAAS,EAAE,uGACTF,aAAa,KAAKmE,IAAI,CAAC7C,EAAE,GACrB,uCAAuC,GACvC,kBAAkB,EACrB;QACH4D,YAAY,EAAEA,CAAA,KAAMvE,gBAAgB,CAACwD,IAAI,CAAC7C,EAAE,CAAE;QAC9C6D,YAAY,EAAEA,CAAA,KAAMxE,gBAAgB,CAAC,IAAI,CAAE;QAC3CyE,OAAO,EAAEA,CAAA,KAAMd,eAAe,CAACH,IAAI,CAAC7C,EAAE,EAAE6C,IAAI,CAACrC,WAAW,EAAEqC,IAAI,CAAC5C,KAAK,CAAE;QAAAsD,QAAA,gBAGtE/E,OAAA;UAAKI,SAAS,EAAC,gBAAgB;UAAA2E,QAAA,gBAC7B/E,OAAA;YAAKI,SAAS,EAAC,wCAAwC;YAAA2E,QAAA,eACrD/E,OAAA;cAAII,SAAS,EAAE,gCACbF,aAAa,KAAKmE,IAAI,CAAC7C,EAAE,GAAG,eAAe,GAAG,eAAe,EAC5D;cAAAuD,QAAA,EACAxB,YAAY,CAACc,IAAI,CAAC5C,KAAK;YAAC;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENnF,OAAA;YAAGI,SAAS,EAAC,gCAAgC;YAAA2E,QAAA,EAC1CxB,YAAY,CAACc,IAAI,CAAC3C,WAAW;UAAC;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGL,CAACvE,aAAa,KAAKyD,IAAI,CAAC7C,EAAE,IAAIV,YAAY,KAAKuD,IAAI,CAAC7C,EAAE,kBACrDxB,OAAA;UAAKI,SAAS,EAAC,eAAe;UAACmF,GAAG,EAAEzE,YAAY,KAAKuD,IAAI,CAAC7C,EAAE,GAAGR,WAAW,GAAG,IAAK;UAAA+D,QAAA,gBAChF/E,OAAA;YACEsF,OAAO,EAAGE,CAAC,IAAK;cACdA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnB1E,eAAe,CAACD,YAAY,KAAKuD,IAAI,CAAC7C,EAAE,GAAG,IAAI,GAAG6C,IAAI,CAAC7C,EAAE,CAAC;YAC5D,CAAE;YACFpB,SAAS,EAAC,iEAAiE;YAC3EqB,KAAK,EAAC,0BAAM;YAAAsD,QAAA,eAEZ/E,OAAA,CAACT,WAAW;cAACmG,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EAGRrE,YAAY,KAAKuD,IAAI,CAAC7C,EAAE,iBACvBxB,OAAA;YAAKI,SAAS,EAAC,+GAA+G;YAAA2E,QAAA,gBAC5H/E,OAAA;cACEsF,OAAO,EAAGE,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB7B,oBAAoB,CAAC,OAAO,EAAES,IAAI,CAAC7C,EAAE,CAAC;cACxC,CAAE;cACFpB,SAAS,EAAC,4EAA4E;cAAA2E,QAAA,gBAEtF/E,OAAA,CAACP,OAAO;gBAACiG,IAAI,EAAE,EAAG;gBAACtF,SAAS,EAAC;cAAM;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnF,OAAA;cACEsF,OAAO,EAAGE,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB7B,oBAAoB,CAAC,QAAQ,EAAES,IAAI,CAAC7C,EAAE,CAAC;cACzC,CAAE;cACFpB,SAAS,EAAC,4EAA4E;cAAA2E,QAAA,gBAEtF/E,OAAA,CAACN,MAAM;gBAACgG,IAAI,EAAE,EAAG;gBAACtF,SAAS,EAAC;cAAM;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnF,OAAA;cACEsF,OAAO,EAAGE,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB7B,oBAAoB,CAAC,SAAS,EAAES,IAAI,CAAC7C,EAAE,CAAC;cAC1C,CAAE;cACFpB,SAAS,EAAC,4EAA4E;cAAA2E,QAAA,gBAEtF/E,OAAA,CAACL,SAAS;gBAAC+F,IAAI,EAAE,EAAG;gBAACtF,SAAS,EAAC;cAAM;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnF,OAAA;cACEsF,OAAO,EAAGE,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB7B,oBAAoB,CAAC,QAAQ,EAAES,IAAI,CAAC7C,EAAE,CAAC;cACzC,CAAE;cACFpB,SAAS,EAAC,yEAAyE;cAAA2E,QAAA,gBAEnF/E,OAAA,CAACJ,OAAO;gBAAC8F,IAAI,EAAE,EAAG;gBAACtF,SAAS,EAAC;cAAM;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA,GArFId,IAAI,CAAC7C,EAAE;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsFT,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL7E,QAAQ,CAACoD,MAAM,KAAK,CAAC,iBACpB1D,OAAA;MAAKI,SAAS,EAAC,gCAAgC;MAAA2E,QAAA,gBAC7C/E,OAAA,CAACR,SAAS;QAACkG,IAAI,EAAE,EAAG;QAACtF,SAAS,EAAC;MAAyB;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DnF,OAAA;QAAGI,SAAS,EAAC,SAAS;QAAA2E,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9E,EAAA,CApSIJ,eAAe;EAAA,QASFH,WAAW;AAAA;AAAA6F,EAAA,GATxB1F,eAAe;AAsSrB,eAAeA,eAAe;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}