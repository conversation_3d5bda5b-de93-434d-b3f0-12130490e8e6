{"version": 3, "file": "static/css/main.5344de24.css", "mappings": "yGAEA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc;;AAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CACd,iCAAoB,CAApB,qBAAoB,CAApB,+DAAoB,CAApB,0BAAoB,EAApB,+DAAoB,CAApB,0BAAoB,EAApB,iEAAoB,CAApB,2BAAoB,EAApB,iEAAoB,CAApB,2BAAoB,EAApB,iEAAoB,CAApB,2BAAoB,EACpB,wCAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,+BAAmB,CAAnB,eAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,OAAmB,CAAnB,kBAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,cAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,gBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,sBAAmB,CAAnB,YAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,wCAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,kDAAmB,CAAnB,mBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,+BAAmB,CAAnB,2BAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,wBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,0BAAmB,CAAnB,gCAAmB,CAAnB,6BAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,wCAAmB,CAAnB,mOAAmB,CAAnB,yCAAmB,CAAnB,wCAAmB,CAAnB,gOAAmB,CAAnB,sCAAmB,CAAnB,wMAAmB,CAAnB,+BAAmB,EAAnB,kEAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,sCAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,yCAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,8GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,+CAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,+CAAmB,CAAnB,qCAAmB,CAAnB,yCAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,8EAAmB,CAAnB,0CAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,4CAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,kDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,mBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,sCAAmB,CAAnB,qBAAmB,CAAnB,oCAAmB,CAAnB,oBAAmB,CAAnB,mDAAmB,CAAnB,8CAAmB,CAAnB,4CAAmB,CAAnB,mDAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,sDAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,8GAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,2BAAmB,CAAnB,aAAmB,CAAnB,qCAAmB,CAAnB,mCAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,yBAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,sCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,iDAAmB,CAAnB,8BAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,kEAAmB,CAAnB,4FAAmB,CAAnB,mEAAmB,CAAnB,kGAAmB,CAAnB,mDAAmB,CAAnB,4DAAmB,CAAnB,qDAAmB,CAAnB,4DAAmB,CAAnB,wEAAmB,CAAnB,kGAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,2EAAmB,CAAnB,kGAAmB,CAAnB,qCAAmB,CAAnB,kBAAmB,CAAnB,kHAAmB,CAAnB,wGAAmB,CAAnB,uEAAmB,CAAnB,wFAAmB,CAAnB,kCAAmB,CAAnB,wDAAmB,CAAnB,wLAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,6IAAmB,CAAnB,yFAAmB,CAAnB,uHAAmB,CAAnB,kDAAmB,CAAnB,wEAAmB,CAAnB,kDAAmB,CAAnB,0EAAmB,CAAnB,kDAAmB,CAAnB,4EAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,+DAAmB,CAEnB,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CACzB,aAAc,CANd,gJAEY,CAHZ,QAAS,CAST,iBAAkB,CADlB,SAEF,CAGA,oBACE,sBAAwB,CACxB,iBACF,CAEA,EACE,iCAAmC,CACnC,8BACF,CAGA,qCACE,YAAa,CAEb,QAAS,CADT,OAEF,CACA,2CACE,gBACF,CACA,2CACE,gBAAuB,CACvB,eACF,CACA,iDACE,gBACF,CAGA,eACE,qBAAyB,CAEzB,+BAAgC,CAGhC,6BAA8B,CAJ9B,iBAAkB,CAKlB,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,YACF,CAEA,sCAPE,kBAAmB,CADnB,YAWF,CAEA,oBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,eACF,CAEA,iBAEE,aAAc,CADd,cAAe,CAEf,eACF,CAEA,gBAGE,QACF,CAEA,+BAJE,kBAAmB,CADnB,YAgBF,CAXA,eAIE,qBAAsB,CAEtB,wBAAyB,CACzB,iBAAkB,CAFlB,aAAc,CAGd,cAAe,CACf,cAAe,CANf,gBAAiB,CAOjB,wDACF,CAEA,qBACE,wBAAyB,CACzB,gCACF,CAEA,iBAEE,cAAe,CADf,gBAEF,CAEA,UAEE,oBAAqB,CADrB,iBAEF,CAEA,kBAGE,qBAAyB,CAIzB,iBAAkB,CAFlB,6BAA4C,CAJ5C,YAAa,CAGb,eAAgB,CAIhB,aAAc,CANd,iBAAkB,CAOlB,OAAQ,CAHR,YAIF,CAEA,oBACE,aAAc,CAGd,aAAc,CACd,cAAe,CAHf,iBAAkB,CAClB,oBAGF,CAEA,0BACE,wBACF,CAEA,kCACE,aACF,CAEA,qBAIE,kBAAmB,CAEnB,wBAAyB,CAJzB,YAAa,CACb,qBAAsB,CAEtB,QAAS,CAJT,YAMF,CAEA,eAKE,qBAAuB,CAFvB,wBAAyB,CACzB,+BAAsC,CAFtC,aAAc,CAId,iBAAkB,CALlB,YAMF,CAGA,0BACE,eAEE,gBAAiB,CADjB,UAEF,CACF,CAEA,qBAYE,kBAAmB,CAPnB,wBAAyB,CAGzB,iBAAkB,CAClB,2BAAsC,CAHtC,UAAY,CAKZ,YAAa,CAGb,cAAe,CADf,OAAQ,CAVR,QAAS,CAYT,eAAgB,CARhB,gBAAiB,CANjB,cAAe,CACf,QAAS,CAET,0BAA2B,CAM3B,YAMF,CAtLA,mDAsLE,CAtLF,oBAsLE,CAtLF,wDAsLE,CAtLF,2CAsLE,CAtLF,wBAsLE,CAtLF,sDAsLE,CAtLF,2CAsLE,CAtLF,wBAsLE,CAtLF,sDAsLE,CAtLF,2CAsLE,CAtLF,wBAsLE,CAtLF,wDAsLE,CAtLF,2CAsLE,CAtLF,wBAsLE,CAtLF,wDAsLE,CAtLF,2CAsLE,CAtLF,wBAsLE,CAtLF,wDAsLE,CAtLF,0CAsLE,CAtLF,wBAsLE,CAtLF,wDAsLE,CAtLF,2CAsLE,CAtLF,wBAsLE,CAtLF,qDAsLE,CAtLF,yCAsLE,CAtLF,wBAsLE,CAtLF,wDAsLE,CAtLF,8CAsLE,CAtLF,+CAsLE,CAtLF,+CAsLE,CAtLF,aAsLE,CAtLF,8CAsLE,CAtLF,+CAsLE,CAtLF,aAsLE,CAtLF,4CAsLE,CAtLF,+CAsLE,CAtLF,aAsLE,CAtLF,4CAsLE,CAtLF,+CAsLE,CAtLF,aAsLE,CAtLF,4CAsLE,CAtLF,8CAsLE,CAtLF,aAsLE,CAtLF,6CAsLE,CAtLF,8CAsLE,CAtLF,aAsLE,CAtLF,6CAsLE,CAtLF,mDAsLE,CAtLF,aAsLE,CAtLF,6CAsLE,CAtLF,8DAsLE,CAtLF,8BAsLE,CAtLF,qFAsLE,CAtLF,+FAsLE,CAtLF,+CAsLE,CAtLF,kGAsLE,CAtLF,mDAsLE,CAtLF,oBAsLE,CAtLF,uDAsLE,CAtLF,uDAsLE,CAtLF,oBAsLE,CAtLF,sDAsLE,CAtLF,kDAsLE,CAtLF,kBAsLE,CAtLF,+HAsLE,CAtLF,kGAsLE,CAtLF,iHAsLE,CAtLF,wFAsLE,CAtLF,+HAsLE,CAtLF,wGAsLE,CAtLF,+CAsLE,CAtLF,yDAsLE,CAtLF,mDAsLE,CAtLF,uDAsLE,CAtLF,iDAsLE,CAtLF,wBAsLE,CAtLF,wDAsLE,CAtLF,yCAsLE,CAtLF,qDAsLE,CAtLF,uBAsLE,CAtLF,qBAsLE", "sources": ["index.css"], "sourcesContent": ["@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');\n\n@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\nbody {\n  margin: 0;\n  font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #F3F4F6; /* 类似 tiktodo-gray-extralight */\n  color: #1F2937; /* 类似 tiktodo-gray-text */\n  padding: 0;\n  overflow-x: hidden;\n}\n\n/* Hide scrollbars for all elements */\n::-webkit-scrollbar {\n  display: none !important;\n  width: 0 !important;\n}\n\n* {\n  -ms-overflow-style: none !important;  /* IE and Edge */\n  scrollbar-width: none !important;  /* Firefox */\n}\n\n/* You can add custom global styles here if needed */\n.custom-scrollbar::-webkit-scrollbar {\n  display: none;\n  width: 0;\n  height: 0;\n}\n.custom-scrollbar::-webkit-scrollbar-track {\n  background: transparent;\n}\n.custom-scrollbar::-webkit-scrollbar-thumb {\n  background: transparent;\n  border-radius: 0;\n}\n.custom-scrollbar::-webkit-scrollbar-thumb:hover {\n  background: transparent;\n}\n\n/* Viewer styles from viewer.html */\n.viewer-header {\n  background-color: #ffffff;\n  padding: 12px 24px;\n  border-bottom: 1px solid #e5e7eb;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.project-title-section {\n  display: flex;\n  align-items: center;\n}\n\n.project-title-text {\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n  margin-left: 8px;\n}\n\n.page-count-text {\n  font-size: 14px;\n  color: #6b7280;\n  margin-left: 8px;\n}\n\n.action-buttons {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.action-button {\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  background-color: #fff;\n  color: #374151;\n  border: 1px solid #d1d5db;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  transition: background-color 0.2s ease, box-shadow 0.2s ease;\n}\n\n.action-button:hover {\n  background-color: #f3f4f6;\n  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n}\n\n.action-button i {\n  margin-right: 6px;\n  font-size: 14px; /* Ensure icons are consistently sized */\n}\n\n.dropdown {\n  position: relative;\n  display: inline-block;\n}\n\n.dropdown-content {\n  display: none;\n  position: absolute;\n  background-color: #ffffff;\n  min-width: 180px;\n  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n  z-index: 1001;\n  border-radius: 6px;\n  padding: 8px 0;\n  right: 0;\n}\n\n.dropdown-content a {\n  color: #374151;\n  padding: 10px 16px;\n  text-decoration: none;\n  display: block;\n  font-size: 14px;\n}\n\n.dropdown-content a:hover {\n  background-color: #f3f4f6;\n}\n\n.dropdown:hover .dropdown-content {\n  display: block;\n}\n\n.slides-display-area {\n  padding: 24px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 24px;\n  background-color: #f0f2f5;\n}\n\n.slide-wrapper {\n  width: 1280px;\n  margin: 0 auto;\n  border: 1px solid #e0e0e0;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  background-color: white;\n  position: relative;\n}\n\n/* Responsive adjustments */\n@media (max-width: 1280px) {\n  .slide-wrapper {\n    width: 100%;\n    max-width: 1280px;\n  }\n}\n\n.export-notification {\n  position: fixed;\n  top: 65px;\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: #374151;\n  color: white;\n  padding: 8px 16px;\n  border-radius: 6px;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.2);\n  z-index: 1000;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 13px;\n  line-height: 1.4;\n} "], "names": [], "sourceRoot": ""}