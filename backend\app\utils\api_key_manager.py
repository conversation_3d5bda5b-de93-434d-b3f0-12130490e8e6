# backend/app/utils/api_key_manager.py
import asyncio
import time
from collections import deque
import logging
from typing import List, Dict
from app.core.config import settings

logger = logging.getLogger(__name__)

class ApiKeyManager:
    """
    一个异步API Key管理器，实现了轮询和精细化的速率限制。
    确保每个独立的Key都遵守其自己的速率限制 (每秒和每分钟)。
    """
    def __init__(self, keys: List[str], requests_per_key_per_minute: int, requests_per_key_per_second: int):
        if not keys:
            raise ValueError("API Key list cannot be empty.")
        
        self.keys = keys
        self.num_keys = len(keys)
        self.requests_per_key_per_minute = requests_per_key_per_minute
        self.min_interval_sec = 1.0 / requests_per_key_per_second if requests_per_key_per_second > 0 else float('inf')

        # 为每个key维护一个时间戳队列 (用于分钟限制)
        self.key_usage_per_minute: Dict[str, deque] = {key: deque() for key in self.keys}
        # 为每个key维护最后一个使用时间 (用于秒限制)
        self.key_last_used: Dict[str, float] = {key: 0.0 for key in self.keys}
        
        # 异步锁，确保在并发环境下安全地获取key
        self._lock = asyncio.Lock()
        
        # 轮询的起始索引
        self._next_key_index = 0

        logger.info(
            f"ApiKeyManager initialized with {self.num_keys} keys. "
            f"Rate limit: {self.requests_per_key_per_minute} RPM and {requests_per_key_per_second} RPS per key."
        )

    def force_cooldown(self, key: str):
        """
        当外部检测到速率限制时，手动将一个key置于冷却状态。
        这会用当前时间戳填满该key的分钟请求队列。
        """
        if key in self.key_usage_per_minute:
            now = time.monotonic()
            # 填满队列，使其在接下来的一分钟内不可用
            self.key_usage_per_minute[key].extend([now] * self.requests_per_key_per_minute)
            logger.warning(f"Key ending in '...{key[-4:]}' has been forced into cooldown due to an external 429 error.")

    async def get_next_key_async(self) -> str:
        """
        异步获取下一个可用的API Key。
        如果所有Key都在冷却中，则会异步等待直到有Key可用。
        此方法会检查每秒和每分钟的速率限制。
        """
        async with self._lock:
            max_loops = self.num_keys * 3  # 防止无限循环
            for _ in range(max_loops):
                # 尝试从当前索引开始，轮询所有key
                for i in range(self.num_keys):
                    key_index = (self._next_key_index + i) % self.num_keys
                    key_to_check = self.keys[key_index]
                    
                    now = time.monotonic()
                    
                    # 1. 检查每分钟限制 (RPM)
                    timestamps_minute = self.key_usage_per_minute[key_to_check]
                    while timestamps_minute and now - timestamps_minute[0] > 60:
                        timestamps_minute.popleft()
                    
                    if len(timestamps_minute) >= self.requests_per_key_per_minute:
                        continue # 此key分钟限制已满，检查下一个

                    # 2. 检查每秒限制 (RPS)
                    last_used = self.key_last_used[key_to_check]
                    if now - last_used < self.min_interval_sec:
                        continue # 此key秒限制内，检查下一个

                    # --- 此key可用 ---
                    self.key_usage_per_minute[key_to_check].append(now)
                    self.key_last_used[key_to_check] = now
                    self._next_key_index = (key_index + 1) % self.num_keys # 更新下一个起始点
                    logger.info(
                        f"Issuing API Key ending in '...{key_to_check[-4:]}'. "
                        f"Usage in last 60s: {len(timestamps_minute)}/{self.requests_per_key_per_minute}."
                    )
                    return key_to_check
                
                # 如果循环结束都没有找到可用的key，计算需要等待多久
                wait_times = []
                now = time.monotonic()
                for key in self.keys:
                    # RPM 等待时间
                    timestamps = self.key_usage_per_minute[key]
                    if len(timestamps) >= self.requests_per_key_per_minute:
                         wait_times.append((timestamps[0] + 60) - now)

                    # RPS 等待时间
                    elapsed = now - self.key_last_used[key]
                    if elapsed < self.min_interval_sec:
                        wait_times.append(self.min_interval_sec - elapsed)
                
                # 如果没有计算出等待时间（理论上不应该），则等待一个最小间隔
                if not wait_times:
                    wait_time = 0.1
                else:
                    # 等待到最近的一个Key可用即可
                    wait_time = max(0, min(wait_times)) + 0.05 # 加一点缓冲
                
                logger.warning(
                    f"All {self.num_keys} API keys are rate-limited. "
                    f"Waiting for the next available key in {wait_time:.2f} seconds."
                )
                await asyncio.sleep(wait_time)
            
            raise Exception("Failed to get an available API key after multiple wait cycles.")

# 创建一个全局单例实例，供整个应用共享
# 1 RPS, 5 RPM per key
key_manager = ApiKeyManager(
    keys=settings.GEMINI_API_KEYS, 
    requests_per_key_per_minute=5,
    requests_per_key_per_second=1
) 