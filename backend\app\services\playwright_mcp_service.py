"""
Playwright MCP 截图服务
通过调用外部的playwright MCP服务来进行截图
"""
import logging
import asyncio
import subprocess
import json
import tempfile
import os
from typing import Optional, Dict, Any
from dataclasses import dataclass
import base64

logger = logging.getLogger(__name__)

@dataclass
class ScreenshotResult:
    """截图结果"""
    success: bool
    screenshot_base64: Optional[str] = None
    error_message: Optional[str] = None
    screenshot_path: Optional[str] = None

class PlaywrightMCPService:
    """Playwright MCP截图服务"""
    
    def __init__(self):
        self.mcp_server_process = None
        self.temp_dir = tempfile.mkdtemp()
        
    async def start_mcp_server(self) -> bool:
        """启动playwright MCP服务器"""
        try:
            # 检查是否已经有MCP服务器在运行
            if self.mcp_server_process and self.mcp_server_process.poll() is None:
                return True
                
            # 启动playwright MCP服务器
            cmd = ["npx", "@executeautomation/playwright-mcp-server", "--headless"]
            
            self.mcp_server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待服务器启动
            await asyncio.sleep(3)
            
            if self.mcp_server_process.poll() is None:
                logger.info("Playwright MCP服务器启动成功")
                return True
            else:
                logger.error("Playwright MCP服务器启动失败")
                return False
                
        except Exception as e:
            logger.error(f"启动Playwright MCP服务器时出错: {e}")
            return False
    
    async def take_screenshot_via_mcp(self, html_content: str, width: int = 1280, height: int = 720) -> ScreenshotResult:
        """通过MCP服务截图"""
        try:
            # 创建临时HTML文件
            temp_html_path = os.path.join(self.temp_dir, f"temp_{asyncio.get_event_loop().time()}.html")
            
            with open(temp_html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # 构建file URL
            file_url = f"file://{temp_html_path.replace(os.sep, '/')}"
            
            # 使用playwright直接截图（不通过MCP协议，而是直接调用playwright）
            result = await self._direct_playwright_screenshot(file_url, width, height)
            
            # 清理临时文件
            try:
                os.remove(temp_html_path)
            except:
                pass
                
            return result
            
        except Exception as e:
            logger.error(f"MCP截图失败: {e}")
            return ScreenshotResult(
                success=False,
                error_message=str(e)
            )
    
    async def _direct_playwright_screenshot(self, url: str, width: int, height: int) -> ScreenshotResult:
        """直接使用playwright进行截图"""
        try:
            from playwright.async_api import async_playwright
            
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                # 设置视口大小
                await page.set_viewport_size({"width": width, "height": height})
                
                # 导航到页面
                await page.goto(url, wait_until="networkidle")
                
                # 等待页面加载
                await asyncio.sleep(2)
                
                # 截图
                screenshot_bytes = await page.screenshot(
                    type="png",
                    full_page=True
                )
                
                await browser.close()
                
                # 转换为base64
                screenshot_base64 = base64.b64encode(screenshot_bytes).decode('utf-8')
                
                return ScreenshotResult(
                    success=True,
                    screenshot_base64=screenshot_base64
                )
                
        except Exception as e:
            logger.error(f"直接playwright截图失败: {e}")
            return ScreenshotResult(
                success=False,
                error_message=str(e)
            )
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.mcp_server_process and self.mcp_server_process.poll() is None:
                self.mcp_server_process.terminate()
                await asyncio.sleep(1)
                if self.mcp_server_process.poll() is None:
                    self.mcp_server_process.kill()
                    
            # 清理临时目录
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir, ignore_errors=True)
                
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")

# 全局实例
_playwright_mcp_service = None

async def get_playwright_mcp_service() -> PlaywrightMCPService:
    """获取playwright MCP服务实例"""
    global _playwright_mcp_service
    
    if _playwright_mcp_service is None:
        _playwright_mcp_service = PlaywrightMCPService()
        await _playwright_mcp_service.start_mcp_server()
    
    return _playwright_mcp_service 