#!/usr/bin/env python3
"""
测试责任链修复的验证脚本

这个脚本测试：
1. VisualStyleAgent 能否直接生成完整的 StructuredPresentationStyleSchema
2. DetailedSlideBlueprintSchema 的 key_elements 字段是否正确定义为 List[Dict[str, Any]]
3. 修复后的系统能否正常运行
"""

import asyncio
import json
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.agents.visual_style_agent import VisualStyleAgent
from app.models.presentation_model import DetailedSlideBlueprintSchema, StructuredPresentationStyleSchema
from pydantic import ValidationError

async def test_visual_style_agent():
    """测试VisualStyleAgent能否生成完整的设计系统"""
    print("🧪 测试 VisualStyleAgent...")
    
    agent = VisualStyleAgent()
    
    # 测试简单的主题
    result, log_id = await agent.process(
        topic="公司年度财务报告",
        num_slides=5,
        style_keywords=["专业", "数据驱动", "现代"],
        project_id="test_001"
    )
    
    if result:
        print("✅ VisualStyleAgent 成功生成完整设计系统")
        print(f"📊 风格摘要: {result.style_summary_text[:100]}...")
        print(f"🎨 主色调: {result.color_palette.primary.hex}")
        print(f"📑 生成了 {len(result.presentation_outlines)} 个幻灯片大纲")
        
        # 验证结构完整性
        assert hasattr(result, 'color_palette'), "缺少 color_palette"
        assert hasattr(result, 'typography'), "缺少 typography"
        assert hasattr(result, 'design_elements'), "缺少 design_elements"
        assert hasattr(result, 'primary_color_var'), "缺少主色调 CSS 变量"
        assert hasattr(result, 'presentation_outlines'), "缺少幻灯片大纲"
        
        print("✅ 设计系统结构完整")
        return True
    else:
        print("❌ VisualStyleAgent 失败")
        return False

def test_detailed_blueprint_schema():
    """测试DetailedSlideBlueprintSchema的key_elements字段类型"""
    print("\n🧪 测试 DetailedSlideBlueprintSchema...")
    
    # 测试正确的数据结构
    valid_data = {
        "slide_number": 1,
        "layout_template_name": "TitleSlideLayout",
        "background_style_description": "linear-gradient(135deg, #0A1931 0%, #1E293B 100%)",
        "key_elements": [  # 这里是列表，不是字符串
            {
                "type": "title",
                "content": "公司年度财务报告",
                "target_area": "title_area",
                "animation_style": "fade-in"
            },
            {
                "type": "subtitle", 
                "content": "2024年度业绩回顾与展望",
                "target_area": "subtitle_area",
                "animation_style": "slide-in-up"
            }
        ],
        "speaker_notes": "这是公司2024年度的财务报告首页，重点介绍了本年度的关键业绩指标和未来发展规划。在这张幻灯片中，我们将详细回顾2024年度的财务表现，包括营收增长情况、利润率变化、市场占有率提升等关键指标，同时展望2025年的战略发展方向和增长预期。"
    }
    
    try:
        blueprint = DetailedSlideBlueprintSchema(**valid_data)
        print("✅ key_elements 字段正确接受 List[Dict[str, Any]] 类型")
        print(f"📝 解析了 {len(blueprint.key_elements)} 个元素")
        
        # 验证字段类型
        assert isinstance(blueprint.key_elements, list), "key_elements 应该是列表类型"
        assert all(isinstance(elem, dict) for elem in blueprint.key_elements), "每个元素应该是字典类型"
        
        print("✅ 数据结构验证通过")
        return True
        
    except ValidationError as e:
        print(f"❌ Pydantic 验证失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_old_string_format():
    """测试旧的字符串格式是否会被拒绝"""
    print("\n🧪 测试旧的 key_elements 字符串格式...")
    
    # 测试错误的数据结构（旧版本的字符串格式）
    invalid_data = {
        "slide_number": 1,
        "layout_template_name": "TitleSlideLayout", 
        "background_style_description": "linear-gradient(135deg, #0A1931 0%, #1E293B 100%)",
        "key_elements": '[{"type": "title", "content": "标题"}]',  # 这是字符串，应该被拒绝
        "speaker_notes": "这应该被拒绝，因为key_elements是字符串格式"
    }
    
    try:
        blueprint = DetailedSlideBlueprintSchema(**invalid_data)
        print("❌ 错误：旧的字符串格式被错误地接受了")
        return False
    except ValidationError as e:
        print("✅ 正确拒绝了旧的字符串格式")
        print(f"📋 验证错误: {str(e)[:100]}...")
        return True
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始验证责任链修复...")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # 测试 1: VisualStyleAgent
    if await test_visual_style_agent():
        tests_passed += 1
    
    # 测试 2: 正确的 DetailedSlideBlueprintSchema
    if test_detailed_blueprint_schema():
        tests_passed += 1
    
    # 测试 3: 旧格式拒绝
    if test_old_string_format():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！责任链修复成功！")
        print("\n📋 修复总结:")
        print("   ✅ VisualStyleAgent 现在直接生成完整设计系统")
        print("   ✅ 移除了偷工减料的 SimplifiedStyleSchema")
        print("   ✅ DetailedSlideBlueprintSchema.key_elements 修复为 List[Dict[str, Any]]")
        print("   ✅ 责任链可以传递真正结构化的数据")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    asyncio.run(main()) 