# backend/app/apis/v1/auth_router.py
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm, OAuth2PasswordBearer
from sqlalchemy.orm import Session
from datetime import timed<PERSON><PERSON>
from jose import JWTError, jwt
from typing import Optional

from app.db.session import get_db
from app.models import user_model as pydantic_user_models
from app.crud import crud_user
from app.core.security import create_access_token, ACCESS_TOKEN_EXPIRE_MINUTES, SECRET_KEY, ALGORITHM
from app.db import models as db_models
from app.core.config import settings

router = APIRouter()

# OAuth2 Bearer Token设置
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/token")

# 创建一个不会引发异常的OAuth2 scheme
class OptionalOAuth2PasswordBearer(OAuth2PasswordBearer):
    async def __call__(self, request: Request = None) -> Optional[str]:
        try:
            return await super().__call__(request)
        except HTTPException:
            return None

# 使用这个可选的oauth2 scheme
optional_oauth2_scheme = OptionalOAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/token")

# 用户身份验证依赖项
async def get_current_user(
    token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)
) -> db_models.User:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
        token_data = pydantic_user_models.TokenData(email=email)
    except JWTError:
        raise credentials_exception
    
    user = crud_user.get_user_by_email(db, email=token_data.email)
    if user is None:
        raise credentials_exception
    if not user.is_active:
        raise HTTPException(status_code=400, detail="用户已被禁用")
    return user

# 可选的用户认证依赖项
async def optional_current_user(
    token: Optional[str] = Depends(optional_oauth2_scheme), db: Session = Depends(get_db)
) -> Optional[db_models.User]:
    """
    获取当前用户，如果未认证则返回None
    - 用于允许某些端点对匿名用户和已认证用户都可用
    - 已认证用户可能有附加权限
    """
    if token is None:
        return None
        
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            return None
        token_data = pydantic_user_models.TokenData(email=email)
    except JWTError:
        return None
    
    user = crud_user.get_user_by_email(db, email=token_data.email)
    if user is None or not user.is_active:
        return None
    
    return user

async def get_current_active_superuser(
    current_user: db_models.User = Depends(get_current_user),
) -> db_models.User:
    """获取当前超级用户"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403, detail="没有足够的权限"
        )
    return current_user


@router.post("/register", response_model=pydantic_user_models.User)
def register_user(user_in: pydantic_user_models.UserCreate, db: Session = Depends(get_db)):
    """注册新用户"""
    db_user = crud_user.get_user_by_email(db, email=user_in.email)
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该邮箱已被注册。",
        )
    created_user = crud_user.create_user(db=db, user=user_in)
    return created_user


@router.post("/token", response_model=pydantic_user_models.Token)
def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)
):
    """用户登录获取令牌"""
    user = crud_user.authenticate_user(
        db, email=form_data.username, password=form_data.password  # OAuth2 form uses 'username' for email
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="邮箱或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not user.is_active:
        raise HTTPException(status_code=400, detail="用户已被禁用")
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires  # "sub" 是JWT标准声明，通常是用户名或用户ID
    )
    return {"access_token": access_token, "token_type": "bearer"}


@router.get("/users/me", response_model=pydantic_user_models.User)
async def read_users_me(current_user: db_models.User = Depends(get_current_user)):
    """获取当前登录用户的信息"""
    return current_user


@router.get("/users/test-superuser")
async def test_superuser_endpoint(
    current_user: db_models.User = Depends(get_current_active_superuser),
):
    """测试超级用户权限的端点"""
    return {"email": current_user.email, "message": "如果你看到这个，说明你是超级用户!"}


@router.get("/users", response_model=list[pydantic_user_models.User])
async def read_users(
    skip: int = 0, 
    limit: int = 100, 
    current_user: db_models.User = Depends(get_current_active_superuser),
    db: Session = Depends(get_db)
):
    """获取所有用户(仅超级管理员)"""
    users = crud_user.get_users(db, skip=skip, limit=limit)
    return users 