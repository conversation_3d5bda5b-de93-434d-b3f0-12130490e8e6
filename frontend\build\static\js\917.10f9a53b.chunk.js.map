{"version": 3, "file": "static/js/917.10f9a53b.chunk.js", "mappings": "0LAGA,MA0KA,EA1KyBA,IAA4C,IAA3C,OAAEC,EAAM,aAAEC,EAAe,EAAC,QAAEC,GAASH,EAC7D,MAAOI,EAAcC,IAAmBC,EAAAA,EAAAA,UAASJ,IAC1CK,EAAiBC,IAAsBF,EAAAA,EAAAA,WAAS,GACjDG,GAAmBC,EAAAA,EAAAA,QAAO,MAC1BC,GAAYD,EAAAA,EAAAA,QAAO,MAGnBE,GAAqBC,EAAAA,EAAAA,cAAY,KACjCJ,EAAiBK,SAASC,aAAaN,EAAiBK,SAC5DL,EAAiBK,QAAUE,YAAW,IAAMR,GAAmB,IAAQ,OACtE,IAGGS,GAAgBJ,EAAAA,EAAAA,cAAY,KAChCR,GAAgBa,GAASA,EAAOjB,EAAOkB,OAAS,EAAID,EAAO,EAAIA,IAC/DV,GAAmB,GACnBI,MACC,CAACX,EAAOkB,OAAQP,IAGbQ,GAAgBP,EAAAA,EAAAA,cAAY,KAChCR,GAAgBa,GAASA,EAAO,EAAIA,EAAO,EAAIA,IAC/CV,GAAmB,GACnBI,MACC,CAACA,IAGES,GAAkBR,EAAAA,EAAAA,cAAY,KAClCL,GAAmB,GACnBI,MACC,CAACA,KAGJU,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAiBC,IAErB,GAAIA,EAAMC,MAA4B,mBAApBD,EAAMC,KAAKC,KAA2B,CACtD,MAAMC,EAAMH,EAAMC,KAAKE,IACX,eAARA,GAAgC,cAARA,GAA+B,aAARA,GAA8B,MAARA,EACvEV,IACiB,cAARU,GAA+B,YAARA,GAA6B,WAARA,EACrDP,IACiB,WAARO,GACTxB,GAEJ,GAOF,OAHAyB,OAAOC,iBAAiB,UAAWN,GAG5B,KACLK,OAAOE,oBAAoB,UAAWP,MAEvC,CAACN,EAAeG,EAAejB,KAGlCmB,EAAAA,EAAAA,YAAU,KAER,MAAMS,EAAeC,IACnBA,EAAEC,iBACED,EAAEE,OAAS,EACbjB,IACSe,EAAEE,OAAS,GACpBd,KAKEe,EAAiBH,IACJ,CAAC,aAAc,YAAa,WAAY,IAAK,YAAa,UAAW,SAAU,UAAUI,SAASJ,EAAEL,MAGnHK,EAAEC,iBAGU,eAAVD,EAAEL,KAAkC,cAAVK,EAAEL,KAAiC,aAAVK,EAAEL,KAAgC,MAAVK,EAAEL,IAC/EV,IACmB,cAAVe,EAAEL,KAAiC,YAAVK,EAAEL,KAA+B,WAAVK,EAAEL,IAC3DP,IACmB,WAAVY,EAAEL,KACXxB,KAIEkC,EAAgB1B,EAAUG,QAQhC,OAPIuB,IAEFA,EAAcR,iBAAiB,QAASE,EAAa,CAAEO,SAAS,IAChEV,OAAOC,iBAAiB,UAAWM,EAAe,CAAEG,SAAS,KAIxD,KACDD,GACFA,EAAcP,oBAAoB,QAASC,GAE7CH,OAAOE,oBAAoB,UAAWK,MAEvC,CAAClB,EAAeG,EAAejB,KAElCmB,EAAAA,EAAAA,YAAU,KACRV,IACO,KACDH,EAAiBK,SAASC,aAAaN,EAAiBK,YAE7D,CAACV,EAAcQ,IAElB,MAAM2B,EAAetC,EAAOG,GAC5B,OAAKmC,GAGGC,EAAAA,EAAAA,MAAA,OACFC,IAAK9B,EACL+B,UAAU,mEACVC,YAAatB,EACbuB,QAAUZ,IAEJA,EAAEa,SAAWb,EAAEc,eAAiBd,EAAEa,SAAWlC,EAAUG,QAAQiC,YACjE9B,KAGJ+B,SAAA,EAEFC,EAAAA,EAAAA,KAAA,OAAKP,UAAU,0DAAyDM,UACtEC,EAAAA,EAAAA,KAACC,EAAAA,EAAa,CACZC,QAAO,cAAAC,OAAgBb,EAAac,IAAMjD,GAC1CkD,cAAef,EAAagB,KAC5BC,kBAAkB,EAClBC,aAAa,OAGjBjB,EAAAA,EAAAA,MAAA,OACEE,UAAS,mCAAAU,OAAqC7C,EAAkB,cAAgB,aAChFqC,QAAUZ,GAAMA,EAAE0B,kBAAkBV,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,UACEP,UAAU,+IACVE,QAASzC,EACTwD,MAAM,iCAAYX,SACnB,SAGA5C,EAAe,IACd6C,EAAAA,EAAAA,KAAA,UACEP,UAAU,kKACVE,QAASxB,EACTuC,MAAM,uCAAaX,SACpB,WAIF5C,EAAeH,EAAOkB,OAAS,IAC9B8B,EAAAA,EAAAA,KAAA,UACEP,UAAU,mKACVE,QAAS3B,EACT0C,MAAM,uCAAaX,SACpB,YAIHR,EAAAA,EAAAA,MAAA,OAAKE,UAAU,+GAA8GM,SAAA,CAC1H5C,EAAe,EAAE,MAAIH,EAAOkB,gBArDX,K", "sources": ["components/FullScreenPlayer.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport Slide<PERSON>enderer from './SlideRenderer';\n\nconst FullScreenPlayer = ({ slides, initialIndex = 0, onClose }) => {\n  const [currentIndex, setCurrentIndex] = useState(initialIndex);\n  const [controlsVisible, setControlsVisible] = useState(true);\n  const controlsTimerRef = useRef(null);\n  const playerRef = useRef(null); // 【新增】为最外层div创建一个ref\n\n  // 重置控制按钮隐藏计时器\n  const resetControlsTimer = useCallback(() => {\n    if (controlsTimerRef.current) clearTimeout(controlsTimerRef.current);\n    controlsTimerRef.current = setTimeout(() => setControlsVisible(false), 2000);\n  }, []);\n\n  // 导航到下一张\n  const goToNextSlide = useCallback(() => {\n    setCurrentIndex(prev => (prev < slides.length - 1 ? prev + 1 : prev));\n    setControlsVisible(true);\n    resetControlsTimer();\n  }, [slides.length, resetControlsTimer]);\n\n  // 导航到上一张\n  const goToPrevSlide = useCallback(() => {\n    setCurrentIndex(prev => (prev > 0 ? prev - 1 : prev));\n    setControlsVisible(true);\n    resetControlsTimer();\n  }, [resetControlsTimer]);\n\n  // 监听鼠标移动以显示控件\n  const handleMouseMove = useCallback(() => {\n    setControlsVisible(true);\n    resetControlsTimer();\n  }, [resetControlsTimer]);\n  \n  // 【新增】useEffect 来处理来自 iframe 的消息\n  useEffect(() => {\n    const handleMessage = (event) => {\n      // 确保消息来源是我们期望的，并且类型正确\n      if (event.data && event.data.type === 'iframe_keydown') {\n        const key = event.data.key;\n        if (key === 'ArrowRight' || key === 'ArrowDown' || key === 'PageDown' || key === ' ') {\n          goToNextSlide();\n        } else if (key === 'ArrowLeft' || key === 'ArrowUp' || key === 'PageUp') {\n          goToPrevSlide();\n        } else if (key === 'Escape') {\n          onClose(); // 按 Esc 退出全屏\n        }\n      }\n    };\n\n    // 添加 message 事件监听器\n    window.addEventListener('message', handleMessage);\n\n    // 清理函数，在组件卸载时移除监听器\n    return () => {\n      window.removeEventListener('message', handleMessage);\n    };\n  }, [goToNextSlide, goToPrevSlide, onClose]); // 依赖项\n\n  // 【重要修复】在useEffect中手动添加非被动的滚轮事件监听器和键盘事件监听器\n  useEffect(() => {\n    // 滚轮事件处理\n    const handleWheel = (e) => {\n      e.preventDefault(); // 现在可以成功调用\n      if (e.deltaY > 0) {\n        goToNextSlide();\n      } else if (e.deltaY < 0) {\n        goToPrevSlide();\n      }\n    };\n    \n    // 键盘事件处理（作为备用，防止iframe消息机制失败）\n    const handleKeyDown = (e) => {\n      const isNavKey = ['ArrowRight', 'ArrowDown', 'PageDown', ' ', 'ArrowLeft', 'ArrowUp', 'PageUp', 'Escape'].includes(e.key);\n      \n      if (isNavKey) {\n        e.preventDefault();\n      }\n\n      if (e.key === 'ArrowRight' || e.key === 'ArrowDown' || e.key === 'PageDown' || e.key === ' ') {\n        goToNextSlide();\n      } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp' || e.key === 'PageUp') {\n        goToPrevSlide();\n      } else if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    const playerElement = playerRef.current;\n    if (playerElement) {\n      // 添加事件监听器并明确设置 passive 为 false\n      playerElement.addEventListener('wheel', handleWheel, { passive: false });\n      window.addEventListener('keydown', handleKeyDown, { passive: false });\n    }\n\n    // 清理函数\n    return () => {\n      if (playerElement) {\n        playerElement.removeEventListener('wheel', handleWheel);\n      }\n      window.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [goToNextSlide, goToPrevSlide, onClose]); // 依赖项\n\n  useEffect(() => {\n    resetControlsTimer();\n    return () => {\n      if (controlsTimerRef.current) clearTimeout(controlsTimerRef.current);\n    };\n  }, [currentIndex, resetControlsTimer]);\n\n  const currentSlide = slides[currentIndex];\n  if (!currentSlide) return null;\n\n  return (\n          <div\n        ref={playerRef} // 【新增】应用ref\n        className=\"fixed inset-0 bg-black flex items-center justify-center z-[9999]\"\n        onMouseMove={handleMouseMove}\n        onClick={(e) => {\n          // 只有在点击主容器时才触发下一张，避免和控制按钮冲突\n          if (e.target === e.currentTarget || e.target === playerRef.current.firstChild) {\n            goToNextSlide();\n          }\n        }}\n        // 【移除】onWheel属性，因为它现在由useEffect处理\n      >\n      <div className=\"relative w-full h-full flex items-center justify-center\">\n        <SlideRenderer\n          slideId={`fullscreen-${currentSlide.id || currentIndex}`}\n          slideFullHtml={currentSlide.html}\n          isAppEditingMode={false}\n          isThumbnail={false}\n        />\n      </div>\n      <div\n        className={`transition-opacity duration-300 ${controlsVisible ? 'opacity-100' : 'opacity-0'}`}\n        onClick={(e) => e.stopPropagation()}\n      >\n        <button\n          className=\"absolute top-5 right-5 w-10 h-10 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-xl\"\n          onClick={onClose}\n          title=\"退出全屏 (Esc)\"\n        >\n          ×\n        </button>\n        {currentIndex > 0 && (\n          <button\n            className=\"absolute left-5 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-2xl\"\n            onClick={goToPrevSlide}\n            title=\"上一张 (← / ↑)\"\n          >\n            ‹\n          </button>\n        )}\n        {currentIndex < slides.length - 1 && (\n          <button\n            className=\"absolute right-5 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-2xl\"\n            onClick={goToNextSlide}\n            title=\"下一张 (→ / ↓)\"\n          >\n            ›\n          </button>\n        )}\n        <div className=\"absolute bottom-5 left-1/2 -translate-x-1/2 bg-black bg-opacity-50 px-4 py-2 rounded-full text-white text-sm\">\n          {currentIndex + 1} / {slides.length}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FullScreenPlayer;"], "names": ["_ref", "slides", "initialIndex", "onClose", "currentIndex", "setCurrentIndex", "useState", "controlsVisible", "setControlsVisible", "controlsTimerRef", "useRef", "playerRef", "resetControlsTimer", "useCallback", "current", "clearTimeout", "setTimeout", "goToNextSlide", "prev", "length", "goToPrevSlide", "handleMouseMove", "useEffect", "handleMessage", "event", "data", "type", "key", "window", "addEventListener", "removeEventListener", "handleWheel", "e", "preventDefault", "deltaY", "handleKeyDown", "includes", "playerElement", "passive", "currentSlide", "_jsxs", "ref", "className", "onMouseMove", "onClick", "target", "currentTarget", "<PERSON><PERSON><PERSON><PERSON>", "children", "_jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slideId", "concat", "id", "slideFullHtml", "html", "isAppEditingMode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stopPropagation", "title"], "sourceRoot": ""}