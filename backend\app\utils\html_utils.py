import re
import logging
from typing import Optional

logger = logging.getLogger(__name__)
MIN_VALID_HTML_LENGTH_DEFAULT = 200 # 可以设为默认值

def extract_html_from_llm_response(response_text: str) -> Optional[str]:
    """
    从LLM响应中提取HTML代码。
    先尝试从Markdown代码块中提取，如果失败则尝试直接查找HTML标签。
    
    Args:
        response_text: LLM响应文本
    
    Returns:
        提取的HTML代码，如果无法提取则返回None
    """
    if not response_text:
        logger.warning("Empty response text, cannot extract HTML.")
        return None
        
    # +++ 新增的修复步骤 +++
    # 移除 <thinking_process> ... </thinking_process> 块
    cleaned_response_text = re.sub(r'<thinking_process>[\s\S]*?</thinking_process>', '', response_text, flags=re.DOTALL).strip()
    logger.info(f"Removed thinking_process tags from response (if any)")
    # ++++++++++++++++++++++
        
    # 1. 尝试从Markdown代码块中提取HTML (现在使用清洗后的文本)
    html_code_block_pattern = r'```(?:html)?\s*(<!DOCTYPE html>[\s\S]*?)</html>\s*```'
    matches = re.search(html_code_block_pattern, cleaned_response_text, re.IGNORECASE)
    
    if matches:
        html_content = matches.group(1) + "</html>"
        logger.info(f"Successfully extracted HTML from code block (length: {len(html_content)})")
        return html_content
        
    # 2. 尝试更宽松的代码块提取，不要求完整的doctype和html结束标签
    loose_html_block_pattern = r'```(?:html)?\s*(<[\s\S]*?>[\s\S]*?)```'
    matches = re.search(loose_html_block_pattern, cleaned_response_text, re.IGNORECASE)
    
    if matches:
        html_content = matches.group(1)
        logger.info(f"Extracted partial HTML from code block using loose pattern (length: {len(html_content)})")
        return html_content
        
    # 3. 尝试直接在响应文本中查找HTML文档
    doctype_pattern = r'(<!DOCTYPE html>[\s\S]*?</html>)'
    matches = re.search(doctype_pattern, cleaned_response_text, re.IGNORECASE)
    
    if matches:
        html_content = matches.group(1)
        logger.info(f"Found complete HTML document outside code block (length: {len(html_content)})")
        return html_content
        
    # 4. 最后尝试查找任何HTML内容 (通过查找开始和结束的HTML标签)
    html_tags_pattern = r'(<html[\s\S]*?>[\s\S]*?</html>)'
    matches = re.search(html_tags_pattern, cleaned_response_text, re.IGNORECASE)
    
    if matches:
        html_content = matches.group(1)
        logger.info(f"Found HTML tags outside code block (length: {len(html_content)})")
        # 如果缺少DOCTYPE，添加它
        if not html_content.strip().startswith("<!DOCTYPE"):
            html_content = "<!DOCTYPE html>\n" + html_content
        return html_content
        
    # 5. 尝试查找任何看起来像HTML的内容 (具有典型的HTML结构元素)
    if "<body" in cleaned_response_text and "</body>" in cleaned_response_text:
        logger.info("Found body tags but not complete HTML document. Attempting to reconstruct.")
        # 尝试提取body内容
        body_pattern = r'<body[^>]*>([\s\S]*?)</body>'
        body_match = re.search(body_pattern, cleaned_response_text, re.IGNORECASE)
        
        if body_match:
            body_content = body_match.group(1)
            # 重构完整的HTML
            reconstructed_html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slide Content</title>
</head>
<body>
{body_content}
</body>
</html>"""
            logger.info(f"Reconstructed HTML from body content (length: {len(reconstructed_html)})")
            return reconstructed_html
    
    # 无法提取有效的HTML
    logger.warning("Failed to extract valid HTML from response text.")
    return None

def validate_basic_html_structure(html_content: str) -> bool:
    """
    验证HTML是否具有基本结构 (DOCTYPE, html, body)。
    
    Args:
        html_content: HTML内容
        
    Returns:
        如果HTML具有基本结构则返回True，否则返回False
    """
    if not html_content:
        return False
        
    has_doctype = re.search(r'<!DOCTYPE\s+html>', html_content, re.IGNORECASE) is not None
    has_html_tags = re.search(r'<html.*?>[\s\S]*?</html>', html_content, re.DOTALL | re.IGNORECASE) is not None
    # 对于结构校验，head 不是强制的，但 body 是
    has_body_tags = re.search(r'<body.*?>[\s\S]*?</body>', html_content, re.DOTALL | re.IGNORECASE) is not None
    
    return has_doctype and has_html_tags and has_body_tags

def sanitize_html_from_llm(html_content: str, min_valid_length: int = MIN_VALID_HTML_LENGTH_DEFAULT) -> Optional[str]:
    """
    清理和验证HTML内容。
    提取HTML、进行必要的修复和清理，确保输出符合预期格式。
    
    Args:
        html_content: 原始HTML内容
        min_valid_length: 最小有效HTML长度
            
    Returns:
        清理后的HTML内容，如果无法提取有效HTML则返回None
    """
    if not html_content or len(html_content.strip()) < min_valid_length:
        logger.warning("HTML content too short or empty.")
        return None
        
    # 首先尝试从响应中提取HTML
    extracted_html = extract_html_from_llm_response(html_content)
    if not extracted_html:
        logger.warning("Failed to extract HTML from response.")
        return None  # 当提取失败时返回None，而不是原始内容
        
    # 验证提取的HTML是否具有基本结构
    if not validate_basic_html_structure(extracted_html):
        logger.warning("Extracted HTML lacks basic structure (DOCTYPE, html, body tags).")
        # 尝试简单修复，添加缺失的基本结构
        if "<body" not in extracted_html.lower() and "<div" in extracted_html.lower():
            logger.info("Attempting to repair HTML by adding missing structure...")
            extracted_html = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slide Content</title>
</head>
<body>
    {extracted_html}
</body>
</html>"""
            
    # 尝试移除可能存在的不必要的代码块标记（如果之前的提取未完全去除）
    cleaned_html = re.sub(r'```html\s*|```\s*$', '', extracted_html, flags=re.IGNORECASE)
    
    return cleaned_html 