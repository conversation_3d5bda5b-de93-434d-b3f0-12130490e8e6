# backend/app/agents/slide_generator_agent.py
import logging
from typing import Optional, Dict, Any, Tuple, List

from app.agents.base_agent import BaseAgent
from app.models.presentation_model import StructuredPresentationStyleSchema, DetailedSlideBlueprintSchema
from app.services.prompt_manager import get_prompt
from app.utils.html_utils import extract_html_from_llm_response # 导入提取工具
from app.utils.html_cleaner import comprehensive_html_cleanup # 导入新的清理工具
from app.utils.content_optimizer import ContentOptimizer # 导入内容优化器

logger = logging.getLogger(__name__)

class SlideGeneratorAgent(BaseAgent):
    """
    重构后的SlideGeneratorAgent - 真正的"忠实工程师"
    
    职责：将合成后的自然语言HTML构建指令转换为完整的HTML代码
    不再需要理解复杂的JSON结构，只需严格按照清晰的文本指令执行
    """
    def __init__(self, model_name_override: Optional[str] = None):
        super().__init__(
            agent_name="SlideGeneratorAgent",
            agent_prompt_subdir="slide_generator",
            model_name_override=model_name_override
        )
        logger.info("SlideGeneratorAgent initialized as 'Faithful Engineer'.")

    async def process_batch(
        self,
        batch_blueprints: List[DetailedSlideBlueprintSchema],
        structured_style: StructuredPresentationStyleSchema,
        project_id: str,
        task_logger: logging.Logger
    ) -> Optional[str]:
        """
        【核心方法重构】
        根据一批（如5个）详细蓝图，生成一个包含所有幻灯片HTML的、用分隔符隔开的单一字符串。
        
        Args:
            batch_blueprints: 一批详细蓝图
            structured_style: 结构化风格配置
            project_id: 项目ID
            task_logger: 日志记录器
            
        Returns:
            包含所有幻灯片HTML的字符串，用分隔符隔开，如果失败则返回None
        """
        context = f"BatchSlideGen_Proj_{project_id}"
        task_logger.info(f"[{self.agent_name}] 正在生成一批 {len(batch_blueprints)} 张幻灯片...")
        
        try:
            import json
            blueprints_json = json.dumps([bp.model_dump() for bp in batch_blueprints], indent=2, ensure_ascii=False)
            style_json = structured_style.model_dump_json(indent=2)
            
            prompt_format_args = {
                "structured_style_json": style_json,
                "list_of_detailed_blueprints_json": blueprints_json,
            }
            
            # 调用LLM批量生成，期望返回一个大的文本块
            response_tuple = await self.call_llm(
                task_prompt_name="batch_main",
                section_name="task:batch_main", 
                prompt_format_args=prompt_format_args,
                call_context_name=context,
                project_id_for_logging=project_id,
                expected_response_mime_type="text/plain"  # 期望是纯文本
            )
            
            if not response_tuple or not response_tuple[0]:
                task_logger.error(f"[{context}] 批量HTML生成失败，LLM返回空响应。")
                return None
                
            combined_html_string, _ = response_tuple
            return combined_html_string
                
        except Exception as e:
            task_logger.error(f"[{context}] 批量HTML生成过程中发生异常: {e}", exc_info=True)
            return None

    async def process(
        self,
        synthesized_html_prompt: str,
        project_id: str,
        slide_number: int
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        【重构后的核心方法】根据合成的HTML构建指令生成完整的HTML代码
        
        这个方法现在变得极其简单和专注：
        1. 接收清晰的自然语言指令
        2. 调用LLM生成HTML
        3. 清理和返回结果
        
        Args:
            synthesized_html_prompt: 合成后的HTML构建指令（自然语言）
            project_id: 项目ID，用于日志记录
            slide_number: 幻灯片编号，用于日志记录

        Returns:
            Tuple of (生成的完整HTML或None, LLM交互日志ID)
        """
        context = f"SlideGen_Proj_{project_id}_Slide_{slide_number}"
        logger.info(f"[{self.agent_name}] 开始根据合成指令生成第 {slide_number} 张幻灯片的HTML")

        try:
            # 准备提示词参数 - 现在变得非常简单
            prompt_format_args = {
                "synthesized_html_prompt": synthesized_html_prompt,
                "common_html_tech_requirements": self._get_common_tech_reqs()
            }
            
            # 调用LLM生成HTML
            response_tuple = await self.call_llm(
                task_prompt_name="main",
                section_name="task:main",
                prompt_format_args=prompt_format_args,
                call_context_name=context,
                project_id_for_logging=project_id,
                expected_response_mime_type="text/plain"
            )

            if not response_tuple or not response_tuple[0]:
                logger.error(f"[{context}] HTML生成失败，LLM返回空响应")
                return None, response_tuple[1] if response_tuple else None

            llm_raw_response, llm_log_id = response_tuple
            
            # 从响应中提取HTML
            extracted_html = extract_html_from_llm_response(llm_raw_response)
            
            if not extracted_html:
                preview_len = 500
                raw_preview = f"{llm_raw_response[:preview_len]}...{llm_raw_response[-preview_len:]}" if len(llm_raw_response) > preview_len * 2 else llm_raw_response
                logger.error(f"[{context}] HTML提取失败。原始响应预览: {raw_preview}")
                return None, llm_log_id
                
            # 清理HTML
            cleaned_html = comprehensive_html_cleanup(extracted_html)
            if not cleaned_html:
                logger.error(f"[{context}] HTML清理失败")
                return None, llm_log_id

            logger.info(f"[{context}] 成功生成并清理第 {slide_number} 张幻灯片的HTML。Log ID: {llm_log_id}")
            return cleaned_html, llm_log_id

        except Exception as e:
            logger.error(f"[{context}] HTML生成过程中发生异常: {e}", exc_info=True)
            return None, None

    def _build_batch_generation_prompt(self, slides_data: List[Dict], structured_style) -> str:
        """构建批量生成的提示词"""
        slides_info = []
        for slide_data in slides_data:
            slides_info.append(f"""
幻灯片 {slide_data['slide_number']}:
- 标题: {slide_data['title']}
- 内容: {slide_data['content']}
- 备注: {slide_data.get('notes', '')}
""")
        
        return f"""
请批量生成 {len(slides_data)} 张幻灯片的HTML，每张幻灯片都必须是完整的、独立的HTML文档。

幻灯片信息:
{''.join(slides_info)}

要求:
1. 每张幻灯片都是完整的HTML文档（<!DOCTYPE html>到</html>）
2. 使用1280x720的PPT标准尺寸
3. 内容必须唯一，不能重复
4. 使用现代化的CSS设计
5. 中文字体优化
"""

    def _parse_batch_html_response(self, response: str) -> Dict[int, str]:
        """
        【重构】解析来自LLM的结构化JSON响应，支持多种可能的格式和回退
        """
        try:
            import json
            data = json.loads(response)
            
            # 方案1: 期望的格式 {"slides": [{"slide_number": 1, "html_content": "..."}, ...]}
            if "slides" in data and isinstance(data["slides"], list):
                results = {}
                for slide_item in data["slides"]:
                    if isinstance(slide_item, dict) and "slide_number" in slide_item and "html_content" in slide_item:
                        slide_num = slide_item["slide_number"]
                        html_content = slide_item["html_content"]
                        if html_content and len(html_content.strip()) > 50:
                            cleaned_html = comprehensive_html_cleanup(html_content)
                            if cleaned_html:
                                results[slide_num] = cleaned_html
                                logger.info(f"✅ 成功解析第{slide_num}张幻灯片 (标准格式)")
                if results:
                    logger.info(f"✅ 标准JSON格式解析成功，共{len(results)}张幻灯片")
                    return results

            # 方案2: 单HTML内容格式 {"html_content": "..."}
            if "html_content" in data and isinstance(data["html_content"], str):
                html_content = data["html_content"]
                if html_content and len(html_content.strip()) > 50:
                    cleaned_html = comprehensive_html_cleanup(html_content)
                    if cleaned_html:
                        logger.info("✅ 单HTML内容格式解析成功")
                        return {1: cleaned_html}

            # 方案3: 数字键格式 {"1": "html...", "2": "html...", ...}
            numeric_keys = [k for k in data.keys() if k.isdigit()]
            if numeric_keys:
                results = {}
                for key in numeric_keys:
                    slide_num = int(key)
                    html_content = data[key]
                    if isinstance(html_content, str) and len(html_content.strip()) > 50:
                        cleaned_html = comprehensive_html_cleanup(html_content)
                        if cleaned_html:
                            results[slide_num] = cleaned_html
                            logger.info(f"✅ 成功解析第{slide_num}张幻灯片 (数字键格式)")
                if results:
                    logger.info(f"✅ 数字键格式解析成功，共{len(results)}张幻灯片")
                    return results

            # 方案4: 直接是HTML数组 ["html1", "html2", ...]
            if isinstance(data, list):
                results = {}
                for i, html_content in enumerate(data):
                    if isinstance(html_content, str) and len(html_content.strip()) > 50:
                        cleaned_html = comprehensive_html_cleanup(html_content)
                        if cleaned_html:
                            slide_num = i + 1
                            results[slide_num] = cleaned_html
                            logger.info(f"✅ 成功解析第{slide_num}张幻灯片 (数组格式)")
                if results:
                    logger.info(f"✅ 数组格式解析成功，共{len(results)}张幻灯片")
                    return results

            logger.error(f"解析批量HTML响应失败：不支持的JSON格式。")
            return self._fallback_extract_all_html_from_text(response)

        except json.JSONDecodeError as e:
            logger.error(f"解析批量HTML响应时发生JSON解码错误: {e}")
            # 尝试从非JSON响应中提取HTML
            return self._fallback_extract_all_html_from_text(response)
        except Exception as e:
            logger.error(f"解析批量HTML响应时发生未知错误: {e}")
            return {}

    def _fallback_extract_all_html_from_text(self, response: str) -> Dict[int, str]:
        """
        【新增】当JSON解析失败时，尝试从文本中提取所有完整的HTML文档
        """
        try:
            import re
            # 使用 findall 查找所有 <!DOCTYPE html> ... </html> 块
            html_docs = re.findall(r'<!DOCTYPE html>[\s\S]*?</html>', response, re.IGNORECASE)
            if not html_docs:
                logger.error("回退提取失败：在文本中未找到任何完整的HTML文档。")
                return {}

            logger.warning(f"JSON解析失败，回退提取成功，找到 {len(html_docs)} 个HTML文档。")
            
            results = {}
            for i, html_content in enumerate(html_docs):
                slide_number = i + 1
                cleaned_html = comprehensive_html_cleanup(html_content)
                if cleaned_html:
                    results[slide_number] = cleaned_html
                    logger.info(f"✅ 回退提取第{slide_number}张幻灯片成功")
            return results
            
        except Exception as e:
            logger.error(f"回退HTML提取过程中发生异常: {e}")
            return {}

    def _get_ppt_tech_requirements(self) -> str:
        """获取PPT专用的HTML技术规范"""
        return """
**PPT HTML技术规范 (严格遵守):**

**【空间限制 - 最高优先级】**
1. 视口尺寸: 严格限制为1280px × 720px，绝对不能溢出
2. 内容适配: 所有内容必须在720px高度内完美显示，不能出现滚动条
3. 文字密度控制: 每张幻灯片的文字总量不超过200个中文字符
4. 元素数量限制: 每张幻灯片最多包含5-7个主要元素（标题、副标题、正文、图表等）
5. 字体大小规划: 
   - 主标题: 28-36px (不超过2行)
   - 副标题: 18-24px (不超过1行) 
   - 正文: 14-18px (每段不超过3行)
   - 说明文字: 12-14px

**【布局原则】**
6. 布局方式: 使用CSS Grid或Flexbox进行精确空间分配
7. 内容层次: 遵循"一个重点"原则，每张幻灯片只突出一个核心信息
8. 留白管理: 至少保留20%的空白区域，避免视觉拥挤
9. 垂直分布: 内容垂直分布要均匀，避免底部堆积

**【技术实现】**
10. 文档结构: 完整的HTML5文档，<!DOCTYPE html>开始，</html>结束
11. 样式方式: 内联CSS或<style>标签，不依赖外部CSS文件
12. 字体系统: font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
13. 图表支持: 如需图表，使用Chart.js CDN并限制图表高度不超过300px
14. 响应式: 确保在1280x720视口下完美显示
15. 中文优化: 行高1.4-1.6，避免过高导致溢出

**【必须包含的CSS设置】**
```css
html, body {
    width: 1280px;
    height: 720px;
    margin: 0;
    padding: 0;
    overflow: hidden;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
    box-sizing: border-box;
}
```

**【内容优化策略】**
- 长文本要分段，每段不超过50字
- 使用项目符号列表替代长段落
- 数据用图表展示，减少文字描述
- 关键信息用KPI卡片突出显示
- 次要信息可以缩小字号或降低透明度
"""

    def _get_common_tech_reqs(self) -> str:
        """获取通用的HTML技术规范（强化版）"""
        return """
**PPT HTML技术规范 (严格遵守):**

**【空间限制 - 最高优先级】**
1. 视口尺寸: 严格限制为1280px × 720px，绝对不能溢出
2. 内容适配: 所有内容必须在720px高度内完美显示，不能出现滚动条
3. 文字密度控制: 每张幻灯片的文字总量不超过150个中文字符（更严格）
4. 元素数量限制: 每张幻灯片最多包含5个主要元素（标题、副标题、正文、图表等）
5. 字体大小规划: 
   - 主标题: 28-32px (不超过2行)
   - 副标题: 18-22px (不超过1行) 
   - 正文: 14-16px (每段不超过3行)
   - 说明文字: 12-14px

**【强制性CSS样式要求】**
必须在<style>标签内包含以下CSS：
```css
/* 强制性空间控制样式 */
html, body {
  width: 1280px !important;
  height: 720px !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
}

body {
  position: relative !important;
  display: block !important;
  transform-origin: 0 0 !important;
}

* {
  box-sizing: border-box !important;
}

/* 防止内容溢出的保护样式 */
.slide-container, .main-content, .content-wrapper {
  max-width: 1280px !important;
  max-height: 720px !important;
  overflow: hidden !important;
}

/* 图表容器强制限制 */
canvas, .chart-container, [class*="chart"], [id*="chart"] {
  max-width: 1240px !important;
  max-height: 300px !important;
  overflow: hidden !important;
}

/* 标题区域高度限制 */
h1, .title, [class*="title"] {
  max-height: 120px !important;
  overflow: hidden !important;
  line-height: 1.2 !important;
}
```

**【CSS自定义属性应用要求】**
6. 必须定义CSS变量: 从structured_style_json中的css_custom_properties_definitions提取所有变量并定义在:root中
7. 必须使用CSS变量: 在样式中引用var(--variable-name)，不要直接写硬编码的颜色值和尺寸
8. 变量命名规范: 遵循--primary-color、--title-font-size、--card-background等命名规范

**【布局模板要求】**
9. 布局模板选择: 必须根据detailed_blueprint_json中的layout_template_suggestion选择对应的布局方式
10. 空间分配: 标题区域最多120px，主内容区域约580px，预留20px边距
11. 布局方式: 优先使用CSS Grid或Flexbox进行精确空间分配
12. 响应式图表: Chart.js配置必须包含responsive: true, maintainAspectRatio: false

**【内容展示优化】**
13. 文本长度控制: 每个文本块不超过30个字符，超长内容用省略号
14. KPI卡片布局: 使用flex布局，每个卡片最大宽度不超过200px
15. 要点列表: 每个要点不超过25个字符，最多显示4个要点
16. 图表配置: 图表高度固定300px，宽度响应式但不超过1240px

**【技术实现细节】**
17. 文档结构: 完整的HTML5文档，<!DOCTYPE html>开始，</html>结束
18. 样式方式: 内联CSS在<style>标签中，不依赖外部CSS文件
19. 字体系统: font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
20. 图表CDN: 如需图表，使用Chart.js CDN: https://cdn.jsdelivr.net/npm/chart.js
21. 颜色方案: 使用CSS变量定义的颜色，确保视觉一致性
22. 中文优化: 行高1.4-1.6，避免过高导致溢出

**【数据绑定要求】**
23. 严格数据对应: HTML内容必须与detailed_blueprint_json中的数据完全对应，不能编造数据
24. 图表数据: 如有chart类型元素，必须使用其chart_config中的真实数据
25. KPI数值: 显示blueprint中指定的确切数值，不能修改或美化

**【质量检查清单】**
- [ ] 所有内容在1280x720范围内
- [ ] 使用了CSS自定义属性变量
- [ ] 包含了强制性CSS样式
- [ ] 图表高度不超过300px
- [ ] 文字内容符合长度限制
- [ ] 布局清晰不拥挤
- [ ] 数据与蓝图完全对应
"""

    async def process_single(
        self,
        structured_style: 'StructuredPresentationStyleSchema',
        detailed_blueprint: 'DetailedSlideBlueprintSchema',
        project_id: str,
        slide_number: int,
        task_logger: logging.Logger
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        【核心方法】根据结构化风格和详细蓝图生成完整的HTML代码
        
        Args:
            structured_style: 结构化的风格定义
            detailed_blueprint: 详细的幻灯片蓝图
            project_id: 项目ID
            slide_number: 幻灯片编号
            task_logger: 日志记录器
            
        Returns:
            Tuple of (生成的HTML或None, LLM交互日志ID)
        """
        context = f"SlideGen_Proj_{project_id}_Slide_{slide_number}"
        task_logger.info(f"[{self.agent_name}] 开始根据蓝图生成第 {slide_number} 张幻灯片的HTML")

        try:
            import json
            
            # 【新增】内容优化步骤 - 在生成前优化蓝图内容
            original_blueprint_dict = detailed_blueprint.model_dump()
            optimized_blueprint_dict = ContentOptimizer.optimize_blueprint_content(original_blueprint_dict)
            
            # 验证优化结果
            is_valid, issues = ContentOptimizer.validate_space_constraints(optimized_blueprint_dict)
            if not is_valid:
                task_logger.warning(f"[{context}] 内容优化后仍存在问题: {issues}")
            else:
                task_logger.info(f"[{context}] 内容优化完成，通过空间限制验证")
            
            # 准备提示词参数
            prompt_format_args = {
                "structured_style_json": structured_style.model_dump_json(indent=2),
                "detailed_blueprint_json": json.dumps(optimized_blueprint_dict, ensure_ascii=False, indent=2),
                "slide_number": slide_number,
                "common_html_tech_requirements": self._get_common_tech_reqs()
            }
            
            # 使用单次处理的提示词
            response_tuple = await self.call_llm(
                task_prompt_name="single_main", # 日志记录名
                section_name="task:single_main", # 提示词section名
                prompt_format_args=prompt_format_args,
                call_context_name=context,
                project_id_for_logging=project_id,
                expected_response_mime_type="text/plain"
            )
            
            if not response_tuple or not response_tuple[0]:
                task_logger.error(f"[{context}] LLM返回空响应")
                return None, response_tuple[1] if response_tuple else None
                
            raw_html, llm_log_id = response_tuple
            
            # 清理和验证HTML
            cleaned_html = comprehensive_html_cleanup(raw_html)
            
            if cleaned_html:
                task_logger.info(f"[{context}] 成功生成HTML，长度: {len(cleaned_html)} 字符")
                return cleaned_html, llm_log_id
            else:
                task_logger.error(f"[{context}] HTML清理和验证失败")
                return None, llm_log_id
                
        except Exception as e:
            task_logger.error(f"[{context}] 单次HTML生成过程中发生异常: {e}", exc_info=True)
            return None, None

    async def edit_html_element(
        self,
        html_content: str,
        element_selector: str,
        edit_instruction: str,
        project_id: str
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        编辑幻灯片中的指定HTML元素
        
        Args:
            html_content: 原始HTML内容
            element_selector: 用于选择元素的CSS选择器
            edit_instruction: 编辑指令
            project_id: 项目ID
            
        Returns:
            Tuple of (更新后的HTML或None, LLM交互日志ID)
        """
        context = f"HtmlEdit_Proj_{project_id}"
        logger.info(f"[{self.agent_name}] 开始编辑HTML元素，选择器: {element_selector}")
        
        try:
            # 准备提示词参数
            prompt_format_args = {
                "original_html": html_content,
                "element_selector": element_selector,
                "edit_instruction": edit_instruction,
                "common_html_tech_requirements": self._get_common_tech_reqs()
            }
            
            # 调用LLM编辑HTML
            response_tuple = await self.call_llm(
                task_prompt_name="edit_element",
                section_name="task:edit_element",
                prompt_format_args=prompt_format_args,
                call_context_name=context,
                project_id_for_logging=project_id,
                expected_response_mime_type="text/plain"
            )
            
            if not response_tuple or not response_tuple[0]:
                logger.error(f"[{context}] HTML编辑失败，LLM返回空响应")
                return None, response_tuple[1] if response_tuple else None
                
            edited_html, llm_log_id = response_tuple
            
            # 清理HTML
            cleaned_html = comprehensive_html_cleanup(edited_html)
            
            if not cleaned_html:
                logger.error(f"[{context}] 编辑后的HTML清理失败")
                return None, llm_log_id
                
            logger.info(f"[{context}] 成功编辑HTML元素")
            return cleaned_html, llm_log_id
            
        except Exception as e:
            logger.error(f"[{context}] HTML编辑过程中发生异常: {e}", exc_info=True)
            return None, None