# backend/app/agents/visual_style_agent.py
import logging
from typing import Optional, List, Dict, Any, Tuple # Added Tuple
from app.agents.base_agent import BaseAgent
from app.models.presentation_model import StructuredPresentationStyleSchema

from app.services.prompt_manager import format_prompt_section

logger = logging.getLogger(__name__)

class VisualStyleAgent(BaseAgent):
    """
    Agent responsible for generating the visual style system for a presentation.
    """
    
    def __init__(self, model_name_override: Optional[str] = None): # <-- 移除 model=None
        """
        Initialize the visual style agent.
        
        Args:
            model_name_override: Optional model name for logging
        """
        super().__init__(
            # 移除 model=model,
            agent_name="VisualStyleAgent",
            system_prompt_name="system",
            agent_prompt_subdir="visual_style",
            model_name_override=model_name_override
        ) # 【删除】system_prompt_version="v1"
        logger.info(f"VisualStyleAgent initialized using {self.model_name_description}.")

    async def process(
        self,
        topic: str,
        num_slides: int,
        style_keywords: Optional[List[str]],
        project_id: str
    ) -> Tuple[Optional[StructuredPresentationStyleSchema], Optional[str]]:
        """
        Generate visual style and presentation outlines for a presentation.
        
        Args:
            topic: The main topic of the presentation
            num_slides: Number of slides planned
            style_keywords: Optional list of style keywords provided by user
            project_id: Current project ID for context and logging
            
        Returns:
            Tuple of (StructuredPresentationStyleSchema with visual style and outlines or None, LLM Interaction Log ID)
        """
        context_name = f"VisualStyleGen_Proj_{project_id}"
        logger.info(f"[{self.agent_name}][{context_name}] Generating visual style and outlines with Instructor...")

        style_keywords_info_for_prompt = (
            f"用户提供的风格偏好关键词是：'{', '.join(style_keywords)}'。请围绕这些关键词进行风格设计，并大胆创新。"
            if style_keywords else
            f"用户未提供明确的风格偏好关键词。请你基于演示文稿主题 '{topic}'，主动推荐并设计一套专业、独特且具有高度美感的视觉风格。"
        )

        # 调整 user_input，明确要求生成大纲
        user_input = f"""
        演示文稿主题: {topic}
        幻灯片数量: {num_slides}
        风格偏好: {style_keywords_info_for_prompt}

        请根据上述信息，生成一个完整的视觉风格指南，并为这个主题设计一个包含 {num_slides} 张幻灯片的详细演示文稿大纲。
        确保大纲的每张幻灯片都有一个明确的标题、至少2个关键要点，并根据内容建议幻灯片类型（例如：'Title Slide', 'Content Slide', 'Data Analysis Slide', 'Summary Slide'）。
        整个大纲应该逻辑严谨，覆盖主题的核心方面。
        """
        
        # 使用format_prompt_section格式化提示词
        prompt = format_prompt_section(
            agent_name="visual_style_agent",
            section_name="task:main",
            user_input=user_input
        )
        
        if not prompt:
            logger.error(f"[{self.agent_name}][{context_name}] Failed to format prompt.")
            return None, None

        # 直接使用完整的StructuredPresentationStyleSchema，让LLM面对真正的设计挑战
        logger.info(f"[{self.agent_name}][{context_name}] 使用完整的StructuredPresentationStyleSchema...")
        response_model, llm_log_id = await self._call_llm_with_instructor(
            prompt=prompt,
            pydantic_schema=StructuredPresentationStyleSchema,
            project_id_for_logging=project_id
        )
        
        if response_model:
            logger.info(f"[{self.agent_name}][{context_name}] 完整设计系统生成成功！")
            logger.info(f"设计系统摘要: {response_model.style_summary_text[:100]}...")
            logger.info(f"生成了 {len(response_model.presentation_outlines)} 个幻灯片大纲")
            return response_model, llm_log_id
        else:
            logger.error(f"[{self.agent_name}][{context_name}] 完整设计系统生成失败")
            return None, llm_log_id
    
