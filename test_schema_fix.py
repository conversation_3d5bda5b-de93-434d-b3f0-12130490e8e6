#!/usr/bin/env python3
"""
测试修复后的 DetailedSlideBlueprintSchema 是否能正确生成 JSON Schema
"""

import sys
import os
import json

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from app.models.presentation_model import DetailedSlideBlueprintSchema, Element

def test_schema_generation():
    """测试 JSON Schema 生成"""
    try:
        # 生成 JSON Schema
        schema = DetailedSlideBlueprintSchema.model_json_schema()
        
        # 检查 key_elements 字段的 schema
        key_elements_schema = schema.get("properties", {}).get("key_elements", {})
        
        print("=== DetailedSlideBlueprintSchema JSON Schema 测试 ===")
        print(f"Schema 生成成功: {bool(schema)}")
        
        # 检查 key_elements 的 items 属性
        items_schema = key_elements_schema.get("items", {})
        print(f"key_elements.items 存在: {bool(items_schema)}")
        
        # 检查是否有 anyOf 或 properties
        has_anyof = "anyOf" in items_schema
        has_properties = "properties" in items_schema
        
        print(f"key_elements.items 有 anyOf: {has_anyof}")
        print(f"key_elements.items 有 properties: {has_properties}")
        
        if has_anyof:
            anyof_items = items_schema.get("anyOf", [])
            print(f"anyOf 项目数量: {len(anyof_items)}")
            
            # 检查每个 anyOf 项目是否有 properties
            for i, item in enumerate(anyof_items):
                has_item_props = "properties" in item
                print(f"  anyOf[{i}] 有 properties: {has_item_props}")
                if has_item_props:
                    props_count = len(item.get("properties", {}))
                    print(f"    properties 数量: {props_count}")
        
        # 保存完整的 schema 到文件以供检查
        with open("detailed_slide_blueprint_schema.json", "w", encoding="utf-8") as f:
            json.dump(schema, f, indent=2, ensure_ascii=False)
        
        print("\n完整 schema 已保存到 detailed_slide_blueprint_schema.json")
        
        # 检查是否修复了原始错误 - 现在应该使用简单的 object 类型
        has_oneof = "oneOf" in items_schema
        has_discriminator = "discriminator" in items_schema
        has_properties = "properties" in items_schema
        has_type = "type" in items_schema

        print(f"key_elements.items 有 oneOf: {has_oneof}")
        print(f"key_elements.items 有 discriminator: {has_discriminator}")
        print(f"key_elements.items 有 properties: {has_properties}")
        print(f"key_elements.items 有 type: {has_type}")

        if has_type and items_schema.get("type") == "object":
            print(f"key_elements.items.type = {items_schema.get('type')}")

        if has_properties:
            props = items_schema.get("properties", {})
            print(f"  properties 数量: {len(props)}")
            if "type" in props:
                type_prop = props["type"]
                if "enum" in type_prop:
                    print(f"  type.enum: {type_prop['enum']}")

        # 检查是否使用了我们的自定义简化 schema
        if has_type and has_properties and not has_oneof and not has_discriminator:
            print("\n✅ 修复成功！key_elements.items 现在使用简化的 object schema（Gemini API 兼容）")
            return True
        elif has_oneof and has_discriminator:
            print("\n⚠️  仍在使用 discriminated union，需要检查自定义 schema 方法")
            return False
        else:
            print("\n❌ 修复失败！schema 结构不符合预期")
            return False
            
    except Exception as e:
        print(f"❌ Schema 生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_element_creation():
    """测试创建 Element 实例"""
    try:
        print("\n=== Element 创建测试 ===")
        
        # 测试创建不同类型的元素
        from app.models.presentation_model import TextElementSchema, KpiCardSchema
        
        text_element = TextElementSchema(
            type="title",
            content="测试标题",
            target_area="title_area"
        )
        print(f"✅ TextElement 创建成功: {text_element.type}")
        
        kpi_element = KpiCardSchema(
            type="kpi_card",
            title="测试KPI",
            value="100",
            icon_fontawesome_class="fas fa-chart-line",
            target_area="kpi_area"
        )
        print(f"✅ KpiCard 创建成功: {kpi_element.type}")
        
        # 测试创建 DetailedSlideBlueprintSchema - 现在使用字典格式
        blueprint = DetailedSlideBlueprintSchema(
            slide_number=1,
            layout_template_name="TitleSlideLayout",
            background_style_description="linear-gradient(135deg, #1A2C3D 0%, #2D4A5C 100%)",
            key_elements=[
                {
                    "type": "title",
                    "content": "测试标题",
                    "target_area": "title_area",
                    "animation_style": "fade-in"
                },
                {
                    "type": "kpi_card",
                    "title": "测试KPI",
                    "value": "100",
                    "icon_fontawesome_class": "fas fa-chart-line",
                    "target_area": "kpi_area",
                    "animation_style": "fade-in"
                }
            ],
            speaker_notes="这是一个测试幻灯片，用于验证修复后的 schema 是否正常工作。包含了标题元素和KPI卡片元素。这个测试验证了 DetailedSlideBlueprintSchema 能够正确处理强类型的 Element 联合类型，确保生成的 JSON Schema 符合 Gemini API 的要求。"
        )
        print(f"✅ DetailedSlideBlueprintSchema 创建成功，包含 {len(blueprint.key_elements)} 个元素")
        
        # 测试 get_typed_elements 方法
        typed_elements = blueprint.get_typed_elements()
        print(f"✅ get_typed_elements 返回 {len(typed_elements)} 个强类型元素")
        
        return True
        
    except Exception as e:
        print(f"❌ Element 创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试 Schema 修复...")
    
    schema_ok = test_schema_generation()
    element_ok = test_element_creation()
    
    if schema_ok and element_ok:
        print("\n🎉 所有测试通过！修复成功！")
        sys.exit(0)
    else:
        print("\n💥 测试失败，需要进一步调试")
        sys.exit(1)
