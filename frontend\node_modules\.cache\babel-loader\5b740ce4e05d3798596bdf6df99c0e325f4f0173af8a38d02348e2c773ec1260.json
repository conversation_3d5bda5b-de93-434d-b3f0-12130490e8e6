{"ast": null, "code": "import { initToolbar } from \"@stagewise/toolbar\";\nimport { useRef, useEffect } from \"react\";\nfunction StagewiseToolbar({\n  config\n}) {\n  const isLoaded = useRef(false);\n  useEffect(() => {\n    if (isLoaded.current) return;\n    isLoaded.current = true;\n    initToolbar(config);\n  }, [config]);\n  return null;\n}\nexport { StagewiseToolbar };", "map": {"version": 3, "names": ["initToolbar", "useRef", "useEffect", "StagewiseToolbar", "config", "isLoaded", "current"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/node_modules/@stagewise/toolbar-react/dist/index.js"], "sourcesContent": ["import { initToolbar } from \"@stagewise/toolbar\";\nimport { useRef, useEffect } from \"react\";\nfunction StagewiseToolbar({ config }) {\n  const isLoaded = useRef(false);\n  useEffect(() => {\n    if (isLoaded.current) return;\n    isLoaded.current = true;\n    initToolbar(config);\n  }, [config]);\n  return null;\n}\nexport {\n  StagewiseToolbar\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAoB;AAChD,SAASC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,SAASC,gBAAgBA,CAAC;EAAEC;AAAO,CAAC,EAAE;EACpC,MAAMC,QAAQ,GAAGJ,MAAM,CAAC,KAAK,CAAC;EAC9BC,SAAS,CAAC,MAAM;IACd,IAAIG,QAAQ,CAACC,OAAO,EAAE;IACtBD,QAAQ,CAACC,OAAO,GAAG,IAAI;IACvBN,WAAW,CAACI,MAAM,CAAC;EACrB,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,OAAO,IAAI;AACb;AACA,SACED,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}