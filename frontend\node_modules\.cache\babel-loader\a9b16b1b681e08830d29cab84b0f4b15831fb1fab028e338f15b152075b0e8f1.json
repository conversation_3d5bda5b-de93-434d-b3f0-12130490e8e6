{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\MiddlePane.js\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect, useState } from 'react';\nimport ChatMessage from './ChatMessage';\nimport { FaPlus } from 'react-icons/fa';\nimport MessageInput from './MessageInput';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MiddlePane = ({\n  chatMessages,\n  onSendMessage,\n  isGenerating,\n  onViewSearchResults,\n  title = \"AI 幻灯片\",\n  onTitleClick,\n  onBackClick,\n  tools = [],\n  currentStep = null,\n  className = \"\"\n}) => {\n  _s();\n  const messagesEndRef = useRef(null);\n  const [isEditingTitle, setIsEditingTitle] = useState(false);\n  const [editableTitle, setEditableTitle] = useState(title || '新对话');\n  const titleRef = useRef(null);\n\n  // 自动滚动到最新消息\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [chatMessages, tools, currentStep]);\n\n  // Update editable title when prop changes\n  useEffect(() => {\n    setEditableTitle(title || '新对话');\n  }, [title]);\n\n  // 处理标题编辑完成\n  const handleTitleEditComplete = () => {\n    setIsEditingTitle(false);\n    if (editableTitle.trim() !== '' && editableTitle !== title) {\n      onTitleClick(editableTitle);\n    } else {\n      setEditableTitle(title || '新对话');\n    }\n  };\n\n  // Handle clicks outside the title input to save changes\n  useEffect(() => {\n    if (isEditingTitle) {\n      const handleClickOutside = e => {\n        if (titleRef.current && !titleRef.current.contains(e.target)) {\n          handleTitleEditComplete();\n        }\n      };\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n      };\n    }\n  }, [isEditingTitle, title, handleTitleEditComplete]);\n\n  // 处理工具查看按钮点击\n  const handleToolView = toolId => {\n    if (onViewSearchResults) {\n      onViewSearchResults(toolId);\n    }\n  };\n\n  // 处理标题输入键盘事件\n  const handleTitleKeyDown = e => {\n    if (e.key === 'Enter') {\n      handleTitleEditComplete();\n    } else if (e.key === 'Escape') {\n      setIsEditingTitle(false);\n      setEditableTitle(title || '新对话');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `border-r border-gray-200 flex flex-col bg-gray-50 max-h-full ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-16 px-3 border-b border-gray-200 bg-white flex items-center sticky top-0 z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onBackClick,\n        className: \"p-2 mr-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100\",\n        title: \"\\u65B0\\u5EFA\\u5E7B\\u706F\\u7247\",\n        children: /*#__PURE__*/_jsxDEV(FaPlus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), isEditingTitle ? /*#__PURE__*/_jsxDEV(\"input\", {\n        ref: titleRef,\n        type: \"text\",\n        value: editableTitle,\n        onChange: e => setEditableTitle(e.target.value),\n        className: \"flex-1 text-base font-medium text-gray-800 border-b border-gray-300 pb-1 focus:outline-none focus:border-blue-500\",\n        autoFocus: true,\n        onKeyDown: handleTitleKeyDown,\n        onBlur: handleTitleEditComplete\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 text-base font-medium text-gray-800 cursor-pointer py-1 px-2 rounded hover:bg-gray-100 overflow-hidden whitespace-nowrap text-ellipsis\",\n        onClick: () => setIsEditingTitle(true),\n        title: editableTitle,\n        children: editableTitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4 space-y-5 custom-scrollbar\",\n      style: {\n        height: \"calc(100% - 110px)\"\n      },\n      children: [chatMessages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: /*#__PURE__*/_jsxDEV(ChatMessage, {\n          message: message,\n          onViewSearchResults: onViewSearchResults\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, message.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 11\n      }, this)), currentStep && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"current-step bg-gray-50 border-l-4 border-blue-500 p-3 rounded my-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"step-indicator font-medium text-blue-500\",\n          children: currentStep.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"step-description text-sm text-gray-600 mt-1\",\n          children: currentStep.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2 bg-gray-50 sticky bottom-0 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(MessageInput, {\n        onSendMessage: onSendMessage,\n        isGenerating: isGenerating,\n        placeholder: \"\\u8F93\\u5165\\u60A8\\u60F3\\u5236\\u4F5C\\u7684\\u5E7B\\u706F\\u7247\\u5185\\u5BB9...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n};\n_s(MiddlePane, \"rGZ207vHd5Yo2Csg4aQQj8jFVpQ=\");\n_c = MiddlePane;\nexport default MiddlePane;\nvar _c;\n$RefreshReg$(_c, \"MiddlePane\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "ChatMessage", "FaPlus", "MessageInput", "jsxDEV", "_jsxDEV", "MiddlePane", "chatMessages", "onSendMessage", "isGenerating", "onViewSearchResults", "title", "onTitleClick", "onBackClick", "tools", "currentStep", "className", "_s", "messagesEndRef", "isEditingTitle", "setIsEditingTitle", "editableTitle", "setEditableTitle", "titleRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleTitleEditComplete", "trim", "handleClickOutside", "e", "contains", "target", "document", "addEventListener", "removeEventListener", "handleToolView", "toolId", "handleTitleKeyDown", "key", "children", "onClick", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "type", "value", "onChange", "autoFocus", "onKeyDown", "onBlur", "style", "height", "map", "message", "id", "name", "description", "placeholder", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/MiddlePane.js"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\nimport ChatMessage from './ChatMessage';\nimport { FaPlus } from 'react-icons/fa';\nimport MessageInput from './MessageInput';\n\nconst MiddlePane = ({ \n  chatMessages, \n  onSendMessage, \n  isGenerating, \n  onViewSearchResults,\n  title = \"AI 幻灯片\",\n  onTitleClick,\n  onBackClick,\n  tools = [],\n  currentStep = null,\n  className = \"\"\n}) => {\n  const messagesEndRef = useRef(null);\n  const [isEditingTitle, setIsEditingTitle] = useState(false);\n  const [editableTitle, setEditableTitle] = useState(title || '新对话');\n  const titleRef = useRef(null);\n\n  // 自动滚动到最新消息\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [chatMessages, tools, currentStep]);\n\n  // Update editable title when prop changes\n  useEffect(() => {\n    setEditableTitle(title || '新对话');\n  }, [title]);\n\n  // 处理标题编辑完成\n  const handleTitleEditComplete = () => {\n    setIsEditingTitle(false);\n    if (editableTitle.trim() !== '' && editableTitle !== title) {\n      onTitleClick(editableTitle);\n    } else {\n      setEditableTitle(title || '新对话');\n    }\n  };\n\n  // Handle clicks outside the title input to save changes\n  useEffect(() => {\n    if (isEditingTitle) {\n      const handleClickOutside = (e) => {\n        if (titleRef.current && !titleRef.current.contains(e.target)) {\n          handleTitleEditComplete();\n        }\n      };\n      document.addEventListener('mousedown', handleClickOutside);\n      return () => {\n        document.removeEventListener('mousedown', handleClickOutside);\n      };\n    }\n  }, [isEditingTitle, title, handleTitleEditComplete]);\n\n  // 处理工具查看按钮点击\n  const handleToolView = (toolId) => {\n    if (onViewSearchResults) {\n      onViewSearchResults(toolId);\n    }\n  };\n\n  // 处理标题输入键盘事件\n  const handleTitleKeyDown = (e) => {\n    if (e.key === 'Enter') {\n      handleTitleEditComplete();\n    } else if (e.key === 'Escape') {\n      setIsEditingTitle(false);\n      setEditableTitle(title || '新对话');\n    }\n  };\n\n  return (\n    <div className={`border-r border-gray-200 flex flex-col bg-gray-50 max-h-full ${className}`}>\n      {/* Header with title and new presentation button - Make sticky */}\n      <div className=\"h-16 px-3 border-b border-gray-200 bg-white flex items-center sticky top-0 z-10\">\n        <button \n          onClick={onBackClick}\n          className=\"p-2 mr-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100\"\n          title=\"新建幻灯片\"\n        >\n          <FaPlus size={16} />\n        </button>\n\n        {isEditingTitle ? (\n          <input\n            ref={titleRef}\n            type=\"text\"\n            value={editableTitle}\n            onChange={(e) => setEditableTitle(e.target.value)}\n            className=\"flex-1 text-base font-medium text-gray-800 border-b border-gray-300 pb-1 focus:outline-none focus:border-blue-500\"\n            autoFocus\n            onKeyDown={handleTitleKeyDown}\n            onBlur={handleTitleEditComplete}\n          />\n        ) : (\n          <div \n            className=\"flex-1 text-base font-medium text-gray-800 cursor-pointer py-1 px-2 rounded hover:bg-gray-100 overflow-hidden whitespace-nowrap text-ellipsis\"\n            onClick={() => setIsEditingTitle(true)}\n            title={editableTitle}\n          >\n            {editableTitle}\n          </div>\n        )}\n      </div>\n\n      {/* Messages area - Adjust to account for sticky header and footer */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-5 custom-scrollbar\" style={{ height: \"calc(100% - 110px)\" }}>\n        {chatMessages.map((message) => (\n          <div key={message.id} className=\"space-y-2\">\n          <ChatMessage \n            message={message}\n            onViewSearchResults={onViewSearchResults}\n            />\n          </div>\n        ))}\n\n        {currentStep && (\n          <div className=\"current-step bg-gray-50 border-l-4 border-blue-500 p-3 rounded my-4\">\n            <span className=\"step-indicator font-medium text-blue-500\">{currentStep.name}</span>\n            <p className=\"step-description text-sm text-gray-600 mt-1\">{currentStep.description}</p>\n          </div>\n        )}\n        \n       \n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input area - Make sticky at bottom */}\n      <div className=\"p-2 bg-gray-50 sticky bottom-0 border-t border-gray-200\">\n        <MessageInput \n          onSendMessage={onSendMessage} \n          isGenerating={isGenerating}\n          placeholder=\"输入您想制作的幻灯片内容...\"\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default MiddlePane; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,MAAM,QAAQ,gBAAgB;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,UAAU,GAAGA,CAAC;EAClBC,YAAY;EACZC,aAAa;EACbC,YAAY;EACZC,mBAAmB;EACnBC,KAAK,GAAG,QAAQ;EAChBC,YAAY;EACZC,WAAW;EACXC,KAAK,GAAG,EAAE;EACVC,WAAW,GAAG,IAAI;EAClBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,cAAc,GAAGpB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAACW,KAAK,IAAI,KAAK,CAAC;EAClE,MAAMY,QAAQ,GAAGzB,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACA,MAAM0B,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAP,cAAc,CAACQ,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED7B,SAAS,CAAC,MAAM;IACdyB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACjB,YAAY,EAAEO,KAAK,EAAEC,WAAW,CAAC,CAAC;;EAEtC;EACAhB,SAAS,CAAC,MAAM;IACduB,gBAAgB,CAACX,KAAK,IAAI,KAAK,CAAC;EAClC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;;EAEX;EACA,MAAMkB,uBAAuB,GAAGA,CAAA,KAAM;IACpCT,iBAAiB,CAAC,KAAK,CAAC;IACxB,IAAIC,aAAa,CAACS,IAAI,CAAC,CAAC,KAAK,EAAE,IAAIT,aAAa,KAAKV,KAAK,EAAE;MAC1DC,YAAY,CAACS,aAAa,CAAC;IAC7B,CAAC,MAAM;MACLC,gBAAgB,CAACX,KAAK,IAAI,KAAK,CAAC;IAClC;EACF,CAAC;;EAED;EACAZ,SAAS,CAAC,MAAM;IACd,IAAIoB,cAAc,EAAE;MAClB,MAAMY,kBAAkB,GAAIC,CAAC,IAAK;QAChC,IAAIT,QAAQ,CAACG,OAAO,IAAI,CAACH,QAAQ,CAACG,OAAO,CAACO,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAC,EAAE;UAC5DL,uBAAuB,CAAC,CAAC;QAC3B;MACF,CAAC;MACDM,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;MAC1D,OAAO,MAAM;QACXI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;MAC/D,CAAC;IACH;EACF,CAAC,EAAE,CAACZ,cAAc,EAAER,KAAK,EAAEkB,uBAAuB,CAAC,CAAC;;EAEpD;EACA,MAAMS,cAAc,GAAIC,MAAM,IAAK;IACjC,IAAI7B,mBAAmB,EAAE;MACvBA,mBAAmB,CAAC6B,MAAM,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIR,CAAC,IAAK;IAChC,IAAIA,CAAC,CAACS,GAAG,KAAK,OAAO,EAAE;MACrBZ,uBAAuB,CAAC,CAAC;IAC3B,CAAC,MAAM,IAAIG,CAAC,CAACS,GAAG,KAAK,QAAQ,EAAE;MAC7BrB,iBAAiB,CAAC,KAAK,CAAC;MACxBE,gBAAgB,CAACX,KAAK,IAAI,KAAK,CAAC;IAClC;EACF,CAAC;EAED,oBACEN,OAAA;IAAKW,SAAS,EAAE,gEAAgEA,SAAS,EAAG;IAAA0B,QAAA,gBAE1FrC,OAAA;MAAKW,SAAS,EAAC,iFAAiF;MAAA0B,QAAA,gBAC9FrC,OAAA;QACEsC,OAAO,EAAE9B,WAAY;QACrBG,SAAS,EAAC,+EAA+E;QACzFL,KAAK,EAAC,gCAAO;QAAA+B,QAAA,eAEbrC,OAAA,CAACH,MAAM;UAAC0C,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,EAER7B,cAAc,gBACbd,OAAA;QACE4C,GAAG,EAAE1B,QAAS;QACd2B,IAAI,EAAC,MAAM;QACXC,KAAK,EAAE9B,aAAc;QACrB+B,QAAQ,EAAGpB,CAAC,IAAKV,gBAAgB,CAACU,CAAC,CAACE,MAAM,CAACiB,KAAK,CAAE;QAClDnC,SAAS,EAAC,mHAAmH;QAC7HqC,SAAS;QACTC,SAAS,EAAEd,kBAAmB;QAC9Be,MAAM,EAAE1B;MAAwB;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,gBAEF3C,OAAA;QACEW,SAAS,EAAC,+IAA+I;QACzJ2B,OAAO,EAAEA,CAAA,KAAMvB,iBAAiB,CAAC,IAAI,CAAE;QACvCT,KAAK,EAAEU,aAAc;QAAAqB,QAAA,EAEpBrB;MAAa;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN3C,OAAA;MAAKW,SAAS,EAAC,uDAAuD;MAACwC,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAqB,CAAE;MAAAf,QAAA,GAC5GnC,YAAY,CAACmD,GAAG,CAAEC,OAAO,iBACxBtD,OAAA;QAAsBW,SAAS,EAAC,WAAW;QAAA0B,QAAA,eAC3CrC,OAAA,CAACJ,WAAW;UACV0D,OAAO,EAAEA,OAAQ;UACjBjD,mBAAmB,EAAEA;QAAoB;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC,GAJMW,OAAO,CAACC,EAAE;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKf,CACN,CAAC,EAEDjC,WAAW,iBACVV,OAAA;QAAKW,SAAS,EAAC,qEAAqE;QAAA0B,QAAA,gBAClFrC,OAAA;UAAMW,SAAS,EAAC,0CAA0C;UAAA0B,QAAA,EAAE3B,WAAW,CAAC8C;QAAI;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpF3C,OAAA;UAAGW,SAAS,EAAC,6CAA6C;UAAA0B,QAAA,EAAE3B,WAAW,CAAC+C;QAAW;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CACN,eAGD3C,OAAA;QAAK4C,GAAG,EAAE/B;MAAe;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGN3C,OAAA;MAAKW,SAAS,EAAC,yDAAyD;MAAA0B,QAAA,eACtErC,OAAA,CAACF,YAAY;QACXK,aAAa,EAAEA,aAAc;QAC7BC,YAAY,EAAEA,YAAa;QAC3BsD,WAAW,EAAC;MAAiB;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/B,EAAA,CA3IIX,UAAU;AAAA0D,EAAA,GAAV1D,UAAU;AA6IhB,eAAeA,UAAU;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}