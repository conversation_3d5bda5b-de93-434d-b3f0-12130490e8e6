# backend/app/models/slide_models.py
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict, Any

class SlideBase(BaseModel):
    id: str
    html_content: str
    js_config: Optional[Dict[str, Any]] = None
    order: int

class Slide(SlideBase):
    pass

# --- New Pydantic model for Slide Outline Item ---
class SlideOutlineItemSchema(BaseModel):
    title: str = Field(description="The title of the slide")
    focus: str = Field(description="A brief focus for the slide content")

class SlideGenerationRequest(BaseModel):
    prompt: str = Field(..., min_length=1, description="Text prompt for slide generation")
    # project_id: Optional[str] = None # For continuing an existing project later
    # num_slides: Optional[int] = Field(default=5, ge=1, le=10) # Later enhancement

class SlideGenerationResponse(BaseModel):
    project_id: str
    title: str
    status: str # e.g., "processing", "completed", "error"
    message: str
    slides: Optional[List[Slide]] = None # Initially empty, will be populated as slides are generated
    total_slides_planned: Optional[int] = None

class AiChatMessage(BaseModel):
    sender: str # "user" or "ai"
    text: str
    type: Optional[str] = None # e.g., "tool_usage", "status_update", "error"
    tool_name: Optional[str] = None
    icon: Optional[str] = None # Emoji or icon identifier
    query: Optional[str] = None
    thinking: Optional[bool] = None # If AI is currently processing this step
    data: Optional[Dict[str, Any]] = None # For structured data like search results or slide previews

# 新增工具调用相关模型
class ToolCall(BaseModel):
    tool_id: str = Field(..., description="唯一标识工具调用的ID")
    tool_type: str = Field(..., description="工具类型，如search, chart, image等")
    tool_name: str = Field(..., description="工具名称")
    description: str = Field(..., description="工具调用的简短描述")
    status: Optional[str] = Field(default="running", description="工具调用状态")
    result: Optional[Dict[str, Any]] = None

class OutlineEvent(BaseModel):
    type: str = Field(default="outline", description="事件类型为大纲")
    content: str = Field(..., description="大纲内容片段")

class CodeEvent(BaseModel):
    type: str = Field(default="code", description="事件类型为代码")
    content: str = Field(..., description="代码内容片段")

class ToolStartEvent(BaseModel):
    type: str = Field(default="tool_start", description="事件类型为工具调用开始")
    tool_id: str = Field(..., description="工具ID")
    tool_type: str = Field(..., description="工具类型")
    tool_name: str = Field(..., description="工具名称")
    description: str = Field(..., description="工具调用描述")

class ToolEndEvent(BaseModel):
    type: str = Field(default="tool_end", description="事件类型为工具调用结束")
    tool_id: str = Field(..., description="工具ID")
    result: Dict[str, Any] = Field(..., description="工具调用结果")

class OrchestratorProgress(BaseModel):
    project_id: str
    message: Optional[AiChatMessage] = None # For chat updates
    slide_update: Optional[Slide] = None # When a new slide is ready for preview
    code_content: Optional[str] = None # For the 'code' tab
    thinking_content: Optional[str] = None # For the 'thinking' tab
    status: Optional[str] = None # Overall project status
    current_slide_index: Optional[int] = None
    total_slides: Optional[int] = None
    type: Optional[str] = None # 事件类型
    
    # 工具调用相关字段
    tool_id: Optional[str] = None # 工具ID
    tool_type: Optional[str] = None # 工具类型
    tool_name: Optional[str] = None # 工具名称
    description: Optional[str] = None # 描述
    content: Optional[str] = None # 内容（用于大纲和代码）
    result: Optional[Dict[str, Any]] = None # 工具调用结果
    
    # 新增字段
    current_agent_name: Optional[str] = None # 当前工作的Agent名称
    file_saved: Optional[Dict[str, str]] = None # 保存文件的信息，如 {"type": "slide_html", "path": "path_to_file"}
    ui_action: Optional[Dict[str, str]] = None # 用于UI交互控制，如选择标签页