from sqlalchemy.orm import Session
from app.db.models import LLMInteractionLog
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

def create_llm_interaction_log(db: Session, **kwargs: Any) -> LLMInteractionLog:
    """
    Create a new LLM interaction log entry.
    
    Args:
        db: Database session
        **kwargs: Fields for the LLMInteractionLog model
        
    Returns:
        Created LLMInteractionLog instance
    """
    # Ensure request_timestamp is timezone-aware
    if "request_timestamp" not in kwargs or not kwargs["request_timestamp"]:
        kwargs["request_timestamp"] = datetime.now(timezone.utc)
    
    db_log = LLMInteractionLog(**kwargs)
    db.add(db_log)
    db.commit()
    db.refresh(db_log)
    return db_log

def update_llm_interaction_log(db: Session, log_id: str, update_data: Dict[str, Any]) -> Optional[LLMInteractionLog]:
    """
    Update an existing LLM interaction log entry.
    
    Args:
        db: Database session
        log_id: ID of the log to update
        update_data: Dictionary of fields to update
        
    Returns:
        Updated LLMInteractionLog instance or None if not found
    """
    db_log = db.query(LLMInteractionLog).filter(LLMInteractionLog.id == log_id).first()
    if db_log:
        # Automatically set response_timestamp if not provided
        if "response_timestamp" not in update_data or not update_data["response_timestamp"]:
            update_data["response_timestamp"] = datetime.now(timezone.utc)
        
        for key, value in update_data.items():
            setattr(db_log, key, value)
        db.commit()
        db.refresh(db_log)
        return db_log
    return None

def get_llm_interaction_log_by_id(db: Session, log_id: str) -> Optional[LLMInteractionLog]:
    """
    Get an LLM interaction log by ID.
    
    Args:
        db: Database session
        log_id: ID of the log to retrieve
        
    Returns:
        LLMInteractionLog instance or None if not found
    """
    return db.query(LLMInteractionLog).filter(LLMInteractionLog.id == log_id).first()

def get_llm_interactions_for_project(db: Session, project_id: str, limit: int = 100, skip: int = 0) -> List[LLMInteractionLog]:
    """
    Get LLM interaction logs for a specific project.
    
    Args:
        db: Database session
        project_id: ID of the project
        limit: Maximum number of records to return
        skip: Number of records to skip (for pagination)
        
    Returns:
        List of LLMInteractionLog instances
    """
    return db.query(LLMInteractionLog).filter(LLMInteractionLog.project_id == project_id)\
             .order_by(LLMInteractionLog.request_timestamp.desc()).offset(skip).limit(limit).all() 