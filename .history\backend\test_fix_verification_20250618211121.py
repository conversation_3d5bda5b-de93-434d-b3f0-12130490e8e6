#!/usr/bin/env python3
"""
修复验证测试脚本 - 验证兼容性解决方案是否有效
"""

import sys
import os
import json

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.models.presentation_model import (
    DetailedSlideBlueprintSchema,
    TextElementSchema,
    KpiCardSchema,
    ChartBlueprintSchema,
    ImageElementSchema,
    ChartConfig
)

def test_compatibility_solution():
    """测试兼容性解决方案"""
    print("🔧 测试兼容性解决方案")
    print("=" * 40)
    
    # 1. 创建测试数据（模拟Instructor生成的Dict格式）
    test_elements = [
        {
            "type": "title",
            "content": "珍珠港事件：历史的转折点",
            "target_area": "title_area",
            "animation_style": "fade-in-up"
        },
        {
            "type": "kpi_card",
            "title": "伤亡人数",
            "value": "2,403人",
            "change": "+100%",
            "icon_fontawesome_class": "fa-solid fa-exclamation-triangle",
            "target_area": "kpi_card_1",
            "animation_style": "slide-in-left"
        },
        {
            "type": "chart",
            "title": "太平洋战争时间线",
            "chart_type": "line",
            "data_fabrication_instruction": "创建一个时间线图表，展示1940-1945年期间太平洋战争的重要事件发展轨迹。X轴为年份，Y轴为事件重要性等级。数据应包含珍珠港事件、中途岛战役、瓜达尔卡纳尔岛战役等关键节点，用折线图展示战争强度的变化趋势。",
            "final_chart_js_config": {
                "chart_canvas_id": "timeline_chart",
                "chart_type": "line",
                "chart_js_data": {
                    "labels": ["1940", "1941", "1942", "1943", "1944", "1945"],
                    "datasets": [{
                        "label": "重要事件",
                        "data": [1, 3, 5, 4, 6, 2],
                        "borderColor": "#dc2626",
                        "backgroundColor": "rgba(220, 38, 38, 0.1)"
                    }]
                },
                "chart_js_options": {
                    "responsive": True,
                    "maintainAspectRatio": False
                },
                "chart_title": "太平洋战争时间线",
                "data_source_description": "基于历史记录"
            },
            "target_area": "chart_area_1",
            "animation_style": "zoom-in"
        }
    ]
    
    # 2. 创建DetailedSlideBlueprintSchema（使用Dict）
    blueprint = DetailedSlideBlueprintSchema(
        slide_number=1,
        layout_template_name="ContentSlideLayout",
        background_style_description="linear-gradient(135deg, #1e1b4b 0%, #7c2d12 100%)",
        key_elements=test_elements,  # 传入Dict列表，不是强类型对象
        speaker_notes="这张幻灯片介绍了珍珠港事件的基本事实。1941年12月7日的袭击造成2,403人死亡，这一事件标志着美国正式参与第二次世界大战。从图表中可以看出，珍珠港事件是太平洋战争期间的一个关键转折点，它直接导致了美国对日宣战，并改变了整个战争的进程。"
    )
    
    print("✅ DetailedSlideBlueprintSchema创建成功（Dict输入）")
    print(f"   - 幻灯片编号: {blueprint.slide_number}")
    print(f"   - 原始元素数量: {len(blueprint.key_elements)}")
    print(f"   - 元素类型: {[elem.get('type') for elem in blueprint.key_elements]}")
    
    # 3. 测试强类型转换
    typed_elements = blueprint.get_typed_elements()
    print(f"\n✅ 强类型转换成功")
    print(f"   - 转换后元素数量: {len(typed_elements)}")
    
    for i, element in enumerate(typed_elements):
        print(f"   - 元素 {i+1}: {element.type} ({type(element).__name__})")
        if isinstance(element, TextElementSchema):
            print(f"     内容: {element.content[:30]}...")
        elif isinstance(element, KpiCardSchema):
            print(f"     KPI: {element.title} = {element.value}")
        elif isinstance(element, ChartBlueprintSchema):
            print(f"     图表: {element.title} ({element.chart_type})")
    
    # 4. 测试JSON序列化（模拟Instructor的输出）
    blueprint_json = blueprint.model_dump_json(indent=2)
    print(f"\n✅ JSON序列化成功，长度: {len(blueprint_json)} 字符")
    
    # 5. 测试JSON反序列化（模拟从数据库加载）
    blueprint_dict = json.loads(blueprint_json)
    blueprint_restored = DetailedSlideBlueprintSchema.model_validate(blueprint_dict)
    restored_typed_elements = blueprint_restored.get_typed_elements()
    
    print(f"✅ JSON反序列化成功")
    print(f"   - 恢复后元素数量: {len(restored_typed_elements)}")
    
    # 6. 验证类型安全性
    for element in restored_typed_elements:
        if isinstance(element, ChartBlueprintSchema):
            chart_config = element.final_chart_js_config
            print(f"✅ 图表配置类型安全: {type(chart_config).__name__}")
            print(f"   - 图表ID: {chart_config.chart_canvas_id}")
            print(f"   - 数据集数量: {len(chart_config.chart_js_data.get('datasets', []))}")
    
    print("\n🎉 兼容性解决方案验证成功！")
    print("✅ Dict输入格式与Instructor兼容")
    print("✅ 强类型转换功能正常")
    print("✅ JSON序列化/反序列化正常")
    print("✅ 类型安全性保持")

if __name__ == "__main__":
    test_compatibility_solution() 