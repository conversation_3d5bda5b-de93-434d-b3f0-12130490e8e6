# backend/app/agents/chat_agent.py
import logging
import asyncio
import uuid
import json
from typing import Optional, AsyncGenerator, Dict, Any, List, Union
import time

import google.genai as genai
from google.genai import types
from google.api_core import exceptions as google_exceptions

from app.agents.base_agent import BaseAgent
from app.models.presentation_model import AiChatMessage
from app.services.prompt_manager import format_prompt_section
from app.utils.api_key_manager import key_manager
from app.utils.llm_communication_logger import llm_comm_logger
from app.core.config import settings # <-- 保留：用于获取GEMINI_MODEL_NAME
from app.services.file_processor import FileProcessor
from app.crud import crud_chat
from app.db.session import get_db

logger = logging.getLogger(__name__)

class ChatAgent(BaseAgent):
    def __init__(self, model_name_override: Optional[str] = None):
        super().__init__(
            agent_name="ChatAgent",
            system_prompt_name="system",
            agent_prompt_subdir="chat",
            model_name_override=model_name_override
        )
        logger.info(f"ChatAgent initialized using {self.model_name_description}.")

    async def process_chat_stream( # <-- 确保这一行是 ChatAgent 类的方法
        self,
        user_message: str,
        project_id_for_logging: Optional[str] = None,
        chat_history: Optional[List[Dict[str, Any]]] = None,
        turbo_mode: bool = False  # 添加极速模式参数
    ) -> AsyncGenerator[str, None]:
        context_name = f"ChatStream_Proj_{project_id_for_logging or 'NoProj'}"
        logger.info(f"[{self.agent_name}][{context_name}] Starting chat stream for user message: {user_message[:50]}...")
        logger.info(f"[{self.agent_name}][{context_name}] Turbo mode: {'ON' if turbo_mode else 'OFF'}")

        main_ai_response_id = f"ai-response-{uuid.uuid4().hex[:8]}"

        full_prompt_content = ""
        try:
            # 如果没有提供聊天历史但有项目ID，从数据库获取历史记录
            if not chat_history and project_id_for_logging:
                try:
                    db = next(get_db())
                    db_messages = crud_chat.get_chat_messages_for_project(db=db, project_id=project_id_for_logging)
                    chat_history = []
                    for msg in db_messages:
                        chat_history.append({
                            "role": "user" if msg.sender == "user" else "model",
                            "content": msg.text,
                            "thinking": getattr(msg, "thinking", False)
                        })
                    logger.info(f"[{context_name}] 从数据库加载了 {len(chat_history)} 条聊天历史记录")
                except Exception as e:
                    logger.error(f"[{context_name}] 加载聊天历史记录失败: {e}")
                    chat_history = []
                finally:
                    db.close()

            # === MODIFICATION START: 初始化文件处理器和变量 ===
            file_processor = None
            uploaded_files = []
            file_parts = []
            base64_images = []
            
            # 首先检测base64图片数据
            import re
            import base64
            
            # 1. 检测 data URL 格式的图片 (data:image/jpeg;base64,...)
            data_url_pattern = r'data:image\/([a-zA-Z]+);base64,([A-Za-z0-9+/=]+)'
            data_url_matches = re.findall(data_url_pattern, user_message)
            
            for format_type, b64_data in data_url_matches:
                try:
                    # 验证base64数据
                    image_data = base64.b64decode(b64_data)
                    mime_type = f"image/{format_type.lower()}"
                    
                    base64_images.append({
                        'data': image_data,
                        'mime_type': mime_type,
                        'format': format_type
                    })
                    logger.info(f"[{context_name}] 检测到base64图片: {mime_type}, 大小: {len(image_data)} bytes")
                except Exception as e:
                    logger.warning(f"[{context_name}] base64图片解码失败: {e}")
            
            # 清理用户消息，移除base64数据避免提示词过长
            cleaned_user_message = user_message
            if base64_images:
                # 将base64 URL替换为简短描述
                for format_type, b64_data in data_url_matches:
                    original_data_url = f"data:image/{format_type};base64,{b64_data}"
                    replacement = f"[图片: {format_type.upper()}, {len(base64.b64decode(b64_data))} bytes]"
                    cleaned_user_message = cleaned_user_message.replace(original_data_url, replacement)
            
            # 使用清理后的消息构建提示词
            cleaned_prompt_args = {"user_message": cleaned_user_message}
            
            # 如果有聊天历史，添加到提示词参数中
            if chat_history and len(chat_history) > 0:
                # 过滤掉thinking消息，只保留用户和AI的对话
                filtered_history = [msg for msg in chat_history if not msg.get("thinking", False)]
                if filtered_history:
                    # 将聊天历史格式化为字符串
                    history_text = ""
                    for msg in filtered_history:
                        role = "用户" if msg["role"] == "user" else "AI"
                        history_text += f"{role}: {msg['content']}\n\n"
                    cleaned_prompt_args["chat_history"] = history_text
                    logger.info(f"[{context_name}] 添加了 {len(filtered_history)} 条聊天历史记录到提示词中")
            
            task_prompt_template = format_prompt_section(
                agent_name="chat_agent",
                section_name="task:main",
                **cleaned_prompt_args
            )

            if not task_prompt_template:
                error_message = AiChatMessage(
                    sender="system",
                    text="Error: Failed to load chat prompt. Please try again.",
                    icon="⚠️",
                    id=f"err-load-prompt-{uuid.uuid4().hex[:8]}",
                    project_id=project_id_for_logging
                )
                yield f"data: {error_message.model_dump_json()}\n\n"
                return
            
            full_prompt_content = self._build_full_prompt(task_prompt_template)

            api_key = await key_manager.get_next_key_async()
            
            client = genai.Client(api_key=api_key)

            model_name = getattr(settings, 'GEMINI_MODEL_NAME', None) or "gemini-2.5-flash-preview-05-20"

            safety_settings_list = [
                types.SafetySetting(category=types.HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold=types.HarmBlockThreshold.BLOCK_NONE),
                types.SafetySetting(category=types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold=types.HarmBlockThreshold.BLOCK_NONE),
                types.SafetySetting(category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold=types.HarmBlockThreshold.BLOCK_NONE),
                types.SafetySetting(category=types.HarmCategory.HARM_CATEGORY_HARASSMENT, threshold=types.HarmBlockThreshold.BLOCK_NONE)
            ]

            # 根据极速模式设置不同的配置
            if turbo_mode:
                # 极速模式：禁用思考和搜索
                generate_content_config = types.GenerateContentConfig(
                    temperature=0.7,
                    max_output_tokens=65536,
                    thinking_config=types.ThinkingConfig(
                        thinking_budget=0  # 设置为0禁用思考
                    ),
                    safety_settings=safety_settings_list
                )
                logger.info(f"[{context_name}] 极速模式已启用：禁用思考和搜索")
            else:
                # 正常模式：启用思考和搜索
                generate_content_config = types.GenerateContentConfig(
                    temperature=0.7,
                    max_output_tokens=65536,
                    thinking_config=types.ThinkingConfig(
                        include_thoughts=True,
                        thinking_budget=24576
                    ),
                    safety_settings=safety_settings_list
                )

            logger.debug(f"Agent '{self.agent_name}': Using safety_settings in config")
            logger.debug(f"Agent '{self.agent_name}': Sending prompt (first 300 chars): {full_prompt_content[:300]}...")

            # 记录LLM请求到项目日志文件
            llm_comm_logger.log_llm_request(
                project_id=project_id_for_logging,
                agent_name=self.agent_name,
                prompt_content=full_prompt_content,
                temperature=0.7,
                model_name=model_name,
                expected_response_type="chat_stream",
                context=f"chat_stream_turbo_{turbo_mode}"
            )

            # 根据极速模式决定是否使用搜索工具
            google_search_tool = None
            if not turbo_mode:
                # 只在非极速模式下启用Google搜索工具
                google_search_tool = types.Tool(
                    google_search=types.GoogleSearch()
                )
                
                # 修改配置以包含搜索工具
                generate_content_config_with_tools = types.GenerateContentConfig(
                    temperature=0.7,
                    max_output_tokens=65536,
                    thinking_config=types.ThinkingConfig(
                        include_thoughts=True,
                        thinking_budget=24576
                    ),
                    safety_settings=safety_settings_list,
                    tools=[google_search_tool]  # 添加Google搜索工具
                )
            else:
                # 极速模式下不使用工具
                generate_content_config_with_tools = generate_content_config

            thinking_message_id = f"thinking-{main_ai_response_id}"
            
            # === MODIFICATION START: 处理文件和图片数据 ===
            file_processor = FileProcessor(api_key)
            
            # 2. 检测传统文件路径
            potential_file_paths = []
            path_patterns = [
                r'[A-Za-z]:[\\\/][^\\\/:\*\?"<>\|]*\.[a-zA-Z0-9]{2,5}',  # Windows绝对路径
                r'\/[^\/\s\*\?"<>\|]*\.[a-zA-Z0-9]{2,5}',  # Unix绝对路径  
                r'\.?\/[^\/\s\*\?"<>\|]*\.[a-zA-Z0-9]{2,5}',  # 相对路径
                r'[a-zA-Z0-9_\-\.\\\/]+\.[a-zA-Z0-9]{2,5}(?=\s|$)',  # 简单文件名或路径
            ]
            
            for pattern in path_patterns:
                matches = re.findall(pattern, user_message)
                potential_file_paths.extend(matches)
            
            # 3. 处理base64图片数据
            
            for img_data in base64_images:
                try:
                    logger.info(f"[{context_name}] 开始处理base64图片: {img_data['mime_type']}")
                    
                    # 直接创建内联图片Part，不需要上传到服务器
                    file_parts.append(
                        types.Part(
                            inline_data=types.Blob(
                                mime_type=img_data['mime_type'],
                                data=img_data['data']
                            )
                        )
                    )
                    
                    # 发送图片处理状态消息
                    image_status_message = AiChatMessage(
                        id=f"image-status-{int(time.time())}",
                        sender="ai",
                        text=f"🖼️ 已成功加载图片: {img_data['format'].upper()}, 大小: {len(img_data['data'])} bytes"
                    )
                    yield image_status_message
                    
                    logger.info(f"[{context_name}] base64图片处理成功: {img_data['mime_type']}")
                    
                except Exception as e:
                    logger.error(f"[{context_name}] base64图片处理失败: {e}")
                    error_message = AiChatMessage(
                        id=f"image-error-{int(time.time())}",
                        sender="ai",
                        text=f"❌ 图片处理失败: {str(e)}"
                    )
                    yield error_message
            
            # 4. 处理传统文件路径
            for file_path in potential_file_paths:
                file_path = file_path.strip()
                if not file_path:
                    continue
                    
                logger.info(f"[{context_name}] 检测到潜在文件路径: {file_path}")
                
                # 验证文件是否存在且可处理
                validation = file_processor.validate_file(file_path)
                if validation['valid']:
                    logger.info(f"[{context_name}] 开始处理文件: {file_path}")
                    
                    # 上传文件
                    upload_result = file_processor.upload_file(file_path)
                    if upload_result['success']:
                        file_info = upload_result['file_info']
                        uploaded_files.append(file_info)
                        
                        # 添加文件数据到内容中
                        file_parts.append(
                            types.Part(
                                file_data=types.FileData(
                                    file_uri=file_info['uri'],
                                    mime_type=file_info['mime_type']
                                )
                            )
                        )
                        
                        logger.info(f"[{context_name}] 文件上传成功: {file_info['uri']}")
                        
                        # 发送文件处理状态消息
                        file_status_message = AiChatMessage(
                            id=f"file-status-{int(time.time())}",
                            sender="ai",
                            text=f"📁 已成功处理文件: {file_info['display_name']} ({file_info['category']})",
                            data={"file_processed": True, "file_info": file_info}
                        )
                        yield file_status_message
                    else:
                        logger.error(f"[{context_name}] 文件上传失败: {upload_result['error']}")
                        error_message = AiChatMessage(
                            id=f"file-error-{int(time.time())}",
                            sender="ai",
                            text=f"❌ 文件处理失败: {upload_result['error']}"
                        )
                        yield error_message
                else:
                    logger.warning(f"[{context_name}] 文件验证失败: {validation['error']}")
            # === MODIFICATION END ===

            # 构建请求内容
            contents = [
                types.Content(
                    parts=[types.Part(text=full_prompt_content)] + file_parts,  # === MODIFICATION: 添加文件部分 ===
                    role='user'
                )
            ]
            
            # === MODIFICATION START: 使用缓存功能 ===
            # 检查是否有足够的历史记录来使用缓存
            # 根据Gemini API文档，最小缓存输入令牌数为2.5 Flash为1,024，2.5 Pro为2,048
            use_cache = False
            cache_name = None
            
            if not turbo_mode and chat_history and len(chat_history) >= 5 and project_id_for_logging:  # 极速模式下不使用缓存
                try:
                    # 尝试获取或创建缓存
                    cache_key = f"chat_{project_id_for_logging}"
                    
                    # 检查是否已有缓存
                    existing_caches = list(client.caches.list())
                    existing_cache = next((c for c in existing_caches if c.display_name == cache_key), None)
                    
                    if existing_cache:
                        # 使用现有缓存
                        cache_name = existing_cache.name
                        logger.info(f"[{context_name}] 使用现有缓存: {cache_name}")
                        use_cache = True
                    else:
                        # 创建新缓存
                        # 将历史记录转换为缓存内容
                        cached_content = []
                        for msg in chat_history[:-1]:  # 不包括最后一条消息
                            role = "user" if msg["role"] == "user" else "model"
                            cached_content.append(
                                types.Content(
                                    parts=[types.Part(text=msg["content"])],
                                    role=role
                                )
                            )
                        
                        if cached_content:
                            # 创建缓存，设置1小时过期时间
                            cache = client.caches.create(
                                config=types.CreateCachedContentConfig(
                                    display_name=cache_key,
                                    contents=cached_content,
                                    ttl="3600s"  # 1小时
                                )
                            )
                            cache_name = cache.name
                            logger.info(f"[{context_name}] 创建新缓存: {cache_name}")
                            use_cache = True
                except Exception as cache_error:
                    logger.error(f"[{context_name}] 缓存创建失败: {cache_error}")
                    use_cache = False
            # === MODIFICATION END ===
            
            # 使用客户端API发送请求
            if use_cache and cache_name:
                # 使用缓存发送请求
                logger.info(f"[{context_name}] 使用缓存发送请求: {cache_name}")
                response_stream = client.models.generate_content_stream(
                    model=model_name,
                    contents=contents,
                    config=types.GenerateContentConfig(
                        temperature=0.7,
                        max_output_tokens=65536,
                        thinking_config=types.ThinkingConfig(
                            include_thoughts=True,
                            thinking_budget=24576
                        ),
                        safety_settings=safety_settings_list,
                        tools=[google_search_tool] if google_search_tool else None,
                        cached_content=cache_name
                    )
                )
            else:
                # 不使用缓存发送请求
                response_stream = client.models.generate_content_stream(
                    model=model_name,
                    contents=contents,
                    config=generate_content_config_with_tools
                )
            
            full_ai_response_text = ""
            full_thinking_summary = ""
            is_first_content_chunk = True
            has_yielded_thinking_start = False
            
            # === MODIFICATION START: 收集搜索查询列表 ===
            all_search_queries: List[str] = []
            # === MODIFICATION END ===

            for chunk in response_stream:
                if hasattr(chunk, 'prompt_feedback') and chunk.prompt_feedback and chunk.prompt_feedback.block_reason:
                    error_msg = f"SAFETY_FILTER_TRIGGERED: {chunk.prompt_feedback.block_reason}"
                    logger.warning(f"[{context_name}] {error_msg}")
                    safety_error_message = AiChatMessage(
                        sender="system",
                        text=f"Error: {error_msg}",
                        icon="⚠️",
                        id=f"safety-block-{uuid.uuid4().hex[:8]}",
                        project_id=project_id_for_logging
                    )
                    yield f"data: {safety_error_message.model_dump_json()}\n\n"
                    break # 中断流式处理

                # === MODIFICATION START: 从 candidates 的 grounding_metadata 中提取搜索查询 ===
                if hasattr(chunk, 'candidates') and chunk.candidates:
                    for candidate in chunk.candidates:
                        # 尝试多种可能的路径获取搜索查询
                        if hasattr(candidate, 'grounding_metadata') and candidate.grounding_metadata:
                            grounding_metadata = candidate.grounding_metadata
                            # 检查 web_search_queries 字段
                            if hasattr(grounding_metadata, 'web_search_queries') and grounding_metadata.web_search_queries:
                                for query in grounding_metadata.web_search_queries:
                                    if query not in all_search_queries:  # 避免重复
                                        all_search_queries.append(query)
                                        logger.info(f"[{context_name}] Collected search query: {query}")
                            # 检查 search_entry_point 字段（Google搜索检索的替代字段）
                            elif hasattr(grounding_metadata, 'search_entry_point') and grounding_metadata.search_entry_point:
                                search_query = grounding_metadata.search_entry_point.rendered_content
                                if search_query and search_query not in all_search_queries:
                                    all_search_queries.append(search_query)
                                    logger.info(f"[{context_name}] Collected search query from entry point: {search_query}")
                        # 备用：直接从候选项中查找工具调用信息
                        elif hasattr(candidate, 'content') and candidate.content:
                            # 查找是否有工具调用相关的元数据
                            pass  # 这里可以添加更多提取逻辑
                # === MODIFICATION END ===
                    
                if hasattr(chunk, 'candidates') and chunk.candidates:
                    candidate = chunk.candidates[0]
                    if hasattr(candidate, 'content') and candidate.content and hasattr(candidate.content, 'parts'):
                        for part in candidate.content.parts:
                            if hasattr(part, 'text') and part.text:
                                if hasattr(part, 'thought') and part.thought:
                                    full_thinking_summary += part.text
                                    if not has_yielded_thinking_start:
                                        thinking_start_message = AiChatMessage(
                                            sender="ai",
                                            text="🤔 思考摘要",
                                            icon="🧠",
                                            id=thinking_message_id,
                                            project_id=project_id_for_logging,
                                            thinking=True,
                                            thinking_status="正在思考...",
                                            is_streaming=True
                                        )
                                        yield f"data: {thinking_start_message.model_dump_json()}\n\n"
                                        has_yielded_thinking_start = True
                                    
                                    thinking_chunk_message = AiChatMessage(
                                        sender="ai",
                                        text=part.text,
                                        icon="🧠",
                                        id=thinking_message_id,
                                        project_id=project_id_for_logging,
                                        thinking=True,
                                        thinking_status="思考中...",
                                        is_streaming=True,
                                        is_append=True
                                    )
                                    yield f"data: {thinking_chunk_message.model_dump_json()}\n\n"
                                else: # 正常回答内容
                                    full_ai_response_text += part.text
                                    if is_first_content_chunk:
                                        is_first_content_chunk = False
                                        if full_thinking_summary.strip():
                                            thinking_complete_message = AiChatMessage(
                                                sender="ai",
                                                text=full_thinking_summary,
                                                icon="🧠",
                                                id=thinking_message_id,
                                                project_id=project_id_for_logging,
                                                thinking=True,
                                                thinking_status="思考完成",
                                                is_streaming=False,
                                                stream_complete=True
                                            )
                                            yield f"data: {thinking_complete_message.model_dump_json()}\n\n"
                                            
                                            # 保存思考过程到数据库
                                            if project_id_for_logging and full_thinking_summary.strip():
                                                try:
                                                    # 获取数据库会话
                                                    db = next(get_db())
                                                    # 添加思考过程到数据库
                                                    crud_chat.add_chat_message(
                                                        db=db, 
                                                        project_id=project_id_for_logging, 
                                                        message=AiChatMessage(
                                                            sender="ai", 
                                                            text=full_thinking_summary, 
                                                            icon="🧠",
                                                            thinking=True,
                                                            thinking_status="思考完成"
                                                        ),
                                                        user_id=None
                                                    )
                                                    logger.info(f"[{context_name}] 思考过程已保存到数据库，项目ID: {project_id_for_logging}")
                                                except Exception as db_error:
                                                    logger.error(f"[{context_name}] 保存思考过程到数据库失败: {db_error}", exc_info=True)
                                                finally:
                                                    db.close()
                                        start_response_message = AiChatMessage(
                                            sender="ai",
                                            text=part.text,
                                            icon="💬",
                                            id=main_ai_response_id,
                                            project_id=project_id_for_logging,
                                            thinking=False,
                                            is_streaming=True
                                        )
                                        yield f"data: {start_response_message.model_dump_json()}\n\n"
                                    else:
                                        chunk_message = AiChatMessage(
                                            sender="ai",
                                            text=part.text,
                                            icon="💬",
                                            id=main_ai_response_id,
                                            project_id=project_id_for_logging,
                                            thinking=False,
                                            is_streaming=True,
                                            is_append=True
                                        )
                                        yield f"data: {chunk_message.model_dump_json()}\n\n"

            if full_ai_response_text.strip():
                final_message = AiChatMessage(
                    sender="ai",
                    text=full_ai_response_text,
                    icon="💬",
                    id=main_ai_response_id,
                    project_id=project_id_for_logging,
                    thinking=False,
                    is_streaming=False,
                    stream_complete=True
                )
                yield f"data: {final_message.model_dump_json()}\n\n"
                
                # 记录LLM响应到项目日志文件
                llm_comm_logger.log_llm_response(
                    project_id=project_id_for_logging,
                    agent_name=self.agent_name,
                    response_text=full_ai_response_text,
                    raw_response={
                        "success": True, 
                        "stream_mode": True,
                        "turbo_mode": turbo_mode,
                        "thinking_summary_length": len(full_thinking_summary) if full_thinking_summary else 0,
                        "search_queries": all_search_queries if all_search_queries else []
                    },
                    duration_ms=0,  # 流式处理时间不好计算
                    success=True,
                    thinking_summary=full_thinking_summary if full_thinking_summary.strip() else None,
                    context=f"chat_stream_turbo_{turbo_mode}"
                )
                
                # 保存AI回复到数据库
                if project_id_for_logging:
                    try:
                        # 获取数据库会话
                        db = next(get_db())
                        # 添加AI消息到数据库
                        crud_chat.add_chat_message(
                            db=db, 
                            project_id=project_id_for_logging, 
                            message=AiChatMessage(sender="ai", text=full_ai_response_text, icon="💬"),
                            user_id=None
                        )
                        logger.info(f"[{context_name}] AI回复已保存到数据库，项目ID: {project_id_for_logging}")
                        
                        # 检查是否是首次对话，如果是则生成话题总结并更新项目标题
                        await self._maybe_update_project_title(db, project_id_for_logging, user_message, full_ai_response_text, context_name)
                        
                    except Exception as db_error:
                        logger.error(f"[{context_name}] 保存AI回复到数据库失败: {db_error}", exc_info=True)
                    finally:
                        db.close()

            # === MODIFICATION START: 在主响应后发送搜索建议 ===
            if all_search_queries:
                # 将所有搜索建议合并到一条消息中，以保持聊天界面的简洁
                # 格式化为"您可能还想搜索：「查询1」、「查询2」"
                combined_query_text = "、".join([f"「{q}」" for q in all_search_queries])
                search_suggestion_message = AiChatMessage(
                    sender="ai", # 这是一个 AI 的建议
                    text=f"您可能还想搜索：{combined_query_text}", # 用于显示给用户
                    icon="🔍",
                    id=f"search-suggest-{uuid.uuid4().hex[:8]}", # 确保消息ID唯一
                    project_id=project_id_for_logging,
                    thinking=False,
                    is_streaming=False, # 这条消息通常不是字符流式的
                    is_append=False,
                    stream_complete=True, # 视为完整消息
                    data={"search_query_suggestions": all_search_queries} # <--- 将原始查询列表放在 data 字段中
                )
                yield f"data: {search_suggestion_message.model_dump_json()}\n\n"
                logger.info(f"[{context_name}] Yielded search suggestions: {all_search_queries}")
            # === MODIFICATION END ===
            
        except google_exceptions.ResourceExhausted as e:
            error_str = f"超出速率限制 (429): {e}"
            logger.error(f"[{context_name}] 聊天流生成期间发生错误: {error_str}", exc_info=True)
            
            # 记录速率限制错误到项目日志
            llm_comm_logger.log_llm_response(
                project_id=project_id_for_logging,
                agent_name=self.agent_name,
                response_text=None,
                raw_response={"error": "rate_limit", "type": "ResourceExhausted"},
                duration_ms=0,
                success=False,
                error_message=error_str,
                context=f"chat_stream_turbo_{turbo_mode}"
            )
            
            ratelimit_error_message = AiChatMessage(
                sender="system",
                text=f"抱歉，AI服务繁忙，请稍后再试。({error_str[:50]}...)",
                icon="⚠️",
                id=f"err-ratelimit-{uuid.uuid4().hex[:8]}",
                project_id=project_id_for_logging
            )
            yield f"data: {ratelimit_error_message.model_dump_json()}\n\n"
        except Exception as e:
            error_str = str(e)
            logger.error(f"[{context_name}] 聊天流生成期间发生错误: {error_str}", exc_info=True)
            
            # 记录一般错误到项目日志
            llm_comm_logger.log_llm_response(
                project_id=project_id_for_logging,
                agent_name=self.agent_name,
                response_text=None,
                raw_response={"error": "general_exception", "type": type(e).__name__},
                duration_ms=0,
                success=False,
                error_message=error_str,
                context=f"chat_stream_turbo_{turbo_mode}"
            )
            
            general_error_message = AiChatMessage(
                sender="system",
                text=f"Error: Failed to generate response due to internal error: {error_str}",
                icon="⚠️",
                id=f"err-general-{uuid.uuid4().hex[:8]}",
                project_id=project_id_for_logging
            )
            yield f"data: {general_error_message.model_dump_json()}\n\n"
        finally:
            # === MODIFICATION START: 清理上传的文件 ===
            if file_processor and uploaded_files:
                logger.info(f"[{context_name}] 清理上传的文件")
                for file_info in uploaded_files:
                    try:
                        file_processor.delete_file(file_info['name'])
                        logger.info(f"[{context_name}] 已删除文件: {file_info['display_name']}")
                    except Exception as cleanup_error:
                        logger.error(f"[{context_name}] 文件清理失败: {cleanup_error}")
            # === MODIFICATION END ===
    
    async def _maybe_update_project_title(self, db, project_id: str, user_message: str, ai_response: str, context_name: str):
        """
        检查是否是首次对话，如果是则生成话题总结并更新项目标题
        """
        try:
            from app.crud import crud_project
            
            # 获取项目信息
            project = crud_project.get_project(db, project_id)
            if not project:
                logger.warning(f"[{context_name}] 项目 {project_id} 不存在，跳过标题更新")
                return
            
            # 检查项目标题是否是自动生成的格式（Chat_YYYYMMDD_HHMMSS）
            import re
            if not re.match(r'^Chat_\d{8}_\d{6}$', project.title):
                logger.info(f"[{context_name}] 项目标题已被自定义，跳过自动更新: {project.title}")
                return
            
            # 获取聊天历史记录数量
            chat_messages = crud_chat.get_chat_messages_for_project(db, project_id)
            # 过滤掉思考消息，只计算实际对话
            actual_messages = [msg for msg in chat_messages if not getattr(msg, 'thinking', False)]
            
            # 如果这是首次对话（用户消息 + AI回复 = 2条消息），则生成话题总结
            if len(actual_messages) <= 2:
                logger.info(f"[{context_name}] 检测到首次对话，开始生成话题总结")
                
                # 生成话题总结
                topic_summary = await self._generate_topic_summary(user_message, ai_response)
                
                if topic_summary and topic_summary.strip():
                    # 更新项目标题
                    crud_project.update_project(db, project_id, {"title": topic_summary})
                    logger.info(f"[{context_name}] 项目标题已更新为: {topic_summary}")
                else:
                    logger.warning(f"[{context_name}] 话题总结生成失败，保持原标题")
            else:
                logger.info(f"[{context_name}] 非首次对话（已有{len(actual_messages)}条消息），跳过标题更新")
                
        except Exception as e:
            logger.error(f"[{context_name}] 更新项目标题时发生错误: {e}", exc_info=True)
    
    async def _generate_topic_summary(self, user_message: str, ai_response: str) -> Optional[str]:
        """
        基于用户消息和AI回复生成简洁的话题总结
        """
        try:
            api_key = await key_manager.get_next_key_async()
            client = genai.Client(api_key=api_key)
            
            model_name = getattr(settings, 'GEMINI_MODEL_NAME', None) or "gemini-2.5-flash-preview-05-20"
            
            # 构建话题总结提示词
            summary_prompt = f"""请为以下对话生成一个简洁的话题标题（不超过20个字符）：

用户：{user_message[:200]}...
AI：{ai_response[:200]}...

要求：
1. 标题要简洁明了，能概括对话主题
2. 不超过20个字符
3. 不要包含"聊天"、"对话"等词汇
4. 直接返回标题，不要其他内容

标题："""

            # 记录LLM请求到项目日志文件（这里没有项目ID，所以记录为系统调用）
            llm_comm_logger.log_llm_request(
                project_id="system_topic_summary",
                agent_name=self.agent_name,
                prompt_content=summary_prompt,
                temperature=0.3,
                model_name=model_name,
                expected_response_type="topic_title",
                context="generate_topic_summary"
            )

            response = client.models.generate_content(
                model=model_name,
                contents=summary_prompt,
                config=types.GenerateContentConfig(
                    temperature=0.3,
                    max_output_tokens=50
                )
            )
            
            if response and response.text:
                topic_title = response.text.strip()
                # 清理标题，移除可能的引号和多余字符
                topic_title = topic_title.strip('"\'').strip()
                
                # 确保标题长度不超过限制
                if len(topic_title) > 50:
                    topic_title = topic_title[:47] + "..."
                
                # 记录成功响应到项目日志
                llm_comm_logger.log_llm_response(
                    project_id="system_topic_summary",
                    agent_name=self.agent_name,
                    response_text=topic_title,
                    raw_response={"success": True, "original_response": response.text},
                    duration_ms=0,
                    success=True,
                    context="generate_topic_summary"
                )
                
                logger.info(f"生成的话题标题: {topic_title}")
                return topic_title
            else:
                # 记录空响应到项目日志
                llm_comm_logger.log_llm_response(
                    project_id="system_topic_summary",
                    agent_name=self.agent_name,
                    response_text=None,
                    raw_response={"error": "empty_response"},
                    duration_ms=0,
                    success=False,
                    error_message="话题总结API返回空响应",
                    context="generate_topic_summary"
                )
                
                logger.warning("话题总结API返回空响应")
                return None
                
        except Exception as e:
            logger.error(f"生成话题总结时发生错误: {e}", exc_info=True)
            return None