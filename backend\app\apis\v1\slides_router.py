from pydantic import BaseModel, Field # 确保在文件顶部导入
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, Response, status, Request
from fastapi.responses import StreamingResponse # Add this
from typing import List, Dict, Any, AsyncGenerator, Optional, TYPE_CHECKING

# Import for type checking only
if TYPE_CHECKING:
    from app.agents.presentation_workflow import PresentationWorkflow

import asyncio  # Adding the missing import
import uuid
import os
import shutil # For deleting directories
from pydantic import BaseModel, ValidationError
import glob
import json
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
import logging # 确保导入 logging

from app.models.slide_models import (
    SlideGenerationRequest, SlideGenerationResponse, OrchestratorProgress,
    ToolStartEvent, ToolEndEvent, OutlineEvent, CodeEvent
) # Import new models
from app.core.config import settings
import json # For streaming
from app.models.presentation_model import OrchestratorProgress, AiChatMessage, ProjectSummarySchema, PresentationDataSchema, Slide, UserIntentSchema, SimpleOverallStyleAndOutlinesSchema, PresentationStyleSchema, SlideMetadataSchema, ProjectDetailsResponse
# 确保在导入后立即调用 model_rebuild()
# 这可以防止在模型被 FastAPI 使用之前出现未完全定义的错误
ProjectSummarySchema.model_rebuild()
ProjectDetailsResponse.model_rebuild()
AiChatMessage.model_rebuild()
Slide.model_rebuild()
OrchestratorProgress.model_rebuild()
PresentationDataSchema.model_rebuild()
UserIntentSchema.model_rebuild()
SimpleOverallStyleAndOutlinesSchema.model_rebuild()
PresentationStyleSchema.model_rebuild()
SlideMetadataSchema.model_rebuild()
# 导入数据库依赖
from app.db.session import get_db
from app.crud import crud_project, crud_chat, crud_slide
from app.db import models as db_models
# 导入认证相关的依赖
from .auth_router import optional_current_user


router = APIRouter()
router_logger = logging.getLogger(__name__) # 为路由创建一个 logger

# 键是项目ID（真实DB ID），值是一个列表，包含asyncio.Queue对象
connected_clients: Dict[str, List[asyncio.Queue]] = {}

# 存储临时ID到实际DB ID的映射
temp_id_to_actual_id_map: Dict[str, str] = {} # e.g., {"proj_abc": "uuid_xyz"}

# 存储实际DB ID到其对应的临时ID列表（一个实际ID可能对应多个临时ID，虽然少见）
actual_id_to_temp_ids_map: Dict[str, List[str]] = {} 

class ProjectUpdate(BaseModel):
    title: Optional[str] = None

# +++++++++++++++ 新增请求模型 +++++++++++++++
class SlideElementEditRequest(BaseModel):
    element_selector: str = Field(..., description="用于定位要编辑元素的CSS选择器，例如 '[data-editable-id=\"elem_abc123\"]'")
    instruction: str = Field(..., description="用户的自然语言编辑指令")
# +++++++++++++++++++++++++++++++++++++++++++

@router.patch("/projects/{project_id}", response_model=ProjectSummarySchema)
async def update_project_details(
    project_id: str,
    project_update: ProjectUpdate,
    db: Session = Depends(get_db),
    current_user: Optional[db_models.User] = Depends(optional_current_user)
):
    """更新项目详情，如标题"""
    user_id = current_user.id if current_user else None
    
    # 获取项目，并检查权限
    db_project = crud_project.get_project(db, project_id=project_id, user_id=user_id)
    if not db_project:
        # 如果是匿名用户，也允许操作无主项目
        if not user_id:
            db_project = crud_project.get_project(db, project_id=project_id)
            if db_project and db_project.user_id:
                raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Permission denied")
        if not db_project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found or permission denied")

    # 执行更新
    updated_project = crud_project.update_project(
        db, 
        project_id=project_id, 
        update_data=project_update.model_dump(exclude_unset=True)
    )

    if not updated_project:
        raise HTTPException(status_code=500, detail="Failed to update project")
    
    # 返回更新后的项目摘要
    return ProjectSummarySchema.from_orm(updated_project)


# 推送进度给客户端的函数
async def push_progress_to_client(progress: OrchestratorProgress):
    """将进度更新推送到相关的客户端"""
    
    target_push_id = progress.project_id # 默认目标是消息体中的 project_id
    payload_to_send = progress # 默认要发送的载荷
    
    # 调试日志：显示尝试推送前的情况
    router_logger.info(f"[SSE Push START] Attempting to push for progress.project_id: {progress.project_id}, status: {progress.status}")
    router_logger.info(f"[SSE Push START] current connected_clients: {list(connected_clients.keys())}")
    router_logger.info(f"[SSE Push START] current temp_id_to_actual_id_map: {temp_id_to_actual_id_map}")

    if progress.status == "id_mapping" and progress.metadata and \
       "actual_project_id" in progress.metadata:
        
        temp_id_from_msg = progress.metadata.get("temp_project_id", progress.project_id) # 消息的 project_id 应该是临时ID
        actual_id_from_db = progress.metadata["actual_project_id"]
        
        router_logger.info(f"[SSE Push ID_MAP_PROCESS] ID Mapping Request: TempID {temp_id_from_msg} -> ActualID {actual_id_from_db}")

        if temp_id_from_msg != actual_id_from_db:
            # 1. 更新全局映射
            temp_id_to_actual_id_map[temp_id_from_msg] = actual_id_from_db
            if actual_id_from_db not in actual_id_to_temp_ids_map:
                actual_id_to_temp_ids_map[actual_id_from_db] = []
            if temp_id_from_msg not in actual_id_to_temp_ids_map[actual_id_from_db]:
                actual_id_to_temp_ids_map[actual_id_from_db].append(temp_id_from_msg)
            router_logger.info(f"[SSE Push ID_MAP_PROCESS] Global map updated: {temp_id_from_msg} -> {actual_id_from_db}")

            # 2. 迁移客户端队列
            if temp_id_from_msg in connected_clients:
                queues_to_move = connected_clients.pop(temp_id_from_msg, []) # 使用 pop 并提供默认值
                if queues_to_move: # 确保真的有队列被移出
                    if actual_id_from_db not in connected_clients:
                        connected_clients[actual_id_from_db] = []
                    connected_clients[actual_id_from_db].extend(queues_to_move)
                    router_logger.info(f"[SSE Push ID_MAP_PROCESS] Moved {len(queues_to_move)} client queue(s) from TempID {temp_id_from_msg} to ActualID {actual_id_from_db}")
                else:
                    router_logger.warning(f"[SSE Push ID_MAP_PROCESS] No active queues found for TempID {temp_id_from_msg} during migration attempt.")
            else:
                router_logger.warning(f"[SSE Push ID_MAP_PROCESS] TempID {temp_id_from_msg} not in connected_clients for queue migration.")
        
        # 3. 准备要发送的ID映射确认消息 (它的 project_id 应该是 actual_id_from_db)
        mapped_progress_to_send = OrchestratorProgress(
            project_id=actual_id_from_db, # **重要：推送此确认消息时，目标ID应为真实ID**
            status="id_mapped_to_client", 
            message=AiChatMessage(
                sender="system", 
                text=f"项目ID已确认。原临时ID: {temp_id_from_msg}, 当前项目ID: {actual_id_from_db}",
                icon="🔗"
            ),
            metadata={
                "temp_project_id": temp_id_from_msg, 
                "actual_project_id": actual_id_from_db 
            }
        )
        target_push_id = actual_id_from_db # 更新推送目标ID
        payload_to_send = mapped_progress_to_send # 更新要发送的载荷
        router_logger.info(f"[SSE Push ID_MAP_PROCESS] Prepared 'id_mapped_to_client' message for ActualID {actual_id_from_db}.")

    # 非ID映射消息，或者 "id_mapped_to_client" 消息
    # 此时 target_push_id 应该是真实的DB ID (或者是原始ID如果未发生映射)
    # payload_to_send 是对应的 OrchestratorProgress 对象
    
    progress_payload_json = payload_to_send.model_dump_json()

    # 推送逻辑 - 始终使用 target_push_id
    if target_push_id in connected_clients:
        clients_for_id = connected_clients[target_push_id]
        if clients_for_id:
            for queue in list(clients_for_id): 
                try:
                    await queue.put(progress_payload_json)
                except Exception as q_err:
                    router_logger.error(f"[SSE Push ERROR] Error putting message to queue for {target_push_id}: {q_err}")
            router_logger.info(f"[SSE Push SUCCESS] Pushed to {len(clients_for_id)} client(s) for ID {target_push_id}, status: {payload_to_send.status}")
        else:
            router_logger.warning(f"[SSE Push WARN] No connected clients for ID {target_push_id} (list is empty).")
    else:
        # 如果目标ID不在 connected_clients，但它是一个已知的临时ID的映射目标（真实ID），
        # 并且这个临时ID仍然存在于 connected_clients 中（意味着迁移由于某种原因不完全或客户端重连到了临时ID），
        # 这是一个复杂的情况，但当前逻辑主要依赖于ID映射消息后队列被正确迁移。
        # 主要的警告应该集中在目标ID找不到。
        router_logger.warning(f"[SSE Push WARN] Target ID {target_push_id} not found in connected_clients. Status: {payload_to_send.status}.")
        router_logger.info(f"[SSE Push WARN] Current connected_clients after potential migration: {list(connected_clients.keys())}")
        router_logger.info(f"[SSE Push WARN] Current temp_id_to_actual_id_map: {temp_id_to_actual_id_map}")

# Update to return PresentationWorkflow
def get_presentation_workflow():
    from app.agents.presentation_workflow import PresentationWorkflow
    return PresentationWorkflow()

@router.post("/slides/generate_from_text_stream",
             # response_model is tricky for streaming, handled by content_type
             summary="Generate slides from text prompt (Streaming)")
async def generate_slides_from_text_stream(
    request_data: SlideGenerationRequest,
    workflow = Depends(get_presentation_workflow),
    db: Session = Depends(get_db),
    current_user: Optional[db_models.User] = Depends(optional_current_user)
):
    """
    Generates a presentation based on a text prompt and streams progress.
    - **prompt**: The text prompt describing the desired presentation.
    """
    router_logger.info(f"[API] 收到流式生成请求，提示词: {request_data.prompt[:50]}...")

    async def event_generator() -> AsyncGenerator[str, None]:
        try:
            # 设置总体超时时间
            TOTAL_TIMEOUT = 300  # 总体超时5分钟
            
            # 确定是否是新项目
            is_new_project = not request_data.project_id
            
            # 创建或获取项目ID
            project_id = request_data.project_id if not is_new_project else f"proj_{uuid.uuid4().hex[:8]}"
            router_logger.info(f"[API] 处理项目，ID: {project_id}, 是新项目: {is_new_project}")
            
            # 立即将临时ID响应给客户端，让它们可以连接SSE
            # 为了前端方便处理，这里使用 OrchestratorProgress 结构而不是自定义的字典
            init_progress = OrchestratorProgress(
                project_id=project_id,
                status="request_received",
                message=AiChatMessage(
                    sender="system",
                    text="请求已接收，正在处理...",
                    icon="🔄"
                )
            )
            yield f"data: {init_progress.model_dump_json()}\n\n"
            router_logger.info(f"[API] 已向客户端发送项目ID: {project_id}，客户端现在应该建立SSE连接")
            
            # 创建生成器之前先提供一个短暂的延迟，让前端有时间建立SSE连接
            # 延迟时间要长一些，以确保前端有足够时间连接SSE
            await asyncio.sleep(1.0)
            router_logger.info(f"[API] 延迟1.0秒后，现在启动后台任务")
            
            # 启动后台任务，而不是在这里直接调用workflow.generate_presentation
            # 注意这里不用await，它会立即返回，后台任务负责数据库操作和项目ID映射
            from app.main import handle_presentation_generation
            background_task = asyncio.create_task(
                handle_presentation_generation(
                    user_prompt=request_data.prompt,
                    project_id_from_api=project_id,
                    is_new_project_id_generated_by_api=is_new_project
                )
            )
            
            # 最后发送一条确认消息，指示任务已启动
            # 为了前端方便处理，这里仍然使用 OrchestratorProgress 结构
            confirmation_progress = OrchestratorProgress(
                project_id=project_id,
                status="background_task_started",
                message=AiChatMessage(
                    sender="system",
                    text="后台任务已启动，请查看SSE连接获取实时进度",
                    icon="⚙️"
                )
            )
            yield f"data: {confirmation_progress.model_dump_json()}\n\n"
            router_logger.info(f"[API] 已通知客户端后台任务已启动")
            
            # 打印一些额外的状态信息以便调试
            router_logger.info(f"[API] 当前SSE连接信息 - connected_clients: {list(connected_clients.keys())}")
            router_logger.info(f"[API] 当前ID映射信息 - temp_id_to_actual_id_map: {temp_id_to_actual_id_map}")
            
            # 再次确认SSE队列注册状态
            if project_id in connected_clients:
                router_logger.info(f"[API] 项目 {project_id} 已有 {len(connected_clients[project_id])} 个客户端连接")
            else:
                router_logger.warning(f"[API] 警告: 项目 {project_id} 在connected_clients中还没有条目！客户端可能尚未连接SSE")
                
            # 不再等待后台任务完成或处理其生成的消息
            # 因为所有消息通过SSE连接发送给客户端
                
        except Exception as e:
            error_msg = str(e).replace('"', '\\"').replace('\n', ' ')
            router_logger.error(f"[API] 生成过程中发生错误: {error_msg}", exc_info=True)
            
            # 使用 OrchestratorProgress 结构发送错误消息
            error_progress = OrchestratorProgress(
                project_id=project_id if 'project_id' in locals() else "unknown",
                status="error",
                message=AiChatMessage(
                    sender="system",
                    text=f"生成过程中发生错误: {error_msg[:100]}...",
                    icon="⚠️"
                )
            )
            yield f"data: {error_progress.model_dump_json()}\n\n"

    # 添加自定义CORS标头，确保前端可以访问
    headers = {
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "text/event-stream",
        "Access-Control-Allow-Origin": "*",  # 允许所有来源，生产环境中应该限制
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type"
    }

    # 使用总体超时包装event_generator
    return StreamingResponse(event_generator(), media_type="text/event-stream", headers=headers)

@router.get("/presentation_updates/{requested_project_id}")
async def presentation_updates(request: Request, requested_project_id: str): # 注入 Request 对象
    """创建一个Server-Sent Events (SSE) 连接，接收特定项目的更新"""
    
    # 客户端初始连接时，我们总是使用 requested_project_id (可能是临时的) 来注册队列
    # ID映射的转换将在 push_progress_to_client 内部处理
    client_connection_id = requested_project_id 
    
    router_logger.info(f"[SSE Connect] 客户端请求连接 project_id: {client_connection_id}")

    # 每次都创建一个新的队列
    queue = asyncio.Queue()
    
    # 将客户端队列添加到 connected_clients
    if client_connection_id not in connected_clients:
        connected_clients[client_connection_id] = []
    connected_clients[client_connection_id].append(queue)
    
    router_logger.info(f"[SSE Connect] 客户端已添加到项目 {client_connection_id} 的监听队列，当前此ID有 {len(connected_clients[client_connection_id])} 个监听者")
    router_logger.info(f"[SSE Connect] 当前 connected_clients 包含的项目ID: {list(connected_clients.keys())}")
    
    async def event_generator():
        # 发送一个初始消息确认连接成功，使用客户端请求的ID
        init_message_payload = {
            "project_id": client_connection_id, 
            "status": "sse_connection_established",
            "message": {"sender":"system", "text": f"SSE连接已建立，监听项目ID: {client_connection_id}", "icon": "🔗"},
            "timestamp": datetime.now().isoformat()
        }
        yield f"data: {json.dumps(init_message_payload)}\n\n"
        router_logger.info(f"[SSE Connect] 已发送连接确认给 {client_connection_id}")
        
        try:
            while True:
                # 检查客户端是否仍然连接
                if await request.is_disconnected():
                    router_logger.info(f"[SSE Connect] 客户端 {client_connection_id} 主动断开连接 (is_disconnected)。")
                    break
                try:
                    message_from_queue = await asyncio.wait_for(queue.get(), timeout=45) # 增加超时
                    yield f"data: {message_from_queue}\n\n"
                except asyncio.TimeoutError:
                    # 发送心跳以保持连接活跃
                    heartbeat_payload = {"project_id": client_connection_id, "status": "heartbeat", "timestamp": datetime.now().isoformat()}
                    yield f"data: {json.dumps(heartbeat_payload)}\n\n"
        except asyncio.CancelledError:
            router_logger.info(f"[SSE Connect] 客户端连接 ({client_connection_id}) 被取消 (CancelledError)。")
            # raise # 此处不应重新抛出，否则FastAPI会报错
        except Exception as e_gen:
            router_logger.error(f"[SSE Connect] event_generator 发生错误 for {client_connection_id}: {e_gen}", exc_info=True)
        finally:
            # 清理
            if client_connection_id in connected_clients and queue in connected_clients[client_connection_id]:
                connected_clients[client_connection_id].remove(queue)
                router_logger.info(f"[SSE Connect] 客户端已从项目 {client_connection_id} 的监听队列移除，剩余 {len(connected_clients[client_connection_id])} 个监听者")
                if not connected_clients[client_connection_id]: # 如果没有监听者了
                    del connected_clients[client_connection_id]
                    router_logger.info(f"[SSE Connect] 项目 {client_connection_id} 的所有客户端已断开，已从字典中移除")
                    
                    # 如果这个ID是临时ID，并且它映射的真实ID也没有其他临时ID映射到它了，
                    # 那么也可以考虑清理 actual_id_to_temp_ids_map
                    actual_id_it_mapped_to = temp_id_to_actual_id_map.pop(client_connection_id, None)
                    if actual_id_it_mapped_to:
                        if actual_id_it_mapped_to in actual_id_to_temp_ids_map:
                            if client_connection_id in actual_id_to_temp_ids_map[actual_id_it_mapped_to]:
                                actual_id_to_temp_ids_map[actual_id_it_mapped_to].remove(client_connection_id)
                            if not actual_id_to_temp_ids_map[actual_id_it_mapped_to]: # 如果真实ID也没有其他临时ID映射了
                                del actual_id_to_temp_ids_map[actual_id_it_mapped_to]
                        router_logger.info(f"[SSE Connect] 已清理临时ID映射关系 for {client_connection_id} -> {actual_id_it_mapped_to}")

    headers = {
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "text/event-stream",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type"
    }
    return StreamingResponse(event_generator(), media_type="text/event-stream", headers=headers)

# Keep the old non-streaming endpoint for now (or remove if not needed)
@router.post("/slides/generate_from_text", response_model=SlideGenerationResponse, summary="Generate slides from text prompt (Non-Streaming - MOCK)")
async def generate_slides_from_text(prompt: str = Form(...)):
    print(f"Received prompt (non-stream): {prompt}")
    mock_response_data = {
        "project_id": "mock_project_123_nonstream",
        "title": f"Presentation about: {prompt[:30]}...",
        "status": "completed_mock", # Mark as mock
        "message": "This is a mock non-streaming response. Use the streaming endpoint for actual generation.",
        "slides": [
            {"id": "mock_slide_1", "html_content": "<h1>Mock Slide 1</h1>", "order": 0},
            {"id": "mock_slide_2", "html_content": "<h1>Mock Slide 2</h1>", "order": 1}
        ],
        "total_slides_planned": 2
    }
    return SlideGenerationResponse(**mock_response_data)


@router.post("/slides/generate_from_file", summary="Generate slides from an uploaded file (Not Implemented Yet)")
async def generate_slides_from_file(file: UploadFile = File(...)):
    if not file.filename:
         raise HTTPException(status_code=400, detail="No file provided or filename is empty.")
    print(f"Received file: {file.filename}, content_type: {file.content_type}")
    raise HTTPException(status_code=501, detail="File upload processing not implemented yet.")

@router.get("/projects/history", response_model=List[ProjectSummarySchema])
async def get_projects_history(
    db: Session = Depends(get_db),
    current_user: Optional[db_models.User] = Depends(optional_current_user)
):
    """
    获取历史项目列表 (从数据库读取)
    - 已登录用户：获取自己的项目
    - 未登录用户：获取无用户关联的公开项目
    """
    user_id = current_user.id if current_user else None
    
    # 如果用户已登录，获取其项目；否则获取无用户关联的项目
    projects_db = crud_project.get_projects_by_user(db, user_id=user_id)

    # 将数据库模型转换为 Pydantic Schema
    history_list = []
    for proj in projects_db:
        summary = ProjectSummarySchema(
            project_id=proj.id,
            title=proj.title,
            num_slides=proj.total_slides_planned,
            created_at=proj.created_at,
            last_modified=proj.last_modified
        )
        history_list.append(summary)
    return history_list

@router.get("/projects/{project_id}/details", response_model=ProjectDetailsResponse)
async def get_project_details(
    project_id: str,
    db: Session = Depends(get_db),
    current_user: Optional[db_models.User] = Depends(optional_current_user)
):
    """
    获取项目详细信息，但不包含幻灯片的HTML内容，以提高性能。
    前端可以根据需要，分别请求每张幻灯片的内容。
    """
    # 检查项目是否存在
    project = crud_project.get_project(db, project_id=project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    # 获取项目的基本信息
    project_response = ProjectDetailsResponse(
        project_id=project.id,
        title=project.title,
        num_slides=project.total_slides_planned,
        created_at=project.created_at,
        last_modified=project.last_modified
    )

    # 解析用户意图数据
    if project.user_intent_data:
        try:
            project_response.user_intent = UserIntentSchema.model_validate(project.user_intent_data)
        except ValidationError:
            router_logger.warning(f"无法解析项目 {project_id} 的用户意图数据")

    # 解析样式和大纲数据
    if project.style_outline_data:
        try:
            project_response.style_outline = SimpleOverallStyleAndOutlinesSchema.model_validate(project.style_outline_data)
        except ValidationError:
            router_logger.warning(f"无法解析项目 {project_id} 的样式和大纲数据")

    # 获取幻灯片元数据（不包含HTML内容）
    slides = crud_slide.get_slides_by_project(db, project_id=project_id)
    slide_metadata_list = []
    
    for slide in slides:
        # 为每张幻灯片创建轻量级元数据对象
        slide_metadata = SlideMetadataSchema(
            id=slide.id,
            slide_number=slide.order_index + 1,  # 转换为1-based索引
            status="ready" if slide.html_content else "pending",
            # 可以从detailed_instructions或HTML中提取标题
            title=f"幻灯片 {slide.order_index + 1}"  # 简单的默认标题
        )
        slide_metadata_list.append(slide_metadata)
    
    project_response.slides = slide_metadata_list

    # 获取聊天历史
    chat_messages = crud_chat.get_chat_messages_for_project(db, project_id=project_id)
    project_response.chat_history = [
        AiChatMessage(
            id=msg.id,
            sender=msg.sender,
            text=msg.text,
            icon=msg.icon,
            timestamp=msg.timestamp.isoformat() if msg.timestamp else None
        ) for msg in chat_messages
    ]

    return project_response

@router.delete("/projects/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_project_data(
    project_id: str,
    db: Session = Depends(get_db),
    current_user: Optional[db_models.User] = Depends(optional_current_user)
):
    """
    删除项目及其所有相关数据
    - 已登录用户：只能删除自己的项目
    - 未登录用户：只能删除无用户关联的项目
    """
    user_id = current_user.id if current_user else None
    
    # 首先检查项目是否存在且用户有权限访问
    db_project = None
    if user_id:
        # 已登录用户，检查项目所有权
        db_project = crud_project.get_project(db, project_id=project_id, user_id=user_id)
    else:
        # 未登录用户，只能获取无用户关联的项目
        db_project = crud_project.get_project(db, project_id=project_id)
        if db_project and db_project.user_id:
            # 如果项目有用户关联但用户未登录，不允许访问
            db_project = None
            
    if not db_project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, 
            detail=f"Project {project_id} not found or you don't have permission to delete it."
        )
    
    # 执行删除
    success = crud_project.delete_project(db, project_id=project_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=f"Failed to delete project {project_id}."
        )
    
    # 同时删除本地文件系统中的项目目录（如果存在）
    project_path_to_delete = os.path.join(settings.BASE_PROJECT_STORAGE_DIR, project_id)
    if os.path.isdir(project_path_to_delete):
        try:
            shutil.rmtree(project_path_to_delete)
            print(f"Successfully deleted local directory: {project_path_to_delete}")
        except Exception as e:
            print(f"Warning: Failed to delete local project directory {project_path_to_delete}: {e}")
    
    return Response(status_code=status.HTTP_204_NO_CONTENT)

@router.get("/projects/{project_id}/images/{image_path:path}")
async def get_project_image(
    project_id: str,
    image_path: str,
    db: Session = Depends(get_db),
    current_user: Optional[db_models.User] = Depends(optional_current_user)
):
    """
    获取项目图片
    """
    try:
        # 检查项目是否存在
        project = crud_project.get_project(db, project_id=project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # 如果有用户登录，检查项目是否属于该用户
        if current_user and project.user_id and project.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="Not authorized to access this project")
        
        # 构建图片完整路径，确保包含 "images" 子目录
        full_image_path = os.path.join(settings.BASE_PROJECT_STORAGE_DIR, project_id, "images", image_path)
        
        # 检查文件是否存在
        if not os.path.exists(full_image_path) or not os.path.isfile(full_image_path):
            raise HTTPException(status_code=404, detail="Image not found")
        
        # 确定MIME类型
        mime_type = "image/jpeg"  # 默认MIME类型
        if image_path.lower().endswith(".png"):
            mime_type = "image/png"
        elif image_path.lower().endswith(".gif"):
            mime_type = "image/gif"
        elif image_path.lower().endswith(".webp"):
            mime_type = "image/webp"
        
        # 返回图片文件
        return StreamingResponse(open(full_image_path, "rb"), media_type=mime_type)
        
    except HTTPException:
        raise
    except Exception as e:
        router_logger.error(f"Error getting project image: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error getting project image: {str(e)}")

@router.get("/slides/{slide_id}/content", response_model=Slide)
async def get_slide_content(
    slide_id: str,
    db: Session = Depends(get_db),
    current_user: Optional[db_models.User] = Depends(optional_current_user)
):
    """
    获取单张幻灯片的完整内容，包括HTML。
    这允许前端在需要时懒加载幻灯片内容，提高性能。
    """
    # 获取幻灯片
    slide = crud_slide.get_slide(db, slide_id=slide_id)
    if not slide:
        raise HTTPException(status_code=404, detail="Slide not found")
    
    # 返回幻灯片对象，使用response_model转换
    return Slide(
        id=slide.id,
        slide_number=slide.order_index + 1,  # 转换为1-based索引
        html=slide.html_content
    )

# +++++++++++++++ 新增API端点 +++++++++++++++
@router.patch("/slides/{slide_id}/edit_element", response_model=Slide)
async def edit_slide_element_api(
    slide_id: str,
    edit_request: SlideElementEditRequest,
    workflow: "PresentationWorkflow" = Depends(get_presentation_workflow),
    db: Session = Depends(get_db),
    current_user: Optional[db_models.User] = Depends(optional_current_user),
):
    """
    接收前端请求，编辑幻灯片中的单个元素。
    """
    # 权限检查：确保用户有权编辑此幻灯片
    slide = crud_slide.get_slide(db, slide_id=slide_id)
    if not slide:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Slide not found")

    project = crud_project.get_project(db, project_id=slide.project_id)
    if current_user and project.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Permission denied")

    # 调用工作流执行编辑
    updated_html = await workflow.edit_slide_element(
        slide_id=slide_id,
        element_selector=edit_request.element_selector,
        instruction=edit_request.instruction,
        db=db,
        project_id_for_logging=slide.project_id
    )

    if updated_html is None:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to edit slide element.")
    
    # 构造并返回更新后的Slide对象
    updated_slide_obj = Slide(
        id=slide.id,
        slide_number=slide.order_index + 1,
        html=updated_html
    )
    return updated_slide_obj
# +++++++++++++++++++++++++++++++++++++++++++