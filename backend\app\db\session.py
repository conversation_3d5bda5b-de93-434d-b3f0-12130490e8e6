# backend/app/db/session.py
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings # 从你的配置模块导入 settings

# 创建数据库引擎
# 'pool_pre_ping=True' 检查连接是否仍然活动，有助于处理连接超时
engine = create_engine(settings.DATABASE_URL, pool_pre_ping=True)

# 创建一个 SessionLocal 类，它将是实际数据库会话的工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 依赖项：获取数据库会话
# 这个函数将在每个API请求中被调用，以获取一个独立的数据库会话
# 并在请求处理完成后关闭它。
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close() 