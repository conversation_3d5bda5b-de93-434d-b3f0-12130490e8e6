# backend/app/agents/presentation_workflow.py
import asyncio
import uuid
import logging
import os
import sys
import time
from typing import AsyncGenerator, Optional, List, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session

# 导入最佳实践相关的库
import instructor
from google import genai
from google.genai import types
from pydantic import BaseModel, Field, ValidationError

from app.core.config import settings
from app.models.presentation_model import (
    UserIntentSchema, StructuredPresentationStyleSchema, DetailedSlideBlueprintSchema,
    SlideOutlineItemSchema, OrchestratorProgress, AiChatMessage, Slide,
    TextElementSchema, KpiCardSchema, ChartBlueprintSchema, ImageElementSchema, Element
)

from app.agents.user_intent_agent import UserIntentAgent
from app.agents.visual_style_agent import VisualStyleAgent
from app.agents.slide_detailer_agent import SlideDetailerAgent
from app.agents.slide_generator_agent import SlideGeneratorAgent
from app.models.presentation_model import DetailedSlideBlueprintSchema
from app.crud import crud_project, crud_slide, crud_chat
from app.db.session import get_db
from app.utils.html_cleaner import comprehensive_html_cleanup
from app.utils.api_key_manager import key_manager

logger = logging.getLogger(__name__)

class PresentationWorkflow:
    """简化的幻灯片生成工作流 - 只生成HTML并保存"""

    # 工作流配置常量
    MAX_CONCURRENT_BLUEPRINT_TASKS = 5  # 蓝图细化并发数
    MAX_CONCURRENT_HTML_TASKS = 5       # HTML生成并发数
    TASK_INTERVAL_SECONDS = 1.0         # 任务间隔（秒）
    
    def __init__(self):
        """初始化工作流及各个Agent"""
        self.user_intent_agent = UserIntentAgent()
        self.visual_style_agent = VisualStyleAgent()
        self.slide_detailer_agent = SlideDetailerAgent()
        self.slide_generator_agent = SlideGeneratorAgent()
        
        # 创建并发控制信号量
        self.blueprint_semaphore = asyncio.Semaphore(self.MAX_CONCURRENT_BLUEPRINT_TASKS)
        self.html_semaphore = asyncio.Semaphore(self.MAX_CONCURRENT_HTML_TASKS)
        
        logger.info("PresentationWorkflow初始化完成")
    
    async def generate_async(
        self, 
        user_prompt: str, 
        project_id: str,
        push_progress_func: Optional[callable] = None
    ) -> None:
        """
        异步生成演示文稿的主要入口点
        
        Args:
            user_prompt: 用户的输入提示
            project_id: 项目唯一标识符 
            push_progress_func: 可选的进度推送函数
        """
        # 设置推送函数
        self._push_progress_func = push_progress_func
        
        # 创建专用的任务日志器
        task_logger = logging.getLogger(f'project_generation.{project_id}')
        task_logger.info(f"开始重构后的演示文稿生成流程，项目ID: {project_id}")
        
        # 保存用户的初始聊天消息
        await self._save_chat_message(
            project_id=project_id,
            sender="user",
            text=user_prompt,
            icon="🗣️"
        )
        
        try:
            # === 阶段1: 分析用户意图 ===
            await self._push_progress(OrchestratorProgress(
                project_id=project_id,
                status="analyzing_intent", 
                message=AiChatMessage(sender="ai", text="🔍 正在分析您的需求..."),
                current_slide_index=0,
                total_slides=0
            ))
            # 保存分析意图消息到聊天记录
            await self._save_chat_message(
                project_id=project_id,
                sender="ai",
                text="🔍 正在分析您的需求...",
                icon="🔍"
            )
            
            user_intent = await self._analyze_user_intent(user_prompt, project_id, task_logger)
            if not user_intent:
                error_message = "❌ 无法理解您的需求，请重新描述"
                await self._push_progress(OrchestratorProgress(
                    project_id=project_id,
                    status="error",
                    message=AiChatMessage(sender="ai", text=error_message),
                    current_slide_index=0,
                    total_slides=0
                ))
                # 保存错误消息到聊天记录
                await self._save_chat_message(
                    project_id=project_id,
                    sender="ai",
                    text=error_message,
                    icon="❌"
                )
                return
            
            intent_message = f"✅ 已理解: {user_intent.topic}, {user_intent.suggested_slide_count}张幻灯片"
            await self._push_progress(OrchestratorProgress(
                project_id=project_id,
                status="intent_analyzed",
                message=AiChatMessage(sender="ai", text=intent_message),
                current_slide_index=0,
                total_slides=user_intent.suggested_slide_count
            ))
            # 保存意图分析消息到聊天记录
            await self._save_chat_message(
                project_id=project_id,
                sender="ai",
                text=intent_message,
                icon="✅"
            )
            
            # === 阶段2: 生成整体风格和大纲 ===
            await self._push_progress(OrchestratorProgress(
                project_id=project_id,
                status="generating_outline_and_style",
                message=AiChatMessage(sender="ai", text="🎨 设计整体风格和大纲..."),
                current_slide_index=0,
                total_slides=user_intent.suggested_slide_count
            ))
            # 保存风格设计消息到聊天记录
            await self._save_chat_message(
                project_id=project_id,
                sender="ai",
                text="🎨 设计整体风格和大纲...",
                icon="🎨"
            )
            
            style_and_outlines = await self._generate_style_and_outlines(user_intent, project_id, task_logger)
            if not style_and_outlines:
                error_message = "❌ 风格设计失败"
                await self._push_progress(OrchestratorProgress(
                    project_id=project_id,
                    status="error",
                    message=AiChatMessage(sender="ai", text=error_message),
                    current_slide_index=0,
                    total_slides=0
                ))
                # 保存错误消息到聊天记录
                await self._save_chat_message(
                    project_id=project_id,
                    sender="ai",
                    text=error_message,
                    icon="❌"
                )
                return
            
            actual_slide_count = len(style_and_outlines.presentation_outlines)
            task_logger.info(f"整体风格和大纲生成成功，共 {actual_slide_count} 张幻灯片")
            
            style_message = f"✅ 设计完成，共 {actual_slide_count} 张幻灯片"
            await self._push_progress(OrchestratorProgress(
                project_id=project_id,
                status="outline_and_style_generated",
                message=AiChatMessage(sender="ai", text=style_message),
                current_slide_index=0,
                total_slides=actual_slide_count
            ))
            # 保存风格设计完成消息到聊天记录
            await self._save_chat_message(
                project_id=project_id,
                sender="ai",
                text=style_message,
                icon="✅"
            )
            
            # === 阶段3: 并行处理所有幻灯片 ===
            await self._push_progress(OrchestratorProgress(
                project_id=project_id,
                status="starting_parallel_generation",
                message=AiChatMessage(sender="ai", text="🚀 开始并行生成幻灯片..."),
                current_slide_index=0,
                total_slides=actual_slide_count
            ))
            # 保存并行生成开始消息到聊天记录
            await self._save_chat_message(
                project_id=project_id,
                sender="ai",
                text="🚀 开始并行生成幻灯片...",
                icon="🚀"
            )
            
            # 创建并发任务
            slide_tasks = []
            for i, outline in enumerate(style_and_outlines.presentation_outlines, 1):
                task_logger.info(f"开始细化第 {i} 张幻灯片蓝图...")
                task = asyncio.create_task(self._process_single_slide(
                    outline, user_intent, style_and_outlines, project_id, actual_slide_count, task_logger
                ))
                slide_tasks.append(task)
                # 错峰启动，避免API请求冲突
                await asyncio.sleep(self.TASK_INTERVAL_SECONDS)
            
            # 等待所有任务完成
            results = await asyncio.gather(*slide_tasks, return_exceptions=True)
            
            # 处理结果并按序号保存完成消息
            successful_slides = 0
            completed_slides = []
            
            # 收集所有成功的幻灯片
            for i, result in enumerate(results, 1):
                if isinstance(result, Exception):
                    task_logger.error(f"幻灯片 {i} 生成失败: {result}")
                else:
                    slide_number, success, html_content = result
                    if success:
                        successful_slides += 1
                        completed_slides.append(slide_number)
                        task_logger.info(f"幻灯片 {slide_number} 生成成功")
            
            # 按幻灯片序号排序，确保消息顺序正确
            completed_slides.sort()
            
            # 逐个保存幻灯片完成消息到聊天记录
            for slide_number in completed_slides:
                await self._save_chat_message(
                    project_id=project_id,
                    sender="ai",
                    text=f"✅ 第 {slide_number} 张幻灯片生成完成",
                    icon="✅"
                )
            
            # === 阶段4: 完成 ===
            if successful_slides == actual_slide_count:
                completion_message = f"🎉 演示文稿生成完成！共 {successful_slides} 张幻灯片"
                await self._push_progress(OrchestratorProgress(
                    project_id=project_id,
                    status="completed",
                    message=AiChatMessage(sender="ai", text=completion_message),
                    current_slide_index=actual_slide_count,
                    total_slides=actual_slide_count
                ))
                # 保存AI的完成消息到聊天记录
                await self._save_chat_message(
                    project_id=project_id,
                    sender="ai",
                    text=completion_message,
                    icon="🎉"
                )
            else:
                completion_message = f"⚠️ 部分完成：{successful_slides}/{actual_slide_count} 张幻灯片生成成功"
                await self._push_progress(OrchestratorProgress(
                    project_id=project_id,
                    status="completed_with_errors",
                    message=AiChatMessage(sender="ai", text=completion_message),
                    current_slide_index=successful_slides,
                    total_slides=actual_slide_count
                ))
                # 保存AI的部分完成消息到聊天记录
                await self._save_chat_message(
                    project_id=project_id,
                    sender="ai",
                    text=completion_message,
                    icon="⚠️"
                )
            
        except Exception as e:
            task_logger.error(f"演示文稿生成过程中发生严重错误: {e}", exc_info=True)
            error_message = f"❌ 生成失败: {str(e)}"
            await self._push_progress(OrchestratorProgress(
                project_id=project_id,
                status="error",
                message=AiChatMessage(sender="ai", text=error_message),
                current_slide_index=0,
                total_slides=0
            ))
            # 保存AI的错误消息到聊天记录
            await self._save_chat_message(
                project_id=project_id,
                sender="ai",
                text=error_message,
                icon="❌"
            )
    
    async def _analyze_user_intent(self, user_prompt: str, project_id: str, task_logger: logging.Logger) -> Optional[UserIntentSchema]:
        """分析用户意图"""
        try:
            user_intent, _ = await self.user_intent_agent.process(user_prompt, project_id)
            if user_intent:
                task_logger.info(f"用户意图分析成功: {user_intent.topic}, {user_intent.suggested_slide_count}张幻灯片")
                return user_intent
            else:
                task_logger.error("用户意图分析失败")
                return None
        except Exception as e:
            task_logger.error(f"用户意图分析异常: {e}", exc_info=True)
            return None
    
    async def _generate_style_and_outlines(self, user_intent: UserIntentSchema, project_id: str, task_logger: logging.Logger) -> Optional[StructuredPresentationStyleSchema]:
        """生成整体风格和大纲"""
        try:
            task_logger.info(f"开始调用VisualStyleAgent.process，参数: topic={user_intent.topic}, num_slides={user_intent.suggested_slide_count}")
            style_and_outlines, llm_log_id = await self.visual_style_agent.process(
                topic=user_intent.topic,
                num_slides=user_intent.suggested_slide_count,
                style_keywords=getattr(user_intent, 'style_keywords', None),
                project_id=project_id
            )
            task_logger.info(f"VisualStyleAgent.process调用完成，llm_log_id={llm_log_id}")
            
            if style_and_outlines:
                task_logger.info(f"风格和大纲生成成功，返回类型: {type(style_and_outlines)}")
                task_logger.info(f"大纲数量: {len(style_and_outlines.presentation_outlines) if hasattr(style_and_outlines, 'presentation_outlines') else '未知'}")
                return style_and_outlines
            else:
                task_logger.error("整体风格和大纲生成失败 - 返回结果为None")
                return None
        except Exception as e:
            task_logger.error(f"整体风格和大纲生成异常: {e}", exc_info=True)
            return None
    
    async def _synthesize_html_prompt(self, blueprint: DetailedSlideBlueprintSchema, structured_style: StructuredPresentationStyleSchema) -> str:
        """将结构化的蓝图转换为给代码生成Agent的清晰自然语言指令。"""
        prompt_parts = [
            f"# 指令：为幻灯片 {blueprint.slide_number} 生成HTML代码",
            f"## 布局与风格",
            f"- **布局模板**: 必须严格实现 '{blueprint.layout_template_name}' 布局。",
            f"- **背景**: 应用此CSS样式: '{blueprint.background_style_description}'。",
            f"- **整体风格**: 遵循以下JSON中定义的颜色、字体和CSS变量: \n```json\n{structured_style.model_dump_json(indent=2)}\n```\n",
            "\n## 核心元素 (必须全部实现):"
        ]

        for i, element in enumerate(blueprint.key_elements):
            element_prompt = f"\n### 元素 {i+1}: {element.type.upper()}"
            if isinstance(element, TextElementSchema):
                element_prompt += (
                    f"\n- **类型**: {element.type}"
                    f"\n- **内容**: '{element.content}'"
                    f"\n- **目标区域**: '{element.target_area}'"
                )
            elif isinstance(element, KpiCardSchema):
                element_prompt += (
                    f"\n- **类型**: KPI 卡片"
                    f"\n- **标题**: '{element.title}'"
                    f"\n- **数值**: '{element.value}'"
                    f"\n- **变化**: '{element.change}'"
                    f"\n- **图标**: 使用 Font Awesome 类 '{element.icon_fontawesome_class}'"
                    f"\n- **目标区域**: '{element.target_area}'"
                )
            elif isinstance(element, ChartBlueprintSchema):
                 element_prompt += (
                    f"\n- **类型**: {element.chart_type} 图表"
                    f"\n- **标题**: '{element.title}'"
                    f"\n- **目标区域**: '{element.target_area}'"
                    f"\n- **Chart.js配置**: 必须使用以下完整的JSON配置来渲染图表。不要修改数据或选项。\n```javascript\n{element.final_chart_js_config.model_dump_json(indent=2)}\n```"
                )
            elif isinstance(element, ImageElementSchema):
                element_prompt += (
                    f"\n- **类型**: 图片"
                    f"\n- **图片描述 (用于占位符)**: '{element.alt_text}'"
                    f"\n- **目标区域**: '{element.target_area}'"
                    f"\n- **注意**: 请使用一个符合描述的占位符图片（例如，使用 unsplash.com 的链接或纯色块）。"
                )
            prompt_parts.append(element_prompt)
        
        prompt_parts.append("\n## 最终要求\n请严格遵守所有技术规范，确保最终HTML在1280x720的视口内完美渲染且无溢出。直接输出完整的HTML代码。")
        return "\n".join(prompt_parts)
    
    async def _process_single_slide(
        self,
        outline: SlideOutlineItemSchema,
        user_intent: UserIntentSchema,
        style_and_outlines: StructuredPresentationStyleSchema,
        project_id: str,
        total_slides: int,
        task_logger: logging.Logger
    ) -> tuple[int, bool, str]:
        """
        处理单张幻灯片的完整流程（简化版）
        
        Returns:
            tuple[幻灯片序号, 是否成功, HTML内容]
        """
        slide_number = outline.slide_number
        
        try:
            # === 1. 蓝图细化阶段 ===
            async with self.blueprint_semaphore:
                blueprint = await self.slide_detailer_agent.refine_single_blueprint_from_outline(
                    user_intent_topic=user_intent.topic,
                    full_presentation_style=style_and_outlines,
                    single_slide_outline=outline,
                    project_id=project_id
                )
                
                if not blueprint:
                    task_logger.error(f"第 {slide_number} 张幻灯片蓝图细化失败")
                    return slide_number, False, ""
                
                task_logger.info(f"第 {slide_number} 张幻灯片蓝图细化成功")
                
                await self._push_progress(OrchestratorProgress(
                    project_id=project_id, status="blueprint_refined",
                    message=AiChatMessage(sender="ai", text=f"📋 第 {slide_number} 张幻灯片蓝图完成"),
                    current_slide_index=slide_number - 1, total_slides=total_slides
                ))
            
            # === 2. HTML生成阶段 ===
            async with self.html_semaphore:
                task_logger.info(f"[{slide_number}] 合成自然语言指令...")
                
                # --- 调用新的指令合成方法 ---
                synthesized_prompt = await self._synthesize_html_prompt(blueprint, style_and_outlines)
                
                task_logger.info(f"[{slide_number}] 调用SlideGeneratorAgent生成HTML...")
                # --- 调用SlideGeneratorAgent的新接口 ---
                html_content, llm_log_id = await self.slide_generator_agent.process(
                    synthesized_html_prompt=synthesized_prompt,
                    project_id=project_id,
                    slide_number=slide_number
                )
                
                if not html_content:
                    task_logger.error(f"第 {slide_number} 张幻灯片HTML生成失败")
                    return slide_number, False, ""
                
                task_logger.info(f"成功生成HTML，长度: {len(html_content)} 字符")
                
                # 清理HTML
                cleaned_html = comprehensive_html_cleanup(html_content)
                
                # === 3. 保存到数据库 ===
                await self._save_slide_to_database(
                    project_id=project_id,
                    slide_number=slide_number,
                    slide_title=outline.title,
                    html_content=cleaned_html,
                    blueprint=blueprint,
                    llm_log_id=llm_log_id  # 传递LLM日志ID
                )
                
                slide_completed_message = f"✅ 第 {slide_number} 张幻灯片生成完成"
                await self._push_progress(OrchestratorProgress(
                    project_id=project_id, status="slide_completed",
                    message=AiChatMessage(sender="ai", text=slide_completed_message),
                    current_slide_index=slide_number, total_slides=total_slides
                ))
                # 注意：不再在此处保存聊天消息，改为在主流程中统一处理以确保顺序
                
                task_logger.info(f"第 {slide_number} 张幻灯片处理完成")
                return slide_number, True, cleaned_html
                
        except Exception as e:
            task_logger.error(f"第 {slide_number} 张幻灯片处理异常:", exc_info=True)
            return slide_number, False, ""
    
    async def _save_slide_to_database(
        self, 
        project_id: str, 
        slide_number: int, 
        slide_title: str, 
        html_content: str,
        blueprint: DetailedSlideBlueprintSchema,
        llm_log_id: Optional[str] = None
    ):
        """保存幻灯片到数据库"""
        try:
            db = next(get_db())
            
            # 准备prompt信息
            prompt = blueprint.model_dump_json() if blueprint else f"幻灯片 {slide_number}: {slide_title}"
            
            # 保存到数据库 - 包含LLM日志关联
            crud_slide.create_slide(
                db=db,
                project_id=project_id,
                slide_number=slide_number,
                html_content=html_content,
                prompt=prompt,
                image_data=None,  # 暂时不处理图片数据
                generating_interaction_log_id=llm_log_id  # 关联LLM日志
            )
            logger.info(f"幻灯片 {slide_number} 已保存到数据库，关联日志ID: {llm_log_id}")
            
        except Exception as e:
            logger.error(f"保存幻灯片 {slide_number} 到数据库失败: {e}")
        finally:
            db.close()
    
    async def _push_progress(self, progress: OrchestratorProgress):
        """推送进度更新"""
        if self._push_progress_func:
            try:
                await self._push_progress_func(progress)
            except Exception as e:
                logger.warning(f"进度推送失败: {e}")
    
    async def _save_chat_message(self, project_id: str, sender: str, text: str, icon: str = None):
        """保存聊天消息到数据库"""
        try:
            db = next(get_db())
            
            # 创建聊天消息对象
            chat_message = AiChatMessage(
                sender=sender,
                text=text,
                icon=icon
            )
            
            # 保存到数据库
            crud_chat.add_chat_message(
                db=db,
                project_id=project_id,
                message=chat_message,
                user_id=None  # 暂时不处理用户ID
            )
            logger.info(f"聊天消息已保存到数据库: {sender} - {text[:50]}...")
            
        except Exception as e:
            logger.error(f"保存聊天消息到数据库失败: {e}")
        finally:
            if 'db' in locals():
                db.close()
                
    async def edit_slide_element(
        self,
        slide_id: str,
        element_selector: str,
        instruction: str,
        db: Session,
        project_id_for_logging: str
    ) -> Optional[str]:
        """
        编辑幻灯片中的特定元素
        
        Args:
            slide_id: 幻灯片ID
            element_selector: 用于选择HTML元素的CSS选择器
            instruction: 编辑指令
            db: 数据库会话
            project_id_for_logging: 用于日志记录的项目ID
            
        Returns:
            Optional[str]: 更新后的HTML内容，如果失败则返回None
        """
        task_logger = logging.getLogger(f'slide_edit.{project_id_for_logging}')
        task_logger.info(f"开始编辑幻灯片元素，slide_id: {slide_id}, selector: {element_selector}")
        
        try:
            # 获取幻灯片
            slide = crud_slide.get_slide(db, slide_id=slide_id)
            if not slide:
                task_logger.error(f"找不到幻灯片: {slide_id}")
                return None
                
            # 获取当前HTML内容
            current_html = slide.html_content
            if not current_html:
                task_logger.error(f"幻灯片没有HTML内容: {slide_id}")
                return None
                
            # 使用SlideGeneratorAgent处理编辑
            updated_html, _ = await self.slide_generator_agent.edit_html_element(
                html_content=current_html,
                element_selector=element_selector,
                edit_instruction=instruction,
                project_id=project_id_for_logging
            )
            
            if not updated_html:
                task_logger.error(f"编辑失败，无法更新HTML: {slide_id}")
                return None
                
            # 清理HTML
            cleaned_html = comprehensive_html_cleanup(updated_html)
            
            # 更新数据库
            updated_slide = crud_slide.update_slide_content(
                db=db, 
                slide_id=slide_id, 
                update_data={"html_content": cleaned_html, "updated_at": datetime.utcnow()}
            )
            
            if not updated_slide:
                task_logger.error(f"更新幻灯片数据库记录失败: {slide_id}")
                return None
                
            task_logger.info(f"成功编辑幻灯片元素: {slide_id}")
            return cleaned_html
            
        except Exception as e:
            task_logger.error(f"编辑幻灯片元素时发生异常: {e}", exc_info=True)
            return None