import React, { useState, useRef, useEffect } from 'react';
import { FaEllipsisV, FaComment, FaShare, FaEdit, FaArchive, FaTrash } from 'react-icons/fa';
import apiService from '../services/api';
import { useNavigate } from 'react-router-dom';

const ChatHistoryList = ({ currentChatId, onSelectChat, className = "" }) => {
  const [chatList, setChatList] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const [hoveredChatId, setHoveredChatId] = useState(null);
  const [dropdownOpen, setDropdownOpen] = useState(null);
  const dropdownRef = useRef(null);

  const navigate = useNavigate();

  // 获取聊天历史的函数
  const fetchChatHistory = async () => {
    try {
      setIsLoading(true);
      // 使用与获取项目历史相同的API，因为它们是同一个数据源
      const history = await apiService.getProjectsHistory();
      
      // 格式化API返回的数据
      const formattedHistory = history.map(item => ({
        id: item.id,
        title: item.title || `未命名对话`,
        lastMessage: item.last_message || '无消息内容',
        timestamp: item.last_modified || item.created_at,
        unreadCount: 0, // 暂时不处理未读消息
        projectType: item.project_type || 'presentation' // 获取项目类型
      }));
      
      setChatList(formattedHistory);
    } catch (error) {
      console.error('获取聊天历史失败:', error);
      setError('加载聊天历史失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 页面加载时获取聊天历史
  useEffect(() => {
    fetchChatHistory();
  }, []);

  // 监听刷新事件
  useEffect(() => {
    const handleRefresh = () => {
      fetchChatHistory();
    };

    window.addEventListener('refreshChatHistory', handleRefresh);
    return () => {
      window.removeEventListener('refreshChatHistory', handleRefresh);
    };
  }, []);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return '刚刚';
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else if (diffInHours < 48) {
      return '昨天';
    } else {
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
    }
  };

  const truncateText = (text, maxLength = 30) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const handleDropdownAction = async (action, chatId) => {
    setDropdownOpen(null);
    
    switch (action) {
      case 'share':
        console.log('分享聊天:', chatId);
        // 实现分享功能
        break;
      case 'rename':
        console.log('重命名聊天:', chatId);
        // 实现重命名功能
        const newTitle = prompt('请输入新的聊天名称:');
        if (newTitle && newTitle.trim()) {
          try {
            await apiService.updateProject(chatId, { title: newTitle.trim() });
            setChatList(prev => prev.map(chat => 
              chat.id === chatId ? { ...chat, title: newTitle.trim() } : chat
            ));
          } catch (error) {
            console.error('重命名失败:', error);
          }
        }
        break;
      case 'archive':
        console.log('归档聊天:', chatId);
        // 实现归档功能 (临时还是删除)
        try {
          await apiService.deleteProject(chatId);
          setChatList(prev => prev.filter(chat => chat.id !== chatId));
        } catch (error) {
          console.error('归档失败:', error);
        }
        break;
      case 'delete':
        console.log('删除聊天:', chatId);
        // 直接删除，无需确认
        try {
          await apiService.deleteProject(chatId);
          setChatList(prev => prev.filter(chat => chat.id !== chatId));
        } catch (error) {
          console.error('删除失败:', error);
        }
        break;
      default:
        break;
    }
  };

  const handleChatClick = (chatId, projectType, title) => {
    // 触发父组件的onSelectChat回调
    onSelectChat?.(chatId);
    
    // 根据项目类型导航到对应页面
    // 首先尝试通过标题检测是否是聊天项目
    const isChatByTitle = title && (
      title.toLowerCase().includes('chat') || 
      title.toLowerCase().includes('聊天') ||
      title.toLowerCase().includes('对话')
    );
    
    if (projectType === 'chat' || isChatByTitle) {
      // 如果是聊天项目，导航到聊天页面并使用正确的查询参数
      navigate(`/chat?chatId=${chatId}`);
      // 强制页面刷新以确保数据加载 - 如果当前已在聊天页面
      if (window.location.pathname === '/chat') {
        window.location.reload();
      }
    } else {
      // 如果是幻灯片项目，使用projectId参数导航
      navigate(`/?projectId=${chatId}`);
      // 强制页面刷新以确保数据加载 - 如果当前已在幻灯片页面
      if (window.location.pathname === '/') {
        window.location.reload();
      }
    }
  };

  // 显示加载状态
  if (isLoading) {
    return (
      <div className={`space-y-1 ${className} p-3 text-center`}>
        <p className="text-sm text-gray-500">加载中...</p>
      </div>
    );
  }

  // 显示错误信息
  if (error) {
    return (
      <div className={`space-y-1 ${className} p-3 text-center`}>
        <p className="text-sm text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* 聊天列表 */}
      <div className="space-y-1">
        {chatList.map((chat) => (
          <div
            key={chat.id}
            className={`relative group flex items-center px-3 py-3 rounded-md cursor-pointer transition-colors duration-150 ${
              currentChatId === chat.id
                ? 'bg-blue-50 border-r-2 border-blue-500'
                : 'hover:bg-gray-50'
            }`}
            onMouseEnter={() => setHoveredChatId(chat.id)}
            onMouseLeave={() => setHoveredChatId(null)}
            onClick={() => handleChatClick(chat.id, chat.projectType, chat.title)}
          >
            {/* 聊天内容 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h4 className={`text-sm font-medium truncate ${
                  currentChatId === chat.id ? 'text-blue-700' : 'text-gray-900'
                }`}>
                  {truncateText(chat.title)}
                </h4>
              </div>
              
              <p className="text-xs text-gray-600 truncate">
                {truncateText(chat.lastMessage)}
              </p>
            </div>

            {/* 三点菜单按钮 */}
            {(hoveredChatId === chat.id || dropdownOpen === chat.id) && (
              <div className="relative ml-2" ref={dropdownOpen === chat.id ? dropdownRef : null}>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setDropdownOpen(dropdownOpen === chat.id ? null : chat.id);
                  }}
                  className="p-1 text-gray-400 hover:text-gray-600 rounded transition-colors"
                  title="更多操作"
                >
                  <FaEllipsisV size={12} />
                </button>

                {/* 下拉菜单 */}
                {dropdownOpen === chat.id && (
                  <div className="absolute right-0 top-6 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50 translate-x-[80px]">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDropdownAction('share', chat.id);
                      }}
                      className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <FaShare size={12} className="mr-2" />
                      共享
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDropdownAction('rename', chat.id);
                      }}
                      className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <FaEdit size={12} className="mr-2" />
                      重命名
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDropdownAction('archive', chat.id);
                      }}
                      className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <FaArchive size={12} className="mr-2" />
                      归档
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDropdownAction('delete', chat.id);
                      }}
                      className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                    >
                      <FaTrash size={12} className="mr-2" />
                      删除
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 空状态 */}
      {chatList.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <FaComment size={24} className="mx-auto mb-2 opacity-50" />
          <p className="text-sm">暂无聊天记录</p>
        </div>
      )}
    </div>
  );
};

export default ChatHistoryList; 