{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\PlayerToolbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\n// 引入 react-icons/fa 图标库\nimport { FaPlay, FaArrowLeft, FaShareAlt, FaDownload, FaEllipsisH, FaFilePdf, FaFilePowerpoint, FaCopy, FaExternalLinkAlt, FaGlobe, FaFileAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PlayerToolbar = ({\n  fileName,\n  totalPages,\n  currentPage = 1,\n  onPlay,\n  // 这个回调将用于触发 App.js 的全屏播放\n  onExitPlayer,\n  onExportSelect,\n  onPublishSelect,\n  onMoreOptionsSelect\n}) => {\n  _s();\n  const [showPublish, setShowPublish] = useState(false);\n  const [showExport, setShowExport] = useState(false);\n  const [showMore, setShowMore] = useState(false);\n  const exportOptions = [{\n    key: 'pdf',\n    label: 'PDF',\n    icon: /*#__PURE__*/_jsxDEV(FaFilePdf, {\n      className: \"mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 39\n    }, this)\n  }, {\n    key: 'pptx',\n    label: 'PPTX',\n    icon: /*#__PURE__*/_jsxDEV(FaFilePowerpoint, {\n      className: \"mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 41\n    }, this)\n  }];\n  const publishOptions = [{\n    key: 'copyLink',\n    label: '复制链接',\n    icon: /*#__PURE__*/_jsxDEV(FaCopy, {\n      className: \"mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 45\n    }, this)\n  }, {\n    key: 'openNewTab',\n    label: '在新标签页打开',\n    icon: /*#__PURE__*/_jsxDEV(FaExternalLinkAlt, {\n      className: \"mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 50\n    }, this)\n  }];\n  const moreOptions = [{\n    key: 'editCanva',\n    label: '在Canva和Figma中编辑',\n    icon: /*#__PURE__*/_jsxDEV(FaExternalLinkAlt, {\n      className: \"mr-2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 57\n    }, this)\n  }];\n\n  // Close dropdowns when clicking outside\n  const handleClickOutside = () => {\n    setShowPublish(false);\n    setShowExport(false);\n    setShowMore(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"viewer-header\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"project-title-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onExitPlayer,\n        className: \"p-2 mr-3 text-gray-600 hover:text-blue-500 rounded-full hover:bg-gray-100 transition-colors\",\n        title: \"\\u8FD4\\u56DE\\u7F16\\u8F91\\u5668\",\n        \"aria-label\": \"\\u8FD4\\u56DE\\u7F16\\u8F91\\u5668\",\n        children: /*#__PURE__*/_jsxDEV(FaArrowLeft, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FaFileAlt, {\n        className: \"text-gray-500 mr-2 text-lg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"project-title-text\",\n        title: fileName,\n        children: fileName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"page-count-text\",\n        children: [currentPage, \" / \", totalPages, \" pages\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"action-buttons\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onPlay,\n        disabled: totalPages === 0,\n        className: \"action-button\",\n        \"aria-label\": \"\\u64AD\\u653E\\u5E7B\\u706F\\u7247\",\n        children: [/*#__PURE__*/_jsxDEV(FaPlay, {\n          className: \"mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), \" Play Slides\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dropdown\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.stopPropagation();\n            setShowPublish(p => !p);\n            setShowExport(false);\n            setShowMore(false);\n          },\n          disabled: totalPages === 0,\n          className: \"action-button\",\n          \"aria-label\": \"\\u53D1\\u5E03\",\n          \"aria-expanded\": showPublish,\n          children: [/*#__PURE__*/_jsxDEV(FaGlobe, {\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), \" Publish\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), showPublish && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-content\",\n          children: publishOptions.map(opt => /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            onClick: e => {\n              e.preventDefault();\n              onPublishSelect(opt.key);\n              setShowPublish(false);\n            },\n            \"aria-label\": opt.label,\n            children: [opt.icon, \" \", opt.label]\n          }, opt.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dropdown\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.stopPropagation();\n            setShowExport(p => !p);\n            setShowPublish(false);\n            setShowMore(false);\n          },\n          disabled: totalPages === 0,\n          className: \"action-button\",\n          \"aria-label\": \"\\u5BFC\\u51FA\",\n          \"aria-expanded\": showExport,\n          children: [/*#__PURE__*/_jsxDEV(FaDownload, {\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), \" Export \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '0.6rem',\n              backgroundColor: '#ff9800',\n              color: 'white',\n              padding: '1px 4px',\n              borderRadius: '4px',\n              fontWeight: 700,\n              position: 'relative',\n              top: '-1px'\n            },\n            children: \"Beta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 52\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), showExport && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-content\",\n          children: exportOptions.map(opt => /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            onClick: e => {\n              e.preventDefault();\n              onExportSelect(opt.key);\n              setShowExport(false);\n            },\n            \"aria-label\": `导出为${opt.label}`,\n            children: [opt.icon, \" \", opt.label]\n          }, opt.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dropdown\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.stopPropagation();\n            setShowMore(p => !p);\n            setShowPublish(false);\n            setShowExport(false);\n          },\n          className: \"action-button\",\n          \"aria-label\": \"\\u66F4\\u591A\\u9009\\u9879\",\n          \"aria-expanded\": showMore,\n          children: /*#__PURE__*/_jsxDEV(FaEllipsisH, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), showMore && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dropdown-content\",\n          children: moreOptions.map(opt => /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#\",\n            onClick: e => {\n              e.preventDefault();\n              onMoreOptionsSelect(opt.key);\n              setShowMore(false);\n            },\n            \"aria-label\": opt.label,\n            children: [opt.icon, \" \", opt.label]\n          }, opt.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(PlayerToolbar, \"URVP4EbpEZOfhQ5YC3Y8sK5sjiA=\");\n_c = PlayerToolbar;\nexport default PlayerToolbar;\nvar _c;\n$RefreshReg$(_c, \"PlayerToolbar\");", "map": {"version": 3, "names": ["React", "useState", "FaPlay", "FaArrowLeft", "FaShareAlt", "FaDownload", "FaEllipsisH", "FaFilePdf", "FaFilePowerpoint", "FaCopy", "FaExternalLinkAlt", "FaGlobe", "FaFileAlt", "jsxDEV", "_jsxDEV", "PlayerToolbar", "fileName", "totalPages", "currentPage", "onPlay", "onExitPlayer", "onExportSelect", "onPublishSelect", "onMoreOptionsSelect", "_s", "showPublish", "setShowPublish", "showExport", "setShowExport", "showMore", "setShowMore", "exportOptions", "key", "label", "icon", "className", "_jsxFileName", "lineNumber", "columnNumber", "publishOptions", "moreOptions", "handleClickOutside", "children", "onClick", "title", "disabled", "e", "stopPropagation", "p", "map", "opt", "href", "preventDefault", "style", "fontSize", "backgroundColor", "color", "padding", "borderRadius", "fontWeight", "position", "top", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/PlayerToolbar.js"], "sourcesContent": ["import React, { useState } from 'react';\n// 引入 react-icons/fa 图标库\nimport { FaPlay, FaArrowLeft, FaShareAlt, FaDownload, FaEllipsisH, FaFilePdf, FaFilePowerpoint, FaCopy, FaExternalLinkAlt, FaGlobe, FaFileAlt } from 'react-icons/fa';\n\nconst PlayerToolbar = ({\n  fileName,\n  totalPages,\n  currentPage = 1,\n  onPlay, // 这个回调将用于触发 App.js 的全屏播放\n  onExitPlayer,\n  onExportSelect,\n  onPublishSelect,\n  onMoreOptionsSelect\n}) => {\n  const [showPublish, setShowPublish] = useState(false);\n  const [showExport, setShowExport] = useState(false);\n  const [showMore, setShowMore] = useState(false);\n\n  const exportOptions = [\n    { key: 'pdf', label: 'PDF', icon: <FaFilePdf className=\"mr-2\" /> },\n    { key: 'pptx', label: 'PPTX', icon: <FaFilePowerpoint className=\"mr-2\" /> }\n  ];\n\n  const publishOptions = [\n    { key: 'copyLink', label: '复制链接', icon: <FaCopy className=\"mr-2\" /> },\n    { key: 'openNewTab', label: '在新标签页打开', icon: <FaExternalLinkAlt className=\"mr-2\" /> }\n  ];\n  \n  const moreOptions = [\n    { key: 'editCanva', label: '在Canva和Figma中编辑', icon: <FaExternalLinkAlt className=\"mr-2\" /> },\n  ];\n\n  // Close dropdowns when clicking outside\n  const handleClickOutside = () => {\n    setShowPublish(false);\n    setShowExport(false);\n    setShowMore(false);\n  };\n\n  return (\n    <div className=\"viewer-header\">\n      <div className=\"project-title-section\">\n        <button \n          onClick={onExitPlayer} \n          className=\"p-2 mr-3 text-gray-600 hover:text-blue-500 rounded-full hover:bg-gray-100 transition-colors\" \n          title=\"返回编辑器\"\n          aria-label=\"返回编辑器\"\n        >\n          <FaArrowLeft />\n        </button>\n        <FaFileAlt className=\"text-gray-500 mr-2 text-lg\" />\n        <span className=\"project-title-text\" title={fileName}>{fileName}</span>\n        <span className=\"page-count-text\">\n          {currentPage} / {totalPages} pages\n        </span>\n      </div>\n      \n      <div className=\"action-buttons\">\n        <button \n          onClick={onPlay} \n          disabled={totalPages === 0}\n          className=\"action-button\"\n          aria-label=\"播放幻灯片\"\n        >\n          <FaPlay className=\"mr-2\" /> Play Slides\n        </button>\n        \n        {/* Publish Dropdown */}\n        <div className=\"dropdown\">\n          <button \n            onClick={(e) => {e.stopPropagation(); setShowPublish(p => !p); setShowExport(false); setShowMore(false);}} \n            disabled={totalPages === 0}\n            className=\"action-button\"\n            aria-label=\"发布\"\n            aria-expanded={showPublish}\n          >\n            <FaGlobe className=\"mr-2\" /> Publish\n          </button>\n          {showPublish && (\n            <div className=\"dropdown-content\">\n              {publishOptions.map(opt => (\n                <a \n                  key={opt.key} \n                  href=\"#\" \n                  onClick={(e) => { e.preventDefault(); onPublishSelect(opt.key); setShowPublish(false);}} \n                  aria-label={opt.label}\n                >\n                  {opt.icon} {opt.label}\n                </a>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Export Dropdown */}\n        <div className=\"dropdown\">\n          <button \n            onClick={(e) => {e.stopPropagation(); setShowExport(p => !p); setShowPublish(false); setShowMore(false);}} \n            disabled={totalPages === 0}\n            className=\"action-button\"\n            aria-label=\"导出\"\n            aria-expanded={showExport}\n          >\n            <FaDownload className=\"mr-2\" /> Export <span style={{fontSize: '0.6rem', backgroundColor: '#ff9800', color: 'white', padding: '1px 4px', borderRadius: '4px', fontWeight: 700, position: 'relative', top: '-1px'}}>Beta</span>\n          </button>\n          {showExport && (\n            <div className=\"dropdown-content\">\n              {exportOptions.map(opt => (\n                <a \n                  key={opt.key} \n                  href=\"#\" \n                  onClick={(e) => { e.preventDefault(); onExportSelect(opt.key); setShowExport(false);}} \n                  aria-label={`导出为${opt.label}`}\n                >\n                  {opt.icon} {opt.label}\n                </a>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* More Options Dropdown */}\n        <div className=\"dropdown\">\n          <button \n            onClick={(e) => {e.stopPropagation(); setShowMore(p => !p); setShowPublish(false); setShowExport(false);}} \n            className=\"action-button\"\n            aria-label=\"更多选项\"\n            aria-expanded={showMore}\n          >\n            <FaEllipsisH />\n          </button>\n          {showMore && (\n            <div className=\"dropdown-content\">\n              {moreOptions.map(opt => (\n                <a \n                  key={opt.key} \n                  href=\"#\" \n                  onClick={(e) => { e.preventDefault(); onMoreOptionsSelect(opt.key); setShowMore(false);}} \n                  aria-label={opt.label}\n                >\n                  {opt.icon} {opt.label}\n                </a>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PlayerToolbar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC;AACA,SAASC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,SAAS,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtK,MAAMC,aAAa,GAAGA,CAAC;EACrBC,QAAQ;EACRC,UAAU;EACVC,WAAW,GAAG,CAAC;EACfC,MAAM;EAAE;EACRC,YAAY;EACZC,cAAc;EACdC,eAAe;EACfC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAM8B,aAAa,GAAG,CACpB;IAAEC,GAAG,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,IAAI,eAAEpB,OAAA,CAACP,SAAS;MAAC4B,SAAS,EAAC;IAAM;MAAAnB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAClE;IAAEN,GAAG,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,eAAEpB,OAAA,CAACN,gBAAgB;MAAC2B,SAAS,EAAC;IAAM;MAAAnB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CAC5E;EAED,MAAMC,cAAc,GAAG,CACrB;IAAEP,GAAG,EAAE,UAAU;IAAEC,KAAK,EAAE,MAAM;IAAEC,IAAI,eAAEpB,OAAA,CAACL,MAAM;MAAC0B,SAAS,EAAC;IAAM;MAAAnB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACrE;IAAEN,GAAG,EAAE,YAAY;IAAEC,KAAK,EAAE,SAAS;IAAEC,IAAI,eAAEpB,OAAA,CAACJ,iBAAiB;MAACyB,SAAS,EAAC;IAAM;MAAAnB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CACtF;EAED,MAAME,WAAW,GAAG,CAClB;IAAER,GAAG,EAAE,WAAW;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,eAAEpB,OAAA,CAACJ,iBAAiB;MAACyB,SAAS,EAAC;IAAM;MAAAnB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CAC7F;;EAED;EACA,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/Bf,cAAc,CAAC,KAAK,CAAC;IACrBE,aAAa,CAAC,KAAK,CAAC;IACpBE,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,oBACEhB,OAAA;IAAKqB,SAAS,EAAC,eAAe;IAAAO,QAAA,gBAC5B5B,OAAA;MAAKqB,SAAS,EAAC,uBAAuB;MAAAO,QAAA,gBACpC5B,OAAA;QACE6B,OAAO,EAAEvB,YAAa;QACtBe,SAAS,EAAC,6FAA6F;QACvGS,KAAK,EAAC,gCAAO;QACb,cAAW,gCAAO;QAAAF,QAAA,eAElB5B,OAAA,CAACX,WAAW;UAAAa,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACTxB,OAAA,CAACF,SAAS;QAACuB,SAAS,EAAC;MAA4B;QAAAnB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpDxB,OAAA;QAAMqB,SAAS,EAAC,oBAAoB;QAACS,KAAK,EAAE5B,QAAS;QAAA0B,QAAA,EAAE1B;MAAQ;QAAAA,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACvExB,OAAA;QAAMqB,SAAS,EAAC,iBAAiB;QAAAO,QAAA,GAC9BxB,WAAW,EAAC,KAAG,EAACD,UAAU,EAAC,QAC9B;MAAA;QAAAD,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENxB,OAAA;MAAKqB,SAAS,EAAC,gBAAgB;MAAAO,QAAA,gBAC7B5B,OAAA;QACE6B,OAAO,EAAExB,MAAO;QAChB0B,QAAQ,EAAE5B,UAAU,KAAK,CAAE;QAC3BkB,SAAS,EAAC,eAAe;QACzB,cAAW,gCAAO;QAAAO,QAAA,gBAElB5B,OAAA,CAACZ,MAAM;UAACiC,SAAS,EAAC;QAAM;UAAAnB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAC7B;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAGTxB,OAAA;QAAKqB,SAAS,EAAC,UAAU;QAAAO,QAAA,gBACvB5B,OAAA;UACE6B,OAAO,EAAGG,CAAC,IAAK;YAACA,CAAC,CAACC,eAAe,CAAC,CAAC;YAAErB,cAAc,CAACsB,CAAC,IAAI,CAACA,CAAC,CAAC;YAAEpB,aAAa,CAAC,KAAK,CAAC;YAAEE,WAAW,CAAC,KAAK,CAAC;UAAC,CAAE;UAC1Ge,QAAQ,EAAE5B,UAAU,KAAK,CAAE;UAC3BkB,SAAS,EAAC,eAAe;UACzB,cAAW,cAAI;UACf,iBAAeV,WAAY;UAAAiB,QAAA,gBAE3B5B,OAAA,CAACH,OAAO;YAACwB,SAAS,EAAC;UAAM;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAC9B;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRb,WAAW,iBACVX,OAAA;UAAKqB,SAAS,EAAC,kBAAkB;UAAAO,QAAA,EAC9BH,cAAc,CAACU,GAAG,CAACC,GAAG,iBACrBpC,OAAA;YAEEqC,IAAI,EAAC,GAAG;YACRR,OAAO,EAAGG,CAAC,IAAK;cAAEA,CAAC,CAACM,cAAc,CAAC,CAAC;cAAE9B,eAAe,CAAC4B,GAAG,CAAClB,GAAG,CAAC;cAAEN,cAAc,CAAC,KAAK,CAAC;YAAC,CAAE;YACxF,cAAYwB,GAAG,CAACjB,KAAM;YAAAS,QAAA,GAErBQ,GAAG,CAAChB,IAAI,EAAC,GAAC,EAACgB,GAAG,CAACjB,KAAK;UAAA,GALhBiB,GAAG,CAAClB,GAAG;YAAAhB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMX,CACJ;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxB,OAAA;QAAKqB,SAAS,EAAC,UAAU;QAAAO,QAAA,gBACvB5B,OAAA;UACE6B,OAAO,EAAGG,CAAC,IAAK;YAACA,CAAC,CAACC,eAAe,CAAC,CAAC;YAAEnB,aAAa,CAACoB,CAAC,IAAI,CAACA,CAAC,CAAC;YAAEtB,cAAc,CAAC,KAAK,CAAC;YAAEI,WAAW,CAAC,KAAK,CAAC;UAAC,CAAE;UAC1Ge,QAAQ,EAAE5B,UAAU,KAAK,CAAE;UAC3BkB,SAAS,EAAC,eAAe;UACzB,cAAW,cAAI;UACf,iBAAeR,UAAW;UAAAe,QAAA,gBAE1B5B,OAAA,CAACT,UAAU;YAAC8B,SAAS,EAAC;UAAM;YAAAnB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAAQ,eAAAxB,OAAA;YAAMuC,KAAK,EAAE;cAACC,QAAQ,EAAE,QAAQ;cAAEC,eAAe,EAAE,SAAS;cAAEC,KAAK,EAAE,OAAO;cAAEC,OAAO,EAAE,SAAS;cAAEC,YAAY,EAAE,KAAK;cAAEC,UAAU,EAAE,GAAG;cAAEC,QAAQ,EAAE,UAAU;cAAEC,GAAG,EAAE;YAAM,CAAE;YAAAnB,QAAA,EAAC;UAAI;YAAA1B,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxN,CAAC,EACRX,UAAU,iBACTb,OAAA;UAAKqB,SAAS,EAAC,kBAAkB;UAAAO,QAAA,EAC9BX,aAAa,CAACkB,GAAG,CAACC,GAAG,iBACpBpC,OAAA;YAEEqC,IAAI,EAAC,GAAG;YACRR,OAAO,EAAGG,CAAC,IAAK;cAAEA,CAAC,CAACM,cAAc,CAAC,CAAC;cAAE/B,cAAc,CAAC6B,GAAG,CAAClB,GAAG,CAAC;cAAEJ,aAAa,CAAC,KAAK,CAAC;YAAC,CAAE;YACtF,cAAY,MAAMsB,GAAG,CAACjB,KAAK,EAAG;YAAAS,QAAA,GAE7BQ,GAAG,CAAChB,IAAI,EAAC,GAAC,EAACgB,GAAG,CAACjB,KAAK;UAAA,GALhBiB,GAAG,CAAClB,GAAG;YAAAhB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMX,CACJ;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxB,OAAA;QAAKqB,SAAS,EAAC,UAAU;QAAAO,QAAA,gBACvB5B,OAAA;UACE6B,OAAO,EAAGG,CAAC,IAAK;YAACA,CAAC,CAACC,eAAe,CAAC,CAAC;YAAEjB,WAAW,CAACkB,CAAC,IAAI,CAACA,CAAC,CAAC;YAAEtB,cAAc,CAAC,KAAK,CAAC;YAAEE,aAAa,CAAC,KAAK,CAAC;UAAC,CAAE;UAC1GO,SAAS,EAAC,eAAe;UACzB,cAAW,0BAAM;UACjB,iBAAeN,QAAS;UAAAa,QAAA,eAExB5B,OAAA,CAACR,WAAW;YAAAU,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,EACRT,QAAQ,iBACPf,OAAA;UAAKqB,SAAS,EAAC,kBAAkB;UAAAO,QAAA,EAC9BF,WAAW,CAACS,GAAG,CAACC,GAAG,iBAClBpC,OAAA;YAEEqC,IAAI,EAAC,GAAG;YACRR,OAAO,EAAGG,CAAC,IAAK;cAAEA,CAAC,CAACM,cAAc,CAAC,CAAC;cAAE7B,mBAAmB,CAAC2B,GAAG,CAAClB,GAAG,CAAC;cAAEF,WAAW,CAAC,KAAK,CAAC;YAAC,CAAE;YACzF,cAAYoB,GAAG,CAACjB,KAAM;YAAAS,QAAA,GAErBQ,GAAG,CAAChB,IAAI,EAAC,GAAC,EAACgB,GAAG,CAACjB,KAAK;UAAA,GALhBiB,GAAG,CAAClB,GAAG;YAAAhB,QAAA,EAAAoB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMX,CACJ;QAAC;UAAAtB,QAAA,EAAAoB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAtB,QAAA,EAAAoB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAtB,QAAA,EAAAoB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAtB,QAAA,EAAAoB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACd,EAAA,CAjJIT,aAAa;AAAA+C,EAAA,GAAb/C,aAAa;AAmJnB,eAAeA,aAAa;AAAC,IAAA+C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}