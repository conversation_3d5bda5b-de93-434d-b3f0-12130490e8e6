#!/usr/bin/env python3
"""
测试LLM通信日志记录功能
"""
import sys
import os
import asyncio

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.llm_communication_logger import llm_comm_logger

def test_llm_logging():
    """测试LLM通信日志记录功能"""
    
    # 测试项目ID
    test_project_id = "test_project_123"
    
    print(f"测试项目: {test_project_id}")
    print("开始测试LLM通信日志记录...")
    
    # 测试请求记录
    print("\n1. 测试LLM请求记录")
    llm_comm_logger.log_llm_request(
        project_id=test_project_id,
        agent_name="TestAgent",
        prompt_content="这是一个测试提示词，用于验证日志记录功能是否正常工作。请回答：什么是人工智能？",
        temperature=0.7,
        model_name="gemini-2.5-flash-preview",
        expected_response_type="text",
        context="test_request"
    )
    
    # 测试成功响应记录
    print("2. 测试成功响应记录")
    llm_comm_logger.log_llm_response(
        project_id=test_project_id,
        agent_name="TestAgent",
        response_text="人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        raw_response={"success": True, "model": "gemini-2.5-flash-preview"},
        duration_ms=1500,
        success=True,
        thinking_summary="用户询问人工智能的定义，我需要提供一个准确、简洁的解释。",
        context="test_request"
    )
    
    # 测试解析记录
    print("3. 测试解析记录")
    llm_comm_logger.log_llm_parsing_attempt(
        project_id=test_project_id,
        agent_name="TestAgent",
        response_text='{"type": "definition", "content": "AI是计算机科学的分支"}',
        expected_schema="DefinitionSchema",
        validation_success=True,
        parsed_result='{"type": "definition", "content": "AI是计算机科学的分支"}',
        context="test_parsing"
    )
    
    # 测试错误记录
    print("4. 测试错误响应记录")
    llm_comm_logger.log_llm_response(
        project_id=test_project_id,
        agent_name="TestAgent",
        response_text=None,
        raw_response={"error": "rate_limit", "type": "ResourceExhausted"},
        duration_ms=500,
        success=False,
        error_message="API密钥达到速率限制",
        context="test_error"
    )
    
    # 测试解析失败记录
    print("5. 测试解析失败记录")
    llm_comm_logger.log_llm_parsing_attempt(
        project_id=test_project_id,
        agent_name="TestAgent",
        response_text="这不是有效的JSON格式",
        expected_schema="JsonSchema",
        validation_success=False,
        validation_error="JSONDecodeError: 期望值在第1行第1列（字符0）",
        context="test_parsing_error"
    )
    
    print(f"\n测试完成！请检查日志文件：backend/logs/{test_project_id}.log")
    
    # 检查日志文件是否创建
    log_file_path = f"logs/{test_project_id}.log"
    if os.path.exists(log_file_path):
        print(f"✅ 日志文件已创建：{log_file_path}")
        
        # 显示日志文件内容的前几行
        with open(log_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print(f"\n日志文件内容预览（共{len(lines)}行）：")
            print("=" * 50)
            for i, line in enumerate(lines[:10]):  # 只显示前10行
                print(f"{i+1:2d}: {line.rstrip()}")
            if len(lines) > 10:
                print("... （更多内容请查看完整日志文件）")
            print("=" * 50)
    else:
        print(f"❌ 日志文件未创建：{log_file_path}")

if __name__ == "__main__":
    test_llm_logging() 