#!/usr/bin/env python3
"""
文件上传API路由
支持用户上传文件并与AI聊天
"""

import os
import uuid
import shutil
from typing import List
from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import JSONResponse
from pathlib import Path
import mimetypes
import logging

from app.core.config import settings
from app.services.file_processor import FileProcessor
from app.utils.api_key_manager import key_manager

router = APIRouter(prefix="/api/upload", tags=["file-upload"])
logger = logging.getLogger(__name__)

# 支持的文件类型和大小限制
ALLOWED_MIME_TYPES = {
    # 图片
    'image/png', 'image/jpeg', 'image/webp', 'image/gif',
    # 音频
    'audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/x-aac', 'audio/flac',
    # 视频
    'video/mp4', 'video/mpeg', 'video/mov', 'video/avi', 'video/webm',
    # 文档
    'text/plain', 'text/csv', 'application/pdf',
    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
}

MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

def ensure_upload_directory():
    """确保上传目录存在"""
    upload_dir = Path(settings.PROJECT_STORAGE_PATH) / "uploads"
    upload_dir.mkdir(parents=True, exist_ok=True)
    return upload_dir

def get_file_category(mime_type: str) -> str:
    """根据MIME类型确定文件分类"""
    if mime_type.startswith('image/'):
        return 'image'
    elif mime_type.startswith('audio/'):
        return 'audio'
    elif mime_type.startswith('video/'):
        return 'video'
    else:
        return 'document'

@router.post("/file")
async def upload_file(
    file: UploadFile = File(...),
    session_id: str = Form(default=None)
):
    """
    上传文件接口
    """
    try:
        # 验证文件
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件大小
        file_content = await file.read()
        if len(file_content) > MAX_FILE_SIZE:
            raise HTTPException(status_code=400, detail=f"文件大小超过限制 ({MAX_FILE_SIZE // 1024 // 1024}MB)")
        
        # 检查文件类型
        mime_type = file.content_type or mimetypes.guess_type(file.filename)[0]
        if mime_type not in ALLOWED_MIME_TYPES:
            raise HTTPException(status_code=400, detail=f"不支持的文件类型: {mime_type}")
        
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        file_extension = Path(file.filename).suffix
        unique_filename = f"{file_id}{file_extension}"
        
        # 保存文件到本地
        upload_dir = ensure_upload_directory()
        local_file_path = upload_dir / unique_filename
        
        # 重新定位到文件开头并保存
        with open(local_file_path, 'wb') as f:
            f.write(file_content)
        
        logger.info(f"文件已保存到本地: {local_file_path}")
        
        # 使用FileProcessor上传到Gemini
        try:
            api_key = await key_manager.get_next_key_async()
            file_processor = FileProcessor(api_key)
            
            upload_result = file_processor.upload_file(str(local_file_path))
            
            if upload_result['success']:
                file_info = upload_result['file_info']
                
                # 返回文件信息
                response_data = {
                    "success": True,
                    "file_id": file_id,
                    "filename": file.filename,
                    "local_path": str(local_file_path),
                    "size": len(file_content),
                    "mime_type": mime_type,
                    "category": get_file_category(mime_type),
                    "gemini_uri": file_info['uri'],
                    "gemini_file_id": file_info['name'],
                    "session_id": session_id or str(uuid.uuid4())
                }
                
                logger.info(f"文件上传成功: {response_data}")
                return JSONResponse(content=response_data)
            
            else:
                # 清理本地文件
                if local_file_path.exists():
                    local_file_path.unlink()
                raise HTTPException(status_code=500, detail=f"文件上传到Gemini失败: {upload_result['error']}")
        
        except Exception as e:
            # 清理本地文件
            if local_file_path.exists():
                local_file_path.unlink()
            raise HTTPException(status_code=500, detail=f"文件处理失败: {str(e)}")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@router.get("/files/{file_id}")
async def get_file_info(file_id: str):
    """
    获取文件信息
    """
    try:
        upload_dir = ensure_upload_directory()
        
        # 查找文件
        for file_path in upload_dir.glob(f"{file_id}.*"):
            if file_path.is_file():
                stat = file_path.stat()
                mime_type = mimetypes.guess_type(str(file_path))[0]
                
                return JSONResponse(content={
                    "success": True,
                    "file_id": file_id,
                    "filename": file_path.name,
                    "local_path": str(file_path),
                    "size": stat.st_size,
                    "mime_type": mime_type,
                    "category": get_file_category(mime_type or ""),
                    "created_at": stat.st_ctime
                })
        
        raise HTTPException(status_code=404, detail="文件未找到")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件信息失败: {str(e)}")

@router.delete("/files/{file_id}")
async def delete_file(file_id: str):
    """
    删除文件
    """
    try:
        upload_dir = ensure_upload_directory()
        
        # 查找并删除文件
        deleted = False
        for file_path in upload_dir.glob(f"{file_id}.*"):
            if file_path.is_file():
                file_path.unlink()
                deleted = True
                logger.info(f"已删除文件: {file_path}")
        
        if deleted:
            return JSONResponse(content={"success": True, "message": "文件已删除"})
        else:
            raise HTTPException(status_code=404, detail="文件未找到")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除文件失败: {str(e)}") 