import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import LeftNav from '../components/LeftNav';
import ChatView from './ChatView';
import apiService from '../services/api';

const ChatViewPage = () => {
  const [currentChatId, setCurrentChatId] = useState(null);
  const [isTaskListOpen, setIsTaskListOpen] = useState(false);
  const [chatMessages, setChatMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const location = useLocation();
  const navigate = useNavigate();

  // 从URL获取chatId参数
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const chatId = params.get('chatId');
    
    if (chatId) {
      setCurrentChatId(chatId);
      loadChatMessages(chatId);
    }
  }, [location.search]);

  // 加载聊天消息
  const loadChatMessages = async (chatId) => {
    if (!chatId) return;
    
    setIsLoading(true);
    try {
      const details = await apiService.getProjectDetails(chatId);
      console.log(`[ChatViewPage] 加载聊天记录:`, details);
      
      if (details && details.chat_history) {
        setChatMessages(details.chat_history);
      } else {
        setChatMessages([{
          id: `system-${Date.now()}`,
          sender: 'system',
          text: `该项目"${details?.title || '未命名项目'}"已加载，但没有找到聊天记录。这可能是因为：
1. 项目是通过其他方式创建的
2. 聊天记录可能已被清除
3. 数据库中项目ID为 ${chatId} 的聊天记录表(chat_messages)可能为空`,
          timestamp: new Date().toISOString(),
          icon: 'ℹ️'
        }]);
      }
    } catch (error) {
      console.error('加载聊天记录失败:', error);
      setChatMessages([{
        id: `error-${Date.now()}`,
        sender: 'system',
        text: `加载聊天记录失败: ${error.message}`,
        timestamp: new Date().toISOString(),
        icon: '⚠️'
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectChat = (chatId) => {
    setCurrentChatId(chatId);
    console.log('选择聊天:', chatId);
    navigate(`/chat?chatId=${chatId}`);
    loadChatMessages(chatId);
  };

  const refreshChatHistory = () => {
    // 触发LeftNav组件刷新聊天历史列表
    window.dispatchEvent(new CustomEvent('refreshChatHistory'));
  };

  const toggleTaskList = () => {
    setIsTaskListOpen(!isTaskListOpen);
  };

  return (
    <div className="flex flex-1 overflow-hidden h-screen">
      <LeftNav 
        isTaskListOpen={isTaskListOpen} 
        onToggleTaskList={toggleTaskList}
        onSelectChat={handleSelectChat}
      />
      <ChatView 
        currentChatId={currentChatId} 
        initialMessages={chatMessages} 
      />
    </div>
  );
};

export default ChatViewPage; 