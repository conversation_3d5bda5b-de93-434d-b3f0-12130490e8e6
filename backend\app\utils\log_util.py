"""
日志工具模块，提供全局唯一的日志去重功能
"""
import os
import sys
import logging
from logging.handlers import RotatingFileHandler
import traceback
from functools import wraps
from datetime import datetime
import threading

# 设置日志目录
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "logs")
os.makedirs(log_dir, exist_ok=True)

# 创建一个自定义的日志格式化器，包括更多上下文信息
class DetailedFormatter(logging.Formatter):
    def format(self, record):
        # 获取当前线程ID
        thread_id = threading.get_ident()
        
        # 设置默认的格式
        if not hasattr(record, 'threadName') or not record.threadName:
            record.threadName = f"Thread-{thread_id}"
        
        # 添加进程ID
        record.process = os.getpid()
        
        # 调用原始format方法
        return super().format(record)

# 配置根日志记录器
def configure_logging():
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)  # 保持INFO级别，以便看到警告和错误

    # 清除现有的处理器，防止重复添加
    if root_logger.handlers:
        for handler in root_logger.handlers:
            root_logger.removeHandler(handler)
            
    # --- 只保留控制台处理器 ---
    console_handler = logging.StreamHandler(sys.stdout)
    # 将控制台日志级别设置为INFO，这样Warning和Error都能显示出来
    console_handler.setLevel(logging.INFO) 
    console_format = DetailedFormatter('%(asctime)s [%(levelname)s] [%(name)s:%(lineno)d] - %(message)s')
    console_handler.setFormatter(console_format)
    root_logger.addHandler(console_handler)

    return root_logger

# 初始化日志记录器
logger = configure_logging()

# 用于跟踪已经记录过的消息，避免重复
_logged_messages = set()
_logged_messages_lock = threading.Lock()

def log_once(message, level=logging.INFO, exc_info=False):
    """
    确保相同的消息只被记录一次
    
    Args:
        message: 要记录的消息
        level: 日志级别
        exc_info: 是否包含异常信息
    """
    global _logged_messages
    
    # 对于错误和异常，总是记录，不去重
    if level >= logging.ERROR or exc_info:
        logger.log(level, message, exc_info=exc_info)
        return
    
    # 对于普通日志消息，去重
    with _logged_messages_lock:
        if message not in _logged_messages:
            _logged_messages.add(message)
            logger.log(level, message)

def get_logger(name):
    """获取指定名称的日志记录器"""
    return logging.getLogger(name)

# 装饰器：记录函数调用
def log_function_call(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        func_name = func.__name__
        log_once(f"调用函数 {func_name}")
        try:
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            log_once(f"函数 {func_name} 执行失败: {str(e)}", level=logging.ERROR, exc_info=True)
            raise
    return wrapper

# 装饰器：记录异步函数调用
def log_async_function_call(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        func_name = func.__name__
        log_once(f"调用异步函数 {func_name}")
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception as e:
            log_once(f"异步函数 {func_name} 执行失败: {str(e)}", level=logging.ERROR, exc_info=True)
            raise
    return wrapper

# 日志工厂类
class LoggerFactory:
    """
    日志工厂类，提供标准的日志创建功能。
    建议直接使用Python标准的logging模块，此类仅为兼容旧代码。
    """
    
    @staticmethod
    def get_logger(name):
        """
        获取指定名称的logger
        
        Args:
            name: logger的名称，通常为__name__
            
        Returns:
            logging.Logger: 配置好的logger实例
        """
        return logging.getLogger(name)