{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\InlineTextEditor.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { FaBold, FaItalic, FaUnderline, FaPaintBrush } from 'react-icons/fa';\n// TinyColor for color picker, or use native input type=\"color\"\n// import { SketchPicker } from 'react-color'; // Example, can be heavy. Let's use native for simplicity first.\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InlineTextEditor = ({\n  initialText,\n  initialStyle,\n  onSave,\n  onCancel,\n  position\n}) => {\n  _s();\n  const [text, setText] = useState(initialText);\n  const [style, setStyle] = useState(initialStyle || {}); // e.g., { fontWeight: 'bold', color: '#000000' }\n  const editorRef = useRef(null);\n  useEffect(() => {\n    setText(initialText);\n    setStyle(initialStyle || {});\n  }, [initialText, initialStyle]);\n  useEffect(() => {\n    // Focus the textarea when editor becomes visible\n    if (editorRef.current) {\n      const textarea = editorRef.current.querySelector('textarea');\n      if (textarea) {\n        textarea.focus();\n        textarea.select();\n      }\n    }\n  }, [position]);\n  const handleStyleChange = (property, value) => {\n    setStyle(prev => ({\n      ...prev,\n      [property]: value\n    }));\n  };\n  const handleSave = () => {\n    onSave(text, style);\n  };\n  if (!position) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: editorRef,\n    className: \"absolute bg-white shadow-2xl rounded-lg p-3 z-50 border border-gray-300\",\n    style: {\n      top: position.y,\n      left: position.x,\n      minWidth: '250px'\n    },\n    onClick: e => e.stopPropagation() // Prevent click from propagating to slide elements\n    ,\n    children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n      value: text,\n      onChange: e => setText(e.target.value),\n      className: \"w-full p-2 border border-gray-200 rounded-md mb-2 focus:ring-tiktodo-blue focus:border-tiktodo-blue outline-none resize-none text-sm\",\n      rows: 3\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-1 mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        title: \"Bold\",\n        onClick: () => handleStyleChange('fontWeight', style.fontWeight === 'bold' ? 'normal' : 'bold'),\n        className: `p-1.5 rounded hover:bg-gray-200 ${style.fontWeight === 'bold' ? 'bg-gray-200' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(FaBold, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        title: \"Italic\",\n        onClick: () => handleStyleChange('fontStyle', style.fontStyle === 'italic' ? 'normal' : 'italic'),\n        className: `p-1.5 rounded hover:bg-gray-200 ${style.fontStyle === 'italic' ? 'bg-gray-200' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(FaItalic, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        title: \"Underline\",\n        onClick: () => handleStyleChange('textDecoration', style.textDecoration === 'underline' ? 'none' : 'underline'),\n        className: `p-1.5 rounded hover:bg-gray-200 ${style.textDecoration === 'underline' ? 'bg-gray-200' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(FaUnderline, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative p-1.5 rounded hover:bg-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(FaPaintBrush, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"color\",\n          value: style.color || '#000000',\n          onChange: e => handleStyleChange('color', e.target.value),\n          className: \"absolute top-0 left-0 w-full h-full opacity-0 cursor-pointer\",\n          title: \"Text Color\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-end space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onCancel,\n        className: \"px-3 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300\",\n        children: \"\\u53D6\\u6D88\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSave,\n        className: \"px-3 py-1 text-xs bg-tiktodo-blue text-white rounded hover:bg-blue-700\",\n        children: \"\\u4FDD\\u5B58\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(InlineTextEditor, \"1gTGIvXNepGa0Cnqm4EvvTCqLsQ=\");\n_c = InlineTextEditor;\nexport default InlineTextEditor;\nvar _c;\n$RefreshReg$(_c, \"InlineTextEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "FaBold", "FaItalic", "FaUnderline", "FaPaintBrush", "jsxDEV", "_jsxDEV", "InlineTextEditor", "initialText", "initialStyle", "onSave", "onCancel", "position", "_s", "text", "setText", "style", "setStyle", "editor<PERSON><PERSON>", "current", "textarea", "querySelector", "focus", "select", "handleStyleChange", "property", "value", "prev", "handleSave", "ref", "className", "top", "y", "left", "x", "min<PERSON><PERSON><PERSON>", "onClick", "e", "stopPropagation", "children", "onChange", "target", "rows", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "fontWeight", "fontStyle", "textDecoration", "type", "color", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/InlineTextEditor.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport {\r\n  FaBold, FaItalic, FaUnderline, FaPaintBrush\r\n} from 'react-icons/fa';\r\n// TinyColor for color picker, or use native input type=\"color\"\r\n// import { SketchPicker } from 'react-color'; // Example, can be heavy. Let's use native for simplicity first.\r\n\r\nconst InlineTextEditor = ({ initialText, initialStyle, onSave, onCancel, position }) => {\r\n  const [text, setText] = useState(initialText);\r\n  const [style, setStyle] = useState(initialStyle || {}); // e.g., { fontWeight: 'bold', color: '#000000' }\r\n  const editorRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    setText(initialText);\r\n    setStyle(initialStyle || {});\r\n  }, [initialText, initialStyle]);\r\n\r\n  useEffect(() => {\r\n    // Focus the textarea when editor becomes visible\r\n    if (editorRef.current) {\r\n      const textarea = editorRef.current.querySelector('textarea');\r\n      if (textarea) {\r\n        textarea.focus();\r\n        textarea.select();\r\n      }\r\n    }\r\n  }, [position]);\r\n\r\n\r\n  const handleStyleChange = (property, value) => {\r\n    setStyle(prev => ({ ...prev, [property]: value }));\r\n  };\r\n\r\n  const handleSave = () => {\r\n    onSave(text, style);\r\n  };\r\n\r\n  if (!position) return null;\r\n\r\n  return (\r\n    <div\r\n      ref={editorRef}\r\n      className=\"absolute bg-white shadow-2xl rounded-lg p-3 z-50 border border-gray-300\"\r\n      style={{ top: position.y, left: position.x, minWidth: '250px' }}\r\n      onClick={(e) => e.stopPropagation()} // Prevent click from propagating to slide elements\r\n    >\r\n      <textarea\r\n        value={text}\r\n        onChange={(e) => setText(e.target.value)}\r\n        className=\"w-full p-2 border border-gray-200 rounded-md mb-2 focus:ring-tiktodo-blue focus:border-tiktodo-blue outline-none resize-none text-sm\"\r\n        rows={3}\r\n      />\r\n      <div className=\"flex items-center space-x-1 mb-2\">\r\n        <button\r\n          title=\"Bold\"\r\n          onClick={() => handleStyleChange('fontWeight', style.fontWeight === 'bold' ? 'normal' : 'bold')}\r\n          className={`p-1.5 rounded hover:bg-gray-200 ${style.fontWeight === 'bold' ? 'bg-gray-200' : ''}`}\r\n        >\r\n          <FaBold />\r\n        </button>\r\n        <button\r\n          title=\"Italic\"\r\n          onClick={() => handleStyleChange('fontStyle', style.fontStyle === 'italic' ? 'normal' : 'italic')}\r\n          className={`p-1.5 rounded hover:bg-gray-200 ${style.fontStyle === 'italic' ? 'bg-gray-200' : ''}`}\r\n        >\r\n          <FaItalic />\r\n        </button>\r\n        <button\r\n          title=\"Underline\"\r\n          onClick={() => handleStyleChange('textDecoration', style.textDecoration === 'underline' ? 'none' : 'underline')}\r\n          className={`p-1.5 rounded hover:bg-gray-200 ${style.textDecoration === 'underline' ? 'bg-gray-200' : ''}`}\r\n        >\r\n          <FaUnderline />\r\n        </button>\r\n        {/* Basic Color Picker */}\r\n        <div className=\"relative p-1.5 rounded hover:bg-gray-200\">\r\n            <FaPaintBrush />\r\n            <input type=\"color\"\r\n                   value={style.color || '#000000'}\r\n                   onChange={(e) => handleStyleChange('color', e.target.value)}\r\n                   className=\"absolute top-0 left-0 w-full h-full opacity-0 cursor-pointer\"\r\n                   title=\"Text Color\"\r\n            />\r\n        </div>\r\n        {/* Add more controls: font size, family, alignment etc. later */}\r\n      </div>\r\n      <div className=\"flex justify-end space-x-2\">\r\n        <button\r\n          onClick={onCancel}\r\n          className=\"px-3 py-1 text-xs bg-gray-200 text-gray-700 rounded hover:bg-gray-300\"\r\n        >\r\n          取消\r\n        </button>\r\n        <button\r\n          onClick={handleSave}\r\n          className=\"px-3 py-1 text-xs bg-tiktodo-blue text-white rounded hover:bg-blue-700\"\r\n        >\r\n          保存\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InlineTextEditor; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,YAAY,QACtC,gBAAgB;AACvB;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,WAAW;EAAEC,YAAY;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAACU,WAAW,CAAC;EAC7C,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAACW,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACxD,MAAMS,SAAS,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACdgB,OAAO,CAACP,WAAW,CAAC;IACpBS,QAAQ,CAACR,YAAY,IAAI,CAAC,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACD,WAAW,EAAEC,YAAY,CAAC,CAAC;EAE/BV,SAAS,CAAC,MAAM;IACd;IACA,IAAImB,SAAS,CAACC,OAAO,EAAE;MACrB,MAAMC,QAAQ,GAAGF,SAAS,CAACC,OAAO,CAACE,aAAa,CAAC,UAAU,CAAC;MAC5D,IAAID,QAAQ,EAAE;QACZA,QAAQ,CAACE,KAAK,CAAC,CAAC;QAChBF,QAAQ,CAACG,MAAM,CAAC,CAAC;MACnB;IACF;EACF,CAAC,EAAE,CAACX,QAAQ,CAAC,CAAC;EAGd,MAAMY,iBAAiB,GAAGA,CAACC,QAAQ,EAAEC,KAAK,KAAK;IAC7CT,QAAQ,CAACU,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,QAAQ,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvBlB,MAAM,CAACI,IAAI,EAAEE,KAAK,CAAC;EACrB,CAAC;EAED,IAAI,CAACJ,QAAQ,EAAE,OAAO,IAAI;EAE1B,oBACEN,OAAA;IACEuB,GAAG,EAAEX,SAAU;IACfY,SAAS,EAAC,yEAAyE;IACnFd,KAAK,EAAE;MAAEe,GAAG,EAAEnB,QAAQ,CAACoB,CAAC;MAAEC,IAAI,EAAErB,QAAQ,CAACsB,CAAC;MAAEC,QAAQ,EAAE;IAAQ,CAAE;IAChEC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;IAAA;IAAAC,QAAA,gBAErCjC,OAAA;MACEoB,KAAK,EAAEZ,IAAK;MACZ0B,QAAQ,EAAGH,CAAC,IAAKtB,OAAO,CAACsB,CAAC,CAACI,MAAM,CAACf,KAAK,CAAE;MACzCI,SAAS,EAAC,sIAAsI;MAChJY,IAAI,EAAE;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACFxC,OAAA;MAAKwB,SAAS,EAAC,kCAAkC;MAAAS,QAAA,gBAC/CjC,OAAA;QACEyC,KAAK,EAAC,MAAM;QACZX,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAAC,YAAY,EAAER,KAAK,CAACgC,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG,MAAM,CAAE;QAChGlB,SAAS,EAAE,mCAAmCd,KAAK,CAACgC,UAAU,KAAK,MAAM,GAAG,aAAa,GAAG,EAAE,EAAG;QAAAT,QAAA,eAEjGjC,OAAA,CAACL,MAAM;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACTxC,OAAA;QACEyC,KAAK,EAAC,QAAQ;QACdX,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAAC,WAAW,EAAER,KAAK,CAACiC,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAE;QAClGnB,SAAS,EAAE,mCAAmCd,KAAK,CAACiC,SAAS,KAAK,QAAQ,GAAG,aAAa,GAAG,EAAE,EAAG;QAAAV,QAAA,eAElGjC,OAAA,CAACJ,QAAQ;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACTxC,OAAA;QACEyC,KAAK,EAAC,WAAW;QACjBX,OAAO,EAAEA,CAAA,KAAMZ,iBAAiB,CAAC,gBAAgB,EAAER,KAAK,CAACkC,cAAc,KAAK,WAAW,GAAG,MAAM,GAAG,WAAW,CAAE;QAChHpB,SAAS,EAAE,mCAAmCd,KAAK,CAACkC,cAAc,KAAK,WAAW,GAAG,aAAa,GAAG,EAAE,EAAG;QAAAX,QAAA,eAE1GjC,OAAA,CAACH,WAAW;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAETxC,OAAA;QAAKwB,SAAS,EAAC,0CAA0C;QAAAS,QAAA,gBACrDjC,OAAA,CAACF,YAAY;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChBxC,OAAA;UAAO6C,IAAI,EAAC,OAAO;UACZzB,KAAK,EAAEV,KAAK,CAACoC,KAAK,IAAI,SAAU;UAChCZ,QAAQ,EAAGH,CAAC,IAAKb,iBAAiB,CAAC,OAAO,EAAEa,CAAC,CAACI,MAAM,CAACf,KAAK,CAAE;UAC5DI,SAAS,EAAC,8DAA8D;UACxEiB,KAAK,EAAC;QAAY;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,eACNxC,OAAA;MAAKwB,SAAS,EAAC,4BAA4B;MAAAS,QAAA,gBACzCjC,OAAA;QACE8B,OAAO,EAAEzB,QAAS;QAClBmB,SAAS,EAAC,uEAAuE;QAAAS,QAAA,EAClF;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxC,OAAA;QACE8B,OAAO,EAAER,UAAW;QACpBE,SAAS,EAAC,wEAAwE;QAAAS,QAAA,EACnF;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CA/FIN,gBAAgB;AAAA8C,EAAA,GAAhB9C,gBAAgB;AAiGtB,eAAeA,gBAAgB;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}