{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\views\\\\SlidePlayerView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport PlayerToolbar from '../components/PlayerToolbar';\nimport SlideRenderer from '../components/SlideRenderer';\nimport PdfExport from '../components/PdfExport';\nimport { FaArrowLeft, FaArrowRight, FaTimes } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SlidePlayerView = () => {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    slides: slidesFromState = [],\n    initialIndex = 0,\n    presentationTitle = \"演示文稿\"\n  } = location.state || {};\n  const [slides, setSlides] = useState(slidesFromState);\n  const [isExporting, setIsExporting] = useState(false);\n  const [exportError, setExportError] = useState('');\n  const [currentSlideIndex, setCurrentSlideIndex] = useState(initialIndex);\n  const pdfExportRef = useRef(null);\n  const slidesDisplayAreaRef = useRef(null);\n  useEffect(() => {\n    if (!slidesFromState || slidesFromState.length === 0) {\n      // console.warn(\"SlidePlayerView: No slides provided.\");\n      // 可以考虑如果 slides 为空则导航回主页或显示错误信息\n      navigate('/', {\n        replace: true\n      });\n    }\n    setSlides(slidesFromState);\n\n    // Set title based on presentation title\n    document.title = presentationTitle || \"演示文稿\";\n  }, [slidesFromState, presentationTitle, navigate]);\n  const handlePrevSlide = useCallback(() => {\n    setCurrentSlideIndex(prev => Math.max(0, prev - 1));\n  }, []);\n  const handleNextSlide = useCallback(() => {\n    setCurrentSlideIndex(prev => Math.min(slides.length - 1, prev + 1));\n  }, [slides]);\n  const handleExitPresentation = () => {\n    navigate('/');\n  };\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if (e.key === 'ArrowRight' || e.key === 'PageDown' || e.key === ' ') {\n        handleNextSlide();\n      } else if (e.key === 'ArrowLeft' || e.key === 'PageUp') {\n        handlePrevSlide();\n      } else if (e.key === 'Escape') {\n        handleExitPresentation();\n      }\n    };\n    window.addEventListener('keydown', handleKeyDown);\n    return () => {\n      window.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [handleNextSlide, handlePrevSlide]);\n\n  // 请求全屏模式（在用户首次交互后）\n  const requestFullScreen = () => {\n    // 发送消息给父窗口，请求全屏\n    window.parent.postMessage({\n      type: 'request-fullscreen',\n      payload: {\n        initialIndex: currentSlideIndex\n      }\n    }, '*');\n  };\n  const handleExport = async formatKey => {\n    if (!slides || slides.length === 0) {\n      alert(\"没有可导出的幻灯片。\");\n      return;\n    }\n    setIsExporting(true);\n    setExportError('');\n    if (formatKey === 'pdf' && pdfExportRef.current) {\n      try {\n        // 假设 exportToPdf 接受 slides 数组\n        await pdfExportRef.current.exportToPdf(slides);\n        // 成功消息可以通过 UI 提示或 toast 显示\n        alert('PDF 导出成功。');\n      } catch (error) {\n        console.error(\"PDF导出失败:\", error);\n        setExportError('PDF 导出失败，请稍后再试。');\n        alert('PDF 导出失败。' + (error.message || ''));\n      } finally {\n        setIsExporting(false);\n      }\n    } else if (formatKey === 'pptx') {\n      try {\n        alert('PPTX 导出功能暂未实现。');\n      } finally {\n        setIsExporting(false);\n      }\n    }\n  };\n  const handlePublish = actionKey => {\n    if (actionKey === 'copyLink') {\n      try {\n        var _location$state;\n        // 理想情况下，这里应该生成一个指向此演示文稿的唯一、持久的链接\n        // 为简单起见，我们复制当前查看器页面的链接（如果适用）或一个占位符\n        const shareableLink = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.shareLink) || window.location.origin + `/share/presentation/${presentationTitle.replace(/\\s+/g, '-')}`; // 示例链接\n        navigator.clipboard.writeText(shareableLink).then(() => alert('链接已复制到剪贴板: ' + shareableLink)).catch(err => {\n          console.error(\"复制链接失败:\", err);\n          alert('复制链接失败。' + (err.message || ''));\n        });\n      } catch (error) {\n        console.error(\"分享链接处理失败:\", error);\n        alert('处理分享链接时出错。');\n      }\n    }\n  };\n  const handleMoreOptions = actionKey => {\n    if (actionKey === 'editCanva') {\n      alert('在Canva中编辑功能暂未实现。');\n    }\n  };\n  if (!slides || slides.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col h-screen bg-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(PlayerToolbar, {\n        fileName: presentationTitle,\n        totalPages: 0,\n        onPlay: () => {},\n        onExitPlayer: () => navigate(\"/\"),\n        onExportSelect: () => {},\n        onPublishSelect: () => {},\n        onMoreOptionsSelect: () => {}\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex items-center justify-center text-gray-600\",\n        children: \"\\u6CA1\\u6709\\u5E7B\\u706F\\u7247\\u53EF\\u4F9B\\u67E5\\u770B\\u6216\\u5BFC\\u51FA\\u3002\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this);\n  }\n  const currentSlide = slides[currentSlideIndex];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full overflow-hidden bg-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(PlayerToolbar, {\n      fileName: presentationTitle,\n      totalPages: slides.length,\n      currentPage: currentSlideIndex + 1,\n      onPlay: requestFullScreen,\n      onExitPlayer: handleExitPresentation,\n      onExportSelect: handleExport,\n      onPublishSelect: handlePublish,\n      onMoreOptionsSelect: handleMoreOptions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), isExporting && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"export-notification\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full mr-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u6B63\\u5728\\u5BFC\\u51FA\\uFF0C\\u8BF7\\u7A0D\\u5019...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }, this), exportError && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-x-0 top-16 flex justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 text-red-700 px-4 py-2 rounded-md shadow flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"mr-2\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: exportError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"ml-2 text-red-500 hover:text-red-700\",\n          onClick: () => setExportError(''),\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: slidesDisplayAreaRef,\n      className: \"slides-display-area flex-1 custom-scrollbar\",\n      children: slides.map((slide, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"slide-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-2 left-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-md z-10\",\n          children: [index + 1, \" / \", slides.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full\",\n          children: /*#__PURE__*/_jsxDEV(SlideRenderer, {\n            slideId: slide.id || `player-slide-render-${index}`,\n            slideFullHtml: slide.html,\n            isAppEditingMode: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this)]\n      }, slide.id || `player-slide-item-${index}`, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PdfExport, {\n      ref: pdfExportRef,\n      filename: presentationTitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(SlidePlayerView, \"PF8oh//2YcwVN+5p76ap9a9YVSg=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = SlidePlayerView;\nexport default SlidePlayerView;\nvar _c;\n$RefreshReg$(_c, \"SlidePlayerView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "useLocation", "useNavigate", "PlayerToolbar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PdfExport", "FaArrowLeft", "FaArrowRight", "FaTimes", "jsxDEV", "_jsxDEV", "SlidePlayerView", "_s", "location", "navigate", "slides", "slidesFromState", "initialIndex", "presentationTitle", "state", "setSlides", "isExporting", "setIsExporting", "exportError", "setExportError", "currentSlideIndex", "setCurrentSlideIndex", "pdfExportRef", "slidesDisplayAreaRef", "length", "replace", "document", "title", "handlePrevSlide", "prev", "Math", "max", "handleNextSlide", "min", "handleExitPresentation", "handleKeyDown", "e", "key", "window", "addEventListener", "removeEventListener", "requestFullScreen", "parent", "postMessage", "type", "payload", "handleExport", "formatKey", "alert", "current", "exportToPdf", "error", "console", "message", "handlePublish", "action<PERSON>ey", "_location$state", "shareableLink", "shareLink", "origin", "navigator", "clipboard", "writeText", "then", "catch", "err", "handleMoreOptions", "className", "children", "fileName", "totalPages", "onPlay", "onExitPlayer", "onExportSelect", "onPublishSelect", "onMoreOptionsSelect", "_jsxFileName", "lineNumber", "columnNumber", "currentSlide", "currentPage", "onClick", "ref", "map", "slide", "index", "slideId", "id", "slideFullHtml", "html", "isAppEditingMode", "filename", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/views/SlidePlayerView.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport PlayerToolbar from '../components/PlayerToolbar';\nimport SlideRenderer from '../components/SlideRenderer';\nimport PdfExport from '../components/PdfExport';\nimport { FaArrowLeft, FaArrowRight, FaTimes } from 'react-icons/fa';\n\nconst SlidePlayerView = () => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  \n  const { slides: slidesFromState = [], initialIndex = 0, presentationTitle = \"演示文稿\" } = location.state || {};\n  \n  const [slides, setSlides] = useState(slidesFromState);\n  const [isExporting, setIsExporting] = useState(false);\n  const [exportError, setExportError] = useState('');\n  const [currentSlideIndex, setCurrentSlideIndex] = useState(initialIndex);\n\n  const pdfExportRef = useRef(null);\n  const slidesDisplayAreaRef = useRef(null);\n\n  useEffect(() => {\n    if (!slidesFromState || slidesFromState.length === 0) {\n      // console.warn(\"SlidePlayerView: No slides provided.\");\n      // 可以考虑如果 slides 为空则导航回主页或显示错误信息\n      navigate('/', { replace: true });\n    }\n    setSlides(slidesFromState);\n    \n    // Set title based on presentation title\n    document.title = presentationTitle || \"演示文稿\";\n  }, [slidesFromState, presentationTitle, navigate]);\n  \n  const handlePrevSlide = useCallback(() => {\n    setCurrentSlideIndex(prev => Math.max(0, prev - 1));\n  }, []);\n    \n  const handleNextSlide = useCallback(() => {\n    setCurrentSlideIndex(prev => Math.min(slides.length - 1, prev + 1));\n  }, [slides]);\n\n  const handleExitPresentation = () => {\n    navigate('/');\n  };\n\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      if (e.key === 'ArrowRight' || e.key === 'PageDown' || e.key === ' ') {\n        handleNextSlide();\n      } else if (e.key === 'ArrowLeft' || e.key === 'PageUp') {\n        handlePrevSlide();\n      } else if (e.key === 'Escape') {\n        handleExitPresentation();\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => {\n      window.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [handleNextSlide, handlePrevSlide]);\n\n  // 请求全屏模式（在用户首次交互后）\n  const requestFullScreen = () => {\n    // 发送消息给父窗口，请求全屏\n    window.parent.postMessage({\n      type: 'request-fullscreen',\n      payload: { initialIndex: currentSlideIndex }\n    }, '*');\n  };\n  \n  const handleExport = async (formatKey) => {\n    if (!slides || slides.length === 0) {\n        alert(\"没有可导出的幻灯片。\");\n        return;\n    }\n    \n    setIsExporting(true);\n    setExportError('');\n    \n    if (formatKey === 'pdf' && pdfExportRef.current) {\n      try {\n        // 假设 exportToPdf 接受 slides 数组\n        await pdfExportRef.current.exportToPdf(slides);\n        // 成功消息可以通过 UI 提示或 toast 显示\n        alert('PDF 导出成功。');\n      } catch (error) {\n        console.error(\"PDF导出失败:\", error);\n        setExportError('PDF 导出失败，请稍后再试。');\n        alert('PDF 导出失败。' + (error.message || ''));\n      } finally {\n        setIsExporting(false);\n      }\n    } else if (formatKey === 'pptx') {\n      try {\n        alert('PPTX 导出功能暂未实现。');\n      } finally {\n        setIsExporting(false);\n      }\n    }\n  };\n  \n  const handlePublish = (actionKey) => {\n    if (actionKey === 'copyLink') {\n      try {\n        // 理想情况下，这里应该生成一个指向此演示文稿的唯一、持久的链接\n        // 为简单起见，我们复制当前查看器页面的链接（如果适用）或一个占位符\n        const shareableLink = location.state?.shareLink || window.location.origin + `/share/presentation/${presentationTitle.replace(/\\s+/g, '-')}`; // 示例链接\n        navigator.clipboard.writeText(shareableLink)\n          .then(() => alert('链接已复制到剪贴板: ' + shareableLink))\n          .catch(err => {\n            console.error(\"复制链接失败:\", err);\n            alert('复制链接失败。' + (err.message || ''));\n          });\n      } catch (error) {\n        console.error(\"分享链接处理失败:\", error);\n        alert('处理分享链接时出错。');\n      }\n    }\n  };\n  \n  const handleMoreOptions = (actionKey) => {\n    if (actionKey === 'editCanva') {\n      alert('在Canva中编辑功能暂未实现。');\n    }\n  };\n\n  if (!slides || slides.length === 0) {\n    return (\n      <div className=\"flex flex-col h-screen bg-gray-200\">\n        <PlayerToolbar\n            fileName={presentationTitle}\n            totalPages={0}\n            onPlay={() => {}}\n            onExitPlayer={() => navigate(\"/\")}\n            onExportSelect={() => {}}\n            onPublishSelect={() => {}}\n            onMoreOptionsSelect={() => {}}\n      />\n        <div className=\"flex-1 flex items-center justify-center text-gray-600\">\n            没有幻灯片可供查看或导出。\n        </div>\n      </div>\n    );\n  }\n\n  const currentSlide = slides[currentSlideIndex];\n\n  return (\n    <div className=\"flex flex-col h-full overflow-hidden bg-gray-100\">\n      <PlayerToolbar\n        fileName={presentationTitle}\n        totalPages={slides.length}\n        currentPage={currentSlideIndex + 1}\n        onPlay={requestFullScreen}\n        onExitPlayer={handleExitPresentation}\n        onExportSelect={handleExport}\n        onPublishSelect={handlePublish}\n        onMoreOptionsSelect={handleMoreOptions}\n      />\n      \n      {/* 导出状态提示 */}\n      {isExporting && (\n        <div className=\"export-notification\">\n          <div className=\"animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full mr-2\"></div>\n          <span>正在导出，请稍候...</span>\n        </div>\n      )}\n      \n      {/* 错误提示 */}\n      {exportError && (\n        <div className=\"fixed inset-x-0 top-16 flex justify-center z-50\">\n          <div className=\"bg-red-50 text-red-700 px-4 py-2 rounded-md shadow flex items-center\">\n            <span className=\"mr-2\">⚠️</span>\n            <span>{exportError}</span>\n            <button \n              className=\"ml-2 text-red-500 hover:text-red-700\" \n              onClick={() => setExportError('')}\n            >\n              ×\n            </button>\n          </div>\n        </div>\n      )}\n      \n      {/* Scrollable area for all slides - styled like viewer.html */}\n      <div \n        ref={slidesDisplayAreaRef}\n        className=\"slides-display-area flex-1 custom-scrollbar\"\n      >\n        {slides.map((slide, index) => (\n          <div \n            key={slide.id || `player-slide-item-${index}`} \n            className=\"slide-wrapper\"\n          >\n            {/* Page number indicator */}\n            <div className=\"absolute top-2 left-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-md z-10\">\n              {index + 1} / {slides.length}\n            </div>\n            {/* SlideRenderer container */}\n            <div className=\"w-full\">\n              <SlideRenderer\n                slideId={slide.id || `player-slide-render-${index}`}\n                slideFullHtml={slide.html}\n                isAppEditingMode={false}\n              />\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      <PdfExport ref={pdfExportRef} filename={presentationTitle} />\n    </div>\n  );\n};\n\nexport default SlidePlayerView; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvE,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,SAASC,WAAW,EAAEC,YAAY,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAEa,MAAM,EAAEC,eAAe,GAAG,EAAE;IAAEC,YAAY,GAAG,CAAC;IAAEC,iBAAiB,GAAG;EAAO,CAAC,GAAGL,QAAQ,CAACM,KAAK,IAAI,CAAC,CAAC;EAE3G,MAAM,CAACJ,MAAM,EAAEK,SAAS,CAAC,GAAGvB,QAAQ,CAACmB,eAAe,CAAC;EACrD,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC4B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAACoB,YAAY,CAAC;EAExE,MAAMU,YAAY,GAAG3B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM4B,oBAAoB,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAEzCF,SAAS,CAAC,MAAM;IACd,IAAI,CAACkB,eAAe,IAAIA,eAAe,CAACa,MAAM,KAAK,CAAC,EAAE;MACpD;MACA;MACAf,QAAQ,CAAC,GAAG,EAAE;QAAEgB,OAAO,EAAE;MAAK,CAAC,CAAC;IAClC;IACAV,SAAS,CAACJ,eAAe,CAAC;;IAE1B;IACAe,QAAQ,CAACC,KAAK,GAAGd,iBAAiB,IAAI,MAAM;EAC9C,CAAC,EAAE,CAACF,eAAe,EAAEE,iBAAiB,EAAEJ,QAAQ,CAAC,CAAC;EAElD,MAAMmB,eAAe,GAAGlC,WAAW,CAAC,MAAM;IACxC2B,oBAAoB,CAACQ,IAAI,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,IAAI,GAAG,CAAC,CAAC,CAAC;EACrD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,eAAe,GAAGtC,WAAW,CAAC,MAAM;IACxC2B,oBAAoB,CAACQ,IAAI,IAAIC,IAAI,CAACG,GAAG,CAACvB,MAAM,CAACc,MAAM,GAAG,CAAC,EAAEK,IAAI,GAAG,CAAC,CAAC,CAAC;EACrE,CAAC,EAAE,CAACnB,MAAM,CAAC,CAAC;EAEZ,MAAMwB,sBAAsB,GAAGA,CAAA,KAAM;IACnCzB,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAEDhB,SAAS,CAAC,MAAM;IACd,MAAM0C,aAAa,GAAIC,CAAC,IAAK;MAC3B,IAAIA,CAAC,CAACC,GAAG,KAAK,YAAY,IAAID,CAAC,CAACC,GAAG,KAAK,UAAU,IAAID,CAAC,CAACC,GAAG,KAAK,GAAG,EAAE;QACnEL,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM,IAAII,CAAC,CAACC,GAAG,KAAK,WAAW,IAAID,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;QACtDT,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM,IAAIQ,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;QAC7BH,sBAAsB,CAAC,CAAC;MAC1B;IACF,CAAC;IAEDI,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,aAAa,CAAC;IACjD,OAAO,MAAM;MACXG,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEL,aAAa,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAACH,eAAe,EAAEJ,eAAe,CAAC,CAAC;;EAEtC;EACA,MAAMa,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAH,MAAM,CAACI,MAAM,CAACC,WAAW,CAAC;MACxBC,IAAI,EAAE,oBAAoB;MAC1BC,OAAO,EAAE;QAAEjC,YAAY,EAAEQ;MAAkB;IAC7C,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAM0B,YAAY,GAAG,MAAOC,SAAS,IAAK;IACxC,IAAI,CAACrC,MAAM,IAAIA,MAAM,CAACc,MAAM,KAAK,CAAC,EAAE;MAChCwB,KAAK,CAAC,YAAY,CAAC;MACnB;IACJ;IAEA/B,cAAc,CAAC,IAAI,CAAC;IACpBE,cAAc,CAAC,EAAE,CAAC;IAElB,IAAI4B,SAAS,KAAK,KAAK,IAAIzB,YAAY,CAAC2B,OAAO,EAAE;MAC/C,IAAI;QACF;QACA,MAAM3B,YAAY,CAAC2B,OAAO,CAACC,WAAW,CAACxC,MAAM,CAAC;QAC9C;QACAsC,KAAK,CAAC,WAAW,CAAC;MACpB,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAChChC,cAAc,CAAC,iBAAiB,CAAC;QACjC6B,KAAK,CAAC,WAAW,IAAIG,KAAK,CAACE,OAAO,IAAI,EAAE,CAAC,CAAC;MAC5C,CAAC,SAAS;QACRpC,cAAc,CAAC,KAAK,CAAC;MACvB;IACF,CAAC,MAAM,IAAI8B,SAAS,KAAK,MAAM,EAAE;MAC/B,IAAI;QACFC,KAAK,CAAC,gBAAgB,CAAC;MACzB,CAAC,SAAS;QACR/B,cAAc,CAAC,KAAK,CAAC;MACvB;IACF;EACF,CAAC;EAED,MAAMqC,aAAa,GAAIC,SAAS,IAAK;IACnC,IAAIA,SAAS,KAAK,UAAU,EAAE;MAC5B,IAAI;QAAA,IAAAC,eAAA;QACF;QACA;QACA,MAAMC,aAAa,GAAG,EAAAD,eAAA,GAAAhD,QAAQ,CAACM,KAAK,cAAA0C,eAAA,uBAAdA,eAAA,CAAgBE,SAAS,KAAIpB,MAAM,CAAC9B,QAAQ,CAACmD,MAAM,GAAG,uBAAuB9C,iBAAiB,CAACY,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QAC7ImC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACL,aAAa,CAAC,CACzCM,IAAI,CAAC,MAAMf,KAAK,CAAC,aAAa,GAAGS,aAAa,CAAC,CAAC,CAChDO,KAAK,CAACC,GAAG,IAAI;UACZb,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEc,GAAG,CAAC;UAC7BjB,KAAK,CAAC,SAAS,IAAIiB,GAAG,CAACZ,OAAO,IAAI,EAAE,CAAC,CAAC;QACxC,CAAC,CAAC;MACN,CAAC,CAAC,OAAOF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCH,KAAK,CAAC,YAAY,CAAC;MACrB;IACF;EACF,CAAC;EAED,MAAMkB,iBAAiB,GAAIX,SAAS,IAAK;IACvC,IAAIA,SAAS,KAAK,WAAW,EAAE;MAC7BP,KAAK,CAAC,kBAAkB,CAAC;IAC3B;EACF,CAAC;EAED,IAAI,CAACtC,MAAM,IAAIA,MAAM,CAACc,MAAM,KAAK,CAAC,EAAE;IAClC,oBACEnB,OAAA;MAAK8D,SAAS,EAAC,oCAAoC;MAAAC,QAAA,gBACjD/D,OAAA,CAACP,aAAa;QACVuE,QAAQ,EAAExD,iBAAkB;QAC5ByD,UAAU,EAAE,CAAE;QACdC,MAAM,EAAEA,CAAA,KAAM,CAAC,CAAE;QACjBC,YAAY,EAAEA,CAAA,KAAM/D,QAAQ,CAAC,GAAG,CAAE;QAClCgE,cAAc,EAAEA,CAAA,KAAM,CAAC,CAAE;QACzBC,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;QAC1BC,mBAAmB,EAAEA,CAAA,KAAM,CAAC;MAAE;QAAAN,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACAzE,OAAA;QAAK8D,SAAS,EAAC,uDAAuD;QAAAC,QAAA,EAAC;MAEvE;QAAAC,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMC,YAAY,GAAGrE,MAAM,CAACU,iBAAiB,CAAC;EAE9C,oBACEf,OAAA;IAAK8D,SAAS,EAAC,kDAAkD;IAAAC,QAAA,gBAC/D/D,OAAA,CAACP,aAAa;MACZuE,QAAQ,EAAExD,iBAAkB;MAC5ByD,UAAU,EAAE5D,MAAM,CAACc,MAAO;MAC1BwD,WAAW,EAAE5D,iBAAiB,GAAG,CAAE;MACnCmD,MAAM,EAAE9B,iBAAkB;MAC1B+B,YAAY,EAAEtC,sBAAuB;MACrCuC,cAAc,EAAE3B,YAAa;MAC7B4B,eAAe,EAAEpB,aAAc;MAC/BqB,mBAAmB,EAAET;IAAkB;MAAAG,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,EAGD9D,WAAW,iBACVX,OAAA;MAAK8D,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC/D,OAAA;QAAK8D,SAAS,EAAC;MAAsF;QAAAE,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5GzE,OAAA;QAAA+D,QAAA,EAAM;MAAW;QAAAC,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACN,EAGA5D,WAAW,iBACVb,OAAA;MAAK8D,SAAS,EAAC,iDAAiD;MAAAC,QAAA,eAC9D/D,OAAA;QAAK8D,SAAS,EAAC,sEAAsE;QAAAC,QAAA,gBACnF/D,OAAA;UAAM8D,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChCzE,OAAA;UAAA+D,QAAA,EAAOlD;QAAW;UAAAmD,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1BzE,OAAA;UACE8D,SAAS,EAAC,sCAAsC;UAChDc,OAAO,EAAEA,CAAA,KAAM9D,cAAc,CAAC,EAAE,CAAE;UAAAiD,QAAA,EACnC;QAED;UAAAC,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAT,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDzE,OAAA;MACE6E,GAAG,EAAE3D,oBAAqB;MAC1B4C,SAAS,EAAC,6CAA6C;MAAAC,QAAA,EAEtD1D,MAAM,CAACyE,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvBhF,OAAA;QAEE8D,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAGzB/D,OAAA;UAAK8D,SAAS,EAAC,2FAA2F;UAAAC,QAAA,GACvGiB,KAAK,GAAG,CAAC,EAAC,KAAG,EAAC3E,MAAM,CAACc,MAAM;QAAA;UAAA6C,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAENzE,OAAA;UAAK8D,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACrB/D,OAAA,CAACN,aAAa;YACZuF,OAAO,EAAEF,KAAK,CAACG,EAAE,IAAI,uBAAuBF,KAAK,EAAG;YACpDG,aAAa,EAAEJ,KAAK,CAACK,IAAK;YAC1BC,gBAAgB,EAAE;UAAM;YAAArB,QAAA,EAAAO,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAT,QAAA,EAAAO,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GAdDM,KAAK,CAACG,EAAE,IAAI,qBAAqBF,KAAK,EAAE;QAAAhB,QAAA,EAAAO,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAe1C,CACN;IAAC;MAAAT,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENzE,OAAA,CAACL,SAAS;MAACkF,GAAG,EAAE5D,YAAa;MAACqE,QAAQ,EAAE9E;IAAkB;MAAAwD,QAAA,EAAAO,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAT,QAAA,EAAAO,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1D,CAAC;AAEV,CAAC;AAACvE,EAAA,CA/MID,eAAe;EAAA,QACFV,WAAW,EACXC,WAAW;AAAA;AAAA+F,EAAA,GAFxBtF,eAAe;AAiNrB,eAAeA,eAAe;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}