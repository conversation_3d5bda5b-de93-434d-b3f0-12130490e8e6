{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport { StagewiseToolbar } from '@stagewise/toolbar-react';\n\n// Main app root\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 10,\n  columnNumber: 3\n}, this));\n\n// Stagewise toolbar configuration\nconst stagewiseConfig = {\n  plugins: []\n};\n\n// Only initialize toolbar in development mode\nif (process.env.NODE_ENV === 'development') {\n  // Create a dedicated container for the toolbar\n  const toolbarContainer = document.createElement('div');\n  toolbarContainer.id = 'stagewise-toolbar-root';\n  document.body.appendChild(toolbarContainer);\n\n  // Initialize toolbar in a separate React root\n  const toolbarRoot = ReactDOM.createRoot(toolbarContainer);\n  toolbarRoot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n    children: /*#__PURE__*/_jsxDEV(StagewiseToolbar, {\n      config: stagewiseConfig\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this));\n}", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "StagewiseToolbar", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stagewiseConfig", "plugins", "process", "env", "NODE_ENV", "toolbarContainer", "createElement", "id", "body", "append<PERSON><PERSON><PERSON>", "toolbarRoot", "config"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\r\nimport ReactDOM from 'react-dom/client';\r\nimport './index.css';\r\nimport App from './App';\r\nimport { StagewiseToolbar } from '@stagewise/toolbar-react';\r\n\r\n// Main app root\r\nconst root = ReactDOM.createRoot(document.getElementById('root'));\r\nroot.render(\r\n  <React.StrictMode>\r\n    <App />\r\n  </React.StrictMode>\r\n);\r\n\r\n// Stagewise toolbar configuration\r\nconst stagewiseConfig = {\r\n  plugins: []\r\n};\r\n\r\n// Only initialize toolbar in development mode\r\nif (process.env.NODE_ENV === 'development') {\r\n  // Create a dedicated container for the toolbar\r\n  const toolbarContainer = document.createElement('div');\r\n  toolbarContainer.id = 'stagewise-toolbar-root';\r\n  document.body.appendChild(toolbarContainer);\r\n\r\n  // Initialize toolbar in a separate React root\r\n  const toolbarRoot = ReactDOM.createRoot(toolbarContainer);\r\n  toolbarRoot.render(\r\n    <React.StrictMode>\r\n      <StagewiseToolbar config={stagewiseConfig} />\r\n    </React.StrictMode>\r\n  );\r\n} "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,gBAAgB,QAAQ,0BAA0B;;AAE3D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,IAAI,GAAGL,QAAQ,CAACM,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACTL,OAAA,CAACL,KAAK,CAACW,UAAU;EAAAC,QAAA,eACfP,OAAA,CAACH,GAAG;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACS,CACpB,CAAC;;AAED;AACA,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE;AACX,CAAC;;AAED;AACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EAC1C;EACA,MAAMC,gBAAgB,GAAGd,QAAQ,CAACe,aAAa,CAAC,KAAK,CAAC;EACtDD,gBAAgB,CAACE,EAAE,GAAG,wBAAwB;EAC9ChB,QAAQ,CAACiB,IAAI,CAACC,WAAW,CAACJ,gBAAgB,CAAC;;EAE3C;EACA,MAAMK,WAAW,GAAG1B,QAAQ,CAACM,UAAU,CAACe,gBAAgB,CAAC;EACzDK,WAAW,CAACjB,MAAM,cAChBL,OAAA,CAACL,KAAK,CAACW,UAAU;IAAAC,QAAA,eACfP,OAAA,CAACF,gBAAgB;MAACyB,MAAM,EAAEX;IAAgB;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CACpB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}