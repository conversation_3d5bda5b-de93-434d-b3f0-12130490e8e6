#!/usr/bin/env python3
"""
最终修复验证测试脚本 - 验证SlideElementSchema和DetailedSlideBlueprintSchema的Gemini API兼容性
"""

import json
import sys
import os
import instructor
from instructor.utils import map_to_gemini_function_schema
from google.generativeai.types import FunctionSchema

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.models.presentation_model import (
    SlideElementSchema,
    DetailedSlideBlueprintSchema
)

def test_slide_element_schema():
    """测试SlideElementSchema的Gemini API兼容性"""
    print("=== 测试 SlideElementSchema ===")
    
    try:
        # 生成JSON Schema
        schema = SlideElementSchema.model_json_schema()
        print("✅ SlideElementSchema JSON Schema生成成功")
        
        # 检查是否存在anyOf或additionalProperties
        schema_str = json.dumps(schema)
        if 'anyOf' in schema_str:
            print("❌ SlideElementSchema包含anyOf结构")
            return False
        else:
            print("✅ SlideElementSchema不包含anyOf结构")
            
        # 测试Gemini function schema转换
        function_schema = map_to_gemini_function_schema(schema)
        print("✅ SlideElementSchema成功转换为Gemini function schema")
        
        # 验证FunctionSchema
        fs = FunctionSchema(**schema)
        print("✅ SlideElementSchema通过FunctionSchema验证")
        
        return True
        
    except Exception as e:
        print(f"❌ SlideElementSchema测试失败: {e}")
        return False

def test_detailed_slide_blueprint_schema():
    """测试DetailedSlideBlueprintSchema的Gemini API兼容性"""
    print("\n=== 测试 DetailedSlideBlueprintSchema ===")
    
    try:
        # 生成JSON Schema
        schema = DetailedSlideBlueprintSchema.model_json_schema()
        print("✅ DetailedSlideBlueprintSchema JSON Schema生成成功")
        
        # 检查是否存在anyOf或additionalProperties
        schema_str = json.dumps(schema)
        if 'anyOf' in schema_str:
            print("❌ DetailedSlideBlueprintSchema包含anyOf结构")
            return False
        else:
            print("✅ DetailedSlideBlueprintSchema不包含anyOf结构")
            
        # 测试Gemini function schema转换
        function_schema = map_to_gemini_function_schema(schema)
        print("✅ DetailedSlideBlueprintSchema成功转换为Gemini function schema")
        
        # 验证FunctionSchema
        fs = FunctionSchema(**schema)
        print("✅ DetailedSlideBlueprintSchema通过FunctionSchema验证")
        
        return True
        
    except Exception as e:
        print(f"❌ DetailedSlideBlueprintSchema测试失败: {e}")
        return False

def test_slide_element_creation():
    """测试SlideElementSchema实例创建"""
    print("\n=== 测试 SlideElementSchema 实例创建 ===")
    
    try:
        # 测试标题元素
        title_element = SlideElementSchema(
            type="title",
            content="珍珠港事件",
            target_area="title_area"
        )
        print("✅ 标题元素创建成功")
        
        # 测试KPI卡片元素
        kpi_element = SlideElementSchema(
            type="kpi_card",
            content="战舰损失",
            target_area="kpi_area",
            kpi_value="8艘",
            kpi_label="战舰沉没",
            kpi_trend="up"
        )
        print("✅ KPI卡片元素创建成功")
        
        # 测试图表元素
        chart_element = SlideElementSchema(
            type="chart",
            content="损失统计",
            target_area="chart_area",
            chart_type="bar",
            chart_data='{"labels": ["战舰", "飞机"], "datasets": [{"data": [8, 188]}]}'
        )
        print("✅ 图表元素创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ SlideElementSchema实例创建失败: {e}")
        return False

def test_detailed_blueprint_creation():
    """测试DetailedSlideBlueprintSchema实例创建"""
    print("\n=== 测试 DetailedSlideBlueprintSchema 实例创建 ===")
    
    try:
        # 创建幻灯片元素
        elements = [
            SlideElementSchema(
                type="title",
                content="珍珠港事件背景",
                target_area="title_area"
            ),
            SlideElementSchema(
                type="paragraph",
                content="1941年12月7日，日本海军偷袭美国珍珠港海军基地，成为太平洋战争的导火索。",
                target_area="content_area"
            )
        ]
        
        # 创建详细蓝图
        blueprint = DetailedSlideBlueprintSchema(
            slide_number=1,
            layout_template_name="ContentSlideLayout",
            background_style_description="linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)",
            key_elements=elements,
            speaker_notes="这张幻灯片介绍了珍珠港事件的历史背景。演讲者应该强调这个事件对于美国参与二战的重要性。"
        )
        print("✅ DetailedSlideBlueprintSchema实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ DetailedSlideBlueprintSchema实例创建失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始最终修复验证测试...\n")
    
    tests = [
        test_slide_element_schema,
        test_detailed_slide_blueprint_schema,
        test_slide_element_creation,
        test_detailed_blueprint_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！Gemini API兼容性问题已解决！")
        return True
    else:
        print("❌ 仍有测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 