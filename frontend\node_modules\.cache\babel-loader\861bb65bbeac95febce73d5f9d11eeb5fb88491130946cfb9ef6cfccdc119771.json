{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\FullScreenPlayer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport Slide<PERSON>enderer from './SlideRenderer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FullScreenPlayer = ({\n  slides,\n  initialIndex = 0,\n  onClose\n}) => {\n  _s();\n  const [currentIndex, setCurrentIndex] = useState(initialIndex);\n  const [controlsVisible, setControlsVisible] = useState(true);\n  const controlsTimerRef = useRef(null);\n  const playerRef = useRef(null); // 【新增】为最外层div创建一个ref\n\n  // 重置控制按钮隐藏计时器\n  const resetControlsTimer = useCallback(() => {\n    if (controlsTimerRef.current) clearTimeout(controlsTimerRef.current);\n    controlsTimerRef.current = setTimeout(() => setControlsVisible(false), 2000);\n  }, []);\n\n  // 导航到下一张\n  const goToNextSlide = useCallback(() => {\n    setCurrentIndex(prev => prev < slides.length - 1 ? prev + 1 : prev);\n    setControlsVisible(true);\n    resetControlsTimer();\n  }, [slides.length, resetControlsTimer]);\n\n  // 导航到上一张\n  const goToPrevSlide = useCallback(() => {\n    setCurrentIndex(prev => prev > 0 ? prev - 1 : prev);\n    setControlsVisible(true);\n    resetControlsTimer();\n  }, [resetControlsTimer]);\n\n  // 监听鼠标移动以显示控件\n  const handleMouseMove = useCallback(() => {\n    setControlsVisible(true);\n    resetControlsTimer();\n  }, [resetControlsTimer]);\n\n  // 【新增】useEffect 来处理来自 iframe 的消息\n  useEffect(() => {\n    const handleMessage = event => {\n      // 确保消息来源是我们期望的，并且类型正确\n      if (event.data && event.data.type === 'iframe_keydown') {\n        const key = event.data.key;\n        if (key === 'ArrowRight' || key === 'ArrowDown' || key === 'PageDown' || key === ' ') {\n          goToNextSlide();\n        } else if (key === 'ArrowLeft' || key === 'ArrowUp' || key === 'PageUp') {\n          goToPrevSlide();\n        } else if (key === 'Escape') {\n          onClose(); // 按 Esc 退出全屏\n        }\n      }\n    };\n\n    // 添加 message 事件监听器\n    window.addEventListener('message', handleMessage);\n\n    // 清理函数，在组件卸载时移除监听器\n    return () => {\n      window.removeEventListener('message', handleMessage);\n    };\n  }, [goToNextSlide, goToPrevSlide, onClose]); // 依赖项\n\n  // 【重要修复】在useEffect中手动添加非被动的滚轮事件监听器和键盘事件监听器\n  useEffect(() => {\n    // 滚轮事件处理\n    const handleWheel = e => {\n      e.preventDefault(); // 现在可以成功调用\n      if (e.deltaY > 0) {\n        goToNextSlide();\n      } else if (e.deltaY < 0) {\n        goToPrevSlide();\n      }\n    };\n\n    // 键盘事件处理（作为备用，防止iframe消息机制失败）\n    const handleKeyDown = e => {\n      const isNavKey = ['ArrowRight', 'ArrowDown', 'PageDown', ' ', 'ArrowLeft', 'ArrowUp', 'PageUp', 'Escape'].includes(e.key);\n      if (isNavKey) {\n        e.preventDefault();\n      }\n      if (e.key === 'ArrowRight' || e.key === 'ArrowDown' || e.key === 'PageDown' || e.key === ' ') {\n        goToNextSlide();\n      } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp' || e.key === 'PageUp') {\n        goToPrevSlide();\n      } else if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n    const playerElement = playerRef.current;\n    if (playerElement) {\n      // 添加事件监听器并明确设置 passive 为 false\n      playerElement.addEventListener('wheel', handleWheel, {\n        passive: false\n      });\n      window.addEventListener('keydown', handleKeyDown, {\n        passive: false\n      });\n    }\n\n    // 清理函数\n    return () => {\n      if (playerElement) {\n        playerElement.removeEventListener('wheel', handleWheel);\n      }\n      window.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [goToNextSlide, goToPrevSlide, onClose]); // 依赖项\n\n  useEffect(() => {\n    resetControlsTimer();\n    return () => {\n      if (controlsTimerRef.current) clearTimeout(controlsTimerRef.current);\n    };\n  }, [currentIndex, resetControlsTimer]);\n  const currentSlide = slides[currentIndex];\n  if (!currentSlide) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: playerRef // 【新增】应用ref\n    ,\n    className: \"fixed inset-0 bg-black flex items-center justify-center z-[9999]\",\n    onMouseMove: handleMouseMove,\n    onClick: e => {\n      // 只有在点击主容器时才触发下一张，避免和控制按钮冲突\n      if (e.target === e.currentTarget || e.target === playerRef.current.firstChild) {\n        goToNextSlide();\n      }\n    }\n    // 【移除】onWheel属性，因为它现在由useEffect处理\n    ,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative w-full h-full flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(SlideRenderer, {\n        slideId: `fullscreen-${currentSlide.id || currentIndex}`,\n        slideFullHtml: currentSlide.html,\n        isAppEditingMode: false,\n        isThumbnail: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `transition-opacity duration-300 ${controlsVisible ? 'opacity-100' : 'opacity-0'}`,\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute top-5 right-5 w-10 h-10 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-xl\",\n        onClick: onClose,\n        title: \"\\u9000\\u51FA\\u5168\\u5C4F (Esc)\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), currentIndex > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute left-5 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-2xl\",\n        onClick: goToPrevSlide,\n        title: \"\\u4E0A\\u4E00\\u5F20 (\\u2190 / \\u2191)\",\n        children: \"\\u2039\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this), currentIndex < slides.length - 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"absolute right-5 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-2xl\",\n        onClick: goToNextSlide,\n        title: \"\\u4E0B\\u4E00\\u5F20 (\\u2192 / \\u2193)\",\n        children: \"\\u203A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-5 left-1/2 -translate-x-1/2 bg-black bg-opacity-50 px-4 py-2 rounded-full text-white text-sm\",\n        children: [currentIndex + 1, \" / \", slides.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 11\n  }, this);\n};\n_s(FullScreenPlayer, \"hwpL8/w350aev1QKgGgpOFKpvr4=\");\n_c = FullScreenPlayer;\nexport default FullScreenPlayer;\nvar _c;\n$RefreshReg$(_c, \"FullScreenPlayer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "FullScreenPlayer", "slides", "initialIndex", "onClose", "_s", "currentIndex", "setCurrentIndex", "controlsVisible", "setControlsVisible", "controlsTimerRef", "playerRef", "resetControlsTimer", "current", "clearTimeout", "setTimeout", "goToNextSlide", "prev", "length", "goToPrevSlide", "handleMouseMove", "handleMessage", "event", "data", "type", "key", "window", "addEventListener", "removeEventListener", "handleWheel", "e", "preventDefault", "deltaY", "handleKeyDown", "is<PERSON>av<PERSON><PERSON>", "includes", "playerElement", "passive", "currentSlide", "ref", "className", "onMouseMove", "onClick", "target", "currentTarget", "<PERSON><PERSON><PERSON><PERSON>", "children", "slideId", "id", "slideFullHtml", "html", "isAppEditingMode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stopPropagation", "title", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/FullScreenPlayer.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport Slide<PERSON>enderer from './SlideRenderer';\n\nconst FullScreenPlayer = ({ slides, initialIndex = 0, onClose }) => {\n  const [currentIndex, setCurrentIndex] = useState(initialIndex);\n  const [controlsVisible, setControlsVisible] = useState(true);\n  const controlsTimerRef = useRef(null);\n  const playerRef = useRef(null); // 【新增】为最外层div创建一个ref\n\n  // 重置控制按钮隐藏计时器\n  const resetControlsTimer = useCallback(() => {\n    if (controlsTimerRef.current) clearTimeout(controlsTimerRef.current);\n    controlsTimerRef.current = setTimeout(() => setControlsVisible(false), 2000);\n  }, []);\n\n  // 导航到下一张\n  const goToNextSlide = useCallback(() => {\n    setCurrentIndex(prev => (prev < slides.length - 1 ? prev + 1 : prev));\n    setControlsVisible(true);\n    resetControlsTimer();\n  }, [slides.length, resetControlsTimer]);\n\n  // 导航到上一张\n  const goToPrevSlide = useCallback(() => {\n    setCurrentIndex(prev => (prev > 0 ? prev - 1 : prev));\n    setControlsVisible(true);\n    resetControlsTimer();\n  }, [resetControlsTimer]);\n\n  // 监听鼠标移动以显示控件\n  const handleMouseMove = useCallback(() => {\n    setControlsVisible(true);\n    resetControlsTimer();\n  }, [resetControlsTimer]);\n  \n  // 【新增】useEffect 来处理来自 iframe 的消息\n  useEffect(() => {\n    const handleMessage = (event) => {\n      // 确保消息来源是我们期望的，并且类型正确\n      if (event.data && event.data.type === 'iframe_keydown') {\n        const key = event.data.key;\n        if (key === 'ArrowRight' || key === 'ArrowDown' || key === 'PageDown' || key === ' ') {\n          goToNextSlide();\n        } else if (key === 'ArrowLeft' || key === 'ArrowUp' || key === 'PageUp') {\n          goToPrevSlide();\n        } else if (key === 'Escape') {\n          onClose(); // 按 Esc 退出全屏\n        }\n      }\n    };\n\n    // 添加 message 事件监听器\n    window.addEventListener('message', handleMessage);\n\n    // 清理函数，在组件卸载时移除监听器\n    return () => {\n      window.removeEventListener('message', handleMessage);\n    };\n  }, [goToNextSlide, goToPrevSlide, onClose]); // 依赖项\n\n  // 【重要修复】在useEffect中手动添加非被动的滚轮事件监听器和键盘事件监听器\n  useEffect(() => {\n    // 滚轮事件处理\n    const handleWheel = (e) => {\n      e.preventDefault(); // 现在可以成功调用\n      if (e.deltaY > 0) {\n        goToNextSlide();\n      } else if (e.deltaY < 0) {\n        goToPrevSlide();\n      }\n    };\n    \n    // 键盘事件处理（作为备用，防止iframe消息机制失败）\n    const handleKeyDown = (e) => {\n      const isNavKey = ['ArrowRight', 'ArrowDown', 'PageDown', ' ', 'ArrowLeft', 'ArrowUp', 'PageUp', 'Escape'].includes(e.key);\n      \n      if (isNavKey) {\n        e.preventDefault();\n      }\n\n      if (e.key === 'ArrowRight' || e.key === 'ArrowDown' || e.key === 'PageDown' || e.key === ' ') {\n        goToNextSlide();\n      } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp' || e.key === 'PageUp') {\n        goToPrevSlide();\n      } else if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    const playerElement = playerRef.current;\n    if (playerElement) {\n      // 添加事件监听器并明确设置 passive 为 false\n      playerElement.addEventListener('wheel', handleWheel, { passive: false });\n      window.addEventListener('keydown', handleKeyDown, { passive: false });\n    }\n\n    // 清理函数\n    return () => {\n      if (playerElement) {\n        playerElement.removeEventListener('wheel', handleWheel);\n      }\n      window.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [goToNextSlide, goToPrevSlide, onClose]); // 依赖项\n\n  useEffect(() => {\n    resetControlsTimer();\n    return () => {\n      if (controlsTimerRef.current) clearTimeout(controlsTimerRef.current);\n    };\n  }, [currentIndex, resetControlsTimer]);\n\n  const currentSlide = slides[currentIndex];\n  if (!currentSlide) return null;\n\n  return (\n          <div\n        ref={playerRef} // 【新增】应用ref\n        className=\"fixed inset-0 bg-black flex items-center justify-center z-[9999]\"\n        onMouseMove={handleMouseMove}\n        onClick={(e) => {\n          // 只有在点击主容器时才触发下一张，避免和控制按钮冲突\n          if (e.target === e.currentTarget || e.target === playerRef.current.firstChild) {\n            goToNextSlide();\n          }\n        }}\n        // 【移除】onWheel属性，因为它现在由useEffect处理\n      >\n      <div className=\"relative w-full h-full flex items-center justify-center\">\n        <SlideRenderer\n          slideId={`fullscreen-${currentSlide.id || currentIndex}`}\n          slideFullHtml={currentSlide.html}\n          isAppEditingMode={false}\n          isThumbnail={false}\n        />\n      </div>\n      <div\n        className={`transition-opacity duration-300 ${controlsVisible ? 'opacity-100' : 'opacity-0'}`}\n        onClick={(e) => e.stopPropagation()}\n      >\n        <button\n          className=\"absolute top-5 right-5 w-10 h-10 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-xl\"\n          onClick={onClose}\n          title=\"退出全屏 (Esc)\"\n        >\n          ×\n        </button>\n        {currentIndex > 0 && (\n          <button\n            className=\"absolute left-5 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-2xl\"\n            onClick={goToPrevSlide}\n            title=\"上一张 (← / ↑)\"\n          >\n            ‹\n          </button>\n        )}\n        {currentIndex < slides.length - 1 && (\n          <button\n            className=\"absolute right-5 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-2xl\"\n            onClick={goToNextSlide}\n            title=\"下一张 (→ / ↓)\"\n          >\n            ›\n          </button>\n        )}\n        <div className=\"absolute bottom-5 left-1/2 -translate-x-1/2 bg-black bg-opacity-50 px-4 py-2 rounded-full text-white text-sm\">\n          {currentIndex + 1} / {slides.length}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FullScreenPlayer;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvE,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,YAAY,GAAG,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAACS,YAAY,CAAC;EAC9D,MAAM,CAACK,eAAe,EAAEC,kBAAkB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAMgB,gBAAgB,GAAGb,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMc,SAAS,GAAGd,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEhC;EACA,MAAMe,kBAAkB,GAAGhB,WAAW,CAAC,MAAM;IAC3C,IAAIc,gBAAgB,CAACG,OAAO,EAAEC,YAAY,CAACJ,gBAAgB,CAACG,OAAO,CAAC;IACpEH,gBAAgB,CAACG,OAAO,GAAGE,UAAU,CAAC,MAAMN,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;EAC9E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,aAAa,GAAGpB,WAAW,CAAC,MAAM;IACtCW,eAAe,CAACU,IAAI,IAAKA,IAAI,GAAGf,MAAM,CAACgB,MAAM,GAAG,CAAC,GAAGD,IAAI,GAAG,CAAC,GAAGA,IAAK,CAAC;IACrER,kBAAkB,CAAC,IAAI,CAAC;IACxBG,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACV,MAAM,CAACgB,MAAM,EAAEN,kBAAkB,CAAC,CAAC;;EAEvC;EACA,MAAMO,aAAa,GAAGvB,WAAW,CAAC,MAAM;IACtCW,eAAe,CAACU,IAAI,IAAKA,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAGA,IAAK,CAAC;IACrDR,kBAAkB,CAAC,IAAI,CAAC;IACxBG,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAMQ,eAAe,GAAGxB,WAAW,CAAC,MAAM;IACxCa,kBAAkB,CAAC,IAAI,CAAC;IACxBG,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAExB;EACAjB,SAAS,CAAC,MAAM;IACd,MAAM0B,aAAa,GAAIC,KAAK,IAAK;MAC/B;MACA,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,KAAK,gBAAgB,EAAE;QACtD,MAAMC,GAAG,GAAGH,KAAK,CAACC,IAAI,CAACE,GAAG;QAC1B,IAAIA,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,GAAG,EAAE;UACpFT,aAAa,CAAC,CAAC;QACjB,CAAC,MAAM,IAAIS,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,SAAS,IAAIA,GAAG,KAAK,QAAQ,EAAE;UACvEN,aAAa,CAAC,CAAC;QACjB,CAAC,MAAM,IAAIM,GAAG,KAAK,QAAQ,EAAE;UAC3BrB,OAAO,CAAC,CAAC,CAAC,CAAC;QACb;MACF;IACF,CAAC;;IAED;IACAsB,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEN,aAAa,CAAC;;IAEjD;IACA,OAAO,MAAM;MACXK,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEP,aAAa,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAACL,aAAa,EAAEG,aAAa,EAAEf,OAAO,CAAC,CAAC,CAAC,CAAC;;EAE7C;EACAT,SAAS,CAAC,MAAM;IACd;IACA,MAAMkC,WAAW,GAAIC,CAAC,IAAK;MACzBA,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;MACpB,IAAID,CAAC,CAACE,MAAM,GAAG,CAAC,EAAE;QAChBhB,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM,IAAIc,CAAC,CAACE,MAAM,GAAG,CAAC,EAAE;QACvBb,aAAa,CAAC,CAAC;MACjB;IACF,CAAC;;IAED;IACA,MAAMc,aAAa,GAAIH,CAAC,IAAK;MAC3B,MAAMI,QAAQ,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACL,CAAC,CAACL,GAAG,CAAC;MAEzH,IAAIS,QAAQ,EAAE;QACZJ,CAAC,CAACC,cAAc,CAAC,CAAC;MACpB;MAEA,IAAID,CAAC,CAACL,GAAG,KAAK,YAAY,IAAIK,CAAC,CAACL,GAAG,KAAK,WAAW,IAAIK,CAAC,CAACL,GAAG,KAAK,UAAU,IAAIK,CAAC,CAACL,GAAG,KAAK,GAAG,EAAE;QAC5FT,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM,IAAIc,CAAC,CAACL,GAAG,KAAK,WAAW,IAAIK,CAAC,CAACL,GAAG,KAAK,SAAS,IAAIK,CAAC,CAACL,GAAG,KAAK,QAAQ,EAAE;QAC7EN,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM,IAAIW,CAAC,CAACL,GAAG,KAAK,QAAQ,EAAE;QAC7BrB,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,MAAMgC,aAAa,GAAGzB,SAAS,CAACE,OAAO;IACvC,IAAIuB,aAAa,EAAE;MACjB;MACAA,aAAa,CAACT,gBAAgB,CAAC,OAAO,EAAEE,WAAW,EAAE;QAAEQ,OAAO,EAAE;MAAM,CAAC,CAAC;MACxEX,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEM,aAAa,EAAE;QAAEI,OAAO,EAAE;MAAM,CAAC,CAAC;IACvE;;IAEA;IACA,OAAO,MAAM;MACX,IAAID,aAAa,EAAE;QACjBA,aAAa,CAACR,mBAAmB,CAAC,OAAO,EAAEC,WAAW,CAAC;MACzD;MACAH,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEK,aAAa,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAACjB,aAAa,EAAEG,aAAa,EAAEf,OAAO,CAAC,CAAC,CAAC,CAAC;;EAE7CT,SAAS,CAAC,MAAM;IACdiB,kBAAkB,CAAC,CAAC;IACpB,OAAO,MAAM;MACX,IAAIF,gBAAgB,CAACG,OAAO,EAAEC,YAAY,CAACJ,gBAAgB,CAACG,OAAO,CAAC;IACtE,CAAC;EACH,CAAC,EAAE,CAACP,YAAY,EAAEM,kBAAkB,CAAC,CAAC;EAEtC,MAAM0B,YAAY,GAAGpC,MAAM,CAACI,YAAY,CAAC;EACzC,IAAI,CAACgC,YAAY,EAAE,OAAO,IAAI;EAE9B,oBACQtC,OAAA;IACFuC,GAAG,EAAE5B,SAAU,CAAC;IAAA;IAChB6B,SAAS,EAAC,kEAAkE;IAC5EC,WAAW,EAAErB,eAAgB;IAC7BsB,OAAO,EAAGZ,CAAC,IAAK;MACd;MACA,IAAIA,CAAC,CAACa,MAAM,KAAKb,CAAC,CAACc,aAAa,IAAId,CAAC,CAACa,MAAM,KAAKhC,SAAS,CAACE,OAAO,CAACgC,UAAU,EAAE;QAC7E7B,aAAa,CAAC,CAAC;MACjB;IACF;IACA;IAAA;IAAA8B,QAAA,gBAEF9C,OAAA;MAAKwC,SAAS,EAAC,yDAAyD;MAAAM,QAAA,eACtE9C,OAAA,CAACF,aAAa;QACZiD,OAAO,EAAE,cAAcT,YAAY,CAACU,EAAE,IAAI1C,YAAY,EAAG;QACzD2C,aAAa,EAAEX,YAAY,CAACY,IAAK;QACjCC,gBAAgB,EAAE,KAAM;QACxBC,WAAW,EAAE;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNxD,OAAA;MACEwC,SAAS,EAAE,mCAAmChC,eAAe,GAAG,aAAa,GAAG,WAAW,EAAG;MAC9FkC,OAAO,EAAGZ,CAAC,IAAKA,CAAC,CAAC2B,eAAe,CAAC,CAAE;MAAAX,QAAA,gBAEpC9C,OAAA;QACEwC,SAAS,EAAC,8IAA8I;QACxJE,OAAO,EAAEtC,OAAQ;QACjBsD,KAAK,EAAC,gCAAY;QAAAZ,QAAA,EACnB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRlD,YAAY,GAAG,CAAC,iBACfN,OAAA;QACEwC,SAAS,EAAC,iKAAiK;QAC3KE,OAAO,EAAEvB,aAAc;QACvBuC,KAAK,EAAC,sCAAa;QAAAZ,QAAA,EACpB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EACAlD,YAAY,GAAGJ,MAAM,CAACgB,MAAM,GAAG,CAAC,iBAC/BlB,OAAA;QACEwC,SAAS,EAAC,kKAAkK;QAC5KE,OAAO,EAAE1B,aAAc;QACvB0C,KAAK,EAAC,sCAAa;QAAAZ,QAAA,EACpB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,eACDxD,OAAA;QAAKwC,SAAS,EAAC,8GAA8G;QAAAM,QAAA,GAC1HxC,YAAY,GAAG,CAAC,EAAC,KAAG,EAACJ,MAAM,CAACgB,MAAM;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnD,EAAA,CAxKIJ,gBAAgB;AAAA0D,EAAA,GAAhB1D,gBAAgB;AA0KtB,eAAeA,gBAAgB;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}