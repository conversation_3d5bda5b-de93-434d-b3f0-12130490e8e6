# backend/app/agents/slide_detailer_agent.py
import logging
from typing import List, Dict, Any, Optional, Tuple
from pydantic import BaseModel

from app.agents.base_agent import BaseAgent
from app.models.presentation_model import (
    SlidePromptSchema,
    DetailedSlideBlueprintSchema,
    ImageRequestDetails,
    StructuredPresentationStyleSchema,
    UserIntentSchema,
    BlueprintListSchema,
    ChartConfig,
    SlideOutlineItemSchema
)
from app.services.prompt_manager import format_prompt_section

logger = logging.getLogger(__name__)

class SlideDetailerAgent(BaseAgent):
    """
    负责为单张幻灯片生成详细的内容创建指令。
    它接收演示文稿的整体信息和单张幻灯片的初步大纲，输出该幻灯片的详细规划。
    """

    def __init__(self, model_name_override: Optional[str] = None):
        """
        初始化幻灯片详细指令生成智能体。
        
        Args:
            model_name_override: Optional model name string for logging
        """
        super().__init__(
            agent_name="SlideDetailerAgent",
            system_prompt_name="system",
            agent_prompt_subdir="slide_detailer",
            model_name_override=model_name_override
        ) # 【删除】system_prompt_version="v1"
        logger.info(f"SlideDetailerAgent initialized using {self.model_name_description}.")

    async def generate_blueprints_from_intent(
        self,
        user_intent: UserIntentSchema,
        structured_style: StructuredPresentationStyleSchema,
        project_id: str
    ) -> Optional[List[DetailedSlideBlueprintSchema]]:
        """
        【核心方法重构】
        根据用户意图和视觉风格，一次性生成所有幻灯片的详细施工蓝图。
        """
        context = f"BlueprintsFromIntent_Proj_{project_id}"
        logger.info(f"[{context}] 正在根据用户意图生成所有幻灯片蓝图...")
        
        try:
            prompt_args = {
                "topic": user_intent.topic,
                "num_slides": user_intent.num_slides,
                "style_keywords": ", ".join(user_intent.style_keywords),
                "detected_language": user_intent.detected_language,
                "passed_overall_style_structured_json_string": structured_style.model_dump_json(indent=2)
            }
            
            # 【关键修改】使用_call_llm_with_instructor方法替代call_llm
            # 1. 首先构建完整的提示词
            prompt_args = {
                "topic": user_intent.topic,
                "num_slides": user_intent.num_slides,
                "style_keywords": ", ".join(user_intent.style_keywords),
                "detected_language": user_intent.detected_language,
                "passed_overall_style_structured_json_string": structured_style.model_dump_json(indent=2)
            }
            
            # 【关键修改】使用 call_llm 方法替代 _call_llm_with_instructor
            # 这样LLM的响应将是原始JSON字符串，然后由BaseAgent内部的call_llm进行Pydantic解析
            response_model_or_str, llm_log_id = await self.call_llm(
                task_prompt_name="batch_main_blueprint_generation", # 仅用于日志记录
                section_name="task:batch_main", # 告诉call_llm加载此提示词部分
                prompt_format_args=prompt_args, # 传递原始参数以进行格式化
                call_context_name=context,
                project_id_for_logging=project_id,
                expected_response_mime_type="application/json", # 期望JSON响应
                expected_pydantic_schema=BlueprintListSchema # 告诉call_llm将其解析为此Schema
            )
            
            # 3. 处理响应
            if not response_model_or_str: # 如果call_llm返回None，表示Pydantic解析失败或LLM无响应
                logger.error(f"[{context}] 蓝图生成失败，LLM返回空响应或Pydantic验证失败。")
                return None
                
            # If call_llm returns a BaseModel, it's already validated
            if isinstance(response_model_or_str, BaseModel):
                detailed_blueprints = response_model_or_str.blueprints
                logger.info(f"[{context}] 成功生成 {len(detailed_blueprints)} 份蓝图。")
                return detailed_blueprints
            else: # If it's not a BaseModel instance, it implies an internal error or unexpected return type
                logger.error(f"[{context}] 蓝图生成返回非Pydantic模型类型或内部错误。")
                return None
            
        except Exception as e:
            logger.error(f"[{context}] 蓝图生成过程中发生异常: {e}", exc_info=True)
            return None

    # 保留旧方法用于兼容性，但将在未来版本中移除
    async def process_batch(
        self,
        topic: str,
        detected_language: str,
        all_slide_outlines: List[SlidePromptSchema],
        structured_style: StructuredPresentationStyleSchema,
        project_id: str,
        task_logger: logging.Logger
    ) -> Optional[List[DetailedSlideBlueprintSchema]]:
        """
        【已弃用】批量生成所有幻灯片的详细蓝图
        请使用新的 generate_blueprints_from_intent 方法
        """
        logger.warning(f"使用已弃用的 process_batch 方法，请改用 generate_blueprints_from_intent")
        # 实现简单的兼容性逻辑
        return None

    async def process(self, 
                      topic: str, 
                      slide_number: int, 
                      overall_style_summary_text: str,
                      project_id: str = "",
                      project_id_for_logging: Optional[str] = None,
                      all_slide_outlines: Optional[List[SlidePromptSchema]] = None, 
                      passed_overall_style_structured_json_string: str = "",
                      passed_slide_outline_key_points: str = "",
                      passed_slide_suggested_layout_type: str = "未指定",
                      passed_slide_visual_focus_element: str = "未指定",
                      detected_language: str = "zh-CN",
                      special_prompt: Optional[str] = None  
                     ) -> Tuple[Optional[DetailedSlideBlueprintSchema], Optional[str]]:
        """
        【已弃用】为单张幻灯片生成详细蓝图
        请使用新的 generate_blueprints_from_intent 方法
        """
        logger.warning(f"使用已弃用的 process 方法，请改用 generate_blueprints_from_intent")
        # 实现简单的兼容性逻辑
        return None, None

    async def refine_single_blueprint_from_outline(
        self,
        user_intent_topic: str, # 从 user_intent 传递主题
        full_presentation_style: StructuredPresentationStyleSchema, # 完整的结构化风格
        single_slide_outline: SlideOutlineItemSchema, # 单张幻灯片的大纲
        project_id: str
    ) -> Optional[DetailedSlideBlueprintSchema]: # 返回单个详细蓝图
        """
        根据单张幻灯片的大纲条目和整体风格，生成该幻灯片的详细施工蓝图。
        """
        context = f"DetailBlueprint_Proj_{project_id}_Slide_{single_slide_outline.slide_number}"
        logger.info(f"[{context}] 正在根据大纲细化第 {single_slide_outline.slide_number} 张幻灯片的详细蓝图...")
        
        try:
            prompt_args = {
                "overall_topic": user_intent_topic, # 用于上下文
                "slide_number": single_slide_outline.slide_number,
                "slide_title": single_slide_outline.title,
                "slide_key_points": "- " + "\n- ".join(single_slide_outline.key_points), # 格式化为列表
                "slide_type_suggestion": single_slide_outline.slide_type_suggestion or "未指定",
                "full_style_structured_json_string": full_presentation_style.model_dump_json(indent=2)
            }
            
            # 使用新的单张蓝图细化提示
            prompt = format_prompt_section(
                agent_name="slide_detailer_agent", 
                section_name="task:single_blueprint_refinement", # 新的Prompt section名称
                **prompt_args
            )
            
            if not prompt:
                logger.error(f"[{context}] 提示词格式化失败")
                return None
                
            # 调用 _call_llm_with_instructor 方法，期望返回 DetailedSlideBlueprintSchema
            response_model, _ = await self._call_llm_with_instructor(
                prompt=prompt,
                pydantic_schema=DetailedSlideBlueprintSchema,
                project_id_for_logging=project_id
            )
            
            if not response_model:
                logger.error(f"[{context}] 详细蓝图生成失败，LLM返回空响应或Pydantic验证失败。")
                return None
                
            logger.info(f"[{context}] 成功生成第 {single_slide_outline.slide_number} 份详细蓝图。")
            return response_model
            
        except Exception as e:
            logger.error(f"[{context}] 详细蓝图生成过程中发生异常: {e}", exc_info=True)
            return None