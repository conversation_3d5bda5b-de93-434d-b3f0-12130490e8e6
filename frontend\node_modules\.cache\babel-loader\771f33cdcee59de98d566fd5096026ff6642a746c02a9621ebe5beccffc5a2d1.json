{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\MessageInput.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { FaPaperPlane, FaSpinner } from 'react-icons/fa';\nimport FileUpload from './FileUpload';\nimport UploadedFiles from './UploadedFiles';\n\n// Add this CSS to your global styles or create a separate CSS file\n// .resize-vertical-only {\n//   resize: vertical;\n//   min-height: 120px;\n//   max-height: 240px;\n// }\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MessageInput = ({\n  onSendMessage,\n  isGenerating,\n  placeholder = \"输入消息...\"\n}) => {\n  _s();\n  const [message, setMessage] = useState('');\n  const [textareaHeight, setTextareaHeight] = useState(120);\n  const [isManuallyResized, setIsManuallyResized] = useState(false);\n  const [uploadedFiles, setUploadedFiles] = useState([]);\n  const [uploadError, setUploadError] = useState('');\n  const textareaRef = useRef(null);\n  const dragStartYRef = useRef(null);\n  const dragStartHeightRef = useRef(null);\n  const isDraggingRef = useRef(false);\n  useEffect(() => {\n    if (textareaRef.current && !isManuallyResized) {\n      const scrollHeight = textareaRef.current.scrollHeight;\n      // Only auto-resize if content exceeds current height\n      if (scrollHeight > textareaHeight) {\n        const newHeight = Math.min(scrollHeight, 240);\n        setTextareaHeight(Math.max(newHeight, 120));\n      }\n    }\n  }, [message, textareaHeight, isManuallyResized]);\n\n  // 自动调整输入框高度\n  useEffect(() => {\n    if (textareaRef.current) {\n      textareaRef.current.style.height = 'auto';\n      const scrollHeight = textareaRef.current.scrollHeight;\n      textareaRef.current.style.height = scrollHeight > 120 ? '120px' : `${scrollHeight}px`;\n    }\n  }, [message]);\n  const handleSubmit = e => {\n    e.preventDefault();\n    if ((message.trim() || uploadedFiles.length > 0) && !isGenerating) {\n      // 发送消息和文件信息\n      onSendMessage({\n        message: message.trim(),\n        files: uploadedFiles\n      });\n      setMessage('');\n      setUploadedFiles([]);\n      setUploadError('');\n      // 重置输入框高度\n      if (textareaRef.current) {\n        textareaRef.current.style.height = 'auto';\n      }\n    }\n  };\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSubmit(e);\n    }\n  };\n  const handleFileUploaded = fileInfo => {\n    setUploadedFiles(prev => [...prev, fileInfo]);\n    setUploadError('');\n    console.log('文件上传成功:', fileInfo);\n  };\n  const handleFileUploadError = error => {\n    setUploadError(error);\n    console.error('文件上传失败:', error);\n  };\n  const handleRemoveFile = fileId => {\n    setUploadedFiles(prev => prev.filter(file => file.file_id !== fileId));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-3\",\n    children: [/*#__PURE__*/_jsxDEV(UploadedFiles, {\n      files: uploadedFiles,\n      onRemoveFile: handleRemoveFile\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), uploadError && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-red-500 text-sm p-2 bg-red-50 rounded border border-red-200\",\n      children: uploadError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"flex items-end bg-white rounded-lg shadow-sm border border-gray-200 p-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n        ref: textareaRef,\n        value: message,\n        onChange: e => setMessage(e.target.value),\n        onKeyDown: handleKeyDown,\n        placeholder: placeholder,\n        disabled: isGenerating,\n        className: \"flex-1 resize-none overflow-auto max-h-32 p-2 focus:outline-none focus:ring-0 text-gray-700\",\n        rows: 1\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FileUpload, {\n        onFileUploaded: handleFileUploaded,\n        onError: handleFileUploadError,\n        className: \"mr-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: !(message.trim() || uploadedFiles.length > 0) || isGenerating,\n        className: `p-2 rounded-full ${(message.trim() || uploadedFiles.length > 0) && !isGenerating ? 'bg-tiktodo-blue text-white hover:bg-tiktodo-blue-dark' : 'bg-gray-200 text-gray-400 cursor-not-allowed'} transition-colors duration-200`,\n        children: isGenerating ? /*#__PURE__*/_jsxDEV(FaSpinner, {\n          className: \"animate-spin\",\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(FaPaperPlane, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(MessageInput, \"6Onk3+5KIJL0lOHevKn64jDbPps=\");\n_c = MessageInput;\nexport default MessageInput;\nvar _c;\n$RefreshReg$(_c, \"MessageInput\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "FaPaperPlane", "FaSpinner", "FileUpload", "UploadedFiles", "jsxDEV", "_jsxDEV", "MessageInput", "onSendMessage", "isGenerating", "placeholder", "_s", "message", "setMessage", "textareaHeight", "setTextareaHeight", "isManuallyResized", "setIsManuallyResized", "uploadedFiles", "setUploadedFiles", "uploadError", "setUploadError", "textareaRef", "dragStartYRef", "dragStartHeightRef", "isDraggingRef", "current", "scrollHeight", "newHeight", "Math", "min", "max", "style", "height", "handleSubmit", "e", "preventDefault", "trim", "length", "files", "handleKeyDown", "key", "shift<PERSON>ey", "handleFileUploaded", "fileInfo", "prev", "console", "log", "handleFileUploadError", "error", "handleRemoveFile", "fileId", "filter", "file", "file_id", "className", "children", "onRemoveFile", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "ref", "value", "onChange", "target", "onKeyDown", "disabled", "rows", "onFileUploaded", "onError", "type", "size", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/MessageInput.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { FaPaper<PERSON>lane, FaSpinner } from 'react-icons/fa';\r\nimport FileUpload from './FileUpload';\r\nimport UploadedFiles from './UploadedFiles';\r\n\r\n// Add this CSS to your global styles or create a separate CSS file\r\n// .resize-vertical-only {\r\n//   resize: vertical;\r\n//   min-height: 120px;\r\n//   max-height: 240px;\r\n// }\r\n\r\nconst MessageInput = ({ onSendMessage, isGenerating, placeholder = \"输入消息...\" }) => {\r\n  const [message, setMessage] = useState('');\r\n  const [textareaHeight, setTextareaHeight] = useState(120);\r\n  const [isManuallyResized, setIsManuallyResized] = useState(false);\r\n  const [uploadedFiles, setUploadedFiles] = useState([]);\r\n  const [uploadError, setUploadError] = useState('');\r\n  const textareaRef = useRef(null);\r\n  const dragStartYRef = useRef(null);\r\n  const dragStartHeightRef = useRef(null);\r\n  const isDraggingRef = useRef(false);\r\n\r\n  useEffect(() => {\r\n    if (textareaRef.current && !isManuallyResized) {\r\n      const scrollHeight = textareaRef.current.scrollHeight;\r\n      // Only auto-resize if content exceeds current height\r\n      if (scrollHeight > textareaHeight) {\r\n        const newHeight = Math.min(scrollHeight, 240);\r\n        setTextareaHeight(Math.max(newHeight, 120));\r\n      }\r\n    }\r\n  }, [message, textareaHeight, isManuallyResized]);\r\n\r\n  // 自动调整输入框高度\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.style.height = 'auto';\r\n      const scrollHeight = textareaRef.current.scrollHeight;\r\n      textareaRef.current.style.height = scrollHeight > 120 ? '120px' : `${scrollHeight}px`;\r\n    }\r\n  }, [message]);\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    if ((message.trim() || uploadedFiles.length > 0) && !isGenerating) {\r\n      // 发送消息和文件信息\r\n      onSendMessage({\r\n        message: message.trim(),\r\n        files: uploadedFiles\r\n      });\r\n      setMessage('');\r\n      setUploadedFiles([]);\r\n      setUploadError('');\r\n      // 重置输入框高度\r\n      if (textareaRef.current) {\r\n        textareaRef.current.style.height = 'auto';\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSubmit(e);\r\n    }\r\n  };\r\n\r\n  const handleFileUploaded = (fileInfo) => {\r\n    setUploadedFiles(prev => [...prev, fileInfo]);\r\n    setUploadError('');\r\n    console.log('文件上传成功:', fileInfo);\r\n  };\r\n\r\n  const handleFileUploadError = (error) => {\r\n    setUploadError(error);\r\n    console.error('文件上传失败:', error);\r\n  };\r\n\r\n  const handleRemoveFile = (fileId) => {\r\n    setUploadedFiles(prev => prev.filter(file => file.file_id !== fileId));\r\n  };\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"space-y-3\">\r\n      {/* 已上传文件列表 */}\r\n      <UploadedFiles \r\n        files={uploadedFiles}\r\n        onRemoveFile={handleRemoveFile}\r\n      />\r\n      \r\n      {/* 错误提示 */}\r\n      {uploadError && (\r\n        <div className=\"text-red-500 text-sm p-2 bg-red-50 rounded border border-red-200\">\r\n          {uploadError}\r\n        </div>\r\n      )}\r\n      \r\n      {/* 消息输入区域 */}\r\n      <form onSubmit={handleSubmit} className=\"flex items-end bg-white rounded-lg shadow-sm border border-gray-200 p-2\">\r\n        <textarea\r\n          ref={textareaRef}\r\n          value={message}\r\n          onChange={(e) => setMessage(e.target.value)}\r\n          onKeyDown={handleKeyDown}\r\n          placeholder={placeholder}\r\n          disabled={isGenerating}\r\n          className=\"flex-1 resize-none overflow-auto max-h-32 p-2 focus:outline-none focus:ring-0 text-gray-700\"\r\n          rows={1}\r\n        />\r\n        \r\n        {/* 文件上传按钮 */}\r\n        <FileUpload \r\n          onFileUploaded={handleFileUploaded}\r\n          onError={handleFileUploadError}\r\n          className=\"mr-2\"\r\n        />\r\n        \r\n        {/* 发送按钮 */}\r\n        <button\r\n          type=\"submit\"\r\n          disabled={!(message.trim() || uploadedFiles.length > 0) || isGenerating}\r\n          className={`p-2 rounded-full ${\r\n            (message.trim() || uploadedFiles.length > 0) && !isGenerating\r\n              ? 'bg-tiktodo-blue text-white hover:bg-tiktodo-blue-dark'\r\n              : 'bg-gray-200 text-gray-400 cursor-not-allowed'\r\n          } transition-colors duration-200`}\r\n        >\r\n          {isGenerating ? (\r\n            <FaSpinner className=\"animate-spin\" size={16} />\r\n          ) : (\r\n            <FaPaperPlane size={16} />\r\n          )}\r\n        </button>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MessageInput;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,YAAY,EAAEC,SAAS,QAAQ,gBAAgB;AACxD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,iBAAiB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,YAAY,GAAGA,CAAC;EAAEC,aAAa;EAAEC,YAAY;EAAEC,WAAW,GAAG;AAAU,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,GAAG,CAAC;EACzD,MAAM,CAACkB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoB,aAAa,EAAEC,gBAAgB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAMwB,WAAW,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMwB,aAAa,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMyB,kBAAkB,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACvC,MAAM0B,aAAa,GAAG1B,MAAM,CAAC,KAAK,CAAC;EAEnCC,SAAS,CAAC,MAAM;IACd,IAAIsB,WAAW,CAACI,OAAO,IAAI,CAACV,iBAAiB,EAAE;MAC7C,MAAMW,YAAY,GAAGL,WAAW,CAACI,OAAO,CAACC,YAAY;MACrD;MACA,IAAIA,YAAY,GAAGb,cAAc,EAAE;QACjC,MAAMc,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACH,YAAY,EAAE,GAAG,CAAC;QAC7CZ,iBAAiB,CAACc,IAAI,CAACE,GAAG,CAACH,SAAS,EAAE,GAAG,CAAC,CAAC;MAC7C;IACF;EACF,CAAC,EAAE,CAAChB,OAAO,EAAEE,cAAc,EAAEE,iBAAiB,CAAC,CAAC;;EAEhD;EACAhB,SAAS,CAAC,MAAM;IACd,IAAIsB,WAAW,CAACI,OAAO,EAAE;MACvBJ,WAAW,CAACI,OAAO,CAACM,KAAK,CAACC,MAAM,GAAG,MAAM;MACzC,MAAMN,YAAY,GAAGL,WAAW,CAACI,OAAO,CAACC,YAAY;MACrDL,WAAW,CAACI,OAAO,CAACM,KAAK,CAACC,MAAM,GAAGN,YAAY,GAAG,GAAG,GAAG,OAAO,GAAG,GAAGA,YAAY,IAAI;IACvF;EACF,CAAC,EAAE,CAACf,OAAO,CAAC,CAAC;EAEb,MAAMsB,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACxB,OAAO,CAACyB,IAAI,CAAC,CAAC,IAAInB,aAAa,CAACoB,MAAM,GAAG,CAAC,KAAK,CAAC7B,YAAY,EAAE;MACjE;MACAD,aAAa,CAAC;QACZI,OAAO,EAAEA,OAAO,CAACyB,IAAI,CAAC,CAAC;QACvBE,KAAK,EAAErB;MACT,CAAC,CAAC;MACFL,UAAU,CAAC,EAAE,CAAC;MACdM,gBAAgB,CAAC,EAAE,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;MAClB;MACA,IAAIC,WAAW,CAACI,OAAO,EAAE;QACvBJ,WAAW,CAACI,OAAO,CAACM,KAAK,CAACC,MAAM,GAAG,MAAM;MAC3C;IACF;EACF,CAAC;EAED,MAAMO,aAAa,GAAIL,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAACM,GAAG,KAAK,OAAO,IAAI,CAACN,CAAC,CAACO,QAAQ,EAAE;MACpCP,CAAC,CAACC,cAAc,CAAC,CAAC;MAClBF,YAAY,CAACC,CAAC,CAAC;IACjB;EACF,CAAC;EAED,MAAMQ,kBAAkB,GAAIC,QAAQ,IAAK;IACvCzB,gBAAgB,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,QAAQ,CAAC,CAAC;IAC7CvB,cAAc,CAAC,EAAE,CAAC;IAClByB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,QAAQ,CAAC;EAClC,CAAC;EAED,MAAMI,qBAAqB,GAAIC,KAAK,IAAK;IACvC5B,cAAc,CAAC4B,KAAK,CAAC;IACrBH,OAAO,CAACG,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;EACjC,CAAC;EAED,MAAMC,gBAAgB,GAAIC,MAAM,IAAK;IACnChC,gBAAgB,CAAC0B,IAAI,IAAIA,IAAI,CAACO,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKH,MAAM,CAAC,CAAC;EACxE,CAAC;EAQD,oBACE7C,OAAA;IAAKiD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBlD,OAAA,CAACF,aAAa;MACZmC,KAAK,EAAErB,aAAc;MACrBuC,YAAY,EAAEP;IAAiB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,EAGDzC,WAAW,iBACVd,OAAA;MAAKiD,SAAS,EAAC,kEAAkE;MAAAC,QAAA,EAC9EpC;IAAW;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CACN,eAGDvD,OAAA;MAAMwD,QAAQ,EAAE5B,YAAa;MAACqB,SAAS,EAAC,yEAAyE;MAAAC,QAAA,gBAC/GlD,OAAA;QACEyD,GAAG,EAAEzC,WAAY;QACjB0C,KAAK,EAAEpD,OAAQ;QACfqD,QAAQ,EAAG9B,CAAC,IAAKtB,UAAU,CAACsB,CAAC,CAAC+B,MAAM,CAACF,KAAK,CAAE;QAC5CG,SAAS,EAAE3B,aAAc;QACzB9B,WAAW,EAAEA,WAAY;QACzB0D,QAAQ,EAAE3D,YAAa;QACvB8C,SAAS,EAAC,6FAA6F;QACvGc,IAAI,EAAE;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAGFvD,OAAA,CAACH,UAAU;QACTmE,cAAc,EAAE3B,kBAAmB;QACnC4B,OAAO,EAAEvB,qBAAsB;QAC/BO,SAAS,EAAC;MAAM;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAGFvD,OAAA;QACEkE,IAAI,EAAC,QAAQ;QACbJ,QAAQ,EAAE,EAAExD,OAAO,CAACyB,IAAI,CAAC,CAAC,IAAInB,aAAa,CAACoB,MAAM,GAAG,CAAC,CAAC,IAAI7B,YAAa;QACxE8C,SAAS,EAAE,oBACT,CAAC3C,OAAO,CAACyB,IAAI,CAAC,CAAC,IAAInB,aAAa,CAACoB,MAAM,GAAG,CAAC,KAAK,CAAC7B,YAAY,GACzD,uDAAuD,GACvD,8CAA8C,iCAClB;QAAA+C,QAAA,EAEjC/C,YAAY,gBACXH,OAAA,CAACJ,SAAS;UAACqD,SAAS,EAAC,cAAc;UAACkB,IAAI,EAAE;QAAG;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEhDvD,OAAA,CAACL,YAAY;UAACwE,IAAI,EAAE;QAAG;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC1B;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClD,EAAA,CAnIIJ,YAAY;AAAAmE,EAAA,GAAZnE,YAAY;AAqIlB,eAAeA,YAAY;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}