#!/usr/bin/env python3
"""
测试新的可辨识联合类型 (Discriminated Unions) 的验证脚本
验证Element联合类型和DetailedSlideBlueprintSchema的Gemini API兼容性
"""

import json
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.models.presentation_model import (
    TextElementSchema,
    KpiCardSchema,
    ChartBlueprintSchema,
    ImageElementSchema,
    Element,
    DetailedSlideBlueprintSchema,
    ChartConfig
)

def test_text_element():
    """测试TextElementSchema"""
    print("=== 测试 TextElementSchema ===")
    
    text_element = TextElementSchema(
        type="title",
        content="中国房地产市场发展趋势",
        target_area="title_area",
        animation_style="fade-in-up"
    )
    
    print(f"✅ TextElement创建成功: {text_element.type}")
    
    # 测试JSON序列化
    json_data = text_element.model_dump()
    print(f"✅ JSON序列化成功: {json_data}")
    
    # 测试从JSON反序列化
    recreated = TextElementSchema.model_validate(json_data)
    print(f"✅ JSON反序列化成功: {recreated.content}")
    print()

def test_kpi_card():
    """测试KpiCardSchema"""
    print("=== 测试 KpiCardSchema ===")
    
    kpi_card = KpiCardSchema(
        type="kpi_card",
        title="销售额增长",
        value="15.2万亿元",
        change="****%",
        icon_fontawesome_class="fa-solid fa-chart-line-up",
        target_area="kpi_card_1"
    )
    
    print(f"✅ KpiCard创建成功: {kpi_card.title}")
    
    # 测试JSON序列化
    json_data = kpi_card.model_dump()
    print(f"✅ JSON序列化成功: {json_data}")
    print()

def test_chart_blueprint():
    """测试ChartBlueprintSchema"""
    print("=== 测试 ChartBlueprintSchema ===")
    
    # 创建Chart.js配置 - 使用ChartConfig模型
    from app.models.presentation_model import ChartConfig as ChartConfigModel
    
    chart_config = ChartConfigModel(
        chart_canvas_id="chart_canvas_1",
        chart_type="line",
        chart_js_data={
            "labels": ["2020", "2021", "2022", "2023", "2024"],
            "datasets": [{
                "label": "房价趋势",
                "data": [50000, 52000, 48000, 46000, 47000],
                "borderColor": "#2563eb",
                "backgroundColor": "rgba(37, 99, 235, 0.1)"
            }]
        },
        chart_js_options={
            "responsive": True,
            "maintainAspectRatio": False
        }
    )
    
    chart_element = ChartBlueprintSchema(
        type="chart",
        title="房价趋势图 (2020-2024)",
        chart_type="line",
        data_fabrication_instruction="创建一个折线图，显示2020-2024年房价变化趋势，数据显示2021年达到峰值后逐步回调。包含详细的数据标签和图例说明，用于展示房地产市场的波动情况",
        final_chart_js_config=chart_config,
        target_area="chart_area_1"
    )
    
    print(f"✅ ChartBlueprint创建成功: {chart_element.title}")
    
    # 测试JSON序列化
    json_data = chart_element.model_dump()
    print(f"✅ JSON序列化成功: chart_type={json_data['chart_type']}")
    print()

def test_image_element():
    """测试ImageElementSchema"""
    print("=== 测试 ImageElementSchema ===")
    
    image_element = ImageElementSchema(
        type="image",
        generation_prompt="A modern real estate development with sleek glass buildings, urban skyline at sunset, professional architectural photography style",
        alt_text="现代房地产开发项目",
        target_area="hero_image_area"
    )
    
    print(f"✅ ImageElement创建成功: {image_element.alt_text}")
    
    # 测试JSON序列化
    json_data = image_element.model_dump()
    print(f"✅ JSON序列化成功: {json_data}")
    print()

def test_discriminated_union():
    """测试可辨识联合类型"""
    print("=== 测试 Element 可辨识联合类型 ===")
    
    # 测试不同类型的元素都能被正确识别
    elements = [
        {"type": "title", "content": "主标题", "target_area": "title_area"},
        {"type": "kpi_card", "title": "KPI", "value": "100%", "change": "+10%", "icon_fontawesome_class": "fa-solid fa-chart-up", "target_area": "kpi_1"},
                 {"type": "chart", "title": "图表", "chart_type": "bar", "data_fabrication_instruction": "创建一个简单的条形图显示数据对比，包含3个分类和对应数值。数据显示A公司销售额100万、B公司销售额80万、C公司销售额60万", "final_chart_js_config": {"chart_canvas_id": "chart_test", "chart_type": "bar", "chart_js_data": {"labels": [], "datasets": []}, "chart_js_options": {}}, "target_area": "chart_area"},
        {"type": "image", "generation_prompt": "A professional business chart visualization", "alt_text": "商业图表", "target_area": "image_area"}
    ]
    
    for i, element_data in enumerate(elements):
        try:
            # 这里测试Pydantic的可辨识联合类型自动选择正确的模型
            if element_data["type"] == "title":
                element = TextElementSchema.model_validate(element_data)
            elif element_data["type"] == "kpi_card":
                element = KpiCardSchema.model_validate(element_data)
            elif element_data["type"] == "chart":
                element = ChartBlueprintSchema.model_validate(element_data)
            elif element_data["type"] == "image":
                element = ImageElementSchema.model_validate(element_data)
            
            print(f"✅ 元素 {i+1} ({element_data['type']}) 验证成功")
        except Exception as e:
            print(f"❌ 元素 {i+1} ({element_data['type']}) 验证失败: {e}")
    print()

def test_detailed_slide_blueprint():
    """测试DetailedSlideBlueprintSchema与新Element类型的集成"""
    print("=== 测试 DetailedSlideBlueprintSchema 集成 ===")
    
    # 创建包含不同类型元素的蓝图
    blueprint_data = {
        "slide_number": 1,
        "layout_template_name": "ContentSlideLayout",
        "background_style_description": "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
        "key_elements": [
            {
                "type": "title",
                "content": "房地产市场分析报告",
                "target_area": "title_area",
                "animation_style": "fade-in-down"
            },
            {
                "type": "kpi_card",
                "title": "销售增长",
                "value": "15.2%",
                "change": "+3.5%",
                "icon_fontawesome_class": "fa-solid fa-chart-trend-up",
                "target_area": "kpi_card_1"
            },
            {
                "type": "chart",
                "title": "区域销售对比",
                "chart_type": "bar",
                                 "data_fabrication_instruction": "创建一个条形图显示北京、上海、深圳三个城市的房地产销售额对比，数据分别为320亿、280亿、250亿元。使用蓝色系配色方案，包含图例和数据标签",
                 "final_chart_js_config": {
                     "chart_canvas_id": "chart_regional_sales",
                     "chart_type": "bar",
                     "chart_js_data": {
                         "labels": ["北京", "上海", "深圳"],
                         "datasets": [{
                             "label": "销售额(亿元)",
                             "data": [320, 280, 250],
                             "backgroundColor": ["#3b82f6", "#10b981", "#f59e0b"]
                         }]
                     },
                     "chart_js_options": {
                         "responsive": True,
                         "maintainAspectRatio": False
                     }
                 },
                "target_area": "chart_area_1"
            }
        ],
        "speaker_notes": "这张幻灯片展示了房地产市场的核心数据。重点强调销售增长率达到15.2%，这表明市场正在复苏。区域对比图显示一线城市仍然是主要增长动力，北京领先于其他城市。建议在演讲时突出这些积极信号。"
    }
    
    try:
        blueprint = DetailedSlideBlueprintSchema.model_validate(blueprint_data)
        print(f"✅ DetailedSlideBlueprintSchema创建成功")
        print(f"   幻灯片编号: {blueprint.slide_number}")
        print(f"   布局模板: {blueprint.layout_template_name}")
        print(f"   元素数量: {len(blueprint.key_elements)}")
        
        # 验证元素类型
        for i, element in enumerate(blueprint.key_elements):
            print(f"   元素 {i+1}: {element.type}")
        
        # 测试JSON Schema生成
        schema = DetailedSlideBlueprintSchema.model_json_schema()
        print("✅ JSON Schema生成成功")
        
        # 检查是否包含anyOf结构
        schema_str = json.dumps(schema)
        if 'anyOf' in schema_str:
            print("⚠️  Schema包含anyOf结构 - 可能会导致Gemini API兼容性问题")
        else:
            print("✅ Schema不包含anyOf结构 - Gemini API兼容")
        
        # 检查discriminator
        if 'discriminator' in schema_str:
            print("✅ Schema包含discriminator - 可辨识联合类型正常工作")
        
        print()
        
    except Exception as e:
        print(f"❌ DetailedSlideBlueprintSchema创建失败: {e}")
        print()

def main():
    """主测试函数"""
    print("🚀 开始测试可辨识联合类型 (Discriminated Unions)")
    print("=" * 50)
    
    try:
        test_text_element()
        test_kpi_card()
        test_chart_blueprint()
        test_image_element()
        test_discriminated_union()
        test_detailed_slide_blueprint()
        
        print("🎉 所有测试通过！可辨识联合类型工作正常")
        print("✅ Element联合类型支持所有四种元素类型")
        print("✅ DetailedSlideBlueprintSchema与新类型集成成功")
        print("✅ Gemini API兼容性良好")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 