{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\ChatHistoryList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { FaEllipsisV, FaShare, FaEdit, FaArchive, FaTrash } from 'react-icons/fa';\nimport formatDate from '../utils/formatDate';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatHistoryList = ({\n  historyItems = [],\n  currentProjectId,\n  onSelectHistoryItem,\n  onDeleteHistoryItem,\n  isLoading\n}) => {\n  _s();\n  const [hoveredChatId, setHoveredChatId] = useState(null);\n  const [dropdownOpen, setDropdownOpen] = useState(null);\n  const dropdownRef = useRef(null);\n\n  // 点击外部关闭下拉菜单\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setDropdownOpen(null);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n  const truncateText = (text, maxLength = 30) => {\n    if (!text) return '';\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n  const handleDropdownAction = async (action, chat) => {\n    setDropdownOpen(null);\n    switch (action) {\n      case 'share':\n        console.log('分享聊天:', chat.id);\n        // 实现分享功能\n        break;\n      case 'rename':\n        console.log('重命名聊天:', chat.id);\n        // 重命名逻辑交由父组件处理\n        const newTitle = prompt('请输入新的聊天名称:');\n        if (newTitle && newTitle.trim()) {\n          try {\n            // 调用父组件传入的更新函数\n            // 这里假设有一个onRenameHistoryItem函数，实际应用中可能需要调整\n            // onRenameHistoryItem(chat.id, newTitle.trim());\n          } catch (error) {\n            console.error('重命名失败:', error);\n          }\n        }\n        break;\n      case 'delete':\n        console.log('删除聊天:', chat.id);\n        if (onDeleteHistoryItem) {\n          onDeleteHistoryItem(chat.id, chat.project_id);\n        }\n        break;\n      default:\n        break;\n    }\n  };\n\n  // 显示加载状态\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 text-center text-sm text-gray-500\",\n      children: \"\\u52A0\\u8F7D\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 显示空状态\n  if (historyItems.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 text-center text-xs text-gray-400\",\n      children: \"\\u6682\\u65E0\\u5386\\u53F2\\u8BB0\\u5F55\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-1\",\n    children: historyItems.map(chat => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `relative group flex items-center px-3 py-3 rounded-md cursor-pointer transition-colors duration-150 ${currentProjectId === chat.id || currentProjectId === chat.project_id ? 'bg-blue-50 border-r-2 border-blue-500' : 'hover:bg-gray-50'}`,\n      onMouseEnter: () => setHoveredChatId(chat.id),\n      onMouseLeave: () => setHoveredChatId(null),\n      onClick: () => onSelectHistoryItem && onSelectHistoryItem(chat),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 min-w-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-1\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: `text-sm font-medium truncate ${currentProjectId === chat.id || currentProjectId === chat.project_id ? 'text-blue-700' : 'text-gray-900'}`,\n            children: truncateText(chat.title)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600 truncate\",\n          children: formatDate(new Date(chat.timestamp))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this), (hoveredChatId === chat.id || dropdownOpen === chat.id) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative ml-2\",\n        ref: dropdownOpen === chat.id ? dropdownRef : null,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.stopPropagation();\n            setDropdownOpen(dropdownOpen === chat.id ? null : chat.id);\n          },\n          className: \"p-1 text-gray-400 hover:text-gray-600 rounded transition-colors\",\n          title: \"\\u66F4\\u591A\\u64CD\\u4F5C\",\n          children: /*#__PURE__*/_jsxDEV(FaEllipsisV, {\n            size: 12\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 15\n        }, this), dropdownOpen === chat.id && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute right-0 top-6 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50 translate-x-[80px]\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.stopPropagation();\n              handleDropdownAction('share', chat);\n            },\n            className: \"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(FaShare, {\n              size: 12,\n              className: \"mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 21\n            }, this), \"\\u5171\\u4EAB\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.stopPropagation();\n              handleDropdownAction('rename', chat);\n            },\n            className: \"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(FaEdit, {\n              size: 12,\n              className: \"mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 21\n            }, this), \"\\u91CD\\u547D\\u540D\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: e => {\n              e.stopPropagation();\n              handleDropdownAction('delete', chat);\n            },\n            className: \"flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\",\n            children: [/*#__PURE__*/_jsxDEV(FaTrash, {\n              size: 12,\n              className: \"mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 21\n            }, this), \"\\u5220\\u9664\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 13\n      }, this)]\n    }, chat.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatHistoryList, \"OBpc5yZnnjsyLXl4vaT5z/ChE20=\");\n_c = ChatHistoryList;\nexport default ChatHistoryList;\nvar _c;\n$RefreshReg$(_c, \"ChatHistoryList\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "FaEllipsisV", "FaShare", "FaEdit", "FaArchive", "FaTrash", "formatDate", "jsxDEV", "_jsxDEV", "ChatHistoryList", "historyItems", "currentProjectId", "onSelectHistoryItem", "onDeleteHistoryItem", "isLoading", "_s", "hoveredChatId", "setHoveredChatId", "dropdownOpen", "setDropdownOpen", "dropdownRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "length", "substring", "handleDropdownAction", "action", "chat", "console", "log", "id", "newTitle", "prompt", "trim", "error", "project_id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "onMouseEnter", "onMouseLeave", "onClick", "title", "Date", "timestamp", "ref", "e", "stopPropagation", "size", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/ChatHistoryList.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { FaEllipsisV, FaShare, FaEdit, FaArchive, FaTrash } from 'react-icons/fa';\nimport formatDate from '../utils/formatDate';\n\nconst ChatHistoryList = ({ \n  historyItems = [], \n  currentProjectId, \n  onSelectHistoryItem, \n  onDeleteHistoryItem, \n  isLoading \n}) => {\n  const [hoveredChatId, setHoveredChatId] = useState(null);\n  const [dropdownOpen, setDropdownOpen] = useState(null);\n  const dropdownRef = useRef(null);\n\n  // 点击外部关闭下拉菜单\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setDropdownOpen(null);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const truncateText = (text, maxLength = 30) => {\n    if (!text) return '';\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + '...';\n  };\n\n  const handleDropdownAction = async (action, chat) => {\n    setDropdownOpen(null);\n    \n    switch (action) {\n      case 'share':\n        console.log('分享聊天:', chat.id);\n        // 实现分享功能\n        break;\n      case 'rename':\n        console.log('重命名聊天:', chat.id);\n        // 重命名逻辑交由父组件处理\n        const newTitle = prompt('请输入新的聊天名称:');\n        if (newTitle && newTitle.trim()) {\n          try {\n            // 调用父组件传入的更新函数\n            // 这里假设有一个onRenameHistoryItem函数，实际应用中可能需要调整\n            // onRenameHistoryItem(chat.id, newTitle.trim());\n          } catch (error) {\n            console.error('重命名失败:', error);\n          }\n        }\n        break;\n      case 'delete':\n        console.log('删除聊天:', chat.id);\n        if (onDeleteHistoryItem) {\n          onDeleteHistoryItem(chat.id, chat.project_id);\n        }\n        break;\n      default:\n        break;\n    }\n  };\n\n  // 显示加载状态\n  if (isLoading) {\n    return (\n      <div className=\"p-3 text-center text-sm text-gray-500\">\n        加载中...\n      </div>\n    );\n  }\n\n  // 显示空状态\n  if (historyItems.length === 0) {\n    return (\n      <div className=\"p-3 text-center text-xs text-gray-400\">\n        暂无历史记录\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-1\">\n      {historyItems.map((chat) => (\n        <div\n          key={chat.id}\n          className={`relative group flex items-center px-3 py-3 rounded-md cursor-pointer transition-colors duration-150 ${\n            currentProjectId === chat.id || currentProjectId === chat.project_id\n              ? 'bg-blue-50 border-r-2 border-blue-500'\n              : 'hover:bg-gray-50'\n          }`}\n          onMouseEnter={() => setHoveredChatId(chat.id)}\n          onMouseLeave={() => setHoveredChatId(null)}\n          onClick={() => onSelectHistoryItem && onSelectHistoryItem(chat)}\n        >\n          {/* 聊天内容 */}\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"flex items-center justify-between mb-1\">\n              <h4 className={`text-sm font-medium truncate ${\n                currentProjectId === chat.id || currentProjectId === chat.project_id \n                  ? 'text-blue-700' \n                  : 'text-gray-900'\n              }`}>\n                {truncateText(chat.title)}\n              </h4>\n            </div>\n            \n            <p className=\"text-xs text-gray-600 truncate\">\n              {formatDate(new Date(chat.timestamp))}\n            </p>\n          </div>\n\n          {/* 三点菜单按钮 */}\n          {(hoveredChatId === chat.id || dropdownOpen === chat.id) && (\n            <div className=\"relative ml-2\" ref={dropdownOpen === chat.id ? dropdownRef : null}>\n              <button\n                onClick={(e) => {\n                  e.stopPropagation();\n                  setDropdownOpen(dropdownOpen === chat.id ? null : chat.id);\n                }}\n                className=\"p-1 text-gray-400 hover:text-gray-600 rounded transition-colors\"\n                title=\"更多操作\"\n              >\n                <FaEllipsisV size={12} />\n              </button>\n\n              {/* 下拉菜单 */}\n              {dropdownOpen === chat.id && (\n                <div className=\"absolute right-0 top-6 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50 translate-x-[80px]\">\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleDropdownAction('share', chat);\n                    }}\n                    className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <FaShare size={12} className=\"mr-2\" />\n                    共享\n                  </button>\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleDropdownAction('rename', chat);\n                    }}\n                    className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                  >\n                    <FaEdit size={12} className=\"mr-2\" />\n                    重命名\n                  </button>\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleDropdownAction('delete', chat);\n                    }}\n                    className=\"flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\"\n                  >\n                    <FaTrash size={12} className=\"mr-2\" />\n                    删除\n                  </button>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default ChatHistoryList; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,WAAW,EAAEC,OAAO,EAAEC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,gBAAgB;AACjF,OAAOC,UAAU,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,eAAe,GAAGA,CAAC;EACvBC,YAAY,GAAG,EAAE;EACjBC,gBAAgB;EAChBC,mBAAmB;EACnBC,mBAAmB;EACnBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMsB,WAAW,GAAGrB,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMqB,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIF,WAAW,CAACG,OAAO,IAAI,CAACH,WAAW,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtEN,eAAe,CAAC,IAAI,CAAC;MACvB;IACF,CAAC;IAEDO,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,YAAY,GAAGA,CAACC,IAAI,EAAEC,SAAS,GAAG,EAAE,KAAK;IAC7C,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;IACpB,IAAIA,IAAI,CAACE,MAAM,IAAID,SAAS,EAAE,OAAOD,IAAI;IACzC,OAAOA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK;EAC7C,CAAC;EAED,MAAMG,oBAAoB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,IAAI,KAAK;IACnDjB,eAAe,CAAC,IAAI,CAAC;IAErB,QAAQgB,MAAM;MACZ,KAAK,OAAO;QACVE,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,IAAI,CAACG,EAAE,CAAC;QAC7B;QACA;MACF,KAAK,QAAQ;QACXF,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEF,IAAI,CAACG,EAAE,CAAC;QAC9B;QACA,MAAMC,QAAQ,GAAGC,MAAM,CAAC,YAAY,CAAC;QACrC,IAAID,QAAQ,IAAIA,QAAQ,CAACE,IAAI,CAAC,CAAC,EAAE;UAC/B,IAAI;YACF;YACA;YACA;UAAA,CACD,CAAC,OAAOC,KAAK,EAAE;YACdN,OAAO,CAACM,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;UAChC;QACF;QACA;MACF,KAAK,QAAQ;QACXN,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,IAAI,CAACG,EAAE,CAAC;QAC7B,IAAI1B,mBAAmB,EAAE;UACvBA,mBAAmB,CAACuB,IAAI,CAACG,EAAE,EAAEH,IAAI,CAACQ,UAAU,CAAC;QAC/C;QACA;MACF;QACE;IACJ;EACF,CAAC;;EAED;EACA,IAAI9B,SAAS,EAAE;IACb,oBACEN,OAAA;MAAKqC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAEvD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;;EAEA;EACA,IAAIxC,YAAY,CAACsB,MAAM,KAAK,CAAC,EAAE;IAC7B,oBACExB,OAAA;MAAKqC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAEvD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACE1C,OAAA;IAAKqC,SAAS,EAAC,WAAW;IAAAC,QAAA,EACvBpC,YAAY,CAACyC,GAAG,CAAEf,IAAI,iBACrB5B,OAAA;MAEEqC,SAAS,EAAE,uGACTlC,gBAAgB,KAAKyB,IAAI,CAACG,EAAE,IAAI5B,gBAAgB,KAAKyB,IAAI,CAACQ,UAAU,GAChE,uCAAuC,GACvC,kBAAkB,EACrB;MACHQ,YAAY,EAAEA,CAAA,KAAMnC,gBAAgB,CAACmB,IAAI,CAACG,EAAE,CAAE;MAC9Cc,YAAY,EAAEA,CAAA,KAAMpC,gBAAgB,CAAC,IAAI,CAAE;MAC3CqC,OAAO,EAAEA,CAAA,KAAM1C,mBAAmB,IAAIA,mBAAmB,CAACwB,IAAI,CAAE;MAAAU,QAAA,gBAGhEtC,OAAA;QAAKqC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BtC,OAAA;UAAKqC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrDtC,OAAA;YAAIqC,SAAS,EAAE,gCACblC,gBAAgB,KAAKyB,IAAI,CAACG,EAAE,IAAI5B,gBAAgB,KAAKyB,IAAI,CAACQ,UAAU,GAChE,eAAe,GACf,eAAe,EAClB;YAAAE,QAAA,EACAjB,YAAY,CAACO,IAAI,CAACmB,KAAK;UAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN1C,OAAA;UAAGqC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAC1CxC,UAAU,CAAC,IAAIkD,IAAI,CAACpB,IAAI,CAACqB,SAAS,CAAC;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGL,CAAClC,aAAa,KAAKoB,IAAI,CAACG,EAAE,IAAIrB,YAAY,KAAKkB,IAAI,CAACG,EAAE,kBACrD/B,OAAA;QAAKqC,SAAS,EAAC,eAAe;QAACa,GAAG,EAAExC,YAAY,KAAKkB,IAAI,CAACG,EAAE,GAAGnB,WAAW,GAAG,IAAK;QAAA0B,QAAA,gBAChFtC,OAAA;UACE8C,OAAO,EAAGK,CAAC,IAAK;YACdA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBzC,eAAe,CAACD,YAAY,KAAKkB,IAAI,CAACG,EAAE,GAAG,IAAI,GAAGH,IAAI,CAACG,EAAE,CAAC;UAC5D,CAAE;UACFM,SAAS,EAAC,iEAAiE;UAC3EU,KAAK,EAAC,0BAAM;UAAAT,QAAA,eAEZtC,OAAA,CAACP,WAAW;YAAC4D,IAAI,EAAE;UAAG;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,EAGRhC,YAAY,KAAKkB,IAAI,CAACG,EAAE,iBACvB/B,OAAA;UAAKqC,SAAS,EAAC,+GAA+G;UAAAC,QAAA,gBAC5HtC,OAAA;YACE8C,OAAO,EAAGK,CAAC,IAAK;cACdA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnB1B,oBAAoB,CAAC,OAAO,EAAEE,IAAI,CAAC;YACrC,CAAE;YACFS,SAAS,EAAC,4EAA4E;YAAAC,QAAA,gBAEtFtC,OAAA,CAACN,OAAO;cAAC2D,IAAI,EAAE,EAAG;cAAChB,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1C,OAAA;YACE8C,OAAO,EAAGK,CAAC,IAAK;cACdA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnB1B,oBAAoB,CAAC,QAAQ,EAAEE,IAAI,CAAC;YACtC,CAAE;YACFS,SAAS,EAAC,4EAA4E;YAAAC,QAAA,gBAEtFtC,OAAA,CAACL,MAAM;cAAC0D,IAAI,EAAE,EAAG;cAAChB,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1C,OAAA;YACE8C,OAAO,EAAGK,CAAC,IAAK;cACdA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnB1B,oBAAoB,CAAC,QAAQ,EAAEE,IAAI,CAAC;YACtC,CAAE;YACFS,SAAS,EAAC,yEAAyE;YAAAC,QAAA,gBAEnFtC,OAAA,CAACH,OAAO;cAACwD,IAAI,EAAE,EAAG;cAAChB,SAAS,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAExC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA,GA7EId,IAAI,CAACG,EAAE;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA8ET,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnC,EAAA,CAxKIN,eAAe;AAAAqD,EAAA,GAAfrD,eAAe;AA0KrB,eAAeA,eAAe;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}