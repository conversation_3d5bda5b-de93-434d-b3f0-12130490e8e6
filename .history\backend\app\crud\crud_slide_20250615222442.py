from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from app.db import models as db_models
from app.models import presentation_model as pydantic_models  # Slide Pydantic模型
from sqlalchemy import func  # 新增：用于更新时间

def get_slides_for_project(db: Session, project_id: str) -> List[db_models.Slide]:
    """获取项目的所有幻灯片，按顺序排序"""
    return db.query(db_models.Slide)\
             .filter(db_models.Slide.project_id == project_id)\
             .order_by(db_models.Slide.order_index)\
             .all()

def get_slide(db: Session, slide_id: str) -> Optional[db_models.Slide]:
    """根据ID获取特定幻灯片"""
    return db.query(db_models.Slide).filter(db_models.Slide.id == slide_id).first()

def get_slide_by_order(db: Session, project_id: str, order_index: int) -> Optional[db_models.Slide]:
    """根据项目ID和顺序索引获取幻灯片"""
    return db.query(db_models.Slide).filter(
        db_models.Slide.project_id == project_id,
        db_models.Slide.order_index == order_index
    ).first()

def create_slide(db: Session, project_id: str, slide_number: int, html_content: str, prompt: str, image_data: Optional[List[Dict[str, Any]]] = None) -> db_models.Slide:
    """
    创建新的幻灯片
    
    Args:
        db: 数据库会话
        project_id: 项目ID
        slide_number: 幻灯片编号 (1-based)
        html_content: HTML内容
        prompt: 生成幻灯片的提示/指令
        image_data: 图片信息列表
        
    Returns:
        创建的数据库幻灯片对象
    """
    # 检查项目是否存在
    project = db.query(db_models.Project).filter(db_models.Project.id == project_id).first()
    if not project:
        raise ValueError(f"Project with id {project_id} not found.")
    
    # 创建新幻灯片
    db_slide = db_models.Slide(
        project_id=project_id,
        order_index=slide_number - 1,  # 转换为0-based索引
        html_content=html_content,
        detailed_instructions=prompt,
        selected_image_infos_json=image_data  # 将图片信息列表存入JSON字段
    )
    
    # 添加到数据库
    db.add(db_slide)
    
    # 更新项目的最后修改时间
    project.last_modified = func.now()
    
    # 提交事务
    db.commit()
    db.refresh(db_slide)
    
    return db_slide

def update_slide_image(db: Session, slide_id: str, image_path: str, image_description: str) -> db_models.Slide:
    """
    更新幻灯片的图片信息
    
    Args:
        db: 数据库会话
        slide_id: 幻灯片ID
        image_path: 图片在项目存储中的相对路径
        image_description: 图片描述
        
    Returns:
        更新后的数据库幻灯片对象
    """
    # 获取幻灯片
    db_slide = db.query(db_models.Slide).filter(db_models.Slide.id == slide_id).first()
    if not db_slide:
        raise ValueError(f"Slide with id {slide_id} not found.")
    
    # 更新图片信息
    db_slide.image_path = image_path
    db_slide.image_description = image_description
    
    # 提交事务
    db.commit()
    db.refresh(db_slide)
    
    return db_slide

def create_or_update_slide(db: Session, project_id: str, slide_data: pydantic_models.Slide, user_id: Optional[str] = None) -> db_models.Slide:
    """创建或更新幻灯片"""
    # 检查项目是否存在并且属于该用户
    project = db.query(db_models.Project).filter(db_models.Project.id == project_id)
    if user_id:
        project = project.filter(db_models.Project.user_id == user_id)
    project = project.first()
    
    if not project:
        raise ValueError(f"Project with id {project_id} not found or not accessible for slide operation.")

    # 计算 order_index (0-based) 从 slide_number (1-based)
    order_index = slide_data.slide_number - 1 if slide_data.slide_number is not None else 0

    db_slide = db.query(db_models.Slide).filter(
        db_models.Slide.project_id == project_id,
        db_models.Slide.order_index == order_index
    ).first()

    if db_slide:  # 更新
        db_slide.html_content = slide_data.html
        # 更新详细说明
        db_slide.detailed_instructions = slide_data.blueprint_json
    else:  # 创建
        db_slide = db_models.Slide(
            project_id=project_id,
            order_index=order_index,
            html_content=slide_data.html,
            detailed_instructions=slide_data.blueprint_json
        )
        db.add(db_slide)
    
    # 更新项目的最后修改时间
    project.last_modified = db_models.func.now()
    
    db.commit()
    db.refresh(db_slide)
    return db_slide

def delete_slide(db: Session, slide_id: str) -> bool:
    """删除特定幻灯片"""
    db_slide = get_slide(db, slide_id)
    if db_slide:
        db.delete(db_slide)
        db.commit()
        return True
    return False

def get_slides_by_project(db: Session, project_id: str) -> List[db_models.Slide]:
    """获取项目的所有幻灯片基本信息，不加载HTML内容"""
    return db.query(db_models.Slide)\
             .filter(db_models.Slide.project_id == project_id)\
             .order_by(db_models.Slide.order_index)\
             .all() 

# +++++++++++++++ 新增一个用于更新内容的函数 +++++++++++++++
def update_slide_content(db: Session, slide_id: str, update_data: dict) -> Optional[db_models.Slide]:
    """更新幻灯片的特定字段，如HTML内容"""
    db_slide = get_slide(db, slide_id)
    if not db_slide:
        return None
    
    for field, value in update_data.items():
        if hasattr(db_slide, field):
            setattr(db_slide, field, value)
    
    # 更新项目的最后修改时间
    if db_slide.project:
        db_slide.project.last_modified = func.now()

    db.commit()
    db.refresh(db_slide)
    return db_slide
# +++++++++++++++++++++++++++++++++++++++++++++++++++++++++