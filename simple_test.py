#!/usr/bin/env python3
import json
import sys
import os

# 添加backend路径到sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from app.models.presentation_model import DetailedSlideBlueprintSchema
    print("SUCCESS: 模型导入成功")
    
    # 生成JSON schema
    schema = DetailedSlideBlueprintSchema.model_json_schema()
    print("SUCCESS: Schema生成成功")
    
    # 检查key_elements中的final_chart_js_config
    key_elements = schema.get('properties', {}).get('key_elements', {})
    items = key_elements.get('items', {})
    item_properties = items.get('properties', {})
    final_chart_config = item_properties.get('final_chart_js_config', {})
    
    if final_chart_config:
        config_properties = final_chart_config.get('properties', {})
        if config_properties:
            print(f"SUCCESS: final_chart_js_config有 {len(config_properties)} 个properties")
            print(f"Properties: {list(config_properties.keys())}")
            
            # 检查required字段
            required = final_chart_config.get('required', [])
            print(f"Required fields: {required}")
            
            print("SUCCESS: Schema修复成功！")
        else:
            print("ERROR: final_chart_js_config的properties为空")
            sys.exit(1)
    else:
        print("ERROR: 找不到final_chart_js_config")
        sys.exit(1)
        
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
