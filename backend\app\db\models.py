# backend/app/db/models.py
from sqlalchemy.dialects.mysql import LONGTEXT 
import uuid
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON, Boolean, Float, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

# 创建一个基础模型类，所有模型都将继承它
Base = declarative_base()

def generate_uuid_str():
    return str(uuid.uuid4())

class User(Base):
    __tablename__ = "users"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    projects = relationship("Project", back_populates="owner", cascade="all, delete-orphan")
    feedbacks = relationship("InteractionFeedback", back_populates="user")

class Project(Base):
    __tablename__ = "projects"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    user_id = Column(String(36), ForeignKey("users.id"), nullable=True, index=True) # Nullable for anonymous or system projects initially
    title = Column(String(255), default="未命名演示")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_modified = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    total_slides_planned = Column(Integer, default=0)
    
    user_intent_data = Column(JSON, nullable=True)
    style_outline_data = Column(JSON, nullable=True)
    detected_language = Column(String(10), nullable=True)

    owner = relationship("User", back_populates="projects")
    chat_messages = relationship("ChatMessage", back_populates="project", cascade="all, delete-orphan", order_by="ChatMessage.timestamp")
    slides = relationship("Slide", back_populates="project", cascade="all, delete-orphan", order_by="Slide.order_index")
    llm_interactions = relationship("LLMInteractionLog", back_populates="project", cascade="all, delete-orphan")

    __table_args__ = (UniqueConstraint('user_id', 'title', name='_user_project_title_uc'),) # Optional: A user can't have two projects with the same title

class ChatMessage(Base):
    __tablename__ = "chat_messages"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String(36), ForeignKey("projects.id"), nullable=False, index=True)
    sender = Column(String(50), nullable=False)  # "user", "ai", "system"
    text = Column(Text, nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    icon = Column(String(50), nullable=True)
    # You can add more fields like 'message_type', 'tool_name' if needed from AiChatMessage

    project = relationship("Project", back_populates="chat_messages")

class Slide(Base):
    __tablename__ = "slides"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    # client_slide_id = Column(String(255), nullable=False, index=True) # e.g., "slide_1", "slide_2" - used by client
    project_id = Column(String(36), ForeignKey("projects.id"), nullable=False, index=True)
    order_index = Column(Integer, nullable=False)  # 0-based index for ordering
    
    html_content = Column(LONGTEXT, nullable=True)
    detailed_instructions = Column(Text, nullable=True)
    # 新增图片相关字段
    image_requests_json = Column(JSON, nullable=True)
    selected_image_infos_json = Column(JSON, nullable=True)
    
    # New: links to LLM interaction that generated this slide's HTML
    generating_interaction_log_id = Column(String(36), ForeignKey("llm_interaction_logs.id"), nullable=True, index=True)

    project = relationship("Project", back_populates="slides")

    __table_args__ = (UniqueConstraint('project_id', 'order_index', name='_project_slide_order_uc'),) # Each slide order in a project must be unique 

# --- New tables ---
class LLMInteractionLog(Base):
    __tablename__ = "llm_interaction_logs"
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String(36), ForeignKey("projects.id", ondelete="SET NULL"), nullable=True, index=True) # SET NULL if project deleted
    slide_id = Column(String(36), ForeignKey("slides.id", ondelete="SET NULL"), nullable=True, index=True) # SET NULL if slide deleted
    
    agent_name = Column(String(100), nullable=False, index=True)
    model_name = Column(String(100), nullable=True)
    prompt_name = Column(String(100), nullable=True) # e.g., "task", "system", "correction_attempt_1"
    prompt_version_tag = Column(String(255), nullable=True, index=True) # e.g., "user_intent/task_v1", 
    
    request_payload_preview = Column(LONGTEXT, nullable=True) # Changed from Text
    response_payload_preview = Column(LONGTEXT, nullable=True) # Changed from Text
    
    request_timestamp = Column(DateTime(timezone=True), server_default=func.now())
    response_timestamp = Column(DateTime(timezone=True), nullable=True)
    duration_ms = Column(Integer, nullable=True)
    
    error_message = Column(Text, nullable=True)
    
    pydantic_validated = Column(Boolean, nullable=True)
    html_checker_score = Column(Float, nullable=True) # 0-10
    
    is_retry = Column(Boolean, default=False)

    project = relationship("Project", back_populates="llm_interactions")
    feedbacks = relationship("InteractionFeedback", back_populates="llm_interaction", cascade="all, delete-orphan")

class InteractionFeedback(Base):
    __tablename__ = "interaction_feedback"
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    llm_interaction_log_id = Column(String(36), ForeignKey("llm_interaction_logs.id", ondelete="CASCADE"), nullable=False, index=True) # CASCADE delete with interaction
    user_id = Column(String(36), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True) # SET NULL if user deleted
    
    rating_type = Column(String(100), nullable=False, default="user_overall_rating_slide") # e.g., "user_visual_rating", "system_html_score"
    rating_value = Column(Float, nullable=True) # User rating 1-5, system score 0-10
    comment = Column(Text, nullable=True)
    feedback_source = Column(String(50), nullable=False, default="user") # "user", "system_html_checker"
    
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    llm_interaction = relationship("LLMInteractionLog", back_populates="feedbacks")
    user = relationship("User", back_populates="feedbacks")
# --- End new tables --- 