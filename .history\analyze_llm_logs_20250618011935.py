#!/usr/bin/env python3
"""
LLM交互记录分析脚本
用于分析特定项目的LLM调用记录，找出错误和优化点
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy.orm import Session
from backend.app.db.session import SessionLocal
from backend.app.db import models as db_models
from backend.app.crud import crud_project, crud_slide, crud_chat
from datetime import datetime
import json

def analyze_project_llm_logs(project_id: str):
    """分析指定项目的LLM交互记录"""
    
    db: Session = SessionLocal()
    
    try:
        print(f"🔍 分析项目 {project_id} 的LLM交互记录...")
        print("=" * 80)
        
        # 1. 获取项目基本信息
        project = crud_project.get_project(db, project_id=project_id)
        if not project:
            print(f"❌ 项目 {project_id} 不存在")
            return
        
        print(f"📋 项目信息:")
        print(f"   标题: {project.title}")
        print(f"   用户ID: {project.user_id or '匿名'}")
        print(f"   创建时间: {project.created_at}")
        print(f"   最后修改: {project.last_modified}")
        print(f"   计划幻灯片数: {project.total_slides_planned}")
        print(f"   实际幻灯片数: {len(project.slides)}")
        print(f"   检测语言: {project.detected_language or '未检测'}")
        print()
        
        # 2. 获取所有LLM交互记录
        llm_logs = db.query(db_models.LLMInteractionLog).filter(
            db_models.LLMInteractionLog.project_id == project_id
        ).order_by(db_models.LLMInteractionLog.timestamp).all()
        
        print(f"🤖 LLM交互记录总数: {len(llm_logs)}")
        print("=" * 80)
        
        # 3. 分析每个LLM调用
        success_count = 0
        error_count = 0
        agent_stats = {}
        model_stats = {}
        
        for i, log in enumerate(llm_logs, 1):
            print(f"\n📝 记录 {i}/{len(llm_logs)}")
            print(f"   时间: {log.request_timestamp}")
            print(f"   Agent: {log.agent_name or 'Unknown'}")
            print(f"   模型: {log.model_name or 'Unknown'}")
            
            # 计算状态（基于是否有错误信息）
            has_error = log.error_message is not None
            success = not has_error
            print(f"   状态: {'✅ 成功' if success else '❌ 失败'}")
            
            # 统计数据
            if success:
                success_count += 1
            else:
                error_count += 1
            
            agent_name = log.agent_name or 'Unknown'
            agent_stats[agent_name] = agent_stats.get(agent_name, 0) + 1
            
            model_name = log.model_name or 'Unknown'
            model_stats[model_name] = model_stats.get(model_name, 0) + 1
            
            # 显示提示词长度（从request_payload_preview）
            if log.request_payload_preview:
                print(f"   请求载荷长度: {len(log.request_payload_preview)} 字符")
                if len(log.request_payload_preview) > 4000:
                    print(f"   ⚠️  请求载荷较长，可能影响性能")
            
            # 显示响应信息（从response_payload_preview）
            if log.response_payload_preview:
                print(f"   响应载荷长度: {len(log.response_payload_preview)} 字符")
            
            if log.error_message:
                print(f"   ❌ 错误信息: {log.error_message}")
            
            # 分析响应时间
            if log.duration_ms:
                response_time = log.duration_ms / 1000.0  # 转换为秒
                print(f"   响应时间: {response_time:.2f}秒", end="")
                if response_time > 30:
                    print(" ⚠️ 响应时间较长")
                elif response_time > 60:
                    print(" ❌ 响应时间过长")
                else:
                    print()
            
            # 显示其他信息
            if log.prompt_name:
                print(f"   提示词名称: {log.prompt_name}")
            if log.prompt_version_tag:
                print(f"   提示词版本: {log.prompt_version_tag}")
            if log.pydantic_validated is not None:
                print(f"   Pydantic验证: {'✅' if log.pydantic_validated else '❌'}")
            if log.html_checker_score is not None:
                print(f"   HTML检查分数: {log.html_checker_score:.1f}/10")
            if log.is_retry:
                print(f"   🔄 重试调用")
            
            # 检查特定问题
            if log.request_payload_preview and log.agent_name:
                check_common_issues_updated(log)
        
        # 4. 生成统计报告
        print("\n" + "=" * 80)
        print("📊 统计报告")
        print("=" * 80)
        
        print(f"✅ 成功调用: {success_count}")
        print(f"❌ 失败调用: {error_count}")
        print(f"📈 成功率: {success_count/(success_count+error_count)*100:.1f}%")
        
        print(f"\n🤖 Agent统计:")
        for agent, count in sorted(agent_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   {agent}: {count} 次调用")
        
        print(f"\n🧠 模型统计:")
        for model, count in sorted(model_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   {model}: {count} 次调用")
        
        # 5. 获取聊天记录
        chat_messages = crud_chat.get_chat_messages_for_project(db, project_id=project_id)
        print(f"\n💬 聊天记录: {len(chat_messages)} 条消息")
        for msg in chat_messages:
            print(f"   [{msg.timestamp}] {msg.sender}: {msg.text[:100]}...")
        
        # 6. 获取幻灯片信息
        slides = db.query(db_models.Slide).filter(
            db_models.Slide.project_id == project_id
        ).order_by(db_models.Slide.order_index).all()
        
        print(f"\n🎯 幻灯片信息: {len(slides)} 张")
        for slide in slides:
            print(f"   幻灯片 {slide.order_index + 1}:")
            print(f"     HTML长度: {len(slide.html_content) if slide.html_content else 0} 字符")
            print(f"     有详细说明: {'是' if slide.detailed_instructions else '否'}")
            if slide.generating_interaction_log_id:
                print(f"     关联日志ID: {slide.generating_interaction_log_id}")
        
        # 7. 问题分析和建议
        print("\n" + "=" * 80)
        print("🔧 问题分析和优化建议")
        print("=" * 80)
        
        analyze_and_suggest_improvements(llm_logs, project, slides, chat_messages)
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

def check_common_issues_updated(log):
    """检查常见问题（更新版）"""
    issues = []
    
    # 检查超长请求载荷
    if log.request_payload_preview and len(log.request_payload_preview) > 6000:
        issues.append("请求载荷过长，可能影响模型性能")
    
    # 检查响应时间
    if log.duration_ms and log.duration_ms > 45000:  # 45秒
        issues.append("响应时间过长，可能需要优化提示词")
    
    # 检查错误模式
    if log.error_message:
        error_lower = log.error_message.lower()
        if "timeout" in error_lower:
            issues.append("超时错误，可能需要减少提示词复杂度")
        elif "rate limit" in error_lower:
            issues.append("速率限制错误，需要增加重试间隔")
        elif "invalid" in error_lower:
            issues.append("无效请求错误，检查输入格式")
        elif "optional" in error_lower:
            issues.append("可能存在Optional字段问题")
    
    # 检查Agent特定问题
    if log.agent_name == "VisualStyleAgent":
        if log.request_payload_preview and "Optional" in log.request_payload_preview:
            issues.append("可能存在Optional字段问题，建议使用简化Schema")
    
    # 检查Pydantic验证失败
    if log.pydantic_validated is False:
        issues.append("Pydantic验证失败，可能是Schema定义问题")
    
    # 检查HTML质量分数
    if log.html_checker_score is not None and log.html_checker_score < 6:
        issues.append(f"HTML质量分数较低 ({log.html_checker_score}/10)")
    
    if issues:
        for issue in issues:
            print(f"   ⚠️  {issue}")

def analyze_and_suggest_improvements(llm_logs, project, slides, chat_messages):
    """分析并提供改进建议"""
    
    suggestions = []
    
    # 1. 分析错误率
    error_logs = [log for log in llm_logs if log.error_message is not None]
    if error_logs:
        error_rate = len(error_logs) / len(llm_logs) * 100
        if error_rate > 20:
            suggestions.append(f"❌ 错误率过高 ({error_rate:.1f}%)，建议检查API配置和网络连接")
        
        # 分析错误类型
        error_types = {}
        for log in error_logs:
            if log.error_message:
                error_type = log.error_message.split(':')[0] if ':' in log.error_message else log.error_message
                error_types[error_type] = error_types.get(error_type, 0) + 1
        
        print("🔍 错误类型分析:")
        for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
            print(f"   {error_type}: {count} 次")
    
    # 2. 分析响应时间
    response_times = [log.response_time_seconds for log in llm_logs if log.response_time_seconds]
    if response_times:
        avg_time = sum(response_times) / len(response_times)
        max_time = max(response_times)
        if avg_time > 20:
            suggestions.append(f"⏰ 平均响应时间较长 ({avg_time:.1f}秒)，建议优化提示词")
        if max_time > 60:
            suggestions.append(f"⏰ 最长响应时间过长 ({max_time:.1f}秒)，可能需要分段处理")
    
    # 3. 分析幻灯片生成情况
    if slides:
        slides_with_content = [s for s in slides if s.html_content]
        if len(slides_with_content) < len(slides):
            suggestions.append(f"📄 有 {len(slides) - len(slides_with_content)} 张幻灯片缺少内容")
    
    # 4. 分析Agent使用模式
    agent_logs = {}
    for log in llm_logs:
        agent_name = log.agent_name or 'Unknown'
        if agent_name not in agent_logs:
            agent_logs[agent_name] = []
        agent_logs[agent_name].append(log)
    
    for agent_name, logs in agent_logs.items():
        agent_errors = [log for log in logs if not log.response_success]
        if agent_errors:
            error_rate = len(agent_errors) / len(logs) * 100
            if error_rate > 30:
                suggestions.append(f"🤖 {agent_name} 错误率高 ({error_rate:.1f}%)，需要检查该Agent的实现")
    
    # 5. 检查聊天记录完整性
    if not chat_messages:
        suggestions.append("💬 缺少聊天记录，用户交互可能没有正确保存")
    
    # 6. 检查数据库一致性
    slides_with_logs = [s for s in slides if s.generating_interaction_log_id]
    if len(slides_with_logs) < len(slides):
        suggestions.append(f"🔗 有 {len(slides) - len(slides_with_logs)} 张幻灯片缺少LLM日志关联")
    
    # 输出建议
    if suggestions:
        for suggestion in suggestions:
            print(f"💡 {suggestion}")
    else:
        print("✅ 未发现明显问题，项目运行良好！")
    
    # 具体优化建议
    print(f"\n🎯 具体优化建议:")
    print(f"1. 考虑使用更简化的Schema避免Optional字段问题")
    print(f"2. 增加重试机制处理网络超时")
    print(f"3. 优化提示词长度和复杂度")
    print(f"4. 添加更详细的错误处理和日志记录")
    print(f"5. 考虑分批处理大型请求")

if __name__ == "__main__":
    # 默认项目ID，可以通过命令行参数修改
    project_id = "13452850-3121-4ff0-9d47-710ccd000404"
    
    if len(sys.argv) > 1:
        project_id = sys.argv[1]
    
    print(f"🚀 开始分析项目: {project_id}")
    analyze_project_llm_logs(project_id) 