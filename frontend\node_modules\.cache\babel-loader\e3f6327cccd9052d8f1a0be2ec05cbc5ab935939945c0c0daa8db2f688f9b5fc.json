{"ast": null, "code": "'use strict';\n\n/* eslint-disable es/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/environment-v8-version');\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\nvar $String = globalThis.String;\n\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- required for testing\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol('symbol detection');\n  // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n  // nb: Do not call `String` directly to avoid this being optimized out to `symbol+''` which will,\n  // of course, fail.\n  return !$String(symbol) || !(Object(symbol) instanceof Symbol) ||\n  // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n  !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});", "map": {"version": 3, "names": ["V8_VERSION", "require", "fails", "globalThis", "$String", "String", "module", "exports", "Object", "getOwnPropertySymbols", "symbol", "Symbol", "sham"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/node_modules/core-js-pure/internals/symbol-constructor-detection.js"], "sourcesContent": ["'use strict';\n/* eslint-disable es/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/environment-v8-version');\nvar fails = require('../internals/fails');\nvar globalThis = require('../internals/global-this');\n\nvar $String = globalThis.String;\n\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- required for testing\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol('symbol detection');\n  // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n  // nb: Do not call `String` directly to avoid this being optimized out to `symbol+''` which will,\n  // of course, fail.\n  return !$String(symbol) || !(Object(symbol) instanceof Symbol) ||\n    // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n    !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});\n"], "mappings": "AAAA,YAAY;;AACZ;AACA,IAAIA,UAAU,GAAGC,OAAO,CAAC,qCAAqC,CAAC;AAC/D,IAAIC,KAAK,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIE,UAAU,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AAEpD,IAAIG,OAAO,GAAGD,UAAU,CAACE,MAAM;;AAE/B;AACAC,MAAM,CAACC,OAAO,GAAG,CAAC,CAACC,MAAM,CAACC,qBAAqB,IAAI,CAACP,KAAK,CAAC,YAAY;EACpE,IAAIQ,MAAM,GAAGC,MAAM,CAAC,kBAAkB,CAAC;EACvC;EACA;EACA;EACA;EACA,OAAO,CAACP,OAAO,CAACM,MAAM,CAAC,IAAI,EAAEF,MAAM,CAACE,MAAM,CAAC,YAAYC,MAAM,CAAC;EAC5D;EACA,CAACA,MAAM,CAACC,IAAI,IAAIZ,UAAU,IAAIA,UAAU,GAAG,EAAE;AACjD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}