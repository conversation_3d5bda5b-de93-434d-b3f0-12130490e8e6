// frontend/src/App.js
import React, { useState, useEffect, useCallback, useRef, lazy, Suspense } from 'react';
import { BrowserRouter, Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import LeftNav from './components/LeftNav';
import MiddlePane from './components/MiddlePane';
import RightPane from './components/RightPane';
import { FaFilePdf, FaGlobe, FaFeatherAlt } from 'react-icons/fa';
import apiService from './services/api';

// Lazy load components that are not needed for initial render
const InlineTextEditor = lazy(() => import('./components/InlineTextEditor'));
const SlidePlayerView = lazy(() => import('./views/SlidePlayerView'));
const FullScreenPlayer = lazy(() => import('./components/FullScreenPlayer'));

const ChatViewPage = lazy(() => import('./views/ChatViewPage'));

// MainApp component that contains all the existing functionality
const MainApp = () => {
  const navigate = useNavigate();
  const location = useLocation(); // 获取 location 对象
  const [isLoading, setIsLoading] = useState(true); // 主加载状态（整体应用）
  const [isLoadingHistory, setIsLoadingHistory] = useState(false); // 项目历史加载状态
  const [isTaskListOpen, setIsTaskListOpen] = useState(false);
  const [currentPresentationTitle, setCurrentPresentationTitle] = useState("AI 幻灯片");
  const [chatTitle, setChatTitle] = useState("AI 幻灯片");
  const [isGenerating, setIsGenerating] = useState(false);
  const [chatMessages, setChatMessages] = useState([]);
  const [currentSlides, setCurrentSlides] = useState([]);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [editingTextElement, setEditingTextElement] = useState(null);
  const [showSearchResultsModal, setShowSearchResultsModal] = useState(false);
  const [currentSearchResults, setCurrentSearchResults] = useState([]);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [currentFullScreenSlideIndex, setCurrentFullScreenSlideIndex] = useState(0);
  const [isEditingMode, setIsEditingMode] = useState(false); // 这个是App层面的编辑模式（例如NLP命令，或是否启用InlineTextEditor）
  const [chatHistory, setChatHistory] = useState([]); // 存储历史聊天记录摘要
  const eventSourceRef = useRef(null);
  
  // 新增状态
  const [tools, setTools] = useState([]);
  const [currentStep, setCurrentStep] = useState(null);
  const [outlineContent, setOutlineContent] = useState('');
  const [slideCode, setSlideCode] = useState('');
  const [activeStep, setActiveStep] = useState('preview');
  const [showHistoryDrawer, setShowHistoryDrawer] = useState(false);
  const [totalSlideCount, setTotalSlideCount] = useState(0);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [deleteItemInfo, setDeleteItemInfo] = useState(null);
  const [slideTabIndices, setSlideTabIndices] = useState({});
  const [currentProjectId, setCurrentProjectId] = useState(null);
  const isNavigatingToNew = useRef(false);
  const [contextMenu, setContextMenu] = useState(null);
  const [editModal, setEditModal] = useState(null);
  const [editInstruction, setEditInstruction] = useState('');
  const [isEditingElement, setIsEditingElement] = useState(false); // 防止重复提交
  
  // 用于播放器全屏的切换逻辑
  const toggleFullScreen = useCallback(async () => {
    // 检查当前是否处于浏览器全屏模式
    const isInFullScreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement);
    
    try {
      if (!isInFullScreen) {
        // 如果当前不处于全屏，则请求进入全屏
        // 准备进入全屏时，设置好初始索引
        if (currentSlides.length > 0) {
          setCurrentFullScreenSlideIndex(currentSlideIndex);
        }
        // 调用浏览器原生 API
        await document.documentElement.requestFullscreen();
        // isFullScreen 状态将由下面的 'fullscreenchange' 事件监听器更新
      } else {
        // 如果当前已处于全屏，则退出全屏
        await document.exitFullscreen();
        // isFullScreen 状态将由下面的 'fullscreenchange' 事件监听器更新
      }
    } catch (error) {
      console.error("全屏操作失败:", error);
      // 如果原生API失败，作为备用方案，仍然显示我们的模拟全屏组件
      setIsFullScreen(prev => !prev);
    }
  }, [currentSlideIndex, currentSlides.length]);

  // 定义 saveCurrentChatToHistory 函数，确保在适当的时机调用
  const saveCurrentChatToHistory = useCallback(() => {
    // 仅当有实质内容时才保存到历史列表
    if (chatMessages.length > 1 && currentProjectId) { // 确保有 project_id
      const currentSession = {
        id: currentProjectId, // 使用 project_id 作为历史记录的唯一 ID
        title: chatTitle || `演示 (${currentProjectId.slice(-4)})`, // 确保有标题
        messages: chatMessages,
        slides: currentSlides,
        timestamp: new Date().toISOString(),
        project_id: currentProjectId
      };
      
      setChatHistory(prevHistory => {
        const existingSessionIndex = prevHistory.findIndex(item => item.id === currentProjectId);
        let updatedHistory;
        if (existingSessionIndex >= 0) {
            updatedHistory = [...prevHistory];
            updatedHistory[existingSessionIndex] = currentSession; // 更新现有会话
          } else {
            updatedHistory = [currentSession, ...prevHistory]; // 添加新会话
        }
        // 限制历史记录数量（例如最近20条）
        // updatedHistory = updatedHistory.slice(0, 20); 
        try {
          localStorage.setItem('chatHistory', JSON.stringify(updatedHistory));
        } catch (error) {
            console.error("保存聊天历史列表到 localStorage 失败:", error);
        }
        return updatedHistory;
      });
    }
  }, [chatMessages, currentSlides, chatTitle, currentProjectId, setChatHistory]); // 添加 currentProjectId 依赖
  
  // 新建演示文稿的函数
  const handleNewPresentation = useCallback(() => {
    isNavigatingToNew.current = true; // 在导航前设置标志
    
    // 清空当前状态
    setChatMessages([]);
    setCurrentSlides([]);
    setChatTitle("AI 幻灯片");
    setCurrentPresentationTitle("AI 幻灯片");
    setIsGenerating(false);
    setCurrentStep(null);
    setTools([]);
    setOutlineContent('');
    setSlideCode('');
    setActiveStep('preview');
    
    // 清除项目ID
    setCurrentProjectId(null); 
    localStorage.removeItem('currentProjectId');
    
    // 导航到根路径，清除URL中的projectId
    navigate('/');
  }, [
    setChatMessages, setCurrentSlides, setChatTitle, setCurrentPresentationTitle,
    setIsGenerating, setCurrentStep, setTools, setOutlineContent, setSlideCode,
    setActiveStep, setCurrentProjectId, navigate
  ]);

  // 专门用于加载历史列表的函数
  const loadProjectHistory = useCallback(async () => {
    setIsLoadingHistory(true);
    try {
      const serverHistory = await apiService.getProjectsHistory();
      console.log("[App.js] Fetched project history summaries:", serverHistory);

      const formattedSummaries = serverHistory.map(proj => ({
        id: proj.id, // 修复：使用后端返回的 'id' 字段
        title: proj.title || `项目 (${proj.id ? proj.id.slice(-4) : '未知'})`, // 修复：使用 'id' 并增加健壮性检查
        timestamp: proj.last_modified || proj.created_at,
        project_id: proj.id, // 修复：使用 'id' 字段来填充 project_id
        isSummary: true // 标记为摘要
      }));

      setChatHistory(formattedSummaries);
      
      // 清理无效的 localStorage.currentProjectId
      const serverProjectIds = new Set(serverHistory.map(p => p.project_id));
      const storedId = localStorage.getItem('currentProjectId');
      if (storedId && !serverProjectIds.has(storedId)) {
        localStorage.removeItem('currentProjectId');
      }

    } catch (error) {
      console.error("加载项目历史失败:", error);
      setChatHistory([]); // 出错时清空
    } finally {
      setIsLoadingHistory(false);
    }
  }, [setChatHistory]);

  // loadProjectDetails 函数现在是加载单个项目详情的唯一入口
  const loadProjectDetails = useCallback(async (projectId) => {
    // 增加对无效projectId的严格检查，防止API调用失败
    if (!projectId || projectId === 'undefined') {
      console.warn("尝试加载一个无效的项目ID，已中止。", projectId);
      setIsLoading(false); // 确保结束加载状态
      handleNewPresentation(); // 重置到一个安全的新建页面
      return;
    }
    
    setIsLoading(true);
    
    try {
      const details = await apiService.getProjectDetails(projectId);
      console.log(`[App.js] Fetched details for project ${projectId}:`, details);

      // 确保聊天历史有数据，必要时进行格式化处理
      let chatHistoryData = details.chat_history || [];
      
      // 确保每条消息都有正确的格式和ID
      if (chatHistoryData.length > 0) {
        chatHistoryData = chatHistoryData.map((msg, index) => ({
          ...msg,
          id: msg.id || `msg-${index}-${Date.now()}`,
          sender: msg.sender || (index % 2 === 0 ? 'user' : 'ai'),
          text: msg.text || msg.content || '无内容',
          timestamp: msg.timestamp || new Date().toISOString()
        }));
        console.log(`[App.js] 处理后的聊天历史数据:`, chatHistoryData);
      } else {
        // 如果没有聊天历史，添加一条更有信息量的系统消息
        chatHistoryData = [{
          id: `system-${Date.now()}`,
          sender: 'system',
          text: `该项目"${details.title || '未命名项目'}"已加载，但没有找到聊天记录。这可能是因为：
1. 项目是通过其他方式创建的
2. 聊天记录可能已被清除
3. 数据库中项目ID为 ${projectId} 的聊天记录表(chat_messages)可能为空

您可以开始新的对话，系统会保留当前项目的幻灯片内容。`,
          timestamp: new Date().toISOString(),
          icon: 'ℹ️'
        }];
      }
      
      setChatMessages(chatHistoryData);
      const displayTitle = details.title || `项目 (${projectId.slice(-4)})`;
      setChatTitle(displayTitle);
      setCurrentPresentationTitle(displayTitle);
      setCurrentProjectId(details.project_id);
      
      // 保存到localStorage以便在刷新后恢复
      localStorage.setItem('currentProjectId', details.project_id);
      
      // 确保URL与当前加载的项目ID同步
      if (!location.search.includes(`projectId=${details.project_id}`)) {
        navigate(`/?projectId=${details.project_id}`, { replace: true });
      }

      setTotalSlideCount(details.total_slides_planned || (details.slides || []).length);
      
      // 懒加载幻灯片内容
      if (details.slides && details.slides.length > 0) {
        const slidesWithMetadata = details.slides.map(s => ({
            id: s.id,
            html: '',
            code: '',
            order: s.slide_number - 1,
            title: `幻灯片 ${s.slide_number}`,
            isLoading: true,
        }));
        setCurrentSlides(slidesWithMetadata);

        const slideContentPromises = details.slides.map(slideMeta =>
            apiService.getSlideContent(slideMeta.id).catch(() => ({ id: slideMeta.id, error: true }))
        );
        const fullSlidesData = await Promise.all(slideContentPromises);

        setCurrentSlides(currentSlides => currentSlides.map(metaSlide => {
            const fullData = fullSlidesData.find(fs => fs.id === metaSlide.id);
            if (fullData && !fullData.error) {
                return { ...metaSlide, html: fullData.html, code: fullData.html, isLoading: false };
            }
            return { ...metaSlide, html: '<div>内容加载失败</div>', code: '/* 内容加载失败 */', isLoading: false };
        }));
      } else {
        setCurrentSlides([]);
      }

    } catch (error) {
      console.error(`加载项目 ${projectId} 详情失败:`, error);
      if (error.message && (error.message.includes('not found') || error.message.includes('permission'))) {
        // 如果项目不存在或无权限，从localStorage移除该项目ID
        if (localStorage.getItem('currentProjectId') === projectId) {
          localStorage.removeItem('currentProjectId');
        }
        handleNewPresentation();
      }
    } finally {
      setIsLoading(false);
    }
  }, [
    setIsLoading, handleNewPresentation,
    setChatMessages, setChatTitle, setCurrentPresentationTitle, setCurrentProjectId,
    location.search, navigate, setTotalSlideCount, setCurrentSlides
  ]);

  // 初始加载和恢复会话的 useEffect
  useEffect(() => {
    const initializeApp = async () => {
      setIsLoading(true);
      
      // 1. 总是先加载历史项目列表
      await loadProjectHistory();

      // 2. 决定要加载哪个项目的详情
      const params = new URLSearchParams(location.search);
      let projectIdToLoad = params.get('projectId');
      
      // 如果URL没有ID，尝试从localStorage获取
      if (!projectIdToLoad || projectIdToLoad === 'undefined') {
        projectIdToLoad = localStorage.getItem('currentProjectId');
      }
      
      if (projectIdToLoad && projectIdToLoad !== 'undefined') {
        // 如果有项目ID（来自URL或localStorage），加载它
        console.log(`[App Init] Resuming session for project: ${projectIdToLoad}`);
        await loadProjectDetails(projectIdToLoad);
      } else {
        // 如果我们是因为点击"新建"而导航到这里的，
        // 只需要重置标志位，然后停止执行。
        if (isNavigatingToNew.current) {
          console.log("[App Init] Navigating to a new presentation. Skipping history load.");
          isNavigatingToNew.current = false;
          setIsLoading(false); // 确保结束加载状态
          return; // 终止此 effect 的后续执行
        }
        
        // 否则，加载最新的项目（如果存在）
        const history = await apiService.getProjectsHistory(); // 再次获取以确保最新
        if (history && history.length > 0) {
          const latestProject = history.sort((a, b) => new Date(b.last_modified) - new Date(a.last_modified))[0];
          // 确保在导航前，最新的项目和其ID是有效的
          if (latestProject && latestProject.project_id) {
            console.log(`[App Init] Loading most recent project: ${latestProject.project_id}`);
            await loadProjectDetails(latestProject.project_id);
          } else {
            // 如果最新项目无效，则新建一个
            console.log("[App Init] No valid recent project found. Creating new presentation.");
            handleNewPresentation();
          }
        } else {
          // 没有历史记录，创建一个新演示
          console.log("[App Init] No project history found. Creating new presentation.");
          handleNewPresentation();
        }
      }
      setIsLoading(false);
    };

    initializeApp();
  }, [location.search, loadProjectHistory, loadProjectDetails, handleNewPresentation, setIsLoading]);
  
  // 自动保存当前会话到历史记录 - 禁用连续更新，只保留页面离开时的保存
  useEffect(() => {
    // 这个效果不再每次状态变化就执行，现在完全依赖beforeunload事件
    console.log("Session management setup");
    
    // 页面即将卸载时保存会话
    const handleBeforeUnload = () => {
      // 只保存当前项目ID，不再保存完整的chatHistory
      if (currentProjectId) {
        localStorage.setItem('currentProjectId', currentProjectId);
      }
    };
    
    // 监听页面即将离开事件
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    // 清理函数
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [currentProjectId]); // 只依赖currentProjectId

  // 仅在特定状态变化时保存到localStorage，但不更新chatHistory状态
  useEffect(() => {
    // 只有当有足够的内容时才保存到localStorage
    if (chatMessages.length > 1 && currentSlides.length > 0) {
      try {
        localStorage.setItem('chatMessages', JSON.stringify(chatMessages));
        localStorage.setItem('currentSlides', JSON.stringify(currentSlides));
        localStorage.setItem('chatTitle', chatTitle);
        localStorage.setItem('presentationTitle', currentPresentationTitle);
        
        // 更新currentProjectId (如果消息中有)
        const projectIdFromMessages = chatMessages.find(msg => msg.project_id)?.project_id;
        if (projectIdFromMessages) {
          setCurrentProjectId(projectIdFromMessages);
          localStorage.setItem('currentProjectId', projectIdFromMessages);
        }
      } catch (error) {
        console.error("保存会话到localStorage失败:", error);
      }
    }
  }, [chatMessages, currentSlides, chatTitle, currentPresentationTitle]);

  const toggleTaskList = () => setIsTaskListOpen(!isTaskListOpen);

  const handleChatTitleChange = async (newTitle) => {
    const originalTitle = chatTitle; // 在更新前保存原始标题
    
    // 乐观更新UI，让用户立即看到变化
    setChatTitle(newTitle);
    setCurrentPresentationTitle(newTitle);
    
    if (currentProjectId) {
      try {
        await apiService.updateProject(currentProjectId, { title: newTitle });
        console.log(`[App] 项目 ${currentProjectId} 的标题已成功更新到数据库: ${newTitle}`);
        
        setChatHistory(prev => prev.map(item => 
          item.project_id === currentProjectId ? { ...item, title: newTitle } : item
        ));
      } catch (error) {
        console.error("更新项目标题失败:", error);
        alert(`标题更新失败: ${error.message}`);
        
        // --- BUG修复 ---
        // 如果API调用失败，将UI恢复到原始标题
        setChatTitle(originalTitle);
        setCurrentPresentationTitle(originalTitle);
      }
    }
  };

  const handleBackClick = () => {
    if (isGenerating) {
      // 如果正在生成，取消生成
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
      setIsGenerating(false);
      setChatMessages(prev => [...prev, {
        id: Date.now(),
        type: 'ai_error',
        text: '生成已取消。',
        sender: 'system', // 明确sender为system
        icon: '❌' // 添加一个图标
      }]);
      return;
    }
    
    // 如果没有在生成，则返回首页
    handleNewPresentation(); // <-- 修复后的正确逻辑
  };
  
  const handleSelectHistory = (historyItem) => {
    if (!historyItem || !historyItem.project_id) {
        console.error("无效的历史项目，缺少project_id:", historyItem);
        return; // 中止操作
    }
    console.log("选择历史项目:", historyItem);
    setShowHistoryDrawer(false); // 关闭抽屉
    
    // 关键：只进行导航，让 useEffect 钩子去处理数据的加载
    navigate(`/?projectId=${historyItem.project_id}`);
  };

  const handleConfirmDelete = async () => {
    if (!deleteItemInfo || !deleteItemInfo.projectId) {
      setShowDeleteConfirmModal(false);
      setDeleteItemInfo(null);
      return;
    }

    const { id: localHistoryItemId, projectId: serverProjectId } = deleteItemInfo;

    try {
      // 1. 调用后端 API 删除服务器端数据
      const response = await apiService.deleteProject(serverProjectId);
      console.log(`项目 ${serverProjectId} 删除响应:`, response);

      // 2. 更新本地 chatHistory 状态
      const updatedHistory = chatHistory.filter(item => item.id !== localHistoryItemId);
      setChatHistory(updatedHistory);
      
      // 3. 如果删除的是当前加载的会话，则重置视图
      if (currentProjectId === serverProjectId) {
        handleNewPresentation(); 
      }
      console.log(`项目 ${serverProjectId} (本地历史项ID: ${localHistoryItemId}) 已成功删除。`);

    } catch (error) {
      console.error(`删除项目 ${serverProjectId} 时出错:`, error);
      alert(`删除项目失败: ${error.message}`);
    } finally {
      setShowDeleteConfirmModal(false);
      setDeleteItemInfo(null);
    }
  };

  const handleToggleEditSlide = () => {
    setIsEditingMode(!isEditingMode);
    setEditingTextElement(null);
  };

  // Text Editing Handlers
  /*
  const handleTextEditStart = useCallback((slideId, elementId, initialText, initialStyle, position) => {
    // Adjust position relative to the overall RightPane or a more stable parent
    const rightPaneContentArea = document.querySelector('.right-pane-content-area');
    let editorX = position.x;
    let editorY = position.y;

    if (rightPaneContentArea) {
      const paneRect = rightPaneContentArea.getBoundingClientRect();
      editorX = position.x + paneRect.left;
      editorY = position.y + paneRect.top - rightPaneContentArea.scrollTop;
    }
    setEditingTextElement({ slideId, elementId, initialText, initialStyle, position: {x: editorX, y: editorY} });
  }, []);
  */

  const handleTextEditSave = (newText, newStyle) => {
    if (!editingTextElement) return;
    const { slideId, elementId } = editingTextElement;
    setCurrentSlides(prevSlides =>
      prevSlides.map(slide => {
        if (slide.id === slideId) {
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = slide.html;
          const targetElement = tempDiv.querySelector(`[data-editable-id="${elementId}"]`);
          if (targetElement) {
            targetElement.innerText = newText;
            Object.keys(newStyle).forEach(key => {
              targetElement.style[key] = newStyle[key];
            });
          }
          return { ...slide, html: tempDiv.innerHTML, code: tempDiv.innerHTML };
        }
        return slide;
      })
    );
    setEditingTextElement(null);
  };

  const handleTextEditCancel = () => setEditingTextElement(null);

  // +++++++++++++++ 新增处理函数 +++++++++++++++
  const handleRewriteClick = (e) => {
    e.stopPropagation();
    if (contextMenu) {
      setEditModal({ slideId: contextMenu.slideId, elementId: contextMenu.elementId });
      setContextMenu(null); // 关闭上下文菜单
    }
  };

  const handleCloseEditModal = () => {
    setEditModal(null);
    setEditInstruction('');
    setIsEditingElement(false);
  };

  const handleSubmitEdit = async () => {
    if (!editInstruction.trim() || !editModal) return;
    
    setIsEditingElement(true);
    const { slideId, elementId } = editModal;
    
    try {
      const result = await apiService.editSlideElement(slideId, `[data-editable-id="${elementId}"]`, editInstruction);
      if (result && result.html) {
        // 更新幻灯片状态
        setCurrentSlides(prevSlides =>
          prevSlides.map(slide => 
            slide.id === slideId ? { ...slide, html: result.html, code: result.html } : slide
          )
        );
      }
    } catch (error) {
      console.error('编辑元素失败:', error);
      alert(`编辑失败: ${error.message}`);
    } finally {
      handleCloseEditModal();
    }
  };
  // +++++++++++++++++++++++++++++++++++++++++++++

  // Handle opening the player view
  const handleOpenViewAndExport = () => {
    navigate('/player', { 
      state: { 
        slides: currentSlides,
        initialIndex: 0,
        presentationTitle: currentPresentationTitle 
      } 
    });
  };
  
  // Handle messages from SlidePlayerView
  useEffect(() => {
    const handleMessageFromPlayerView = (event) => {
      if (event.data && event.data.type === 'request-fullscreen') {
        // Set the appropriate slide index if provided
        if (event.data.payload && event.data.payload.initialIndex !== undefined) {
          setCurrentFullScreenSlideIndex(event.data.payload.initialIndex);
        }
        // Toggle fullscreen
        toggleFullScreen();
      }
    };

    window.addEventListener('message', handleMessageFromPlayerView);
    return () => {
      window.removeEventListener('message', handleMessageFromPlayerView);
    };
  }, [toggleFullScreen]);
  
  // +++++++++++++++ 新增useEffect来处理来自iframe的消息 +++++++++++++++
  useEffect(() => {
    const handleIframeMessage = (event) => {
      if (event.data && event.data.type === 'element_clicked' && isEditingMode) {
        const { slideId, elementId, clickPosition } = event.data.payload;
        
        // 找到对应的iframe，计算屏幕上的绝对位置
        const iframe = document.querySelector(`#slide-preview-${slideId} iframe`);
        if (iframe) {
          const iframeRect = iframe.getBoundingClientRect();
          const scale = iframeRect.width / 1280; // 假设设计宽度是1280
          
          const absoluteX = iframeRect.left + (clickPosition.x * scale) + window.scrollX;
          const absoluteY = iframeRect.top + (clickPosition.y * scale) + window.scrollY;
          
          setContextMenu({
            x: absoluteX,
            y: absoluteY,
            slideId,
            elementId
          });
        }
      }
    };

    window.addEventListener('message', handleIframeMessage);
    // 点击其他地方时关闭菜单
    const closeMenu = () => setContextMenu(null);
    window.addEventListener('click', closeMenu);

    return () => {
      window.removeEventListener('message', handleIframeMessage);
      window.removeEventListener('click', closeMenu);
    };
  }, [isEditingMode]); // 只在编辑模式下监听
  // ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
  
  useEffect(() => {
    // 这个 useEffect 现在是同步 isFullScreen 状态的唯一来源
    const handleFullscreenChange = () => {
      const isCurrentlyFullScreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullscreenElement || document.msFullscreenElement);
      setIsFullScreen(isCurrentlyFullScreen);
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // 用于 RightPane 设置当前活动/聚焦的幻灯片
  const handleSetCurrentSlideIndex = (index) => {
    setCurrentSlideIndex(index);
  };

  const handleSlideTabChange = (slideIndex, tabIndex) => {
    setSlideTabIndices(prev => ({
      ...prev,
      [slideIndex]: tabIndex
    }));
  };

  // 用于播放特定索引的幻灯片
  const handlePlaySlideAtIndex = (index) => {
    setCurrentFullScreenSlideIndex(index);
    toggleFullScreen();
  };

  const handleSendMessage = async (messageData) => {
    // 处理参数：可能是字符串或对象
    let messageText, files = [];
    
    if (typeof messageData === 'string') {
      messageText = messageData;
    } else if (typeof messageData === 'object' && messageData !== null) {
      messageText = messageData.message || '';
      files = messageData.files || [];
    } else {
      messageText = '';
    }

    if (!messageText.trim() || isGenerating) return;

    // 生成独特的消息ID
    const userMsgId = `user-msg-${Date.now()}`;
    
    // 添加用户消息到聊天
    const userMessage = {
      id: userMsgId,
      type: 'user',
      text: messageText,
      files: files.length > 0 ? files : undefined, // 只在有文件时添加files属性
    };
    
    // 判断是否是"继续"生成请求
    const isContinueRequest = messageText.toLowerCase().includes("继续") || 
                             messageText.toLowerCase().includes("continue");
    
    // 【核心修改1】优化新生成请求时的状态清空逻辑
    if (!isContinueRequest) {
      // 对于新的演示文稿生成请求，清除所有之前的AI消息，并重置项目相关状态
      setChatMessages([userMessage]); // 仅保留当前用户消息
      setCurrentSlides([]);
      setTotalSlideCount(0);
      setCurrentProjectId(null); // 这将强制后端创建一个新项目
      
      // 重置其他UI相关状态，以获得一个全新的开始
    setCurrentStep(null);
      setTools([]);
    setOutlineContent('');
    setSlideCode('');
    setActiveStep('preview');
    } else {
      // 对于"继续"请求，仅追加新的用户消息
    setChatMessages(prev => [...prev, userMessage]);
    }
    
    let retryAttempts = 0;
    const MAX_MESSAGE_RETRIES = 3;

    const executeGeneration = async (projectIdToUse) => {
      try {
        // 设置生成状态为true
        setIsGenerating(true);
                
        // 关闭之前的EventSource连接（如果存在）
        if (eventSourceRef.current) {
          eventSourceRef.current.close();
        }
        
        // 初始化临时幻灯片数组 (这个现在不再由前端维护，后端SSE会直接推送)
        // let tempSlidesData = []; 
        
        // 创建消息ID映射，用于后续更新或替换特定消息
        const messageIdMap = {};
        
        // 决定使用哪个 project_id
        // `projectIdToUse` 会是 `null` (新生成) 或 `currentProjectId` (继续生成)
        const effectiveProjectId = projectIdToUse; 
        
        // 使用新的简化工作流API
        eventSourceRef.current = await apiService.generatePresentation(
          messageText,
          // 进度处理
          (progressData) => {
            // console.log("进度更新:", progressData);
            
            // 处理 id_mapped_to_client 事件 - 当临时ID被映射到实际ID时
            if (progressData.status === 'id_mapped_to_client' && progressData.metadata && progressData.metadata.actual_project_id) {
              const newActualId = progressData.metadata.actual_project_id;
              console.log(`[App.js] 临时项目ID已映射到实际ID: ${newActualId}`);
              setCurrentProjectId(newActualId);
              localStorage.setItem('currentProjectId', newActualId);
              // 更新URL，但不触发页面重新加载
              navigate(`/?projectId=${newActualId}`, { replace: true });
              return; // 不需要进一步处理这个事件
            }
            
            // 保存项目ID - 如果这是首次获取项目ID (且不是 id_mapped_to_client 消息)
            if (!currentProjectId && progressData.project_id && progressData.status !== 'id_mapping') {
              console.log("[App.js] 从SSE获取到项目ID:", progressData.project_id);
              setCurrentProjectId(progressData.project_id);
              localStorage.setItem('currentProjectId', progressData.project_id); // 添加这行
            }
            
            // 如果进度正常，重置重试次数
            retryAttempts = 0;
            
            // 更新AI消息
            if (progressData.message) {
              const messageText = progressData.message.text;
              
              // 跳过初始化和连接建立消息
              if (messageText === '⏳正在解析您的请求，AI引擎启动中...' || 
                  messageText === '🔌连接已建立，等待更新..') {
                return;
              }
              
              // 检查是否是需要替换的消息类型
              let shouldReplace = false;
              let messageToReplaceId = null;
              let additionalInfo = '';
              
              // 处理风格确定消息
              if (messageText.includes('的整体风格已确定')) {
                // 不再替换之前的消息，而是添加新的完成消息
                const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
                
                // 计算实际用时
                let additionalInfo = '';
                const startTime = messageIdMap['style_waiting_start_time'];
                if (startTime) {
                  const elapsedTime = Date.now() - startTime;
                  const seconds = Math.floor(elapsedTime / 1000);
                  if (seconds < 60) {
                    additionalInfo = `（实际用时${seconds}秒）`;
                  } else {
                    const minutes = Math.floor(seconds / 60);
                    const remainingSeconds = seconds % 60;
                    additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;
                  }
                }
                
                setChatMessages(prev => [
                  ...prev, 
                  { 
                    id: newMsgId, 
                    type: 'ai', 
                    text: additionalInfo ? `${messageText} ${additionalInfo}` : messageText,
                    icon: progressData.message.icon || undefined,
                    thinking: progressData.message.thinking || false,
                    sender: progressData.message.sender || 'ai'
                  }
                ]);
                return; // 直接返回，避免重复添加
              }
              
              // 处理内容规划完成消息
              if (messageText.includes('内容规划完成：共')) {
                const contentPlanningMsgId = messageIdMap['content_planning'];
                if (contentPlanningMsgId) {
                  messageToReplaceId = contentPlanningMsgId;
                  shouldReplace = true;
                  
                  // 计算实际用时
                  const startTime = messageIdMap['content_planning_start_time'];
                  if (startTime) {
                    const elapsedTime = Date.now() - startTime;
                    const seconds = Math.floor(elapsedTime / 1000);
                    if (seconds < 60) {
                      additionalInfo = `（实际用时${seconds}秒）`;
                    } else {
                      const minutes = Math.floor(seconds / 60);
                      const remainingSeconds = seconds % 60;
                      additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;
                    }
                  }
                }
              }
              
              // 处理幻灯片生成完成消息
              const slideGenCompleteRegex = /第 (\d+)\/(\d+) 张.*生成完毕/;
              const slideMatch = messageText.match(slideGenCompleteRegex);
              if (slideMatch) {
                const slideNum = slideMatch[1];
                const slideProcessingMsgId = messageIdMap[`slide_processing_${slideNum}`];
                if (slideProcessingMsgId) {
                  messageToReplaceId = slideProcessingMsgId;
                  shouldReplace = true;
                  
                  // 计算实际用时
                  const startTime = messageIdMap[`slide_processing_${slideNum}_start_time`];
                  if (startTime) {
                    const elapsedTime = Date.now() - startTime;
                    const seconds = Math.floor(elapsedTime / 1000);
                    if (seconds < 60) {
                      additionalInfo = `（实际用时${seconds}秒）`;
                    } else {
                      const minutes = Math.floor(seconds / 60);
                      const remainingSeconds = seconds % 60;
                      additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;
                    }
                  }
                }
              }
              
              // 记录特殊消息的ID和时间戳，用于后续替换
              if (messageText.includes('正在为') && messageText.includes('规划整体视觉风格')) {
                const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
                messageIdMap['style_waiting'] = newMsgId;
                messageIdMap['style_waiting_start_time'] = Date.now();
                
                // 创建新消息
                setChatMessages(prev => [
                  ...prev, 
                  { 
                    id: newMsgId, 
                    type: 'ai', 
                    text: messageText,
                    icon: progressData.message.icon || undefined,
                    thinking: progressData.message.thinking || false, // 使用 thinking 字段
                    sender: progressData.message.sender || 'ai' // 确保sender正确
                  }
                ]);
                return; // 直接返回，避免重复添加
              }
              
              if (messageText.includes('正在为') && messageText.includes('规划详细内容')) {
                const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
                messageIdMap['content_planning'] = newMsgId;
                messageIdMap['content_planning_start_time'] = Date.now();
                
                // 创建新消息
                setChatMessages(prev => [
                  ...prev, 
                  { 
                    id: newMsgId, 
                    type: 'ai', 
                    text: messageText,
                    icon: progressData.message.icon || undefined,
                    thinking: progressData.message.thinking || false, // 使用 thinking 字段
                    sender: progressData.message.sender || 'ai'
                  }
                ]);
                return; // 直接返回，避免重复添加
              }
              
              const slideProcessingRegex = /开始处理第 (\d+)\/(\d+) 张幻灯片/;
              const match = messageText.match(slideProcessingRegex);
              if (match) {
                const slideNum = match[1];
                const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
                messageIdMap[`slide_processing_${slideNum}`] = newMsgId;
                messageIdMap[`slide_processing_${slideNum}_start_time`] = Date.now();
                
                // 创建新消息
                setChatMessages(prev => [
                  ...prev, 
                  { 
                    id: newMsgId, 
                    type: 'ai', 
                    text: messageText,
                    icon: progressData.message.icon || undefined,
                    thinking: progressData.message.thinking || false, // 使用 thinking 字段
                    sender: progressData.message.sender || 'ai'
                  }
                ]);
                return; // 直接返回，避免重复添加
              }
              
              // 创建新消息或替换现有消息
              const newAiMsgId = shouldReplace ? messageToReplaceId : `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
              
              setChatMessages(prev => {
                const currentAiMessageIndex = prev.findIndex(msg => msg.id === newAiMsgId);
                if (currentAiMessageIndex !== -1) {
                // 替换现有消息
                    const updatedMessages = [...prev];
                    updatedMessages[currentAiMessageIndex] = {
                        ...updatedMessages[currentAiMessageIndex], 
                        text: additionalInfo ? `${messageText} ${additionalInfo}` : messageText,
                        icon: progressData.message.icon || undefined,
                        thinking: progressData.message.thinking || false, // 使用 thinking 字段
                        sender: progressData.message.sender || 'ai'
                    };
                    return updatedMessages;
              } else {
                // 添加新消息
                    return [
                  ...prev, 
                  { 
                    id: newAiMsgId, 
                    type: 'ai', 
                    text: messageText,
                    icon: progressData.message.icon || undefined,
                            thinking: progressData.message.thinking || false, // 使用 thinking 字段
                            sender: progressData.message.sender || 'ai'
                  }
                    ];
              }
              });
            }
            
            // 当接收到开始状态和AI识别的主题时，更新标题
            if (progressData.status === "starting" && progressData.message && progressData.message.text) {
              const topicMatch = progressData.message.text.match(/关于「(.+?)」/);
              if (topicMatch && topicMatch[1]) { 
                  const extractedTopic = topicMatch[1].trim();
                  if (extractedTopic && chatTitle !== `AI幻灯片 ${extractedTopic}`) { 
                      const newTitle = `AI幻灯片 ${extractedTopic}`;
                      setChatTitle(newTitle);
                  }
              }
            }

            // 新增/修改：当意图分析完成时，更精确地更新标题
            // 当意图分析完成时，根据后端元数据或消息文本更新标题
            if (progressData.status === "intent_analyzed") {
              // 优先使用 metadata 中的精确标题 (这是最可靠的方法)
              if (progressData.metadata && progressData.metadata.updated_title) {
                const newTitle = progressData.metadata.updated_title;
                console.log(`[App.js] Intent analyzed, updating title from metadata: ${newTitle}`);
                setChatTitle(newTitle);
                setCurrentPresentationTitle(newTitle);
              } 
              // 如果 metadata 中没有标题，则回退到从消息文本中解析 (作为备用方案)
              else if (progressData.message && progressData.message.text) {
                const topicMatch = progressData.message.text.match(/主题："(.+?)"/);
                if (topicMatch && topicMatch[1]) {
                    const extractedTopic = topicMatch[1].trim();
                    const newTitle = `AI幻灯片 ${extractedTopic}`;
                    console.log(`[App.js] Intent analyzed, updating title from message text (fallback): ${newTitle}`);
                    setChatTitle(newTitle);
                    setCurrentPresentationTitle(newTitle);
                }
              }
            }
            
            // 处理思考内容和总幻灯片数
            if (progressData.thinking_content) {
              setOutlineContent(progressData.thinking_content);
            }
            
            if (progressData.total_slides && progressData.total_slides > 0) {
              setTotalSlideCount(progressData.total_slides);
            }
            
            // 处理UI交互动作
            if (progressData.ui_action) {
              // 处理标签页切换
              if (progressData.ui_action.action === "select_tab" && 
                  progressData.current_slide_index !== undefined) {
                
                // 设置当前幻灯片的标签页
                if (progressData.ui_action.tab === "code") {
                  setActiveStep('code');
                  setSlideTabIndices(prev => ({
                    ...prev,
                    [progressData.current_slide_index]: 1 // 1 表示代码标签
                  }));
                } else if (progressData.ui_action.tab === "preview") {
                  setActiveStep('preview');
                  setSlideTabIndices(prev => ({
                    ...prev,
                    [progressData.current_slide_index]: 0 // 0 表示预览标签
                  }));
                }
              }
            }
            
            // 处理幻灯片更新
            if (progressData.slide_update) {
              const newSlide = {
                id: progressData.slide_update.id,
                html: progressData.slide_update.html || progressData.slide_update.html_content, // 兼容字段
                code: progressData.slide_update.code || progressData.slide_update.html || progressData.slide_update.html_content,
                order: progressData.slide_update.slide_number !== undefined ? progressData.slide_update.slide_number - 1 : 0, // 确保使用slide_number转为0-based
                title: `幻灯片 ${progressData.slide_update.slide_number}`
              };

              // 使用函数式更新来保证我们总是基于最新的幻灯片列表进行操作
              setCurrentSlides(prevSlides => {
                const existingIndex = prevSlides.findIndex(s => s.id === newSlide.id || s.order === newSlide.order);
                let updatedSlides;

                if (existingIndex >= 0) {
                  // 更新现有幻灯片
                  updatedSlides = [...prevSlides];
                  updatedSlides[existingIndex] = newSlide;
                } else {
                  // 添加新幻灯片
                  updatedSlides = [...prevSlides, newSlide];
                }
                
                // 按 order 排序，以防消息乱序
                updatedSlides.sort((a, b) => a.order - b.order);
                return updatedSlides;
              });

              // 更新当前焦点幻灯片
              if (progressData.current_slide_index !== undefined) {
                setCurrentSlideIndex(progressData.current_slide_index);
              }
              
              // 如果有代码内容，更新代码显示
              if (progressData.code_content) {
                setSlideCode(progressData.code_content);
              }
            }
            
            // 如果状态为完成，更新最终消息
            if (progressData.status === 'completed') {
              // 添加完成消息
              const completionMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
              setChatMessages(prev => [
                ...prev, 
                { 
                  id: completionMsgId, 
                  type: 'ai', 
                  text: `✅ 幻灯片已全部生成完毕 (${(progressData.total_slides || currentSlides.length)}张)，您可以点击每张幻灯片进行查看和编辑。`,
                  icon: '✅',
                  thinking: false, // 完成时设置为非思考中
                  sender: 'ai', // 确保sender正确
                  project_id: progressData.project_id || currentProjectId // 确保保存项目ID
                }
              ]);
              
              setIsEditingMode(true);
              
              // 设置演示文稿标题
              if (!currentPresentationTitle || currentPresentationTitle === "AI幻灯片") {
                setCurrentPresentationTitle(messageText.length > 30 ? messageText.substring(0, 30) + "..." : messageText);
              }
              
              // 保存聊天历史
              saveCurrentChatToHistory();
              
              // 完成后刷新项目历史列表
              refreshProjectHistory();
            }
          },
          // 错误处理
          (error) => {
            console.error('App.js - 生成幻灯片时发生错误:', error);
            
            // 确保关闭旧的连接
            if (eventSourceRef.current && typeof eventSourceRef.current.close === 'function') {
              eventSourceRef.current.close();
            }
            
            // 处理特殊错误类型，尝试重新连接
            if ((error.message === 'SSE_HEARTBEAT_TIMEOUT' || error.message.includes('connection failed')) && retryAttempts < MAX_MESSAGE_RETRIES) {
              retryAttempts++;
              setChatMessages(prev => [...prev, {
                id: `sys-retry-${Date.now()}`, 
                type: 'system-info', 
                text: `连接中断，正在尝试重新连接... (${retryAttempts}/${MAX_MESSAGE_RETRIES}) 项目: ${effectiveProjectId || '新项目'}`,
                sender: 'system',
                icon: '⏳'
              }]);
              
              // 延迟重试，每次重试增加延迟
              setTimeout(() => executeGeneration(currentProjectId), 3000 * retryAttempts);
            } else {
              // 超过最大重试次数或其他错误
              setIsGenerating(false);
              setChatMessages(prev => [...prev, {
                id: `error-${Date.now()}`, 
                type: 'ai_error', 
                text: `抱歉，发生错误: ${error.message}`,
                sender: 'system',
                icon: '⚠️'
              }]);
            }
          },
          // 完成处理
          () => {
            console.log('App.js - 幻灯片生成流完成');
            setIsGenerating(false);
            // 不再在这里添加完成消息，依赖后端推送的 'completed' status
            saveCurrentChatToHistory(); // 确保最终状态被保存
          },
          // 传递项目ID（如果是新会话，后端会创建；如果是重试，则使用 existing_project_id）
          effectiveProjectId ? { project_id: effectiveProjectId } : {}
        );
      } catch (apiError) {
        console.error('App.js - API调用启动错误:', apiError);
        setIsGenerating(false);
        setChatMessages(prev => [...prev, {
          id: `error-api-${Date.now()}`, 
          type: 'ai_error', 
          text: `启动生成失败: ${apiError.message}`,
          sender: 'system',
          icon: '❌'
        }]);
      }
    };

    // 执行生成。如果不是"继续"请求，`projectIdToUse` 将是 `null`，后端将创建新项目ID。
    // 否则，它将是 `currentProjectId`。
    executeGeneration(isContinueRequest ? currentProjectId : null);
  };

  // 新增函数：刷新项目历史列表
  const refreshProjectHistory = async () => {
    try {
      const serverHistory = await apiService.getProjectsHistory();
      console.log("[App.js] Refreshed project history from server:", serverHistory);
      
      // 将后端返回的项目摘要转换为前端 chatHistory 格式
      const formattedServerHistory = serverHistory.map(proj => ({
        id: proj.id, // 修复
        title: proj.title || `项目 (${proj.id ? proj.id.slice(-4) : '未知'})`, // 修复
        timestamp: proj.last_modified || proj.created_at,
        project_id: proj.id, // 修复
        messages: [], 
        slides: [],
        isSummary: true
      }));
      
      // 更新 chatHistory 状态，保留本地项目
      setChatHistory(prev => {
        // 获取所有服务器项目的 ID
        const serverProjectIds = new Set(formattedServerHistory.map(item => item.project_id)); // 这里现在可以正确获取ID了
        
        // 保留本地项目（那些不在服务器上的）
        const localOnlyProjects = prev.filter(item => !serverProjectIds.has(item.project_id)); // 这里的去重逻辑现在也能正常工作了
        
        // 合并并按时间戳排序
        const combined = [...formattedServerHistory, ...localOnlyProjects]
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
          
        // 保存到 localStorage
        try {
          localStorage.setItem('chatHistory', JSON.stringify(combined));
        } catch (error) {
          console.error("保存刷新的聊天历史到本地存储失败:", error);
        }
        
        return combined;
      });
    } catch (error) {
      console.error("刷新项目历史列表失败:", error);
    }
  };

  const handleViewSearchResults = (results) => {
    // 处理AI服务返回的搜索结果
    // 这里传入的是LLM返回的Tool Call结果中的JSON (或对象)
    if (!results || (!Array.isArray(results) && !results.results)) {
      console.error("Invalid search results:", results);
      return;
    }
    
    // 标准化结果
    let normalizedResults = Array.isArray(results) ? results : results.results;
    
    // 确保至少有一个结果
    if (!normalizedResults || normalizedResults.length === 0) {
      console.error("Empty search results:", normalizedResults);
      return;
    }
    
    // 准备格式化结果
    const formattedResults = normalizedResults.map((result, index) => ({
      id: result.id || `result-${index}`,
      title: result.title || result.name || "未知标题",
      snippet: result.content || result.snippet || result.description || "没有内容预览",
      source: result.url || result.source || result.link || "#",
      type: result.source_type || (result.url?.includes('.pdf') ? 'pdf' : 'web')
    }));
    
    // 更新状态并显示模态
    setCurrentSearchResults(formattedResults);
    setShowSearchResultsModal(true);
  };

  // Auto-generate slides on first load - disabled for production with real backend
  /*
  useEffect(() => {
    if (currentSlides.length === 0 && !isGenerating) {
      processSimulatedGeneration("生成一个中国房地产市场2025年趋势的PPT");
    }
  }, []);
  */

  // 移除旧的键盘事件监听代码，现在由 FullScreenPlayer 组件通过 postMessage 处理

  // 监听来自 SlidePlayerView 的请求，以启动全屏播放器
  useEffect(() => {
    if (location.state && location.state.action === 'startFullScreenPlayer') {
      const { slides: slidesToPlay, initialIndex, presentationTitle: titleFromPlayer } = location.state;
      
      // 你可能需要更新 currentSlides 和 presentationTitle，如果它们与 App.js 的当前状态不同
      // 或者，如果 App.js 是这些状态的唯一来源，SlidePlayerView 应该总是从 App.js 获取最新的
      if (slidesToPlay && slidesToPlay.length > 0) {
         setCurrentSlides(slidesToPlay); // 确保幻灯片数据是最新的
      }
      if (titleFromPlayer) {
         setCurrentPresentationTitle(titleFromPlayer);
      }

      setCurrentFullScreenSlideIndex(initialIndex || 0);
      toggleFullScreen(); // 触发全屏

      // 清除 location.state 防止刷新或其他导航时再次触发
      navigate(location.pathname, { replace: true, state: {} }); 
    }
  }, [location.state, navigate, toggleFullScreen, location.pathname]); // 添加 location.pathname 为依赖项

  const handleCloseHistoryDrawer = () => {
    setShowHistoryDrawer(false);
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirmModal(false);
    setDeleteItemInfo(null);
  };

  return (
    <div className="flex flex-col h-screen bg-white">
      {isLoading ? (
        // 加载中的显示内容
        <div className="flex items-center justify-center h-screen bg-gray-50">
          <div className="text-center">
            <svg className="animate-spin h-10 w-10 text-blue-500 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p className="text-gray-600">加载中...</p>
          </div>
        </div>
      ) : (
        <>
          {/* Render fullscreen player if in fullscreen mode */}
          {isFullScreen && currentSlides.length > 0 && (
            <Suspense fallback={<div className="fixed inset-0 bg-black flex justify-center items-center text-white">全屏播放器加载中...</div>}>
              <FullScreenPlayer
                slides={currentSlides}
                initialIndex={currentFullScreenSlideIndex}
                onClose={toggleFullScreen}
              />
            </Suspense>
          )}
          
          {/* Overlay for content area - Always in DOM, opacity and pointer-events toggled */}
          <div 
            className={`fixed inset-0 bg-black z-40 md:ml-60 transition-opacity duration-150 ease-in-out bg-opacity-0 pointer-events-none`}
          />
    
          {/* 删除历史抽屉面板 */}
          
          <div className="flex flex-1 overflow-hidden">
            <LeftNav 
              isTaskListOpen={isTaskListOpen} 
              onToggleTaskList={toggleTaskList} 
            />
            
            <div className="flex flex-1 overflow-hidden"> 
              <MiddlePane
                isGenerating={isGenerating}
                chatMessages={chatMessages}
                onSendMessage={handleSendMessage}
                onViewSearchResults={handleViewSearchResults}
                title={chatTitle}
                onTitleClick={handleChatTitleChange}
                onBackClick={handleBackClick}
                tools={tools}
                currentStep={currentStep}
                className="w-1/3"
              />
              <RightPane
                allSlides={currentSlides}
                currentFocusedSlideIndex={currentSlideIndex}
                onSetFocusedSlideIndex={handleSetCurrentSlideIndex}
                onPlaySlideAtIndex={handlePlaySlideAtIndex}
                isAppEditingMode={isEditingMode}
                onToggleAppEditMode={handleToggleEditSlide}
                onTextEditStart={(elementId, textContent, style) => {
                  setEditingTextElement({ 
                    id: elementId, 
                    content: textContent, 
                    style: style 
                  });
                }}
                presentationTitle={currentPresentationTitle}
                onOpenViewAndExport={handleOpenViewAndExport}
                outlineContent={outlineContent}
                slideCode={slideCode}
                activeStep={activeStep}
                className="w-2/3"
                totalSlideCount={totalSlideCount}
                slideTabIndices={slideTabIndices}
                onSlideTabChange={handleSlideTabChange}
              />
            </div>
          </div>

          {/* Delete Confirmation Modal */}
          {showDeleteConfirmModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100] p-4">
              <div className="bg-white rounded-lg shadow-xl max-w-md w-full flex flex-col">
                <div className="p-8 flex items-center justify-center">
                  <p className="text-gray-700 text-xl font-medium text-center">
                    删除后不可恢复，确定吗？
                  </p>
                </div>
                <div className="p-4 border-t flex justify-center space-x-6">
                  <button
                    onClick={handleConfirmDelete}
                    className="px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 text-sm"
                  >
                    确定
                  </button>
                  <button
                    onClick={handleCancelDelete}
                    className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm"
                  >
                    取消
                  </button>
                </div>
              </div>
            </div>
          )}

          {showSearchResultsModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30 p-4">
              <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] flex flex-col">
                <div className="flex justify-between items-center p-4 border-b">
                  <h3 className="text-lg font-semibold">搜索结果</h3>
                  <button onClick={() => setShowSearchResultsModal(false)} className="text-gray-500 hover:text-gray-700 text-2xl">×</button>
                </div>
                <div className="p-4 overflow-y-auto custom-scrollbar">
                  {currentSearchResults.map(result => (
                    <div key={result.id} className="mb-4 p-3 border rounded-md hover:shadow-md transition-shadow">
                      <a href={result.source} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline font-medium flex items-center">
                        {result.type === 'pdf' ? <FaFilePdf className="mr-2 text-red-500" /> : <FaGlobe className="mr-2 text-blue-400" />}
                        {result.title}
                      </a>
                      <p className="text-sm text-gray-600 mt-1">{result.snippet}</p>
                      <p className="text-xs text-gray-400 mt-1">{result.source}</p>
                    </div>
                  ))}
                </div>
                <div className="p-3 border-t text-right">
                  <button
                    onClick={() => setShowSearchResultsModal(false)}
                    className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </div>
          )}
          {/* +++++++++++++++ 新增UI组件渲染 +++++++++++++++ */}
          {/* 上下文菜单 */}
          {contextMenu && (
            <div
              className="fixed bg-white shadow-lg rounded-md p-1 border border-gray-200 z-[100]"
              style={{ top: contextMenu.y + 10, left: contextMenu.x }}
              onClick={e => e.stopPropagation()} // 防止点击菜单自身时关闭
            >
              <button
                onClick={handleRewriteClick}
                className="flex items-center w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
              >
                <FaFeatherAlt className="mr-2" />
                重写
              </button>
              {/* 在这里可以添加更多按钮，如"换图"、"改样式"等 */}
            </div>
          )}

          {/* 编辑指令输入弹窗 */}
          {editModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[110]">
              <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
                <h3 className="text-lg font-medium mb-4">告诉AI你想如何修改</h3>
                <textarea
                  value={editInstruction}
                  onChange={(e) => setEditInstruction(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-300"
                  rows="3"
                  placeholder="例如：这段话写得更活泼一点"
                  disabled={isEditingElement}
                />
                <div className="flex justify-end space-x-3 mt-4">
                  <button
                    onClick={handleCloseEditModal}
                    className="px-4 py-2 bg-gray-200 rounded-md text-sm hover:bg-gray-300"
                    disabled={isEditingElement}
                  >
                    取消
                  </button>
                  <button
                    onClick={handleSubmitEdit}
                    className="px-4 py-2 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 disabled:bg-blue-300"
                    disabled={isEditingElement || !editInstruction.trim()}
                  >
                    {isEditingElement ? '提交中...' : '提交'}
                  </button>
                </div>
              </div>
            </div>
          )}
          {/* +++++++++++++++++++++++++++++++++++++++++++++++++++ */}

          {editingTextElement && (
            <Suspense fallback={<div className="fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center">编辑器加载中...</div>}>
            <InlineTextEditor
              initialText={editingTextElement.content}
              initialStyle={editingTextElement.style}
              position={editingTextElement.position}
              onSave={handleTextEditSave}
              onCancel={handleTextEditCancel}
            />
            </Suspense>
          )}
        </>
      )}
    </div>
  );
};

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<MainApp />} />
        <Route path="/player" element={
          <Suspense fallback={<div className="flex justify-center items-center h-screen">播放器加载中...</div>}>
            <SlidePlayerView />
          </Suspense>
        } />
        <Route path="/chat" element={
          <Suspense fallback={<div className="flex justify-center items-center h-screen">加载AI聊天...</div>}>
            <ChatViewPage />
          </Suspense>
        } />
      </Routes>
    </BrowserRouter>
  );
}

export default App;