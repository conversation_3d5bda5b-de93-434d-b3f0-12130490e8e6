#!/usr/bin/env python3
"""
测试RGBA颜色格式修复的验证脚本

验证check_hex_code函数能否正确处理rgba格式并转换为hex格式
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.models.presentation_model import check_hex_code, ColorPaletteSchema, ColorDetailSchema
from pydantic import ValidationError

def test_color_hex_validator():
    """测试ColorHex验证器能否处理各种格式"""
    print("🧪 测试 ColorHex 验证器...")
    
    # 测试用例
    test_cases = [
        # (输入, 预期输出)
        ("#FF0000", "#FF0000"),  # 标准HEX
        ("FF0000", "#FF0000"),   # 无#的HEX
        ("rgba(196, 166, 107, 0.3)", "#c4a66b"),  # 这是原始错误输入
        ("rgb(255, 0, 0)", "#ff0000"),  # RGB格式
        ("rgba(255, 255, 255, 1.0)", "#ffffff"),  # 白色
        ("rgba(0, 0, 0, 0.5)", "#000000"),  # 黑色
    ]
    
    for input_val, expected in test_cases:
        try:
            result = check_hex_code(input_val)
            if result == expected:
                print(f"✅ '{input_val}' -> '{result}' (预期: '{expected}')")
            else:
                print(f"❌ '{input_val}' -> '{result}' (预期: '{expected}')")
        except Exception as e:
            print(f"❌ '{input_val}' -> 错误: {e}")
    
    print()

def test_color_palette_with_rgba():
    """测试ColorPaletteSchema能否接受有问题的rgba值"""
    print("🧪 测试 ColorPaletteSchema 处理 rgba 值...")
    
    # 模拟LLM输出的有问题的数据
    test_data = {
        "theme_name": "测试主题",
        "primary": {
            "name": "主色",
            "hex": "#1A2C3D",
            "usage_suggestion": "主要背景"
        },
        "secondary": {
            "name": "辅色", 
            "hex": "#5A6B7C",
            "usage_suggestion": "辅助元素"
        },
        "accent": {
            "name": "强调色",
            "hex": "#A32A2A", 
            "usage_suggestion": "强调元素"
        },
        "text_on_dark_bg": "#E0E0E0",
        "text_on_light_bg": "#1A202C",
        "background_main": "#0F1A25",
        "background_gradient_end": "#F8FAFC",
        "background_gradient_direction": "to bottom right",
        "card_background": "#FFFFFF",
        "card_border": "rgba(196, 166, 107, 0.3)",  # 这是引起错误的字段
        "card_shadow_color_rgba": "rgba(0,0,0,0.1)",
        "chart_colors": ["#FF6384", "#36A2EB", "#FFCE56"]
    }
    
    try:
        color_palette = ColorPaletteSchema(**test_data)
        print(f"✅ ColorPaletteSchema 创建成功！")
        print(f"   card_border 修复为: {color_palette.card_border}")
        return True
    except ValidationError as e:
        print(f"❌ ColorPaletteSchema 验证失败: {e}")
        return False
    except Exception as e:
        print(f"❌ ColorPaletteSchema 创建失败: {e}")
        return False

def main():
    print("=" * 60)
    print("🔧 RGBA颜色格式修复验证")
    print("=" * 60)
    
    # 测试验证器
    test_color_hex_validator()
    
    # 测试完整的Color Palette
    success = test_color_palette_with_rgba()
    
    print("=" * 60)
    if success:
        print("🎉 所有测试通过！RGBA格式修复成功！")
        print("现在VisualStyleAgent应该能够正常工作。")
    else:
        print("❌ 仍有测试失败，需要进一步修复。")
    print("=" * 60)

if __name__ == "__main__":
    main() 