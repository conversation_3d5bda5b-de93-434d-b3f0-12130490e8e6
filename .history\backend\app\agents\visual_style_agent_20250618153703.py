# backend/app/agents/visual_style_agent.py
import logging
from typing import Optional, List, Dict, Any, Tuple # Added Tuple
from app.agents.base_agent import BaseAgent
from app.models.presentation_model import StructuredPresentationStyleSchema

from app.services.prompt_manager import format_prompt_section

logger = logging.getLogger(__name__)

class VisualStyleAgent(BaseAgent):
    """
    Agent responsible for generating the visual style system for a presentation.
    """
    
    def __init__(self, model_name_override: Optional[str] = None): # <-- 移除 model=None
        """
        Initialize the visual style agent.
        
        Args:
            model_name_override: Optional model name for logging
        """
        super().__init__(
            # 移除 model=model,
            agent_name="VisualStyleAgent",
            system_prompt_name="system",
            agent_prompt_subdir="visual_style",
            model_name_override=model_name_override
        ) # 【删除】system_prompt_version="v1"
        logger.info(f"VisualStyleAgent initialized using {self.model_name_description}.")

    async def process(
        self,
        topic: str,
        num_slides: int,
        style_keywords: Optional[List[str]],
        project_id: str
    ) -> Tuple[Optional[StructuredPresentationStyleSchema], Optional[str]]:
        """
        Generate visual style and presentation outlines for a presentation.
        
        Args:
            topic: The main topic of the presentation
            num_slides: Number of slides planned
            style_keywords: Optional list of style keywords provided by user
            project_id: Current project ID for context and logging
            
        Returns:
            Tuple of (StructuredPresentationStyleSchema with visual style and outlines or None, LLM Interaction Log ID)
        """
        context_name = f"VisualStyleGen_Proj_{project_id}"
        logger.info(f"[{self.agent_name}][{context_name}] Generating visual style and outlines with Instructor...")

        style_keywords_info_for_prompt = (
            f"用户提供的风格偏好关键词是：'{', '.join(style_keywords)}'。请围绕这些关键词进行风格设计，并大胆创新。"
            if style_keywords else
            f"用户未提供明确的风格偏好关键词。请你基于演示文稿主题 '{topic}'，主动推荐并设计一套专业、独特且具有高度美感的视觉风格。"
        )

        # 调整 user_input，明确要求生成大纲
        user_input = f"""
        演示文稿主题: {topic}
        幻灯片数量: {num_slides}
        风格偏好: {style_keywords_info_for_prompt}

        请根据上述信息，生成一个完整的视觉风格指南，并为这个主题设计一个包含 {num_slides} 张幻灯片的详细演示文稿大纲。
        确保大纲的每张幻灯片都有一个明确的标题、至少2个关键要点，并根据内容建议幻灯片类型（例如：'Title Slide', 'Content Slide', 'Data Analysis Slide', 'Summary Slide'）。
        整个大纲应该逻辑严谨，覆盖主题的核心方面。
        """
        
        # 使用format_prompt_section格式化提示词
        prompt = format_prompt_section(
            agent_name="visual_style_agent",
            section_name="task:main",
            user_input=user_input
        )
        
        if not prompt:
            logger.error(f"[{self.agent_name}][{context_name}] Failed to format prompt.")
            return None, None

        # 直接使用完整的StructuredPresentationStyleSchema，让LLM面对真正的设计挑战
        logger.info(f"[{self.agent_name}][{context_name}] 使用完整的StructuredPresentationStyleSchema...")
        response_model, llm_log_id = await self._call_llm_with_instructor(
            prompt=prompt,
            pydantic_schema=StructuredPresentationStyleSchema,
            project_id_for_logging=project_id
        )
        
        if response_model:
            logger.info(f"[{self.agent_name}][{context_name}] 完整设计系统生成成功！")
            logger.info(f"设计系统摘要: {response_model.style_summary_text[:100]}...")
            logger.info(f"生成了 {len(response_model.presentation_outlines)} 个幻灯片大纲")
            return response_model, llm_log_id
        else:
            logger.error(f"[{self.agent_name}][{context_name}] 完整设计系统生成失败")
            return None, llm_log_id
    
    def _convert_simplified_to_full_schema(self, simplified: SimplifiedStyleSchema) -> StructuredPresentationStyleSchema:
        """将简化Schema转换为完整的StructuredPresentationStyleSchema"""
        try:
            # 从原始Schema导入必要的类
            from app.models.presentation_model import (
                ColorPaletteSchema, ColorDetailSchema, TypographySchema, DesignElementFeaturesSchema,
                SlideOutlineItemSchema
            )
            
            # 创建色彩方案 - 使用正确的ColorDetailSchema结构
            color_palette = ColorPaletteSchema(
                theme_name=simplified.theme_name,
                primary=ColorDetailSchema(
                    name="主色调",
                    hex=simplified.primary_color,
                    usage_suggestion="页面主体背景和主要元素"
                ),
                secondary=ColorDetailSchema(
                    name="辅色调", 
                    hex=simplified.secondary_color,
                    usage_suggestion="次要元素和边框"
                ),
                accent=ColorDetailSchema(
                    name="强调色",
                    hex=simplified.accent_color,
                    usage_suggestion="按钮和重点突出"
                ),
                text_on_dark_bg="#FFFFFF",
                text_on_light_bg=simplified.text_color,
                background_main=simplified.background_color,
                background_gradient_end=simplified.background_color,
                background_gradient_direction="to bottom right",
                card_background="#FFFFFF",
                card_border="#E5E7EB",
                card_shadow_color_rgba="rgba(0, 0, 0, 0.1)",
                chart_colors=[simplified.primary_color, simplified.secondary_color, simplified.accent_color]
            )
            
            # 创建字体方案 - 使用正确的字段名
            typography = TypographySchema(
                heading_font_family_css=f"'{simplified.heading_font}', sans-serif",
                body_font_family_css=f"'{simplified.primary_font}', sans-serif",
                heading_font_cdn_url="",
                body_font_cdn_url="",
                heading_font_weight="700",
                body_font_weight="400",
                font_size_scale_css_vars={
                    "--font-size-h1": "36px",
                    "--font-size-h2": "28px", 
                    "--font-size-h3": "24px",
                    "--font-size-body": "16px",
                    "--font-size-caption": "14px"
                },
                line_height_css_vars={
                    "--line-height-heading": "1.3",
                    "--line-height-body": "1.6"
                }
            )
            
            # 创建设计元素 - 使用正确的字段名
            design_elements = DesignElementFeaturesSchema(
                overall_feel_keywords=["现代", "专业", "数据驱动"],
                card_style="圆角12px，背景色var(--card-background)，边框1px solid var(--card-border)，阴影0 4px 12px var(--card-shadow-color-rgba)",
                background_details="主体背景使用 var(--background-main) 到 var(--background-gradient-end) 的 var(--background-gradient-direction) 渐变",
                icon_style_suggestion="使用FontAwesome的细线条(light)风格图标",
                animation_suggestion="fade-in-up 0.5s ease-out forwards",
                spacing_system_css_vars={
                    "--space-xs": "4px",
                    "--space-sm": "8px",
                    "--space-md": "16px",
                    "--space-lg": "24px",
                    "--space-xl": "32px"
                },
                divider_style="1px solid var(--secondary-color)",
                chart_style="扁平化图表，色彩参考强调色",
                border_radius_suggestion="12px",
                visual_balance_principles=["左右对称", "色彩呼应", "层次分明"]
            )
            
            # 创建幻灯片大纲
            slide_outlines = [
                SlideOutlineItemSchema(
                    slide_number=i + 1,
                    title=title,
                    key_points=[f"{title}的关键要点1", f"{title}的关键要点2"],
                    slide_type_suggestion="Title Slide" if i == 0 else "Content Slide",
                    visual_element_hint="文本内容"
                )
                for i, title in enumerate(simplified.slide_outlines)
            ]
            
            # 创建完整的Schema - 使用正确的字段名
            full_schema = StructuredPresentationStyleSchema(
                style_summary_text=simplified.style_summary,
                color_palette=color_palette,
                typography=typography,
                design_elements=design_elements,
                css_custom_properties_definitions={
                    "--primary-color": simplified.primary_color,
                    "--secondary-color": simplified.secondary_color,
                    "--accent-color": simplified.accent_color,
                    "--background-color": simplified.background_color,
                    "--text-color": simplified.text_color,
                    "--primary-font": simplified.primary_font,
                    "--heading-font": simplified.heading_font
                },
                presentation_outlines=slide_outlines
            )
            
            return full_schema
            
        except Exception as e:
            logger.error(f"转换Schema时出错: {e}")
            # 如果转换失败，返回None让调用者处理
            return None