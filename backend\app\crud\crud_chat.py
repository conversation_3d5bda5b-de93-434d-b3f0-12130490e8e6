from sqlalchemy.orm import Session
from typing import List, Optional
from app.db import models as db_models
from app.models import presentation_model as pydantic_models

def get_chat_messages_for_project(db: Session, project_id: str, skip: int = 0, limit: int = 1000) -> List[db_models.ChatMessage]:
    """获取项目的所有聊天消息，按时间顺序排序"""
    return db.query(db_models.ChatMessage)\
             .filter(db_models.ChatMessage.project_id == project_id)\
             .order_by(db_models.ChatMessage.timestamp)\
             .offset(skip).limit(limit).all()

def add_chat_message(db: Session, project_id: str, message: pydantic_models.AiChatMessage, user_id: Optional[str] = None) -> db_models.ChatMessage:
    """添加新的聊天消息到项目"""
    # 检查项目是否存在并且属于该用户 (如果提供了user_id)
    project = db.query(db_models.Project).filter(db_models.Project.id == project_id)
    if user_id:
        project = project.filter(db_models.Project.user_id == user_id)
    project = project.first()

    if not project:
        # 可以抛出异常或返回 None，取决于你的错误处理策略
        raise ValueError(f"Project with id {project_id} not found or not accessible.")

    db_message = db_models.ChatMessage(
        project_id=project_id,
        sender=message.sender,
        text=message.text,
        icon=message.icon
    )
    db.add(db_message)
    db.commit()
    db.refresh(db_message)
    return db_message

def get_chat_message(db: Session, message_id: str) -> Optional[db_models.ChatMessage]:
    """根据ID获取特定的聊天消息"""
    return db.query(db_models.ChatMessage).filter(db_models.ChatMessage.id == message_id).first()

def delete_chat_message(db: Session, message_id: str) -> bool:
    """删除特定的聊天消息"""
    db_message = get_chat_message(db, message_id)
    if db_message:
        db.delete(db_message)
        db.commit()
        return True
    return False 