{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\n// frontend/src/App.js\nimport React, { useState, useEffect, useCallback, useRef, lazy, Suspense } from 'react';\nimport { BrowserRouter, Routes, Route, useNavigate, useLocation } from 'react-router-dom';\nimport LeftNav from './components/LeftNav';\nimport MiddlePane from './components/MiddlePane';\nimport RightPane from './components/RightPane';\nimport { FaFilePdf, FaGlobe, FaTrash, FaFeatherAlt } from 'react-icons/fa';\nimport formatDate from './utils/formatDate';\nimport apiService from './services/api';\n\n// Lazy load components that are not needed for initial render\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InlineTextEditor = /*#__PURE__*/lazy(_c = () => import('./components/InlineTextEditor'));\n_c2 = InlineTextEditor;\nconst SlidePlayerView = /*#__PURE__*/lazy(_c3 = () => import('./views/SlidePlayerView'));\n_c4 = SlidePlayerView;\nconst FullScreenPlayer = /*#__PURE__*/lazy(_c5 = () => import('./components/FullScreenPlayer'));\n_c6 = FullScreenPlayer;\nconst ChatViewPage = /*#__PURE__*/lazy(_c7 = () => import('./views/ChatViewPage'));\n\n// 添加临时组件定义，后续应该替换为实际组件\n_c8 = ChatViewPage;\nconst SearchResultsModal = ({\n  results,\n  onClose\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30 p-4\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center p-4 border-b\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold\",\n        children: \"\\u641C\\u7D22\\u7ED3\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"text-gray-500 hover:text-gray-700 text-2xl\",\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 overflow-y-auto custom-scrollbar\",\n      children: results.map(result => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-4 p-3 border rounded-md hover:shadow-md transition-shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: result.source,\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"text-blue-500 hover:underline font-medium flex items-center\",\n          children: [result.type === 'pdf' ? /*#__PURE__*/_jsxDEV(FaFilePdf, {\n            className: \"mr-2 text-red-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 40\n          }, this) : /*#__PURE__*/_jsxDEV(FaGlobe, {\n            className: \"mr-2 text-blue-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 86\n          }, this), result.title]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-600 mt-1\",\n          children: result.snippet\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-400 mt-1\",\n          children: result.source\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 13\n        }, this)]\n      }, result.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-3 border-t text-right\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm\",\n        children: \"\\u5173\\u95ED\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 20,\n  columnNumber: 3\n}, this);\n_c9 = SearchResultsModal;\nconst HistoryDrawer = ({\n  history,\n  onClose,\n  onSelectItem,\n  currentProjectId\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"fixed right-0 top-0 h-full w-80 bg-white shadow-lg z-40 overflow-y-auto\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4 border-b flex justify-between items-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"font-medium\",\n      children: \"\\u5386\\u53F2\\u9879\\u76EE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClose,\n      className: \"text-gray-500 hover:text-gray-700\",\n      children: \"\\xD7\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-2\",\n    children: history.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `p-3 hover:bg-gray-50 cursor-pointer rounded-md ${item.id === currentProjectId ? 'bg-blue-50' : ''}`,\n      onClick: () => onSelectItem(item),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium\",\n        children: item.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500 mt-1\",\n        children: formatDate(new Date(item.timestamp))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 11\n      }, this)]\n    }, item.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 51,\n  columnNumber: 3\n}, this);\n_c0 = HistoryDrawer;\nconst DeleteConfirmModal = ({\n  title,\n  message,\n  onConfirm,\n  onCancel\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100] p-4\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-xl max-w-md w-full flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium mb-2\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 border-t flex justify-end space-x-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onCancel,\n        className: \"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm\",\n        children: \"\\u53D6\\u6D88\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onConfirm,\n        className: \"px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm\",\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 72,\n  columnNumber: 3\n}, this);\n\n// MainApp component that contains all the existing functionality\n_c1 = DeleteConfirmModal;\nconst MainApp = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation(); // 获取 location 对象\n  const [isLoading, setIsLoading] = useState(true); // 主加载状态（整体应用）\n  const [isLoadingHistory, setIsLoadingHistory] = useState(false); // 项目历史加载状态\n  const [isTaskListOpen, setIsTaskListOpen] = useState(false);\n  const [currentPresentationTitle, setCurrentPresentationTitle] = useState(\"AI 幻灯片\");\n  const [chatTitle, setChatTitle] = useState(\"AI 幻灯片\");\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [chatMessages, setChatMessages] = useState([]);\n  const [currentSlides, setCurrentSlides] = useState([]);\n  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);\n  const [editingTextElement, setEditingTextElement] = useState(null);\n  const [showSearchResultsModal, setShowSearchResultsModal] = useState(false);\n  const [currentSearchResults, setCurrentSearchResults] = useState([]);\n  const [isFullScreen, setIsFullScreen] = useState(false);\n  const [currentFullScreenSlideIndex, setCurrentFullScreenSlideIndex] = useState(0);\n  const [isEditingMode, setIsEditingMode] = useState(false); // 这个是App层面的编辑模式（例如NLP命令，或是否启用InlineTextEditor）\n  const [chatHistory, setChatHistory] = useState([]); // 存储历史聊天记录摘要\n  const eventSourceRef = useRef(null);\n  const chatRef = useRef(null); // 添加聊天内容引用\n\n  // 新增状态\n  const [tools, setTools] = useState([]);\n  const [currentStep, setCurrentStep] = useState(null);\n  const [outlineContent, setOutlineContent] = useState('');\n  const [slideCode, setSlideCode] = useState('');\n  const [activeStep, setActiveStep] = useState('preview');\n  const [showHistoryDrawer, setShowHistoryDrawer] = useState(false);\n  const [totalSlideCount, setTotalSlideCount] = useState(0);\n  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);\n  const [deleteItemInfo, setDeleteItemInfo] = useState(null);\n  const [slideTabIndices, setSlideTabIndices] = useState({});\n  const [currentProjectId, setCurrentProjectId] = useState(null);\n  const isNavigatingToNew = useRef(false);\n  const [contextMenu, setContextMenu] = useState(null);\n  const [editModal, setEditModal] = useState(null);\n  const [editInstruction, setEditInstruction] = useState('');\n  const [isEditingElement, setIsEditingElement] = useState(false); // 防止重复提交\n\n  // 用于播放器全屏的切换逻辑\n  const toggleFullScreen = useCallback(async () => {\n    // 检查当前是否处于浏览器全屏模式\n    const isInFullScreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement);\n    try {\n      if (!isInFullScreen) {\n        // 如果当前不处于全屏，则请求进入全屏\n        // 准备进入全屏时，设置好初始索引\n        if (currentSlides.length > 0) {\n          setCurrentFullScreenSlideIndex(currentSlideIndex);\n        }\n        // 调用浏览器原生 API\n        await document.documentElement.requestFullscreen();\n        // isFullScreen 状态将由下面的 'fullscreenchange' 事件监听器更新\n      } else {\n        // 如果当前已处于全屏，则退出全屏\n        await document.exitFullscreen();\n        // isFullScreen 状态将由下面的 'fullscreenchange' 事件监听器更新\n      }\n    } catch (error) {\n      console.error(\"全屏操作失败:\", error);\n      // 如果原生API失败，作为备用方案，仍然显示我们的模拟全屏组件\n      setIsFullScreen(prev => !prev);\n    }\n  }, [currentSlideIndex, currentSlides.length]);\n\n  // 定义 saveCurrentChatToHistory 函数，确保在适当的时机调用\n  const saveCurrentChatToHistory = useCallback(() => {\n    // 仅当有实质内容时才保存到历史列表\n    if (chatMessages.length > 1 && currentProjectId) {\n      // 确保有 project_id\n      const currentSession = {\n        id: currentProjectId,\n        // 使用 project_id 作为历史记录的唯一 ID\n        title: chatTitle || `演示 (${currentProjectId.slice(-4)})`,\n        // 确保有标题\n        messages: chatMessages,\n        slides: currentSlides,\n        timestamp: new Date().toISOString(),\n        project_id: currentProjectId\n      };\n      setChatHistory(prevHistory => {\n        const existingSessionIndex = prevHistory.findIndex(item => item.id === currentProjectId);\n        let updatedHistory;\n        if (existingSessionIndex >= 0) {\n          updatedHistory = [...prevHistory];\n          updatedHistory[existingSessionIndex] = currentSession; // 更新现有会话\n        } else {\n          updatedHistory = [currentSession, ...prevHistory]; // 添加新会话\n        }\n        // 限制历史记录数量（例如最近20条）\n        // updatedHistory = updatedHistory.slice(0, 20); \n        try {\n          localStorage.setItem('chatHistory', JSON.stringify(updatedHistory));\n        } catch (error) {\n          console.error(\"保存聊天历史列表到 localStorage 失败:\", error);\n        }\n        return updatedHistory;\n      });\n    }\n  }, [chatMessages, currentSlides, chatTitle, currentProjectId, setChatHistory]); // 添加 currentProjectId 依赖\n\n  // 新建演示文稿的函数\n  const handleNewPresentation = useCallback(() => {\n    isNavigatingToNew.current = true; // 在导航前设置标志\n\n    // 清空当前状态\n    setChatMessages([]);\n    setCurrentSlides([]);\n    setChatTitle(\"AI 幻灯片\");\n    setCurrentPresentationTitle(\"AI 幻灯片\");\n    setIsGenerating(false);\n    setCurrentStep(null);\n    setTools([]);\n    setOutlineContent('');\n    setSlideCode('');\n    setActiveStep('preview');\n\n    // 清除项目ID\n    setCurrentProjectId(null);\n    localStorage.removeItem('currentProjectId');\n\n    // 导航到根路径，清除URL中的projectId\n    navigate('/');\n  }, [setChatMessages, setCurrentSlides, setChatTitle, setCurrentPresentationTitle, setIsGenerating, setCurrentStep, setTools, setOutlineContent, setSlideCode, setActiveStep, setCurrentProjectId, navigate]);\n\n  // 专门用于加载历史列表的函数\n  const loadProjectHistory = useCallback(async () => {\n    setIsLoadingHistory(true);\n    try {\n      const serverHistory = await apiService.getProjectsHistory();\n      console.log(\"[App.js] Fetched project history summaries:\", serverHistory);\n      const formattedSummaries = serverHistory.map(proj => ({\n        id: proj.id,\n        // 修复：使用后端返回的 'id' 字段\n        title: proj.title || `项目 (${proj.id ? proj.id.slice(-4) : '未知'})`,\n        // 修复：使用 'id' 并增加健壮性检查\n        timestamp: proj.last_modified || proj.created_at,\n        project_id: proj.id,\n        // 修复：使用 'id' 字段来填充 project_id\n        isSummary: true // 标记为摘要\n      }));\n      setChatHistory(formattedSummaries);\n\n      // 清理无效的 localStorage.currentProjectId\n      const serverProjectIds = new Set(serverHistory.map(p => p.project_id));\n      const storedId = localStorage.getItem('currentProjectId');\n      if (storedId && !serverProjectIds.has(storedId)) {\n        localStorage.removeItem('currentProjectId');\n      }\n    } catch (error) {\n      console.error(\"加载项目历史失败:\", error);\n      setChatHistory([]); // 出错时清空\n    } finally {\n      setIsLoadingHistory(false);\n    }\n  }, [setChatHistory, setIsLoadingHistory]);\n\n  // loadProjectDetails 函数现在是加载单个项目详情的唯一入口\n  const loadProjectDetails = useCallback(async projectId => {\n    // 增加对无效projectId的严格检查，防止API调用失败\n    if (!projectId || projectId === 'undefined') {\n      console.warn(\"尝试加载一个无效的项目ID，已中止。\", projectId);\n      setIsLoading(false); // 确保结束加载状态\n      handleNewPresentation(); // 重置到一个安全的新建页面\n      return;\n    }\n    setIsLoading(true);\n    try {\n      const details = await apiService.getProjectDetails(projectId);\n      console.log(`[App.js] Fetched details for project ${projectId}:`, details);\n\n      // 确保聊天历史有数据，必要时进行格式化处理\n      let chatHistoryData = details.chat_history || [];\n\n      // 确保每条消息都有正确的格式和ID\n      if (chatHistoryData.length > 0) {\n        chatHistoryData = chatHistoryData.map((msg, index) => ({\n          ...msg,\n          id: msg.id || `msg-${index}-${Date.now()}`,\n          sender: msg.sender || (index % 2 === 0 ? 'user' : 'ai'),\n          text: msg.text || msg.content || '无内容',\n          timestamp: msg.timestamp || new Date().toISOString()\n        }));\n        console.log(`[App.js] 处理后的聊天历史数据:`, chatHistoryData);\n      } else {\n        // 如果没有聊天历史，可以添加一个系统消息\n        chatHistoryData = [{\n          id: `system-${Date.now()}`,\n          sender: 'system',\n          text: '项目已加载，但没有聊天记录。',\n          timestamp: new Date().toISOString()\n        }];\n      }\n      setChatMessages(chatHistoryData);\n      const displayTitle = details.title || `项目 (${projectId.slice(-4)})`;\n      setChatTitle(displayTitle);\n      setCurrentPresentationTitle(displayTitle);\n      setCurrentProjectId(details.project_id);\n\n      // 保存到localStorage以便在刷新后恢复\n      localStorage.setItem('currentProjectId', details.project_id);\n\n      // 确保URL与当前加载的项目ID同步\n      if (!location.search.includes(`projectId=${details.project_id}`)) {\n        navigate(`/?projectId=${details.project_id}`, {\n          replace: true\n        });\n      }\n      setTotalSlideCount(details.total_slides_planned || (details.slides || []).length);\n\n      // 懒加载幻灯片内容\n      if (details.slides && details.slides.length > 0) {\n        const slidesWithMetadata = details.slides.map(s => ({\n          id: s.id,\n          html: '',\n          code: '',\n          order: s.slide_number - 1,\n          title: `幻灯片 ${s.slide_number}`,\n          isLoading: true\n        }));\n        setCurrentSlides(slidesWithMetadata);\n        const slideContentPromises = details.slides.map(slideMeta => apiService.getSlideContent(slideMeta.id).catch(() => ({\n          id: slideMeta.id,\n          error: true\n        })));\n        const fullSlidesData = await Promise.all(slideContentPromises);\n        setCurrentSlides(currentSlides => currentSlides.map(metaSlide => {\n          const fullData = fullSlidesData.find(fs => fs.id === metaSlide.id);\n          if (fullData && !fullData.error) {\n            return {\n              ...metaSlide,\n              html: fullData.html,\n              code: fullData.html,\n              isLoading: false\n            };\n          }\n          return {\n            ...metaSlide,\n            html: '<div>内容加载失败</div>',\n            code: '/* 内容加载失败 */',\n            isLoading: false\n          };\n        }));\n      } else {\n        setCurrentSlides([]);\n      }\n    } catch (error) {\n      console.error(`加载项目 ${projectId} 详情失败:`, error);\n      if (error.message && (error.message.includes('not found') || error.message.includes('permission'))) {\n        // 如果项目不存在或无权限，从localStorage移除该项目ID\n        if (localStorage.getItem('currentProjectId') === projectId) {\n          localStorage.removeItem('currentProjectId');\n        }\n        handleNewPresentation();\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  }, [setIsLoading, handleNewPresentation, setChatMessages, setChatTitle, setCurrentPresentationTitle, setCurrentProjectId, location.search, navigate, setTotalSlideCount, setCurrentSlides]);\n\n  // 初始加载和恢复会话的 useEffect\n  useEffect(() => {\n    const initializeApp = async () => {\n      setIsLoading(true);\n\n      // 1. 总是先加载历史项目列表\n      await loadProjectHistory();\n\n      // 2. 决定要加载哪个项目的详情\n      const params = new URLSearchParams(location.search);\n      let projectIdToLoad = params.get('projectId');\n\n      // 如果URL没有ID，尝试从localStorage获取\n      if (!projectIdToLoad || projectIdToLoad === 'undefined') {\n        projectIdToLoad = localStorage.getItem('currentProjectId');\n      }\n      if (projectIdToLoad && projectIdToLoad !== 'undefined') {\n        // 如果有项目ID（来自URL或localStorage），加载它\n        console.log(`[App Init] Resuming session for project: ${projectIdToLoad}`);\n        await loadProjectDetails(projectIdToLoad);\n      } else {\n        // 如果我们是因为点击\"新建\"而导航到这里的，\n        // 只需要重置标志位，然后停止执行。\n        if (isNavigatingToNew.current) {\n          console.log(\"[App Init] Navigating to a new presentation. Skipping history load.\");\n          isNavigatingToNew.current = false;\n          setIsLoading(false); // 确保结束加载状态\n          return; // 终止此 effect 的后续执行\n        }\n\n        // 否则，加载最新的项目（如果存在）\n        const history = await apiService.getProjectsHistory(); // 再次获取以确保最新\n        if (history && history.length > 0) {\n          const latestProject = history.sort((a, b) => new Date(b.last_modified) - new Date(a.last_modified))[0];\n          // 确保在导航前，最新的项目和其ID是有效的\n          if (latestProject && latestProject.project_id) {\n            console.log(`[App Init] Loading most recent project: ${latestProject.project_id}`);\n            await loadProjectDetails(latestProject.project_id);\n          } else {\n            // 如果最新项目无效，则新建一个\n            console.log(\"[App Init] No valid recent project found. Creating new presentation.\");\n            handleNewPresentation();\n          }\n        } else {\n          // 没有历史记录，创建一个新演示\n          console.log(\"[App Init] No project history found. Creating new presentation.\");\n          handleNewPresentation();\n        }\n      }\n      setIsLoading(false);\n    };\n    initializeApp();\n  }, [location.search, loadProjectHistory, loadProjectDetails, handleNewPresentation, setIsLoading]);\n\n  // 自动保存当前会话到历史记录 - 禁用连续更新，只保留页面离开时的保存\n  useEffect(() => {\n    // 这个效果不再每次状态变化就执行，现在完全依赖beforeunload事件\n    console.log(\"Session management setup\");\n\n    // 页面即将卸载时保存会话\n    const handleBeforeUnload = () => {\n      // 只保存当前项目ID，不再保存完整的chatHistory\n      if (currentProjectId) {\n        localStorage.setItem('currentProjectId', currentProjectId);\n      }\n    };\n\n    // 监听页面即将离开事件\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, [currentProjectId]); // 只依赖currentProjectId\n\n  // 仅在特定状态变化时保存到localStorage，但不更新chatHistory状态\n  useEffect(() => {\n    // 只有当有足够的内容时才保存到localStorage\n    if (chatMessages.length > 1 && currentSlides.length > 0) {\n      try {\n        var _chatMessages$find;\n        localStorage.setItem('chatMessages', JSON.stringify(chatMessages));\n        localStorage.setItem('currentSlides', JSON.stringify(currentSlides));\n        localStorage.setItem('chatTitle', chatTitle);\n        localStorage.setItem('presentationTitle', currentPresentationTitle);\n\n        // 更新currentProjectId (如果消息中有)\n        const projectIdFromMessages = (_chatMessages$find = chatMessages.find(msg => msg.project_id)) === null || _chatMessages$find === void 0 ? void 0 : _chatMessages$find.project_id;\n        if (projectIdFromMessages) {\n          setCurrentProjectId(projectIdFromMessages);\n          localStorage.setItem('currentProjectId', projectIdFromMessages);\n        }\n      } catch (error) {\n        console.error(\"保存会话到localStorage失败:\", error);\n      }\n    }\n  }, [chatMessages, currentSlides, chatTitle, currentPresentationTitle]);\n  const toggleTaskList = () => setIsTaskListOpen(!isTaskListOpen);\n  const handleChatTitleChange = async newTitle => {\n    const originalTitle = chatTitle; // 在更新前保存原始标题\n\n    // 乐观更新UI，让用户立即看到变化\n    setChatTitle(newTitle);\n    setCurrentPresentationTitle(newTitle);\n    if (currentProjectId) {\n      try {\n        await apiService.updateProject(currentProjectId, {\n          title: newTitle\n        });\n        console.log(`[App] 项目 ${currentProjectId} 的标题已成功更新到数据库: ${newTitle}`);\n        setChatHistory(prev => prev.map(item => item.project_id === currentProjectId ? {\n          ...item,\n          title: newTitle\n        } : item));\n      } catch (error) {\n        console.error(\"更新项目标题失败:\", error);\n        alert(`标题更新失败: ${error.message}`);\n\n        // --- BUG修复 ---\n        // 如果API调用失败，将UI恢复到原始标题\n        setChatTitle(originalTitle);\n        setCurrentPresentationTitle(originalTitle);\n      }\n    }\n  };\n  const handleBackClick = () => {\n    if (isGenerating) {\n      // 如果正在生成，取消生成\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n        eventSourceRef.current = null;\n      }\n      setIsGenerating(false);\n      setChatMessages(prev => [...prev, {\n        id: Date.now(),\n        type: 'ai_error',\n        text: '生成已取消。',\n        sender: 'system',\n        // 明确sender为system\n        icon: '❌' // 添加一个图标\n      }]);\n      return;\n    }\n\n    // 如果没有在生成，则返回首页\n    handleNewPresentation(); // <-- 修复后的正确逻辑\n  };\n  const handleSelectHistory = historyItem => {\n    // FIX: 增加检查，确保 historyItem 和 project_id 存在且有效\n    if (!historyItem || !historyItem.project_id) {\n      console.error(\"无法选择无效的历史项目:\", historyItem);\n      return; // 中止操作\n    }\n    console.log(\"选择历史项目:\", historyItem);\n    setShowHistoryDrawer(false); // 关闭抽屉\n\n    // 关键：只进行导航，让 useEffect 钩子去处理数据的加载\n    navigate(`/?projectId=${historyItem.project_id}`);\n  };\n  const handleDeleteHistory = async (historyItemId, projectIdentifier) => {\n    var _chatHistory$find;\n    // 确保不删除当前会话\n    if (historyItemId === 'current-session') return;\n\n    // 准备删除确认\n    setDeleteItemInfo({\n      id: historyItemId,\n      // 这个是前端 chatHistory 数组中的项的 ID\n      projectId: projectIdentifier,\n      // 这个是后端的真实项目 ID\n      title: ((_chatHistory$find = chatHistory.find(item => item.id === historyItemId)) === null || _chatHistory$find === void 0 ? void 0 : _chatHistory$find.title) || \"该项目\"\n    });\n    setShowDeleteConfirmModal(true);\n  };\n  const handleConfirmDelete = async () => {\n    if (!deleteItemInfo || !deleteItemInfo.projectId) {\n      setShowDeleteConfirmModal(false);\n      setDeleteItemInfo(null);\n      return;\n    }\n    const {\n      id: localHistoryItemId,\n      projectId: serverProjectId\n    } = deleteItemInfo;\n    try {\n      // 1. 调用后端 API 删除服务器端数据\n      const response = await apiService.deleteProject(serverProjectId);\n      console.log(`项目 ${serverProjectId} 删除响应:`, response);\n\n      // 2. 更新本地 chatHistory 状态\n      const updatedHistory = chatHistory.filter(item => item.id !== localHistoryItemId);\n      setChatHistory(updatedHistory);\n\n      // 3. 如果删除的是当前加载的会话，则重置视图\n      if (currentProjectId === serverProjectId) {\n        handleNewPresentation();\n      }\n      console.log(`项目 ${serverProjectId} (本地历史项ID: ${localHistoryItemId}) 已成功删除。`);\n    } catch (error) {\n      console.error(`删除项目 ${serverProjectId} 时出错:`, error);\n      alert(`删除项目失败: ${error.message}`);\n    } finally {\n      setShowDeleteConfirmModal(false);\n      setDeleteItemInfo(null);\n    }\n  };\n  const handleToggleEditSlide = () => {\n    setIsEditingMode(!isEditingMode);\n    setEditingTextElement(null);\n  };\n\n  // Text Editing Handlers\n  /*\n  const handleTextEditStart = useCallback((slideId, elementId, initialText, initialStyle, position) => {\n    // Adjust position relative to the overall RightPane or a more stable parent\n    const rightPaneContentArea = document.querySelector('.right-pane-content-area');\n    let editorX = position.x;\n    let editorY = position.y;\n     if (rightPaneContentArea) {\n      const paneRect = rightPaneContentArea.getBoundingClientRect();\n      editorX = position.x + paneRect.left;\n      editorY = position.y + paneRect.top - rightPaneContentArea.scrollTop;\n    }\n    setEditingTextElement({ slideId, elementId, initialText, initialStyle, position: {x: editorX, y: editorY} });\n  }, []);\n  */\n\n  const handleTextEditSave = (newText, newStyle) => {\n    if (!editingTextElement) return;\n    const {\n      slideId,\n      elementId\n    } = editingTextElement;\n    setCurrentSlides(prevSlides => prevSlides.map(slide => {\n      if (slide.id === slideId) {\n        const tempDiv = document.createElement('div');\n        tempDiv.innerHTML = slide.html;\n        const targetElement = tempDiv.querySelector(`[data-editable-id=\"${elementId}\"]`);\n        if (targetElement) {\n          targetElement.innerText = newText;\n          Object.keys(newStyle).forEach(key => {\n            targetElement.style[key] = newStyle[key];\n          });\n        }\n        return {\n          ...slide,\n          html: tempDiv.innerHTML,\n          code: tempDiv.innerHTML\n        };\n      }\n      return slide;\n    }));\n    setEditingTextElement(null);\n  };\n  const handleTextEditCancel = () => setEditingTextElement(null);\n\n  // +++++++++++++++ 新增处理函数 +++++++++++++++\n  const handleRewriteClick = e => {\n    e.stopPropagation();\n    if (contextMenu) {\n      setEditModal({\n        slideId: contextMenu.slideId,\n        elementId: contextMenu.elementId\n      });\n      setContextMenu(null); // 关闭上下文菜单\n    }\n  };\n  const handleCloseEditModal = () => {\n    setEditModal(null);\n    setEditInstruction('');\n    setIsEditingElement(false);\n  };\n  const handleSubmitEdit = async () => {\n    if (!editInstruction.trim() || !editModal) return;\n    setIsEditingElement(true);\n    const {\n      slideId,\n      elementId\n    } = editModal;\n    try {\n      const result = await apiService.editSlideElement(slideId, `[data-editable-id=\"${elementId}\"]`, editInstruction);\n      if (result && result.html) {\n        // 更新幻灯片状态\n        setCurrentSlides(prevSlides => prevSlides.map(slide => slide.id === slideId ? {\n          ...slide,\n          html: result.html,\n          code: result.html\n        } : slide));\n      }\n    } catch (error) {\n      console.error('编辑元素失败:', error);\n      alert(`编辑失败: ${error.message}`);\n    } finally {\n      handleCloseEditModal();\n    }\n  };\n  // +++++++++++++++++++++++++++++++++++++++++++++\n\n  // Handle opening the player view\n  const handleOpenViewAndExport = () => {\n    navigate('/player', {\n      state: {\n        slides: currentSlides,\n        initialIndex: 0,\n        presentationTitle: currentPresentationTitle\n      }\n    });\n  };\n\n  // Handle messages from SlidePlayerView\n  useEffect(() => {\n    const handleMessageFromPlayerView = event => {\n      if (event.data && event.data.type === 'request-fullscreen') {\n        // Set the appropriate slide index if provided\n        if (event.data.payload && event.data.payload.initialIndex !== undefined) {\n          setCurrentFullScreenSlideIndex(event.data.payload.initialIndex);\n        }\n        // Toggle fullscreen\n        toggleFullScreen();\n      }\n    };\n    window.addEventListener('message', handleMessageFromPlayerView);\n    return () => {\n      window.removeEventListener('message', handleMessageFromPlayerView);\n    };\n  }, [toggleFullScreen]);\n\n  // +++++++++++++++ 新增useEffect来处理来自iframe的消息 +++++++++++++++\n  useEffect(() => {\n    const handleIframeMessage = event => {\n      if (event.data && event.data.type === 'element_clicked' && isEditingMode) {\n        const {\n          slideId,\n          elementId,\n          clickPosition\n        } = event.data.payload;\n\n        // 找到对应的iframe，计算屏幕上的绝对位置\n        const iframe = document.querySelector(`#slide-preview-${slideId} iframe`);\n        if (iframe) {\n          const iframeRect = iframe.getBoundingClientRect();\n          const scale = iframeRect.width / 1280; // 假设设计宽度是1280\n\n          const absoluteX = iframeRect.left + clickPosition.x * scale + window.scrollX;\n          const absoluteY = iframeRect.top + clickPosition.y * scale + window.scrollY;\n          setContextMenu({\n            x: absoluteX,\n            y: absoluteY,\n            slideId,\n            elementId\n          });\n        }\n      }\n    };\n    window.addEventListener('message', handleIframeMessage);\n    // 点击其他地方时关闭菜单\n    const closeMenu = () => setContextMenu(null);\n    window.addEventListener('click', closeMenu);\n    return () => {\n      window.removeEventListener('message', handleIframeMessage);\n      window.removeEventListener('click', closeMenu);\n    };\n  }, [isEditingMode]); // 只在编辑模式下监听\n  // ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n\n  useEffect(() => {\n    // 这个 useEffect 现在是同步 isFullScreen 状态的唯一来源\n    const handleFullscreenChange = () => {\n      const isCurrentlyFullScreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullscreenElement || document.msFullscreenElement);\n      setIsFullScreen(isCurrentlyFullScreen);\n    };\n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);\n    document.addEventListener('mozfullscreenchange', handleFullscreenChange);\n    document.addEventListener('MSFullscreenChange', handleFullscreenChange);\n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);\n      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);\n      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);\n    };\n  }, []);\n\n  // 用于 RightPane 设置当前活动/聚焦的幻灯片\n  const handleSetCurrentSlideIndex = index => {\n    setCurrentSlideIndex(index);\n  };\n  const handleSlideTabChange = (slideIndex, tabIndex) => {\n    setSlideTabIndices(prev => ({\n      ...prev,\n      [slideIndex]: tabIndex\n    }));\n  };\n\n  // 用于播放特定索引的幻灯片\n  const handlePlaySlideAtIndex = index => {\n    setCurrentFullScreenSlideIndex(index);\n    toggleFullScreen();\n  };\n  const handleSendMessage = async messageData => {\n    // 处理参数：可能是字符串或对象\n    let messageText,\n      files = [];\n    if (typeof messageData === 'string') {\n      messageText = messageData;\n    } else if (typeof messageData === 'object' && messageData !== null) {\n      messageText = messageData.message || '';\n      files = messageData.files || [];\n    } else {\n      messageText = '';\n    }\n    if (!messageText.trim() || isGenerating) return;\n\n    // 生成独特的消息ID\n    const userMsgId = `user-msg-${Date.now()}`;\n\n    // 添加用户消息到聊天\n    const userMessage = {\n      id: userMsgId,\n      type: 'user',\n      text: messageText,\n      files: files.length > 0 ? files : undefined // 只在有文件时添加files属性\n    };\n\n    // 判断是否是\"继续\"生成请求\n    const isContinueRequest = messageText.toLowerCase().includes(\"继续\") || messageText.toLowerCase().includes(\"continue\");\n\n    // 【核心修改1】优化新生成请求时的状态清空逻辑\n    if (!isContinueRequest) {\n      // 对于新的演示文稿生成请求，清除所有之前的AI消息，并重置项目相关状态\n      setChatMessages([userMessage]); // 仅保留当前用户消息\n      setCurrentSlides([]);\n      setTotalSlideCount(0);\n      setCurrentProjectId(null); // 这将强制后端创建一个新项目\n\n      // 重置其他UI相关状态，以获得一个全新的开始\n      setCurrentStep(null);\n      setTools([]);\n      setOutlineContent('');\n      setSlideCode('');\n      setActiveStep('preview');\n    } else {\n      // 对于\"继续\"请求，仅追加新的用户消息\n      setChatMessages(prev => [...prev, userMessage]);\n    }\n    let retryAttempts = 0;\n    const MAX_MESSAGE_RETRIES = 3;\n    const executeGeneration = async projectIdToUse => {\n      try {\n        // 设置生成状态为true\n        setIsGenerating(true);\n\n        // 关闭之前的EventSource连接（如果存在）\n        if (eventSourceRef.current) {\n          eventSourceRef.current.close();\n        }\n\n        // 初始化临时幻灯片数组 (这个现在不再由前端维护，后端SSE会直接推送)\n        // let tempSlidesData = []; \n\n        // 创建消息ID映射，用于后续更新或替换特定消息\n        const messageIdMap = {};\n\n        // 决定使用哪个 project_id\n        // `projectIdToUse` 会是 `null` (新生成) 或 `currentProjectId` (继续生成)\n        const effectiveProjectId = projectIdToUse;\n\n        // 使用新的简化工作流API\n        eventSourceRef.current = await apiService.generatePresentation(messageText,\n        // 进度处理\n        progressData => {\n          // console.log(\"进度更新:\", progressData);\n\n          // 处理 id_mapped_to_client 事件 - 当临时ID被映射到实际ID时\n          if (progressData.status === 'id_mapped_to_client' && progressData.metadata && progressData.metadata.actual_project_id) {\n            const newActualId = progressData.metadata.actual_project_id;\n            console.log(`[App.js] 临时项目ID已映射到实际ID: ${newActualId}`);\n            setCurrentProjectId(newActualId);\n            localStorage.setItem('currentProjectId', newActualId);\n            // 更新URL，但不触发页面重新加载\n            navigate(`/?projectId=${newActualId}`, {\n              replace: true\n            });\n            return; // 不需要进一步处理这个事件\n          }\n\n          // 保存项目ID - 如果这是首次获取项目ID (且不是 id_mapped_to_client 消息)\n          if (!currentProjectId && progressData.project_id && progressData.status !== 'id_mapping') {\n            console.log(\"[App.js] 从SSE获取到项目ID:\", progressData.project_id);\n            setCurrentProjectId(progressData.project_id);\n            localStorage.setItem('currentProjectId', progressData.project_id); // 添加这行\n          }\n\n          // 如果进度正常，重置重试次数\n          retryAttempts = 0;\n\n          // 更新AI消息\n          if (progressData.message) {\n            const messageText = progressData.message.text;\n\n            // 跳过初始化和连接建立消息\n            if (messageText === '⏳正在解析您的请求，AI引擎启动中...' || messageText === '🔌连接已建立，等待更新..') {\n              return;\n            }\n\n            // 检查是否是需要替换的消息类型\n            let shouldReplace = false;\n            let messageToReplaceId = null;\n            let additionalInfo = '';\n\n            // 处理风格确定消息\n            if (messageText.includes('的整体风格已确定')) {\n              const styleWaitingMsgId = messageIdMap['style_waiting'];\n              if (styleWaitingMsgId) {\n                messageToReplaceId = styleWaitingMsgId;\n                shouldReplace = true;\n\n                // 计算实际用时\n                const startTime = messageIdMap['style_waiting_start_time'];\n                if (startTime) {\n                  const elapsedTime = Date.now() - startTime;\n                  const seconds = Math.floor(elapsedTime / 1000);\n                  if (seconds < 60) {\n                    additionalInfo = `（实际用时${seconds}秒）`;\n                  } else {\n                    const minutes = Math.floor(seconds / 60);\n                    const remainingSeconds = seconds % 60;\n                    additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                  }\n                }\n              }\n            }\n\n            // 处理内容规划完成消息\n            if (messageText.includes('内容规划完成：共')) {\n              const contentPlanningMsgId = messageIdMap['content_planning'];\n              if (contentPlanningMsgId) {\n                messageToReplaceId = contentPlanningMsgId;\n                shouldReplace = true;\n\n                // 计算实际用时\n                const startTime = messageIdMap['content_planning_start_time'];\n                if (startTime) {\n                  const elapsedTime = Date.now() - startTime;\n                  const seconds = Math.floor(elapsedTime / 1000);\n                  if (seconds < 60) {\n                    additionalInfo = `（实际用时${seconds}秒）`;\n                  } else {\n                    const minutes = Math.floor(seconds / 60);\n                    const remainingSeconds = seconds % 60;\n                    additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                  }\n                }\n              }\n            }\n\n            // 处理幻灯片生成完成消息\n            const slideGenCompleteRegex = /第 (\\d+)\\/(\\d+) 张.*生成完毕/;\n            const slideMatch = messageText.match(slideGenCompleteRegex);\n            if (slideMatch) {\n              const slideNum = slideMatch[1];\n              const slideProcessingMsgId = messageIdMap[`slide_processing_${slideNum}`];\n              if (slideProcessingMsgId) {\n                messageToReplaceId = slideProcessingMsgId;\n                shouldReplace = true;\n\n                // 计算实际用时\n                const startTime = messageIdMap[`slide_processing_${slideNum}_start_time`];\n                if (startTime) {\n                  const elapsedTime = Date.now() - startTime;\n                  const seconds = Math.floor(elapsedTime / 1000);\n                  if (seconds < 60) {\n                    additionalInfo = `（实际用时${seconds}秒）`;\n                  } else {\n                    const minutes = Math.floor(seconds / 60);\n                    const remainingSeconds = seconds % 60;\n                    additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                  }\n                }\n              }\n            }\n\n            // 记录特殊消息的ID和时间戳，用于后续替换\n            if (messageText.includes('正在为') && messageText.includes('规划整体视觉风格')) {\n              const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n              messageIdMap['style_waiting'] = newMsgId;\n              messageIdMap['style_waiting_start_time'] = Date.now();\n\n              // 创建新消息\n              setChatMessages(prev => [...prev, {\n                id: newMsgId,\n                type: 'ai',\n                text: messageText,\n                icon: progressData.message.icon || undefined,\n                thinking: progressData.message.thinking || false,\n                // 使用 thinking 字段\n                sender: progressData.message.sender || 'ai' // 确保sender正确\n              }]);\n              return; // 直接返回，避免重复添加\n            }\n            if (messageText.includes('正在为') && messageText.includes('规划详细内容')) {\n              const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n              messageIdMap['content_planning'] = newMsgId;\n              messageIdMap['content_planning_start_time'] = Date.now();\n\n              // 创建新消息\n              setChatMessages(prev => [...prev, {\n                id: newMsgId,\n                type: 'ai',\n                text: messageText,\n                icon: progressData.message.icon || undefined,\n                thinking: progressData.message.thinking || false,\n                // 使用 thinking 字段\n                sender: progressData.message.sender || 'ai'\n              }]);\n              return; // 直接返回，避免重复添加\n            }\n            const slideProcessingRegex = /开始处理第 (\\d+)\\/(\\d+) 张幻灯片/;\n            const match = messageText.match(slideProcessingRegex);\n            if (match) {\n              const slideNum = match[1];\n              const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n              messageIdMap[`slide_processing_${slideNum}`] = newMsgId;\n              messageIdMap[`slide_processing_${slideNum}_start_time`] = Date.now();\n\n              // 创建新消息\n              setChatMessages(prev => [...prev, {\n                id: newMsgId,\n                type: 'ai',\n                text: messageText,\n                icon: progressData.message.icon || undefined,\n                thinking: progressData.message.thinking || false,\n                // 使用 thinking 字段\n                sender: progressData.message.sender || 'ai'\n              }]);\n              return; // 直接返回，避免重复添加\n            }\n\n            // 创建新消息或替换现有消息\n            const newAiMsgId = shouldReplace ? messageToReplaceId : `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n            setChatMessages(prev => {\n              const currentAiMessageIndex = prev.findIndex(msg => msg.id === newAiMsgId);\n              if (currentAiMessageIndex !== -1) {\n                // 替换现有消息\n                const updatedMessages = [...prev];\n                updatedMessages[currentAiMessageIndex] = {\n                  ...updatedMessages[currentAiMessageIndex],\n                  text: additionalInfo ? `${messageText} ${additionalInfo}` : messageText,\n                  icon: progressData.message.icon || undefined,\n                  thinking: progressData.message.thinking || false,\n                  // 使用 thinking 字段\n                  sender: progressData.message.sender || 'ai'\n                };\n                return updatedMessages;\n              } else {\n                // 添加新消息\n                return [...prev, {\n                  id: newAiMsgId,\n                  type: 'ai',\n                  text: messageText,\n                  icon: progressData.message.icon || undefined,\n                  thinking: progressData.message.thinking || false,\n                  // 使用 thinking 字段\n                  sender: progressData.message.sender || 'ai'\n                }];\n              }\n            });\n          }\n\n          // 当接收到开始状态和AI识别的主题时，更新标题\n          if (progressData.status === \"starting\" && progressData.message && progressData.message.text) {\n            const topicMatch = progressData.message.text.match(/关于「(.+?)」/);\n            if (topicMatch && topicMatch[1]) {\n              const extractedTopic = topicMatch[1].trim();\n              if (extractedTopic && chatTitle !== `AI幻灯片 ${extractedTopic}`) {\n                const newTitle = `AI幻灯片 ${extractedTopic}`;\n                setChatTitle(newTitle);\n              }\n            }\n          }\n\n          // 新增/修改：当意图分析完成时，更精确地更新标题\n          // 当意图分析完成时，根据后端元数据或消息文本更新标题\n          if (progressData.status === \"intent_analyzed\") {\n            // 优先使用 metadata 中的精确标题 (这是最可靠的方法)\n            if (progressData.metadata && progressData.metadata.updated_title) {\n              const newTitle = progressData.metadata.updated_title;\n              console.log(`[App.js] Intent analyzed, updating title from metadata: ${newTitle}`);\n              setChatTitle(newTitle);\n              setCurrentPresentationTitle(newTitle);\n            }\n            // 如果 metadata 中没有标题，则回退到从消息文本中解析 (作为备用方案)\n            else if (progressData.message && progressData.message.text) {\n              const topicMatch = progressData.message.text.match(/主题：\"(.+?)\"/);\n              if (topicMatch && topicMatch[1]) {\n                const extractedTopic = topicMatch[1].trim();\n                const newTitle = `AI幻灯片 ${extractedTopic}`;\n                console.log(`[App.js] Intent analyzed, updating title from message text (fallback): ${newTitle}`);\n                setChatTitle(newTitle);\n                setCurrentPresentationTitle(newTitle);\n              }\n            }\n          }\n\n          // 处理思考内容和总幻灯片数\n          if (progressData.thinking_content) {\n            setOutlineContent(progressData.thinking_content);\n          }\n          if (progressData.total_slides && progressData.total_slides > 0) {\n            setTotalSlideCount(progressData.total_slides);\n          }\n\n          // 处理UI交互动作\n          if (progressData.ui_action) {\n            // 处理标签页切换\n            if (progressData.ui_action.action === \"select_tab\" && progressData.current_slide_index !== undefined) {\n              // 设置当前幻灯片的标签页\n              if (progressData.ui_action.tab === \"code\") {\n                setActiveStep('code');\n                setSlideTabIndices(prev => ({\n                  ...prev,\n                  [progressData.current_slide_index]: 1 // 1 表示代码标签\n                }));\n              } else if (progressData.ui_action.tab === \"preview\") {\n                setActiveStep('preview');\n                setSlideTabIndices(prev => ({\n                  ...prev,\n                  [progressData.current_slide_index]: 0 // 0 表示预览标签\n                }));\n              }\n            }\n          }\n\n          // 处理幻灯片更新\n          if (progressData.slide_update) {\n            const newSlide = {\n              id: progressData.slide_update.id,\n              html: progressData.slide_update.html || progressData.slide_update.html_content,\n              // 兼容字段\n              code: progressData.slide_update.code || progressData.slide_update.html || progressData.slide_update.html_content,\n              order: progressData.slide_update.slide_number !== undefined ? progressData.slide_update.slide_number - 1 : 0,\n              // 确保使用slide_number转为0-based\n              title: `幻灯片 ${progressData.slide_update.slide_number}`\n            };\n\n            // 使用函数式更新来保证我们总是基于最新的幻灯片列表进行操作\n            setCurrentSlides(prevSlides => {\n              const existingIndex = prevSlides.findIndex(s => s.id === newSlide.id || s.order === newSlide.order);\n              let updatedSlides;\n              if (existingIndex >= 0) {\n                // 更新现有幻灯片\n                updatedSlides = [...prevSlides];\n                updatedSlides[existingIndex] = newSlide;\n              } else {\n                // 添加新幻灯片\n                updatedSlides = [...prevSlides, newSlide];\n              }\n\n              // 按 order 排序，以防消息乱序\n              updatedSlides.sort((a, b) => a.order - b.order);\n              return updatedSlides;\n            });\n\n            // 更新当前焦点幻灯片\n            if (progressData.current_slide_index !== undefined) {\n              setCurrentSlideIndex(progressData.current_slide_index);\n            }\n\n            // 如果有代码内容，更新代码显示\n            if (progressData.code_content) {\n              setSlideCode(progressData.code_content);\n            }\n          }\n\n          // 如果状态为完成，更新最终消息\n          if (progressData.status === 'completed') {\n            // 添加完成消息\n            const completionMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n            setChatMessages(prev => [...prev, {\n              id: completionMsgId,\n              type: 'ai',\n              text: `✅ 幻灯片已全部生成完毕 (${progressData.total_slides || currentSlides.length}张)，您可以点击每张幻灯片进行查看和编辑。`,\n              icon: '✅',\n              thinking: false,\n              // 完成时设置为非思考中\n              sender: 'ai',\n              // 确保sender正确\n              project_id: progressData.project_id || currentProjectId // 确保保存项目ID\n            }]);\n            setIsEditingMode(true);\n\n            // 设置演示文稿标题\n            if (!currentPresentationTitle || currentPresentationTitle === \"AI幻灯片\") {\n              setCurrentPresentationTitle(messageText.length > 30 ? messageText.substring(0, 30) + \"...\" : messageText);\n            }\n\n            // 保存聊天历史\n            saveCurrentChatToHistory();\n\n            // 完成后刷新项目历史列表\n            refreshProjectHistory();\n          }\n        },\n        // 错误处理\n        error => {\n          console.error('App.js - 生成幻灯片时发生错误:', error);\n\n          // 确保关闭旧的连接\n          if (eventSourceRef.current && typeof eventSourceRef.current.close === 'function') {\n            eventSourceRef.current.close();\n          }\n\n          // 处理特殊错误类型，尝试重新连接\n          if ((error.message === 'SSE_HEARTBEAT_TIMEOUT' || error.message.includes('connection failed')) && retryAttempts < MAX_MESSAGE_RETRIES) {\n            retryAttempts++;\n            setChatMessages(prev => [...prev, {\n              id: `sys-retry-${Date.now()}`,\n              type: 'system-info',\n              text: `连接中断，正在尝试重新连接... (${retryAttempts}/${MAX_MESSAGE_RETRIES}) 项目: ${effectiveProjectId || '新项目'}`,\n              sender: 'system',\n              icon: '⏳'\n            }]);\n\n            // 延迟重试，每次重试增加延迟\n            setTimeout(() => executeGeneration(currentProjectId), 3000 * retryAttempts);\n          } else {\n            // 超过最大重试次数或其他错误\n            setIsGenerating(false);\n            setChatMessages(prev => [...prev, {\n              id: `error-${Date.now()}`,\n              type: 'ai_error',\n              text: `抱歉，发生错误: ${error.message}`,\n              sender: 'system',\n              icon: '⚠️'\n            }]);\n          }\n        },\n        // 完成处理\n        () => {\n          console.log('App.js - 幻灯片生成流完成');\n          setIsGenerating(false);\n          // 不再在这里添加完成消息，依赖后端推送的 'completed' status\n          saveCurrentChatToHistory(); // 确保最终状态被保存\n        },\n        // 传递项目ID（如果是新会话，后端会创建；如果是重试，则使用 existing_project_id）\n        effectiveProjectId ? {\n          project_id: effectiveProjectId\n        } : {});\n      } catch (apiError) {\n        console.error('App.js - API调用启动错误:', apiError);\n        setIsGenerating(false);\n        setChatMessages(prev => [...prev, {\n          id: `error-api-${Date.now()}`,\n          type: 'ai_error',\n          text: `启动生成失败: ${apiError.message}`,\n          sender: 'system',\n          icon: '❌'\n        }]);\n      }\n    };\n\n    // 执行生成。如果不是\"继续\"请求，`projectIdToUse` 将是 `null`，后端将创建新项目ID。\n    // 否则，它将是 `currentProjectId`。\n    executeGeneration(isContinueRequest ? currentProjectId : null);\n  };\n\n  // 新增函数：刷新项目历史列表\n  const refreshProjectHistory = async () => {\n    try {\n      const serverHistory = await apiService.getProjectsHistory();\n      console.log(\"[App.js] Refreshed project history from server:\", serverHistory);\n\n      // 将后端返回的项目摘要转换为前端 chatHistory 格式\n      const formattedServerHistory = serverHistory.map(proj => ({\n        id: proj.id,\n        // 修复\n        title: proj.title || `项目 (${proj.id ? proj.id.slice(-4) : '未知'})`,\n        // 修复\n        timestamp: proj.last_modified || proj.created_at,\n        project_id: proj.id,\n        // 修复\n        messages: [],\n        slides: [],\n        isSummary: true\n      }));\n\n      // 更新 chatHistory 状态，保留本地项目\n      setChatHistory(prev => {\n        // 获取所有服务器项目的 ID\n        const serverProjectIds = new Set(formattedServerHistory.map(item => item.project_id)); // 这里现在可以正确获取ID了\n\n        // 保留本地项目（那些不在服务器上的）\n        const localOnlyProjects = prev.filter(item => !serverProjectIds.has(item.project_id)); // 这里的去重逻辑现在也能正常工作了\n\n        // 合并并按时间戳排序\n        const combined = [...formattedServerHistory, ...localOnlyProjects].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));\n\n        // 保存到 localStorage\n        try {\n          localStorage.setItem('chatHistory', JSON.stringify(combined));\n        } catch (error) {\n          console.error(\"保存刷新的聊天历史到本地存储失败:\", error);\n        }\n        return combined;\n      });\n    } catch (error) {\n      console.error(\"刷新项目历史列表失败:\", error);\n    }\n  };\n  const handleViewSearchResults = results => {\n    setCurrentSearchResults(results);\n    setShowSearchResultsModal(true);\n  };\n\n  // Auto-generate slides on first load - disabled for production with real backend\n  /*\n  useEffect(() => {\n    if (currentSlides.length === 0 && !isGenerating) {\n      processSimulatedGeneration(\"生成一个中国房地产市场2025年趋势的PPT\");\n    }\n  }, []);\n  */\n\n  // 移除旧的键盘事件监听代码，现在由 FullScreenPlayer 组件通过 postMessage 处理\n\n  // 监听来自 SlidePlayerView 的请求，以启动全屏播放器\n  useEffect(() => {\n    if (location.state && location.state.action === 'startFullScreenPlayer') {\n      const {\n        slides: slidesToPlay,\n        initialIndex,\n        presentationTitle: titleFromPlayer\n      } = location.state;\n\n      // 你可能需要更新 currentSlides 和 presentationTitle，如果它们与 App.js 的当前状态不同\n      // 或者，如果 App.js 是这些状态的唯一来源，SlidePlayerView 应该总是从 App.js 获取最新的\n      if (slidesToPlay && slidesToPlay.length > 0) {\n        setCurrentSlides(slidesToPlay); // 确保幻灯片数据是最新的\n      }\n      if (titleFromPlayer) {\n        setCurrentPresentationTitle(titleFromPlayer);\n      }\n      setCurrentFullScreenSlideIndex(initialIndex || 0);\n      toggleFullScreen(); // 触发全屏\n\n      // 清除 location.state 防止刷新或其他导航时再次触发\n      navigate(location.pathname, {\n        replace: true,\n        state: {}\n      });\n    }\n  }, [location.state, navigate, toggleFullScreen, location.pathname]); // 添加 location.pathname 为依赖项\n\n  // Handlers for history drawer\n  const handleToggleHistoryDrawer = () => {\n    const newDrawerState = !showHistoryDrawer;\n    setShowHistoryDrawer(newDrawerState);\n    if (newDrawerState) {\n      // 如果是打开抽屉\n      refreshProjectHistory(); // 刷新历史记录\n    }\n  };\n  const handleCloseHistoryDrawer = () => {\n    setShowHistoryDrawer(false);\n  };\n  const handleSelectHistoryItem = historyItem => {\n    handleSelectHistory(historyItem); // Assuming handleSelectHistory is defined in App.js\n    handleCloseHistoryDrawer();\n  };\n  const handleCancelDelete = () => {\n    setDeleteItemInfo(null);\n    setShowDeleteConfirmModal(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-screen bg-white\",\n    children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col justify-center items-center h-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/tiktodo-loading.svg\",\n        alt: \"Loading\",\n        className: \"h-16 w-16 mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1353,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xl font-semibold text-gray-800 mb-2\",\n          children: \"TikTodo \\u8F7D\\u5165\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1355,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: \"\\u8BF7\\u7A0D\\u7B49\\uFF0C\\u9A6C\\u4E0A\\u5C31\\u597D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1356,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1354,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1352,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-tiktodo-blue to-tiktodo-purple h-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1361,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-1 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(LeftNav, {\n          isTaskListOpen: isTaskListOpen,\n          onToggleTaskList: toggleTaskList,\n          historyItems: chatHistory,\n          currentProjectId: currentProjectId,\n          onSelectHistoryItem: handleSelectHistoryItem,\n          onDeleteHistoryItem: handleDeleteHistory,\n          isLoadingHistory: isLoadingHistory\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1363,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(MiddlePane, {\n          title: chatTitle || \"AI 幻灯片\",\n          onTitleChange: handleChatTitleChange,\n          isGenerating: isGenerating,\n          messages: chatMessages,\n          onSendMessage: handleSendMessage,\n          chatRef: chatRef,\n          handleViewSearchResults: handleViewSearchResults // 查看搜索结果的处理函数\n\n          // 工具栏\n          ,\n          showHistoryToggle: true,\n          onToggleTaskList: toggleTaskList,\n          onToggleHistory: handleToggleHistoryDrawer,\n          onNewPresentation: handleNewPresentation\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1372,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(RightPane, {\n          slides: currentSlides,\n          currentSlideIndex: currentSlideIndex,\n          onSlideIndexChange: handleSetCurrentSlideIndex,\n          onSlideTabChange: handleSlideTabChange,\n          onPlayClick: handlePlaySlideAtIndex,\n          onEditSlide: handleToggleEditSlide,\n          isEditingMode: isEditingMode,\n          slideTabIndices: slideTabIndices,\n          isGeneratingSlides: isGenerating,\n          tools: tools,\n          currentStep: currentStep,\n          outlineContent: outlineContent,\n          slideCode: slideCode,\n          activeStep: activeStep,\n          currentFullScreenSlideIndex: currentFullScreenSlideIndex,\n          onShareClick: () => {\n            alert(\"分享功能开发中\");\n          },\n          onRewriteClick: handleRewriteClick,\n          editModal: editModal,\n          editInstruction: editInstruction,\n          onCloseEditModal: handleCloseEditModal,\n          onSubmitEdit: handleSubmitEdit,\n          onViewExport: handleOpenViewAndExport\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1388,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1362,\n        columnNumber: 11\n      }, this), showSearchResultsModal && /*#__PURE__*/_jsxDEV(SearchResultsModal, {\n        results: currentSearchResults,\n        onClose: () => setShowSearchResultsModal(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1418,\n        columnNumber: 13\n      }, this), showHistoryDrawer && /*#__PURE__*/_jsxDEV(HistoryDrawer, {\n        history: chatHistory,\n        onClose: handleCloseHistoryDrawer,\n        onSelectItem: handleSelectHistoryItem,\n        currentProjectId: currentProjectId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1426,\n        columnNumber: 13\n      }, this), showDeleteConfirmModal && deleteItemInfo && /*#__PURE__*/_jsxDEV(DeleteConfirmModal, {\n        title: `删除 ${deleteItemInfo.title || '项目'}`,\n        message: `您确定要删除此项目吗？此操作不可恢复。`,\n        onConfirm: handleConfirmDelete,\n        onCancel: handleCancelDelete\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1436,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1350,\n    columnNumber: 5\n  }, this);\n};\n_s(MainApp, \"7brGnZgVEGlnNdbjiRbY1AT/QNM=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c10 = MainApp;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(MainApp, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1453,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1453,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/player\",\n        element: /*#__PURE__*/_jsxDEV(Suspense, {\n          fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: \"\\u64AD\\u653E\\u5668\\u52A0\\u8F7D\\u4E2D...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1455,\n            columnNumber: 31\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(SlidePlayerView, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1456,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1455,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1454,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/chat\",\n        element: /*#__PURE__*/_jsxDEV(Suspense, {\n          fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: \"\\u52A0\\u8F7DAI\\u804A\\u5929...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1460,\n            columnNumber: 31\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(ChatViewPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1461,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1460,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1459,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1452,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1451,\n    columnNumber: 5\n  }, this);\n}\n_c11 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11;\n$RefreshReg$(_c, \"InlineTextEditor$lazy\");\n$RefreshReg$(_c2, \"InlineTextEditor\");\n$RefreshReg$(_c3, \"SlidePlayerView$lazy\");\n$RefreshReg$(_c4, \"SlidePlayerView\");\n$RefreshReg$(_c5, \"FullScreenPlayer$lazy\");\n$RefreshReg$(_c6, \"FullScreenPlayer\");\n$RefreshReg$(_c7, \"ChatViewPage$lazy\");\n$RefreshReg$(_c8, \"ChatViewPage\");\n$RefreshReg$(_c9, \"SearchResultsModal\");\n$RefreshReg$(_c0, \"HistoryDrawer\");\n$RefreshReg$(_c1, \"DeleteConfirmModal\");\n$RefreshReg$(_c10, \"MainApp\");\n$RefreshReg$(_c11, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "lazy", "Suspense", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "useNavigate", "useLocation", "LeftNav", "MiddlePane", "RightPane", "FaFilePdf", "FaGlobe", "FaTrash", "FaFeatherAlt", "formatDate", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InlineTextEditor", "_c", "_c2", "SlidePlayerView", "_c3", "_c4", "FullScreenPlayer", "_c5", "_c6", "ChatViewPage", "_c7", "_c8", "SearchResultsModal", "results", "onClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "result", "href", "source", "target", "rel", "type", "title", "snippet", "id", "_c9", "HistoryDrawer", "history", "onSelectItem", "currentProjectId", "item", "Date", "timestamp", "_c0", "DeleteConfirmModal", "message", "onConfirm", "onCancel", "_c1", "MainApp", "_s", "navigate", "location", "isLoading", "setIsLoading", "isLoadingHistory", "setIsLoadingHistory", "isTaskListOpen", "setIsTaskListOpen", "currentPresentationTitle", "setCurrentPresentationTitle", "chatTitle", "setChatTitle", "isGenerating", "setIsGenerating", "chatMessages", "setChatMessages", "currentSlides", "setCurrentSlides", "currentSlideIndex", "setCurrentSlideIndex", "editingTextElement", "setEditingTextElement", "showSearchResultsModal", "setShowSearchResultsModal", "currentSearchResults", "setCurrentSearchResults", "isFullScreen", "setIsFullScreen", "currentFullScreenSlideIndex", "setCurrentFullScreenSlideIndex", "isEditingMode", "setIsEditingMode", "chatHistory", "setChatHistory", "eventSourceRef", "chatRef", "tools", "setTools", "currentStep", "setCurrentStep", "outlineContent", "setOutlineContent", "slideCode", "setSlideCode", "activeStep", "setActiveStep", "showHistoryDrawer", "setShowHistoryDrawer", "totalSlideCount", "setTotalSlideCount", "showDeleteConfirmModal", "setShowDeleteConfirmModal", "deleteItemInfo", "setDeleteItemInfo", "slideTabIndices", "setSlideTabIndices", "setCurrentProjectId", "isNavigatingToNew", "contextMenu", "setContextMenu", "editModal", "setEditModal", "editInstruction", "setEditInstruction", "isEditingElement", "setIsEditingElement", "toggleFullScreen", "isInFullScreen", "document", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "length", "documentElement", "requestFullscreen", "exitFullscreen", "error", "console", "prev", "saveCurrentChatToHistory", "currentSession", "slice", "messages", "slides", "toISOString", "project_id", "prevHistory", "existingSessionIndex", "findIndex", "updatedHistory", "localStorage", "setItem", "JSON", "stringify", "handleNewPresentation", "current", "removeItem", "loadProjectHistory", "serverHistory", "getProjectsHistory", "log", "formattedSummaries", "proj", "last_modified", "created_at", "is<PERSON>ummary", "serverProjectIds", "Set", "p", "storedId", "getItem", "has", "loadProjectDetails", "projectId", "warn", "details", "getProjectDetails", "chatHistoryData", "chat_history", "msg", "index", "now", "sender", "text", "content", "displayTitle", "search", "includes", "replace", "total_slides_planned", "slidesWithMetadata", "s", "html", "code", "order", "slide_number", "slideContentPromises", "slideMeta", "getSlideC<PERSON>nt", "catch", "fullSlidesData", "Promise", "all", "metaSlide", "fullData", "find", "fs", "initializeApp", "params", "URLSearchParams", "projectIdToLoad", "get", "latestProject", "sort", "a", "b", "handleBeforeUnload", "window", "addEventListener", "removeEventListener", "_chatMessages$find", "projectIdFromMessages", "toggleTaskList", "handleChatTitleChange", "newTitle", "originalTitle", "updateProject", "alert", "handleBackClick", "close", "icon", "handleSelectHistory", "historyItem", "handleDeleteHistory", "historyItemId", "projectIdentifier", "_chatHistory$find", "handleConfirmDelete", "localHistoryItemId", "serverProjectId", "response", "deleteProject", "filter", "handleToggleEditSlide", "handleTextEditSave", "newText", "newStyle", "slideId", "elementId", "prevSlides", "slide", "tempDiv", "createElement", "innerHTML", "targetElement", "querySelector", "innerText", "Object", "keys", "for<PERSON>ach", "key", "style", "handleTextEditCancel", "handleRewriteClick", "e", "stopPropagation", "handleCloseEditModal", "handleSubmitEdit", "trim", "editSlideElement", "handleOpenViewAndExport", "state", "initialIndex", "presentationTitle", "handleMessageFromPlayerView", "event", "data", "payload", "undefined", "handleIframeMessage", "clickPosition", "iframe", "iframeRect", "getBoundingClientRect", "scale", "width", "absoluteX", "left", "x", "scrollX", "absoluteY", "top", "y", "scrollY", "closeMenu", "handleFullscreenChange", "isCurrentlyFullScreen", "mozFullscreenElement", "handleSetCurrentSlideIndex", "handleSlideTabChange", "slideIndex", "tabIndex", "handlePlaySlideAtIndex", "handleSendMessage", "messageData", "messageText", "files", "userMsgId", "userMessage", "isContinueRequest", "toLowerCase", "retryAttempts", "MAX_MESSAGE_RETRIES", "executeGeneration", "projectIdToUse", "messageIdMap", "effectiveProjectId", "generatePresentation", "progressData", "status", "metadata", "actual_project_id", "newActualId", "shouldReplace", "messageToReplaceId", "additionalInfo", "styleWaitingMsgId", "startTime", "elapsedTime", "seconds", "Math", "floor", "minutes", "remainingSeconds", "contentPlanningMsgId", "slideGenCompleteRegex", "slideMatch", "match", "slideNum", "slideProcessingMsgId", "newMsgId", "random", "toString", "substring", "thinking", "slideProcessingRegex", "newAiMsgId", "currentAiMessageIndex", "updatedMessages", "topicMatch", "extractedTopic", "updated_title", "thinking_content", "total_slides", "ui_action", "action", "current_slide_index", "tab", "slide_update", "newSlide", "html_content", "existingIndex", "updatedSlides", "code_content", "completionMsgId", "refreshProjectHistory", "setTimeout", "apiError", "formattedServerHistory", "localOnlyProjects", "combined", "handleViewSearchResults", "slidesToPlay", "titleFromPlayer", "pathname", "handleToggleHistoryDrawer", "newDrawerState", "handleCloseHistoryDrawer", "handleSelectHistoryItem", "handleCancelDelete", "src", "alt", "onToggleTaskList", "historyItems", "onSelectHistoryItem", "onDeleteHistoryItem", "onTitleChange", "onSendMessage", "showHistoryToggle", "onToggleHistory", "onNewPresentation", "onSlideIndexChange", "onSlideTabChange", "onPlayClick", "onEditSlide", "isGeneratingSlides", "onShareClick", "onRewriteClick", "onCloseEditModal", "onSubmitEdit", "onViewExport", "_c10", "App", "path", "element", "fallback", "_c11", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/App.js"], "sourcesContent": ["// frontend/src/App.js\nimport React, { useState, useEffect, useCallback, useRef, lazy, Suspense } from 'react';\nimport { BrowserRouter, Routes, Route, useNavigate, useLocation } from 'react-router-dom';\nimport LeftNav from './components/LeftNav';\nimport MiddlePane from './components/MiddlePane';\nimport RightPane from './components/RightPane';\nimport { FaFilePdf, FaGlobe, FaTrash, FaFeatherAlt } from 'react-icons/fa';\nimport formatDate from './utils/formatDate';\nimport apiService from './services/api';\n\n// Lazy load components that are not needed for initial render\nconst InlineTextEditor = lazy(() => import('./components/InlineTextEditor'));\nconst SlidePlayerView = lazy(() => import('./views/SlidePlayerView'));\nconst FullScreenPlayer = lazy(() => import('./components/FullScreenPlayer'));\n\nconst ChatViewPage = lazy(() => import('./views/ChatViewPage'));\n\n// 添加临时组件定义，后续应该替换为实际组件\nconst SearchResultsModal = ({ results, onClose }) => (\n  <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30 p-4\">\n    <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] flex flex-col\">\n      <div className=\"flex justify-between items-center p-4 border-b\">\n        <h3 className=\"text-lg font-semibold\">搜索结果</h3>\n        <button onClick={onClose} className=\"text-gray-500 hover:text-gray-700 text-2xl\">×</button>\n      </div>\n      <div className=\"p-4 overflow-y-auto custom-scrollbar\">\n        {results.map(result => (\n          <div key={result.id} className=\"mb-4 p-3 border rounded-md hover:shadow-md transition-shadow\">\n            <a href={result.source} target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-500 hover:underline font-medium flex items-center\">\n              {result.type === 'pdf' ? <FaFilePdf className=\"mr-2 text-red-500\" /> : <FaGlobe className=\"mr-2 text-blue-400\" />}\n              {result.title}\n            </a>\n            <p className=\"text-sm text-gray-600 mt-1\">{result.snippet}</p>\n            <p className=\"text-xs text-gray-400 mt-1\">{result.source}</p>\n          </div>\n        ))}\n      </div>\n      <div className=\"p-3 border-t text-right\">\n        <button\n          onClick={onClose}\n          className=\"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm\"\n        >\n          关闭\n        </button>\n      </div>\n    </div>\n  </div>\n);\n\nconst HistoryDrawer = ({ history, onClose, onSelectItem, currentProjectId }) => (\n  <div className=\"fixed right-0 top-0 h-full w-80 bg-white shadow-lg z-40 overflow-y-auto\">\n    <div className=\"p-4 border-b flex justify-between items-center\">\n      <h3 className=\"font-medium\">历史项目</h3>\n      <button onClick={onClose} className=\"text-gray-500 hover:text-gray-700\">×</button>\n    </div>\n    <div className=\"p-2\">\n      {history.map(item => (\n        <div \n          key={item.id} \n          className={`p-3 hover:bg-gray-50 cursor-pointer rounded-md ${item.id === currentProjectId ? 'bg-blue-50' : ''}`}\n          onClick={() => onSelectItem(item)}\n        >\n          <div className=\"font-medium\">{item.title}</div>\n          <div className=\"text-xs text-gray-500 mt-1\">{formatDate(new Date(item.timestamp))}</div>\n        </div>\n      ))}\n    </div>\n  </div>\n);\n\nconst DeleteConfirmModal = ({ title, message, onConfirm, onCancel }) => (\n  <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100] p-4\">\n    <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full flex flex-col\">\n      <div className=\"p-6\">\n        <h3 className=\"text-lg font-medium mb-2\">{title}</h3>\n        <p className=\"text-gray-600\">{message}</p>\n      </div>\n      <div className=\"p-4 border-t flex justify-end space-x-3\">\n        <button\n          onClick={onCancel}\n          className=\"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm\"\n        >\n          取消\n        </button>\n        <button\n          onClick={onConfirm}\n          className=\"px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 text-sm\"\n        >\n          删除\n        </button>\n      </div>\n    </div>\n  </div>\n);\n\n// MainApp component that contains all the existing functionality\nconst MainApp = () => {\n  const navigate = useNavigate();\n  const location = useLocation(); // 获取 location 对象\n  const [isLoading, setIsLoading] = useState(true); // 主加载状态（整体应用）\n  const [isLoadingHistory, setIsLoadingHistory] = useState(false); // 项目历史加载状态\n  const [isTaskListOpen, setIsTaskListOpen] = useState(false);\n  const [currentPresentationTitle, setCurrentPresentationTitle] = useState(\"AI 幻灯片\");\n  const [chatTitle, setChatTitle] = useState(\"AI 幻灯片\");\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [chatMessages, setChatMessages] = useState([]);\n  const [currentSlides, setCurrentSlides] = useState([]);\n  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);\n  const [editingTextElement, setEditingTextElement] = useState(null);\n  const [showSearchResultsModal, setShowSearchResultsModal] = useState(false);\n  const [currentSearchResults, setCurrentSearchResults] = useState([]);\n  const [isFullScreen, setIsFullScreen] = useState(false);\n  const [currentFullScreenSlideIndex, setCurrentFullScreenSlideIndex] = useState(0);\n  const [isEditingMode, setIsEditingMode] = useState(false); // 这个是App层面的编辑模式（例如NLP命令，或是否启用InlineTextEditor）\n  const [chatHistory, setChatHistory] = useState([]); // 存储历史聊天记录摘要\n  const eventSourceRef = useRef(null);\n  const chatRef = useRef(null); // 添加聊天内容引用\n  \n  // 新增状态\n  const [tools, setTools] = useState([]);\n  const [currentStep, setCurrentStep] = useState(null);\n  const [outlineContent, setOutlineContent] = useState('');\n  const [slideCode, setSlideCode] = useState('');\n  const [activeStep, setActiveStep] = useState('preview');\n  const [showHistoryDrawer, setShowHistoryDrawer] = useState(false);\n  const [totalSlideCount, setTotalSlideCount] = useState(0);\n  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);\n  const [deleteItemInfo, setDeleteItemInfo] = useState(null);\n  const [slideTabIndices, setSlideTabIndices] = useState({});\n  const [currentProjectId, setCurrentProjectId] = useState(null);\n  const isNavigatingToNew = useRef(false);\n  const [contextMenu, setContextMenu] = useState(null);\n  const [editModal, setEditModal] = useState(null);\n  const [editInstruction, setEditInstruction] = useState('');\n  const [isEditingElement, setIsEditingElement] = useState(false); // 防止重复提交\n  \n  // 用于播放器全屏的切换逻辑\n  const toggleFullScreen = useCallback(async () => {\n    // 检查当前是否处于浏览器全屏模式\n    const isInFullScreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement);\n    \n    try {\n      if (!isInFullScreen) {\n        // 如果当前不处于全屏，则请求进入全屏\n        // 准备进入全屏时，设置好初始索引\n        if (currentSlides.length > 0) {\n          setCurrentFullScreenSlideIndex(currentSlideIndex);\n        }\n        // 调用浏览器原生 API\n        await document.documentElement.requestFullscreen();\n        // isFullScreen 状态将由下面的 'fullscreenchange' 事件监听器更新\n      } else {\n        // 如果当前已处于全屏，则退出全屏\n        await document.exitFullscreen();\n        // isFullScreen 状态将由下面的 'fullscreenchange' 事件监听器更新\n      }\n    } catch (error) {\n      console.error(\"全屏操作失败:\", error);\n      // 如果原生API失败，作为备用方案，仍然显示我们的模拟全屏组件\n      setIsFullScreen(prev => !prev);\n    }\n  }, [currentSlideIndex, currentSlides.length]);\n\n  // 定义 saveCurrentChatToHistory 函数，确保在适当的时机调用\n  const saveCurrentChatToHistory = useCallback(() => {\n    // 仅当有实质内容时才保存到历史列表\n    if (chatMessages.length > 1 && currentProjectId) { // 确保有 project_id\n      const currentSession = {\n        id: currentProjectId, // 使用 project_id 作为历史记录的唯一 ID\n        title: chatTitle || `演示 (${currentProjectId.slice(-4)})`, // 确保有标题\n        messages: chatMessages,\n        slides: currentSlides,\n        timestamp: new Date().toISOString(),\n        project_id: currentProjectId\n      };\n      \n      setChatHistory(prevHistory => {\n        const existingSessionIndex = prevHistory.findIndex(item => item.id === currentProjectId);\n        let updatedHistory;\n        if (existingSessionIndex >= 0) {\n            updatedHistory = [...prevHistory];\n            updatedHistory[existingSessionIndex] = currentSession; // 更新现有会话\n          } else {\n            updatedHistory = [currentSession, ...prevHistory]; // 添加新会话\n        }\n        // 限制历史记录数量（例如最近20条）\n        // updatedHistory = updatedHistory.slice(0, 20); \n        try {\n          localStorage.setItem('chatHistory', JSON.stringify(updatedHistory));\n        } catch (error) {\n            console.error(\"保存聊天历史列表到 localStorage 失败:\", error);\n        }\n        return updatedHistory;\n      });\n    }\n  }, [chatMessages, currentSlides, chatTitle, currentProjectId, setChatHistory]); // 添加 currentProjectId 依赖\n  \n  // 新建演示文稿的函数\n  const handleNewPresentation = useCallback(() => {\n    isNavigatingToNew.current = true; // 在导航前设置标志\n    \n    // 清空当前状态\n    setChatMessages([]);\n    setCurrentSlides([]);\n    setChatTitle(\"AI 幻灯片\");\n    setCurrentPresentationTitle(\"AI 幻灯片\");\n    setIsGenerating(false);\n    setCurrentStep(null);\n    setTools([]);\n    setOutlineContent('');\n    setSlideCode('');\n    setActiveStep('preview');\n    \n    // 清除项目ID\n    setCurrentProjectId(null); \n    localStorage.removeItem('currentProjectId');\n    \n    // 导航到根路径，清除URL中的projectId\n    navigate('/');\n  }, [\n    setChatMessages, setCurrentSlides, setChatTitle, setCurrentPresentationTitle,\n    setIsGenerating, setCurrentStep, setTools, setOutlineContent, setSlideCode,\n    setActiveStep, setCurrentProjectId, navigate\n  ]);\n\n  // 专门用于加载历史列表的函数\n  const loadProjectHistory = useCallback(async () => {\n    setIsLoadingHistory(true);\n    try {\n      const serverHistory = await apiService.getProjectsHistory();\n      console.log(\"[App.js] Fetched project history summaries:\", serverHistory);\n\n      const formattedSummaries = serverHistory.map(proj => ({\n        id: proj.id, // 修复：使用后端返回的 'id' 字段\n        title: proj.title || `项目 (${proj.id ? proj.id.slice(-4) : '未知'})`, // 修复：使用 'id' 并增加健壮性检查\n        timestamp: proj.last_modified || proj.created_at,\n        project_id: proj.id, // 修复：使用 'id' 字段来填充 project_id\n        isSummary: true // 标记为摘要\n      }));\n\n      setChatHistory(formattedSummaries);\n      \n      // 清理无效的 localStorage.currentProjectId\n      const serverProjectIds = new Set(serverHistory.map(p => p.project_id));\n      const storedId = localStorage.getItem('currentProjectId');\n      if (storedId && !serverProjectIds.has(storedId)) {\n        localStorage.removeItem('currentProjectId');\n      }\n\n    } catch (error) {\n      console.error(\"加载项目历史失败:\", error);\n      setChatHistory([]); // 出错时清空\n    } finally {\n      setIsLoadingHistory(false);\n    }\n  }, [setChatHistory, setIsLoadingHistory]);\n\n  // loadProjectDetails 函数现在是加载单个项目详情的唯一入口\n  const loadProjectDetails = useCallback(async (projectId) => {\n    // 增加对无效projectId的严格检查，防止API调用失败\n    if (!projectId || projectId === 'undefined') {\n      console.warn(\"尝试加载一个无效的项目ID，已中止。\", projectId);\n      setIsLoading(false); // 确保结束加载状态\n      handleNewPresentation(); // 重置到一个安全的新建页面\n      return;\n    }\n    \n    setIsLoading(true);\n    \n    try {\n      const details = await apiService.getProjectDetails(projectId);\n      console.log(`[App.js] Fetched details for project ${projectId}:`, details);\n\n      // 确保聊天历史有数据，必要时进行格式化处理\n      let chatHistoryData = details.chat_history || [];\n      \n      // 确保每条消息都有正确的格式和ID\n      if (chatHistoryData.length > 0) {\n        chatHistoryData = chatHistoryData.map((msg, index) => ({\n          ...msg,\n          id: msg.id || `msg-${index}-${Date.now()}`,\n          sender: msg.sender || (index % 2 === 0 ? 'user' : 'ai'),\n          text: msg.text || msg.content || '无内容',\n          timestamp: msg.timestamp || new Date().toISOString()\n        }));\n        console.log(`[App.js] 处理后的聊天历史数据:`, chatHistoryData);\n      } else {\n        // 如果没有聊天历史，可以添加一个系统消息\n        chatHistoryData = [{\n          id: `system-${Date.now()}`,\n          sender: 'system',\n          text: '项目已加载，但没有聊天记录。',\n          timestamp: new Date().toISOString()\n        }];\n      }\n      \n      setChatMessages(chatHistoryData);\n      const displayTitle = details.title || `项目 (${projectId.slice(-4)})`;\n      setChatTitle(displayTitle);\n      setCurrentPresentationTitle(displayTitle);\n      setCurrentProjectId(details.project_id);\n      \n      // 保存到localStorage以便在刷新后恢复\n      localStorage.setItem('currentProjectId', details.project_id);\n      \n      // 确保URL与当前加载的项目ID同步\n      if (!location.search.includes(`projectId=${details.project_id}`)) {\n        navigate(`/?projectId=${details.project_id}`, { replace: true });\n      }\n\n      setTotalSlideCount(details.total_slides_planned || (details.slides || []).length);\n      \n      // 懒加载幻灯片内容\n      if (details.slides && details.slides.length > 0) {\n        const slidesWithMetadata = details.slides.map(s => ({\n            id: s.id,\n            html: '',\n            code: '',\n            order: s.slide_number - 1,\n            title: `幻灯片 ${s.slide_number}`,\n            isLoading: true,\n        }));\n        setCurrentSlides(slidesWithMetadata);\n\n        const slideContentPromises = details.slides.map(slideMeta =>\n            apiService.getSlideContent(slideMeta.id).catch(() => ({ id: slideMeta.id, error: true }))\n        );\n        const fullSlidesData = await Promise.all(slideContentPromises);\n\n        setCurrentSlides(currentSlides => currentSlides.map(metaSlide => {\n            const fullData = fullSlidesData.find(fs => fs.id === metaSlide.id);\n            if (fullData && !fullData.error) {\n                return { ...metaSlide, html: fullData.html, code: fullData.html, isLoading: false };\n            }\n            return { ...metaSlide, html: '<div>内容加载失败</div>', code: '/* 内容加载失败 */', isLoading: false };\n        }));\n      } else {\n        setCurrentSlides([]);\n      }\n\n    } catch (error) {\n      console.error(`加载项目 ${projectId} 详情失败:`, error);\n      if (error.message && (error.message.includes('not found') || error.message.includes('permission'))) {\n        // 如果项目不存在或无权限，从localStorage移除该项目ID\n        if (localStorage.getItem('currentProjectId') === projectId) {\n          localStorage.removeItem('currentProjectId');\n        }\n        handleNewPresentation();\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  }, [\n    setIsLoading, handleNewPresentation,\n    setChatMessages, setChatTitle, setCurrentPresentationTitle, setCurrentProjectId,\n    location.search, navigate, setTotalSlideCount, setCurrentSlides\n  ]);\n\n  // 初始加载和恢复会话的 useEffect\n  useEffect(() => {\n    const initializeApp = async () => {\n      setIsLoading(true);\n      \n      // 1. 总是先加载历史项目列表\n      await loadProjectHistory();\n\n      // 2. 决定要加载哪个项目的详情\n      const params = new URLSearchParams(location.search);\n      let projectIdToLoad = params.get('projectId');\n      \n      // 如果URL没有ID，尝试从localStorage获取\n      if (!projectIdToLoad || projectIdToLoad === 'undefined') {\n        projectIdToLoad = localStorage.getItem('currentProjectId');\n      }\n      \n      if (projectIdToLoad && projectIdToLoad !== 'undefined') {\n        // 如果有项目ID（来自URL或localStorage），加载它\n        console.log(`[App Init] Resuming session for project: ${projectIdToLoad}`);\n        await loadProjectDetails(projectIdToLoad);\n      } else {\n        // 如果我们是因为点击\"新建\"而导航到这里的，\n        // 只需要重置标志位，然后停止执行。\n        if (isNavigatingToNew.current) {\n          console.log(\"[App Init] Navigating to a new presentation. Skipping history load.\");\n          isNavigatingToNew.current = false;\n          setIsLoading(false); // 确保结束加载状态\n          return; // 终止此 effect 的后续执行\n        }\n        \n        // 否则，加载最新的项目（如果存在）\n        const history = await apiService.getProjectsHistory(); // 再次获取以确保最新\n        if (history && history.length > 0) {\n          const latestProject = history.sort((a, b) => new Date(b.last_modified) - new Date(a.last_modified))[0];\n          // 确保在导航前，最新的项目和其ID是有效的\n          if (latestProject && latestProject.project_id) {\n            console.log(`[App Init] Loading most recent project: ${latestProject.project_id}`);\n            await loadProjectDetails(latestProject.project_id);\n          } else {\n            // 如果最新项目无效，则新建一个\n            console.log(\"[App Init] No valid recent project found. Creating new presentation.\");\n            handleNewPresentation();\n          }\n        } else {\n          // 没有历史记录，创建一个新演示\n          console.log(\"[App Init] No project history found. Creating new presentation.\");\n          handleNewPresentation();\n        }\n      }\n      setIsLoading(false);\n    };\n\n    initializeApp();\n  }, [location.search, loadProjectHistory, loadProjectDetails, handleNewPresentation, setIsLoading]);\n  \n  // 自动保存当前会话到历史记录 - 禁用连续更新，只保留页面离开时的保存\n  useEffect(() => {\n    // 这个效果不再每次状态变化就执行，现在完全依赖beforeunload事件\n    console.log(\"Session management setup\");\n    \n    // 页面即将卸载时保存会话\n    const handleBeforeUnload = () => {\n      // 只保存当前项目ID，不再保存完整的chatHistory\n      if (currentProjectId) {\n        localStorage.setItem('currentProjectId', currentProjectId);\n      }\n    };\n    \n    // 监听页面即将离开事件\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    \n    // 清理函数\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, [currentProjectId]); // 只依赖currentProjectId\n\n  // 仅在特定状态变化时保存到localStorage，但不更新chatHistory状态\n  useEffect(() => {\n    // 只有当有足够的内容时才保存到localStorage\n    if (chatMessages.length > 1 && currentSlides.length > 0) {\n      try {\n        localStorage.setItem('chatMessages', JSON.stringify(chatMessages));\n        localStorage.setItem('currentSlides', JSON.stringify(currentSlides));\n        localStorage.setItem('chatTitle', chatTitle);\n        localStorage.setItem('presentationTitle', currentPresentationTitle);\n        \n        // 更新currentProjectId (如果消息中有)\n        const projectIdFromMessages = chatMessages.find(msg => msg.project_id)?.project_id;\n        if (projectIdFromMessages) {\n          setCurrentProjectId(projectIdFromMessages);\n          localStorage.setItem('currentProjectId', projectIdFromMessages);\n        }\n      } catch (error) {\n        console.error(\"保存会话到localStorage失败:\", error);\n      }\n    }\n  }, [chatMessages, currentSlides, chatTitle, currentPresentationTitle]);\n\n  const toggleTaskList = () => setIsTaskListOpen(!isTaskListOpen);\n\n  const handleChatTitleChange = async (newTitle) => {\n    const originalTitle = chatTitle; // 在更新前保存原始标题\n    \n    // 乐观更新UI，让用户立即看到变化\n    setChatTitle(newTitle);\n    setCurrentPresentationTitle(newTitle);\n    \n    if (currentProjectId) {\n      try {\n        await apiService.updateProject(currentProjectId, { title: newTitle });\n        console.log(`[App] 项目 ${currentProjectId} 的标题已成功更新到数据库: ${newTitle}`);\n        \n        setChatHistory(prev => prev.map(item => \n          item.project_id === currentProjectId ? { ...item, title: newTitle } : item\n        ));\n      } catch (error) {\n        console.error(\"更新项目标题失败:\", error);\n        alert(`标题更新失败: ${error.message}`);\n        \n        // --- BUG修复 ---\n        // 如果API调用失败，将UI恢复到原始标题\n        setChatTitle(originalTitle);\n        setCurrentPresentationTitle(originalTitle);\n      }\n    }\n  };\n\n  const handleBackClick = () => {\n    if (isGenerating) {\n      // 如果正在生成，取消生成\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n        eventSourceRef.current = null;\n      }\n      setIsGenerating(false);\n      setChatMessages(prev => [...prev, {\n        id: Date.now(),\n        type: 'ai_error',\n        text: '生成已取消。',\n        sender: 'system', // 明确sender为system\n        icon: '❌' // 添加一个图标\n      }]);\n      return;\n    }\n    \n    // 如果没有在生成，则返回首页\n    handleNewPresentation(); // <-- 修复后的正确逻辑\n  };\n  \n  const handleSelectHistory = (historyItem) => {\n    // FIX: 增加检查，确保 historyItem 和 project_id 存在且有效\n    if (!historyItem || !historyItem.project_id) {\n        console.error(\"无法选择无效的历史项目:\", historyItem);\n        return; // 中止操作\n    }\n    console.log(\"选择历史项目:\", historyItem);\n    setShowHistoryDrawer(false); // 关闭抽屉\n    \n    // 关键：只进行导航，让 useEffect 钩子去处理数据的加载\n    navigate(`/?projectId=${historyItem.project_id}`);\n  };\n\n  const handleDeleteHistory = async (historyItemId, projectIdentifier) => {\n    // 确保不删除当前会话\n    if (historyItemId === 'current-session') return;\n    \n    // 准备删除确认\n    setDeleteItemInfo({\n      id: historyItemId, // 这个是前端 chatHistory 数组中的项的 ID\n      projectId: projectIdentifier, // 这个是后端的真实项目 ID\n      title: chatHistory.find(item => item.id === historyItemId)?.title || \"该项目\"\n    });\n    setShowDeleteConfirmModal(true);\n  };\n\n  const handleConfirmDelete = async () => {\n    if (!deleteItemInfo || !deleteItemInfo.projectId) {\n      setShowDeleteConfirmModal(false);\n      setDeleteItemInfo(null);\n      return;\n    }\n\n    const { id: localHistoryItemId, projectId: serverProjectId } = deleteItemInfo;\n\n    try {\n      // 1. 调用后端 API 删除服务器端数据\n      const response = await apiService.deleteProject(serverProjectId);\n      console.log(`项目 ${serverProjectId} 删除响应:`, response);\n\n      // 2. 更新本地 chatHistory 状态\n      const updatedHistory = chatHistory.filter(item => item.id !== localHistoryItemId);\n      setChatHistory(updatedHistory);\n      \n      // 3. 如果删除的是当前加载的会话，则重置视图\n      if (currentProjectId === serverProjectId) {\n        handleNewPresentation(); \n      }\n      console.log(`项目 ${serverProjectId} (本地历史项ID: ${localHistoryItemId}) 已成功删除。`);\n\n    } catch (error) {\n      console.error(`删除项目 ${serverProjectId} 时出错:`, error);\n      alert(`删除项目失败: ${error.message}`);\n    } finally {\n      setShowDeleteConfirmModal(false);\n      setDeleteItemInfo(null);\n    }\n  };\n\n  const handleToggleEditSlide = () => {\n    setIsEditingMode(!isEditingMode);\n    setEditingTextElement(null);\n  };\n\n  // Text Editing Handlers\n  /*\n  const handleTextEditStart = useCallback((slideId, elementId, initialText, initialStyle, position) => {\n    // Adjust position relative to the overall RightPane or a more stable parent\n    const rightPaneContentArea = document.querySelector('.right-pane-content-area');\n    let editorX = position.x;\n    let editorY = position.y;\n\n    if (rightPaneContentArea) {\n      const paneRect = rightPaneContentArea.getBoundingClientRect();\n      editorX = position.x + paneRect.left;\n      editorY = position.y + paneRect.top - rightPaneContentArea.scrollTop;\n    }\n    setEditingTextElement({ slideId, elementId, initialText, initialStyle, position: {x: editorX, y: editorY} });\n  }, []);\n  */\n\n  const handleTextEditSave = (newText, newStyle) => {\n    if (!editingTextElement) return;\n    const { slideId, elementId } = editingTextElement;\n    setCurrentSlides(prevSlides =>\n      prevSlides.map(slide => {\n        if (slide.id === slideId) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = slide.html;\n          const targetElement = tempDiv.querySelector(`[data-editable-id=\"${elementId}\"]`);\n          if (targetElement) {\n            targetElement.innerText = newText;\n            Object.keys(newStyle).forEach(key => {\n              targetElement.style[key] = newStyle[key];\n            });\n          }\n          return { ...slide, html: tempDiv.innerHTML, code: tempDiv.innerHTML };\n        }\n        return slide;\n      })\n    );\n    setEditingTextElement(null);\n  };\n\n  const handleTextEditCancel = () => setEditingTextElement(null);\n\n  // +++++++++++++++ 新增处理函数 +++++++++++++++\n  const handleRewriteClick = (e) => {\n    e.stopPropagation();\n    if (contextMenu) {\n      setEditModal({ slideId: contextMenu.slideId, elementId: contextMenu.elementId });\n      setContextMenu(null); // 关闭上下文菜单\n    }\n  };\n\n  const handleCloseEditModal = () => {\n    setEditModal(null);\n    setEditInstruction('');\n    setIsEditingElement(false);\n  };\n\n  const handleSubmitEdit = async () => {\n    if (!editInstruction.trim() || !editModal) return;\n    \n    setIsEditingElement(true);\n    const { slideId, elementId } = editModal;\n    \n    try {\n      const result = await apiService.editSlideElement(slideId, `[data-editable-id=\"${elementId}\"]`, editInstruction);\n      if (result && result.html) {\n        // 更新幻灯片状态\n        setCurrentSlides(prevSlides =>\n          prevSlides.map(slide => \n            slide.id === slideId ? { ...slide, html: result.html, code: result.html } : slide\n          )\n        );\n      }\n    } catch (error) {\n      console.error('编辑元素失败:', error);\n      alert(`编辑失败: ${error.message}`);\n    } finally {\n      handleCloseEditModal();\n    }\n  };\n  // +++++++++++++++++++++++++++++++++++++++++++++\n\n  // Handle opening the player view\n  const handleOpenViewAndExport = () => {\n    navigate('/player', { \n      state: { \n        slides: currentSlides,\n        initialIndex: 0,\n        presentationTitle: currentPresentationTitle \n      } \n    });\n  };\n  \n  // Handle messages from SlidePlayerView\n  useEffect(() => {\n    const handleMessageFromPlayerView = (event) => {\n      if (event.data && event.data.type === 'request-fullscreen') {\n        // Set the appropriate slide index if provided\n        if (event.data.payload && event.data.payload.initialIndex !== undefined) {\n          setCurrentFullScreenSlideIndex(event.data.payload.initialIndex);\n        }\n        // Toggle fullscreen\n        toggleFullScreen();\n      }\n    };\n\n    window.addEventListener('message', handleMessageFromPlayerView);\n    return () => {\n      window.removeEventListener('message', handleMessageFromPlayerView);\n    };\n  }, [toggleFullScreen]);\n  \n  // +++++++++++++++ 新增useEffect来处理来自iframe的消息 +++++++++++++++\n  useEffect(() => {\n    const handleIframeMessage = (event) => {\n      if (event.data && event.data.type === 'element_clicked' && isEditingMode) {\n        const { slideId, elementId, clickPosition } = event.data.payload;\n        \n        // 找到对应的iframe，计算屏幕上的绝对位置\n        const iframe = document.querySelector(`#slide-preview-${slideId} iframe`);\n        if (iframe) {\n          const iframeRect = iframe.getBoundingClientRect();\n          const scale = iframeRect.width / 1280; // 假设设计宽度是1280\n          \n          const absoluteX = iframeRect.left + (clickPosition.x * scale) + window.scrollX;\n          const absoluteY = iframeRect.top + (clickPosition.y * scale) + window.scrollY;\n          \n          setContextMenu({\n            x: absoluteX,\n            y: absoluteY,\n            slideId,\n            elementId\n          });\n        }\n      }\n    };\n\n    window.addEventListener('message', handleIframeMessage);\n    // 点击其他地方时关闭菜单\n    const closeMenu = () => setContextMenu(null);\n    window.addEventListener('click', closeMenu);\n\n    return () => {\n      window.removeEventListener('message', handleIframeMessage);\n      window.removeEventListener('click', closeMenu);\n    };\n  }, [isEditingMode]); // 只在编辑模式下监听\n  // ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n  \n  useEffect(() => {\n    // 这个 useEffect 现在是同步 isFullScreen 状态的唯一来源\n    const handleFullscreenChange = () => {\n      const isCurrentlyFullScreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullscreenElement || document.msFullscreenElement);\n      setIsFullScreen(isCurrentlyFullScreen);\n    };\n    \n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);\n    document.addEventListener('mozfullscreenchange', handleFullscreenChange);\n    document.addEventListener('MSFullscreenChange', handleFullscreenChange);\n    \n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);\n      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);\n      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);\n    };\n  }, []);\n\n  // 用于 RightPane 设置当前活动/聚焦的幻灯片\n  const handleSetCurrentSlideIndex = (index) => {\n    setCurrentSlideIndex(index);\n  };\n\n  const handleSlideTabChange = (slideIndex, tabIndex) => {\n    setSlideTabIndices(prev => ({\n      ...prev,\n      [slideIndex]: tabIndex\n    }));\n  };\n\n  // 用于播放特定索引的幻灯片\n  const handlePlaySlideAtIndex = (index) => {\n    setCurrentFullScreenSlideIndex(index);\n    toggleFullScreen();\n  };\n\n  const handleSendMessage = async (messageData) => {\n    // 处理参数：可能是字符串或对象\n    let messageText, files = [];\n    \n    if (typeof messageData === 'string') {\n      messageText = messageData;\n    } else if (typeof messageData === 'object' && messageData !== null) {\n      messageText = messageData.message || '';\n      files = messageData.files || [];\n    } else {\n      messageText = '';\n    }\n\n    if (!messageText.trim() || isGenerating) return;\n\n    // 生成独特的消息ID\n    const userMsgId = `user-msg-${Date.now()}`;\n    \n    // 添加用户消息到聊天\n    const userMessage = {\n      id: userMsgId,\n      type: 'user',\n      text: messageText,\n      files: files.length > 0 ? files : undefined, // 只在有文件时添加files属性\n    };\n    \n    // 判断是否是\"继续\"生成请求\n    const isContinueRequest = messageText.toLowerCase().includes(\"继续\") || \n                             messageText.toLowerCase().includes(\"continue\");\n    \n    // 【核心修改1】优化新生成请求时的状态清空逻辑\n    if (!isContinueRequest) {\n      // 对于新的演示文稿生成请求，清除所有之前的AI消息，并重置项目相关状态\n      setChatMessages([userMessage]); // 仅保留当前用户消息\n      setCurrentSlides([]);\n      setTotalSlideCount(0);\n      setCurrentProjectId(null); // 这将强制后端创建一个新项目\n      \n      // 重置其他UI相关状态，以获得一个全新的开始\n    setCurrentStep(null);\n      setTools([]);\n    setOutlineContent('');\n    setSlideCode('');\n    setActiveStep('preview');\n    } else {\n      // 对于\"继续\"请求，仅追加新的用户消息\n    setChatMessages(prev => [...prev, userMessage]);\n    }\n    \n    let retryAttempts = 0;\n    const MAX_MESSAGE_RETRIES = 3;\n\n    const executeGeneration = async (projectIdToUse) => {\n      try {\n        // 设置生成状态为true\n        setIsGenerating(true);\n                \n        // 关闭之前的EventSource连接（如果存在）\n        if (eventSourceRef.current) {\n          eventSourceRef.current.close();\n        }\n        \n        // 初始化临时幻灯片数组 (这个现在不再由前端维护，后端SSE会直接推送)\n        // let tempSlidesData = []; \n        \n        // 创建消息ID映射，用于后续更新或替换特定消息\n        const messageIdMap = {};\n        \n        // 决定使用哪个 project_id\n        // `projectIdToUse` 会是 `null` (新生成) 或 `currentProjectId` (继续生成)\n        const effectiveProjectId = projectIdToUse; \n        \n        // 使用新的简化工作流API\n        eventSourceRef.current = await apiService.generatePresentation(\n          messageText,\n          // 进度处理\n          (progressData) => {\n            // console.log(\"进度更新:\", progressData);\n            \n            // 处理 id_mapped_to_client 事件 - 当临时ID被映射到实际ID时\n            if (progressData.status === 'id_mapped_to_client' && progressData.metadata && progressData.metadata.actual_project_id) {\n              const newActualId = progressData.metadata.actual_project_id;\n              console.log(`[App.js] 临时项目ID已映射到实际ID: ${newActualId}`);\n              setCurrentProjectId(newActualId);\n              localStorage.setItem('currentProjectId', newActualId);\n              // 更新URL，但不触发页面重新加载\n              navigate(`/?projectId=${newActualId}`, { replace: true });\n              return; // 不需要进一步处理这个事件\n            }\n            \n            // 保存项目ID - 如果这是首次获取项目ID (且不是 id_mapped_to_client 消息)\n            if (!currentProjectId && progressData.project_id && progressData.status !== 'id_mapping') {\n              console.log(\"[App.js] 从SSE获取到项目ID:\", progressData.project_id);\n              setCurrentProjectId(progressData.project_id);\n              localStorage.setItem('currentProjectId', progressData.project_id); // 添加这行\n            }\n            \n            // 如果进度正常，重置重试次数\n            retryAttempts = 0;\n            \n            // 更新AI消息\n            if (progressData.message) {\n              const messageText = progressData.message.text;\n              \n              // 跳过初始化和连接建立消息\n              if (messageText === '⏳正在解析您的请求，AI引擎启动中...' || \n                  messageText === '🔌连接已建立，等待更新..') {\n                return;\n              }\n              \n              // 检查是否是需要替换的消息类型\n              let shouldReplace = false;\n              let messageToReplaceId = null;\n              let additionalInfo = '';\n              \n              // 处理风格确定消息\n              if (messageText.includes('的整体风格已确定')) {\n                const styleWaitingMsgId = messageIdMap['style_waiting'];\n                if (styleWaitingMsgId) {\n                  messageToReplaceId = styleWaitingMsgId;\n                  shouldReplace = true;\n                  \n                  // 计算实际用时\n                  const startTime = messageIdMap['style_waiting_start_time'];\n                  if (startTime) {\n                    const elapsedTime = Date.now() - startTime;\n                    const seconds = Math.floor(elapsedTime / 1000);\n                    if (seconds < 60) {\n                      additionalInfo = `（实际用时${seconds}秒）`;\n                    } else {\n                      const minutes = Math.floor(seconds / 60);\n                      const remainingSeconds = seconds % 60;\n                      additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                    }\n                  }\n                }\n              }\n              \n              // 处理内容规划完成消息\n              if (messageText.includes('内容规划完成：共')) {\n                const contentPlanningMsgId = messageIdMap['content_planning'];\n                if (contentPlanningMsgId) {\n                  messageToReplaceId = contentPlanningMsgId;\n                  shouldReplace = true;\n                  \n                  // 计算实际用时\n                  const startTime = messageIdMap['content_planning_start_time'];\n                  if (startTime) {\n                    const elapsedTime = Date.now() - startTime;\n                    const seconds = Math.floor(elapsedTime / 1000);\n                    if (seconds < 60) {\n                      additionalInfo = `（实际用时${seconds}秒）`;\n                    } else {\n                      const minutes = Math.floor(seconds / 60);\n                      const remainingSeconds = seconds % 60;\n                      additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                    }\n                  }\n                }\n              }\n              \n              // 处理幻灯片生成完成消息\n              const slideGenCompleteRegex = /第 (\\d+)\\/(\\d+) 张.*生成完毕/;\n              const slideMatch = messageText.match(slideGenCompleteRegex);\n              if (slideMatch) {\n                const slideNum = slideMatch[1];\n                const slideProcessingMsgId = messageIdMap[`slide_processing_${slideNum}`];\n                if (slideProcessingMsgId) {\n                  messageToReplaceId = slideProcessingMsgId;\n                  shouldReplace = true;\n                  \n                  // 计算实际用时\n                  const startTime = messageIdMap[`slide_processing_${slideNum}_start_time`];\n                  if (startTime) {\n                    const elapsedTime = Date.now() - startTime;\n                    const seconds = Math.floor(elapsedTime / 1000);\n                    if (seconds < 60) {\n                      additionalInfo = `（实际用时${seconds}秒）`;\n                    } else {\n                      const minutes = Math.floor(seconds / 60);\n                      const remainingSeconds = seconds % 60;\n                      additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                    }\n                  }\n                }\n              }\n              \n              // 记录特殊消息的ID和时间戳，用于后续替换\n              if (messageText.includes('正在为') && messageText.includes('规划整体视觉风格')) {\n                const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n                messageIdMap['style_waiting'] = newMsgId;\n                messageIdMap['style_waiting_start_time'] = Date.now();\n                \n                // 创建新消息\n                setChatMessages(prev => [\n                  ...prev, \n                  { \n                    id: newMsgId, \n                    type: 'ai', \n                    text: messageText,\n                    icon: progressData.message.icon || undefined,\n                    thinking: progressData.message.thinking || false, // 使用 thinking 字段\n                    sender: progressData.message.sender || 'ai' // 确保sender正确\n                  }\n                ]);\n                return; // 直接返回，避免重复添加\n              }\n              \n              if (messageText.includes('正在为') && messageText.includes('规划详细内容')) {\n                const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n                messageIdMap['content_planning'] = newMsgId;\n                messageIdMap['content_planning_start_time'] = Date.now();\n                \n                // 创建新消息\n                setChatMessages(prev => [\n                  ...prev, \n                  { \n                    id: newMsgId, \n                    type: 'ai', \n                    text: messageText,\n                    icon: progressData.message.icon || undefined,\n                    thinking: progressData.message.thinking || false, // 使用 thinking 字段\n                    sender: progressData.message.sender || 'ai'\n                  }\n                ]);\n                return; // 直接返回，避免重复添加\n              }\n              \n              const slideProcessingRegex = /开始处理第 (\\d+)\\/(\\d+) 张幻灯片/;\n              const match = messageText.match(slideProcessingRegex);\n              if (match) {\n                const slideNum = match[1];\n                const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n                messageIdMap[`slide_processing_${slideNum}`] = newMsgId;\n                messageIdMap[`slide_processing_${slideNum}_start_time`] = Date.now();\n                \n                // 创建新消息\n                setChatMessages(prev => [\n                  ...prev, \n                  { \n                    id: newMsgId, \n                    type: 'ai', \n                    text: messageText,\n                    icon: progressData.message.icon || undefined,\n                    thinking: progressData.message.thinking || false, // 使用 thinking 字段\n                    sender: progressData.message.sender || 'ai'\n                  }\n                ]);\n                return; // 直接返回，避免重复添加\n              }\n              \n              // 创建新消息或替换现有消息\n              const newAiMsgId = shouldReplace ? messageToReplaceId : `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n              \n              setChatMessages(prev => {\n                const currentAiMessageIndex = prev.findIndex(msg => msg.id === newAiMsgId);\n                if (currentAiMessageIndex !== -1) {\n                // 替换现有消息\n                    const updatedMessages = [...prev];\n                    updatedMessages[currentAiMessageIndex] = {\n                        ...updatedMessages[currentAiMessageIndex], \n                        text: additionalInfo ? `${messageText} ${additionalInfo}` : messageText,\n                        icon: progressData.message.icon || undefined,\n                        thinking: progressData.message.thinking || false, // 使用 thinking 字段\n                        sender: progressData.message.sender || 'ai'\n                    };\n                    return updatedMessages;\n              } else {\n                // 添加新消息\n                    return [\n                  ...prev, \n                  { \n                    id: newAiMsgId, \n                    type: 'ai', \n                    text: messageText,\n                    icon: progressData.message.icon || undefined,\n                            thinking: progressData.message.thinking || false, // 使用 thinking 字段\n                            sender: progressData.message.sender || 'ai'\n                  }\n                    ];\n              }\n              });\n            }\n            \n            // 当接收到开始状态和AI识别的主题时，更新标题\n            if (progressData.status === \"starting\" && progressData.message && progressData.message.text) {\n              const topicMatch = progressData.message.text.match(/关于「(.+?)」/);\n              if (topicMatch && topicMatch[1]) { \n                  const extractedTopic = topicMatch[1].trim();\n                  if (extractedTopic && chatTitle !== `AI幻灯片 ${extractedTopic}`) { \n                      const newTitle = `AI幻灯片 ${extractedTopic}`;\n                      setChatTitle(newTitle);\n                  }\n              }\n            }\n\n            // 新增/修改：当意图分析完成时，更精确地更新标题\n            // 当意图分析完成时，根据后端元数据或消息文本更新标题\n            if (progressData.status === \"intent_analyzed\") {\n              // 优先使用 metadata 中的精确标题 (这是最可靠的方法)\n              if (progressData.metadata && progressData.metadata.updated_title) {\n                const newTitle = progressData.metadata.updated_title;\n                console.log(`[App.js] Intent analyzed, updating title from metadata: ${newTitle}`);\n                setChatTitle(newTitle);\n                setCurrentPresentationTitle(newTitle);\n              } \n              // 如果 metadata 中没有标题，则回退到从消息文本中解析 (作为备用方案)\n              else if (progressData.message && progressData.message.text) {\n                const topicMatch = progressData.message.text.match(/主题：\"(.+?)\"/);\n                if (topicMatch && topicMatch[1]) {\n                    const extractedTopic = topicMatch[1].trim();\n                    const newTitle = `AI幻灯片 ${extractedTopic}`;\n                    console.log(`[App.js] Intent analyzed, updating title from message text (fallback): ${newTitle}`);\n                    setChatTitle(newTitle);\n                    setCurrentPresentationTitle(newTitle);\n                }\n              }\n            }\n            \n            // 处理思考内容和总幻灯片数\n            if (progressData.thinking_content) {\n              setOutlineContent(progressData.thinking_content);\n            }\n            \n            if (progressData.total_slides && progressData.total_slides > 0) {\n              setTotalSlideCount(progressData.total_slides);\n            }\n            \n            // 处理UI交互动作\n            if (progressData.ui_action) {\n              // 处理标签页切换\n              if (progressData.ui_action.action === \"select_tab\" && \n                  progressData.current_slide_index !== undefined) {\n                \n                // 设置当前幻灯片的标签页\n                if (progressData.ui_action.tab === \"code\") {\n                  setActiveStep('code');\n                  setSlideTabIndices(prev => ({\n                    ...prev,\n                    [progressData.current_slide_index]: 1 // 1 表示代码标签\n                  }));\n                } else if (progressData.ui_action.tab === \"preview\") {\n                  setActiveStep('preview');\n                  setSlideTabIndices(prev => ({\n                    ...prev,\n                    [progressData.current_slide_index]: 0 // 0 表示预览标签\n                  }));\n                }\n              }\n            }\n            \n            // 处理幻灯片更新\n            if (progressData.slide_update) {\n              const newSlide = {\n                id: progressData.slide_update.id,\n                html: progressData.slide_update.html || progressData.slide_update.html_content, // 兼容字段\n                code: progressData.slide_update.code || progressData.slide_update.html || progressData.slide_update.html_content,\n                order: progressData.slide_update.slide_number !== undefined ? progressData.slide_update.slide_number - 1 : 0, // 确保使用slide_number转为0-based\n                title: `幻灯片 ${progressData.slide_update.slide_number}`\n              };\n\n              // 使用函数式更新来保证我们总是基于最新的幻灯片列表进行操作\n              setCurrentSlides(prevSlides => {\n                const existingIndex = prevSlides.findIndex(s => s.id === newSlide.id || s.order === newSlide.order);\n                let updatedSlides;\n\n                if (existingIndex >= 0) {\n                  // 更新现有幻灯片\n                  updatedSlides = [...prevSlides];\n                  updatedSlides[existingIndex] = newSlide;\n                } else {\n                  // 添加新幻灯片\n                  updatedSlides = [...prevSlides, newSlide];\n                }\n                \n                // 按 order 排序，以防消息乱序\n                updatedSlides.sort((a, b) => a.order - b.order);\n                return updatedSlides;\n              });\n\n              // 更新当前焦点幻灯片\n              if (progressData.current_slide_index !== undefined) {\n                setCurrentSlideIndex(progressData.current_slide_index);\n              }\n              \n              // 如果有代码内容，更新代码显示\n              if (progressData.code_content) {\n                setSlideCode(progressData.code_content);\n              }\n            }\n            \n            // 如果状态为完成，更新最终消息\n            if (progressData.status === 'completed') {\n              // 添加完成消息\n              const completionMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n              setChatMessages(prev => [\n                ...prev, \n                { \n                  id: completionMsgId, \n                  type: 'ai', \n                  text: `✅ 幻灯片已全部生成完毕 (${(progressData.total_slides || currentSlides.length)}张)，您可以点击每张幻灯片进行查看和编辑。`,\n                  icon: '✅',\n                  thinking: false, // 完成时设置为非思考中\n                  sender: 'ai', // 确保sender正确\n                  project_id: progressData.project_id || currentProjectId // 确保保存项目ID\n                }\n              ]);\n              \n              setIsEditingMode(true);\n              \n              // 设置演示文稿标题\n              if (!currentPresentationTitle || currentPresentationTitle === \"AI幻灯片\") {\n                setCurrentPresentationTitle(messageText.length > 30 ? messageText.substring(0, 30) + \"...\" : messageText);\n              }\n              \n              // 保存聊天历史\n              saveCurrentChatToHistory();\n              \n              // 完成后刷新项目历史列表\n              refreshProjectHistory();\n            }\n          },\n          // 错误处理\n          (error) => {\n            console.error('App.js - 生成幻灯片时发生错误:', error);\n            \n            // 确保关闭旧的连接\n            if (eventSourceRef.current && typeof eventSourceRef.current.close === 'function') {\n              eventSourceRef.current.close();\n            }\n            \n            // 处理特殊错误类型，尝试重新连接\n            if ((error.message === 'SSE_HEARTBEAT_TIMEOUT' || error.message.includes('connection failed')) && retryAttempts < MAX_MESSAGE_RETRIES) {\n              retryAttempts++;\n              setChatMessages(prev => [...prev, {\n                id: `sys-retry-${Date.now()}`, \n                type: 'system-info', \n                text: `连接中断，正在尝试重新连接... (${retryAttempts}/${MAX_MESSAGE_RETRIES}) 项目: ${effectiveProjectId || '新项目'}`,\n                sender: 'system',\n                icon: '⏳'\n              }]);\n              \n              // 延迟重试，每次重试增加延迟\n              setTimeout(() => executeGeneration(currentProjectId), 3000 * retryAttempts);\n            } else {\n              // 超过最大重试次数或其他错误\n              setIsGenerating(false);\n              setChatMessages(prev => [...prev, {\n                id: `error-${Date.now()}`, \n                type: 'ai_error', \n                text: `抱歉，发生错误: ${error.message}`,\n                sender: 'system',\n                icon: '⚠️'\n              }]);\n            }\n          },\n          // 完成处理\n          () => {\n            console.log('App.js - 幻灯片生成流完成');\n            setIsGenerating(false);\n            // 不再在这里添加完成消息，依赖后端推送的 'completed' status\n            saveCurrentChatToHistory(); // 确保最终状态被保存\n          },\n          // 传递项目ID（如果是新会话，后端会创建；如果是重试，则使用 existing_project_id）\n          effectiveProjectId ? { project_id: effectiveProjectId } : {}\n        );\n      } catch (apiError) {\n        console.error('App.js - API调用启动错误:', apiError);\n        setIsGenerating(false);\n        setChatMessages(prev => [...prev, {\n          id: `error-api-${Date.now()}`, \n          type: 'ai_error', \n          text: `启动生成失败: ${apiError.message}`,\n          sender: 'system',\n          icon: '❌'\n        }]);\n      }\n    };\n\n    // 执行生成。如果不是\"继续\"请求，`projectIdToUse` 将是 `null`，后端将创建新项目ID。\n    // 否则，它将是 `currentProjectId`。\n    executeGeneration(isContinueRequest ? currentProjectId : null);\n  };\n\n  // 新增函数：刷新项目历史列表\n  const refreshProjectHistory = async () => {\n    try {\n      const serverHistory = await apiService.getProjectsHistory();\n      console.log(\"[App.js] Refreshed project history from server:\", serverHistory);\n      \n      // 将后端返回的项目摘要转换为前端 chatHistory 格式\n      const formattedServerHistory = serverHistory.map(proj => ({\n        id: proj.id, // 修复\n        title: proj.title || `项目 (${proj.id ? proj.id.slice(-4) : '未知'})`, // 修复\n        timestamp: proj.last_modified || proj.created_at,\n        project_id: proj.id, // 修复\n        messages: [], \n        slides: [],\n        isSummary: true\n      }));\n      \n      // 更新 chatHistory 状态，保留本地项目\n      setChatHistory(prev => {\n        // 获取所有服务器项目的 ID\n        const serverProjectIds = new Set(formattedServerHistory.map(item => item.project_id)); // 这里现在可以正确获取ID了\n        \n        // 保留本地项目（那些不在服务器上的）\n        const localOnlyProjects = prev.filter(item => !serverProjectIds.has(item.project_id)); // 这里的去重逻辑现在也能正常工作了\n        \n        // 合并并按时间戳排序\n        const combined = [...formattedServerHistory, ...localOnlyProjects]\n          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));\n          \n        // 保存到 localStorage\n        try {\n          localStorage.setItem('chatHistory', JSON.stringify(combined));\n        } catch (error) {\n          console.error(\"保存刷新的聊天历史到本地存储失败:\", error);\n        }\n        \n        return combined;\n      });\n    } catch (error) {\n      console.error(\"刷新项目历史列表失败:\", error);\n    }\n  };\n\n  const handleViewSearchResults = (results) => {\n    setCurrentSearchResults(results);\n    setShowSearchResultsModal(true);\n  };\n\n  // Auto-generate slides on first load - disabled for production with real backend\n  /*\n  useEffect(() => {\n    if (currentSlides.length === 0 && !isGenerating) {\n      processSimulatedGeneration(\"生成一个中国房地产市场2025年趋势的PPT\");\n    }\n  }, []);\n  */\n\n  // 移除旧的键盘事件监听代码，现在由 FullScreenPlayer 组件通过 postMessage 处理\n\n  // 监听来自 SlidePlayerView 的请求，以启动全屏播放器\n  useEffect(() => {\n    if (location.state && location.state.action === 'startFullScreenPlayer') {\n      const { slides: slidesToPlay, initialIndex, presentationTitle: titleFromPlayer } = location.state;\n      \n      // 你可能需要更新 currentSlides 和 presentationTitle，如果它们与 App.js 的当前状态不同\n      // 或者，如果 App.js 是这些状态的唯一来源，SlidePlayerView 应该总是从 App.js 获取最新的\n      if (slidesToPlay && slidesToPlay.length > 0) {\n         setCurrentSlides(slidesToPlay); // 确保幻灯片数据是最新的\n      }\n      if (titleFromPlayer) {\n         setCurrentPresentationTitle(titleFromPlayer);\n      }\n\n      setCurrentFullScreenSlideIndex(initialIndex || 0);\n      toggleFullScreen(); // 触发全屏\n\n      // 清除 location.state 防止刷新或其他导航时再次触发\n      navigate(location.pathname, { replace: true, state: {} }); \n    }\n  }, [location.state, navigate, toggleFullScreen, location.pathname]); // 添加 location.pathname 为依赖项\n\n  // Handlers for history drawer\n  const handleToggleHistoryDrawer = () => {\n    const newDrawerState = !showHistoryDrawer;\n    setShowHistoryDrawer(newDrawerState);\n    if (newDrawerState) { // 如果是打开抽屉\n      refreshProjectHistory(); // 刷新历史记录\n    }\n  };\n\n  const handleCloseHistoryDrawer = () => {\n    setShowHistoryDrawer(false);\n  };\n\n  const handleSelectHistoryItem = (historyItem) => {\n    handleSelectHistory(historyItem); // Assuming handleSelectHistory is defined in App.js\n    handleCloseHistoryDrawer();\n  };\n\n  const handleCancelDelete = () => {\n    setDeleteItemInfo(null);\n    setShowDeleteConfirmModal(false);\n  };\n\n  return (\n    <div className=\"flex flex-col h-screen bg-white\">\n      {isLoading ? (\n        <div className=\"flex flex-col justify-center items-center h-full\">\n          <img src=\"/tiktodo-loading.svg\" alt=\"Loading\" className=\"h-16 w-16 mb-4\" />\n          <div className=\"text-center\">\n            <div className=\"text-xl font-semibold text-gray-800 mb-2\">TikTodo 载入中...</div>\n            <div className=\"text-sm text-gray-500\">请稍等，马上就好</div>\n          </div>\n        </div>\n      ) : (\n        <>\n          <div className=\"bg-gradient-to-r from-tiktodo-blue to-tiktodo-purple h-1\"></div>\n          <div className=\"flex flex-1 overflow-hidden\">\n            <LeftNav \n              isTaskListOpen={isTaskListOpen}\n              onToggleTaskList={toggleTaskList}\n              historyItems={chatHistory}\n              currentProjectId={currentProjectId}\n              onSelectHistoryItem={handleSelectHistoryItem}\n              onDeleteHistoryItem={handleDeleteHistory}\n              isLoadingHistory={isLoadingHistory}\n            />\n            <MiddlePane\n              title={chatTitle || \"AI 幻灯片\"}\n              onTitleChange={handleChatTitleChange}\n              isGenerating={isGenerating}\n              messages={chatMessages}\n              onSendMessage={handleSendMessage}\n              chatRef={chatRef}\n              handleViewSearchResults={handleViewSearchResults} // 查看搜索结果的处理函数\n              \n              // 工具栏\n              showHistoryToggle={true}\n              onToggleTaskList={toggleTaskList}\n              onToggleHistory={handleToggleHistoryDrawer}\n              onNewPresentation={handleNewPresentation}\n            />\n            \n            <RightPane\n              slides={currentSlides}\n              currentSlideIndex={currentSlideIndex}\n              onSlideIndexChange={handleSetCurrentSlideIndex}\n              onSlideTabChange={handleSlideTabChange}\n              onPlayClick={handlePlaySlideAtIndex}\n              onEditSlide={handleToggleEditSlide}\n              isEditingMode={isEditingMode}\n              slideTabIndices={slideTabIndices}\n              isGeneratingSlides={isGenerating}\n              tools={tools}\n              currentStep={currentStep}\n              outlineContent={outlineContent}\n              slideCode={slideCode}\n              activeStep={activeStep}\n              currentFullScreenSlideIndex={currentFullScreenSlideIndex}\n              onShareClick={() => {\n                alert(\"分享功能开发中\");\n              }}\n              onRewriteClick={handleRewriteClick}\n              editModal={editModal}\n              editInstruction={editInstruction}\n              onCloseEditModal={handleCloseEditModal}\n              onSubmitEdit={handleSubmitEdit}\n              onViewExport={handleOpenViewAndExport}\n            />\n          </div>\n          \n          {/* 搜索结果弹窗 */}\n          {showSearchResultsModal && (\n            <SearchResultsModal \n              results={currentSearchResults} \n              onClose={() => setShowSearchResultsModal(false)} \n            />\n          )}\n          \n          {/* 历史项目侧边抽屉 */}\n          {showHistoryDrawer && (\n            <HistoryDrawer \n              history={chatHistory} \n              onClose={handleCloseHistoryDrawer}\n              onSelectItem={handleSelectHistoryItem}\n              currentProjectId={currentProjectId}\n            />\n          )}\n          \n          {/* 删除确认弹窗 */}\n          {showDeleteConfirmModal && deleteItemInfo && (\n            <DeleteConfirmModal \n              title={`删除 ${deleteItemInfo.title || '项目'}`}\n              message={`您确定要删除此项目吗？此操作不可恢复。`}\n              onConfirm={handleConfirmDelete}\n              onCancel={handleCancelDelete}\n            />\n          )}\n        </>\n      )}\n    </div>\n  );\n};\n\nfunction App() {\n  return (\n    <BrowserRouter>\n      <Routes>\n        <Route path=\"/\" element={<MainApp />} />\n        <Route path=\"/player\" element={\n          <Suspense fallback={<div className=\"flex justify-center items-center h-screen\">播放器加载中...</div>}>\n            <SlidePlayerView />\n          </Suspense>\n        } />\n        <Route path=\"/chat\" element={\n          <Suspense fallback={<div className=\"flex justify-center items-center h-screen\">加载AI聊天...</div>}>\n            <ChatViewPage />\n          </Suspense>\n        } />\n      </Routes>\n    </BrowserRouter>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,OAAO;AACvF,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACzF,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,SAASC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAEC,YAAY,QAAQ,gBAAgB;AAC1E,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,UAAU,MAAM,gBAAgB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,gBAAgB,gBAAGpB,IAAI,CAAAqB,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC;AAACC,GAAA,GAAvEF,gBAAgB;AACtB,MAAMG,eAAe,gBAAGvB,IAAI,CAAAwB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,GAAA,GAAhEF,eAAe;AACrB,MAAMG,gBAAgB,gBAAG1B,IAAI,CAAA2B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC;AAACC,GAAA,GAAvEF,gBAAgB;AAEtB,MAAMG,YAAY,gBAAG7B,IAAI,CAAA8B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;;AAE/D;AAAAC,GAAA,GAFMF,YAAY;AAGlB,MAAMG,kBAAkB,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAQ,CAAC,kBAC9CjB,OAAA;EAAKkB,SAAS,EAAC,gFAAgF;EAAAC,QAAA,eAC7FnB,OAAA;IAAKkB,SAAS,EAAC,2EAA2E;IAAAC,QAAA,gBACxFnB,OAAA;MAAKkB,SAAS,EAAC,gDAAgD;MAAAC,QAAA,gBAC7DnB,OAAA;QAAIkB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/CvB,OAAA;QAAQwB,OAAO,EAAEP,OAAQ;QAACC,SAAS,EAAC,4CAA4C;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxF,CAAC,eACNvB,OAAA;MAAKkB,SAAS,EAAC,sCAAsC;MAAAC,QAAA,EAClDH,OAAO,CAACS,GAAG,CAACC,MAAM,iBACjB1B,OAAA;QAAqBkB,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3FnB,OAAA;UAAG2B,IAAI,EAAED,MAAM,CAACE,MAAO;UAACC,MAAM,EAAC,QAAQ;UAACC,GAAG,EAAC,qBAAqB;UAACZ,SAAS,EAAC,6DAA6D;UAAAC,QAAA,GACtIO,MAAM,CAACK,IAAI,KAAK,KAAK,gBAAG/B,OAAA,CAACP,SAAS;YAACyB,SAAS,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGvB,OAAA,CAACN,OAAO;YAACwB,SAAS,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAChHG,MAAM,CAACM,KAAK;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACJvB,OAAA;UAAGkB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAEO,MAAM,CAACO;QAAO;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9DvB,OAAA;UAAGkB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAEO,MAAM,CAACE;QAAM;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA,GANrDG,MAAM,CAACQ,EAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOd,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNvB,OAAA;MAAKkB,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCnB,OAAA;QACEwB,OAAO,EAAEP,OAAQ;QACjBC,SAAS,EAAC,0EAA0E;QAAAC,QAAA,EACrF;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACY,GAAA,GA7BIpB,kBAAkB;AA+BxB,MAAMqB,aAAa,GAAGA,CAAC;EAAEC,OAAO;EAAEpB,OAAO;EAAEqB,YAAY;EAAEC;AAAiB,CAAC,kBACzEvC,OAAA;EAAKkB,SAAS,EAAC,yEAAyE;EAAAC,QAAA,gBACtFnB,OAAA;IAAKkB,SAAS,EAAC,gDAAgD;IAAAC,QAAA,gBAC7DnB,OAAA;MAAIkB,SAAS,EAAC,aAAa;MAAAC,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrCvB,OAAA;MAAQwB,OAAO,EAAEP,OAAQ;MAACC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,EAAC;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/E,CAAC,eACNvB,OAAA;IAAKkB,SAAS,EAAC,KAAK;IAAAC,QAAA,EACjBkB,OAAO,CAACZ,GAAG,CAACe,IAAI,iBACfxC,OAAA;MAEEkB,SAAS,EAAE,kDAAkDsB,IAAI,CAACN,EAAE,KAAKK,gBAAgB,GAAG,YAAY,GAAG,EAAE,EAAG;MAChHf,OAAO,EAAEA,CAAA,KAAMc,YAAY,CAACE,IAAI,CAAE;MAAArB,QAAA,gBAElCnB,OAAA;QAAKkB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAEqB,IAAI,CAACR;MAAK;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/CvB,OAAA;QAAKkB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAEtB,UAAU,CAAC,IAAI4C,IAAI,CAACD,IAAI,CAACE,SAAS,CAAC;MAAC;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA,GALnFiB,IAAI,CAACN,EAAE;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMT,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACoB,GAAA,GAnBIP,aAAa;AAqBnB,MAAMQ,kBAAkB,GAAGA,CAAC;EAAEZ,KAAK;EAAEa,OAAO;EAAEC,SAAS;EAAEC;AAAS,CAAC,kBACjE/C,OAAA;EAAKkB,SAAS,EAAC,mFAAmF;EAAAC,QAAA,eAChGnB,OAAA;IAAKkB,SAAS,EAAC,6DAA6D;IAAAC,QAAA,gBAC1EnB,OAAA;MAAKkB,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBnB,OAAA;QAAIkB,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EAAEa;MAAK;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrDvB,OAAA;QAAGkB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAE0B;MAAO;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eACNvB,OAAA;MAAKkB,SAAS,EAAC,yCAAyC;MAAAC,QAAA,gBACtDnB,OAAA;QACEwB,OAAO,EAAEuB,QAAS;QAClB7B,SAAS,EAAC,0EAA0E;QAAAC,QAAA,EACrF;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvB,OAAA;QACEwB,OAAO,EAAEsB,SAAU;QACnB5B,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EAChF;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;;AAED;AAAAyB,GAAA,GAzBMJ,kBAAkB;AA0BxB,MAAMK,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAG/D,WAAW,CAAC,CAAC;EAC9B,MAAMgE,QAAQ,GAAG/D,WAAW,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC4E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACjE,MAAM,CAAC8E,cAAc,EAAEC,iBAAiB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgF,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGjF,QAAQ,CAAC,QAAQ,CAAC;EAClF,MAAM,CAACkF,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACoF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsF,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwF,aAAa,EAAEC,gBAAgB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3F,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAAC4F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7F,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAAC8F,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACgG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACkG,YAAY,EAAEC,eAAe,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoG,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGrG,QAAQ,CAAC,CAAC,CAAC;EACjF,MAAM,CAACsG,aAAa,EAAEC,gBAAgB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACwG,WAAW,EAAEC,cAAc,CAAC,GAAGzG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACpD,MAAM0G,cAAc,GAAGvG,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMwG,OAAO,GAAGxG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACyG,KAAK,EAAEC,QAAQ,CAAC,GAAG7G,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8G,WAAW,EAAEC,cAAc,CAAC,GAAG/G,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgH,cAAc,EAAEC,iBAAiB,CAAC,GAAGjH,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACkH,SAAS,EAAEC,YAAY,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoH,UAAU,EAAEC,aAAa,CAAC,GAAGrH,QAAQ,CAAC,SAAS,CAAC;EACvD,MAAM,CAACsH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvH,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwH,eAAe,EAAEC,kBAAkB,CAAC,GAAGzH,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC0H,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3H,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC4H,cAAc,EAAEC,iBAAiB,CAAC,GAAG7H,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8H,eAAe,EAAEC,kBAAkB,CAAC,GAAG/H,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC4D,gBAAgB,EAAEoE,mBAAmB,CAAC,GAAGhI,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAMiI,iBAAiB,GAAG9H,MAAM,CAAC,KAAK,CAAC;EACvC,MAAM,CAAC+H,WAAW,EAAEC,cAAc,CAAC,GAAGnI,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoI,SAAS,EAAEC,YAAY,CAAC,GAAGrI,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACsI,eAAe,EAAEC,kBAAkB,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACwI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEjE;EACA,MAAM0I,gBAAgB,GAAGxI,WAAW,CAAC,YAAY;IAC/C;IACA,MAAMyI,cAAc,GAAG,CAAC,EAAEC,QAAQ,CAACC,iBAAiB,IAAID,QAAQ,CAACE,uBAAuB,IAAIF,QAAQ,CAACG,oBAAoB,IAAIH,QAAQ,CAACI,mBAAmB,CAAC;IAE1J,IAAI;MACF,IAAI,CAACL,cAAc,EAAE;QACnB;QACA;QACA,IAAInD,aAAa,CAACyD,MAAM,GAAG,CAAC,EAAE;UAC5B5C,8BAA8B,CAACX,iBAAiB,CAAC;QACnD;QACA;QACA,MAAMkD,QAAQ,CAACM,eAAe,CAACC,iBAAiB,CAAC,CAAC;QAClD;MACF,CAAC,MAAM;QACL;QACA,MAAMP,QAAQ,CAACQ,cAAc,CAAC,CAAC;QAC/B;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B;MACAlD,eAAe,CAACoD,IAAI,IAAI,CAACA,IAAI,CAAC;IAChC;EACF,CAAC,EAAE,CAAC7D,iBAAiB,EAAEF,aAAa,CAACyD,MAAM,CAAC,CAAC;;EAE7C;EACA,MAAMO,wBAAwB,GAAGtJ,WAAW,CAAC,MAAM;IACjD;IACA,IAAIoF,YAAY,CAAC2D,MAAM,GAAG,CAAC,IAAIrF,gBAAgB,EAAE;MAAE;MACjD,MAAM6F,cAAc,GAAG;QACrBlG,EAAE,EAAEK,gBAAgB;QAAE;QACtBP,KAAK,EAAE6B,SAAS,IAAI,OAAOtB,gBAAgB,CAAC8F,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;QAAE;QAC1DC,QAAQ,EAAErE,YAAY;QACtBsE,MAAM,EAAEpE,aAAa;QACrBzB,SAAS,EAAE,IAAID,IAAI,CAAC,CAAC,CAAC+F,WAAW,CAAC,CAAC;QACnCC,UAAU,EAAElG;MACd,CAAC;MAED6C,cAAc,CAACsD,WAAW,IAAI;QAC5B,MAAMC,oBAAoB,GAAGD,WAAW,CAACE,SAAS,CAACpG,IAAI,IAAIA,IAAI,CAACN,EAAE,KAAKK,gBAAgB,CAAC;QACxF,IAAIsG,cAAc;QAClB,IAAIF,oBAAoB,IAAI,CAAC,EAAE;UAC3BE,cAAc,GAAG,CAAC,GAAGH,WAAW,CAAC;UACjCG,cAAc,CAACF,oBAAoB,CAAC,GAAGP,cAAc,CAAC,CAAC;QACzD,CAAC,MAAM;UACLS,cAAc,GAAG,CAACT,cAAc,EAAE,GAAGM,WAAW,CAAC,CAAC,CAAC;QACvD;QACA;QACA;QACA,IAAI;UACFI,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACJ,cAAc,CAAC,CAAC;QACrE,CAAC,CAAC,OAAOb,KAAK,EAAE;UACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACtD;QACA,OAAOa,cAAc;MACvB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC5E,YAAY,EAAEE,aAAa,EAAEN,SAAS,EAAEtB,gBAAgB,EAAE6C,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEhF;EACA,MAAM8D,qBAAqB,GAAGrK,WAAW,CAAC,MAAM;IAC9C+H,iBAAiB,CAACuC,OAAO,GAAG,IAAI,CAAC,CAAC;;IAElC;IACAjF,eAAe,CAAC,EAAE,CAAC;IACnBE,gBAAgB,CAAC,EAAE,CAAC;IACpBN,YAAY,CAAC,QAAQ,CAAC;IACtBF,2BAA2B,CAAC,QAAQ,CAAC;IACrCI,eAAe,CAAC,KAAK,CAAC;IACtB0B,cAAc,CAAC,IAAI,CAAC;IACpBF,QAAQ,CAAC,EAAE,CAAC;IACZI,iBAAiB,CAAC,EAAE,CAAC;IACrBE,YAAY,CAAC,EAAE,CAAC;IAChBE,aAAa,CAAC,SAAS,CAAC;;IAExB;IACAW,mBAAmB,CAAC,IAAI,CAAC;IACzBmC,YAAY,CAACM,UAAU,CAAC,kBAAkB,CAAC;;IAE3C;IACAjG,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC,EAAE,CACDe,eAAe,EAAEE,gBAAgB,EAAEN,YAAY,EAAEF,2BAA2B,EAC5EI,eAAe,EAAE0B,cAAc,EAAEF,QAAQ,EAAEI,iBAAiB,EAAEE,YAAY,EAC1EE,aAAa,EAAEW,mBAAmB,EAAExD,QAAQ,CAC7C,CAAC;;EAEF;EACA,MAAMkG,kBAAkB,GAAGxK,WAAW,CAAC,YAAY;IACjD2E,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACF,MAAM8F,aAAa,GAAG,MAAMxJ,UAAU,CAACyJ,kBAAkB,CAAC,CAAC;MAC3DtB,OAAO,CAACuB,GAAG,CAAC,6CAA6C,EAAEF,aAAa,CAAC;MAEzE,MAAMG,kBAAkB,GAAGH,aAAa,CAAC7H,GAAG,CAACiI,IAAI,KAAK;QACpDxH,EAAE,EAAEwH,IAAI,CAACxH,EAAE;QAAE;QACbF,KAAK,EAAE0H,IAAI,CAAC1H,KAAK,IAAI,OAAO0H,IAAI,CAACxH,EAAE,GAAGwH,IAAI,CAACxH,EAAE,CAACmG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG;QAAE;QACnE3F,SAAS,EAAEgH,IAAI,CAACC,aAAa,IAAID,IAAI,CAACE,UAAU;QAChDnB,UAAU,EAAEiB,IAAI,CAACxH,EAAE;QAAE;QACrB2H,SAAS,EAAE,IAAI,CAAC;MAClB,CAAC,CAAC,CAAC;MAEHzE,cAAc,CAACqE,kBAAkB,CAAC;;MAElC;MACA,MAAMK,gBAAgB,GAAG,IAAIC,GAAG,CAACT,aAAa,CAAC7H,GAAG,CAACuI,CAAC,IAAIA,CAAC,CAACvB,UAAU,CAAC,CAAC;MACtE,MAAMwB,QAAQ,GAAGnB,YAAY,CAACoB,OAAO,CAAC,kBAAkB,CAAC;MACzD,IAAID,QAAQ,IAAI,CAACH,gBAAgB,CAACK,GAAG,CAACF,QAAQ,CAAC,EAAE;QAC/CnB,YAAY,CAACM,UAAU,CAAC,kBAAkB,CAAC;MAC7C;IAEF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC5C,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC,SAAS;MACR5B,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,CAAC4B,cAAc,EAAE5B,mBAAmB,CAAC,CAAC;;EAEzC;EACA,MAAM4G,kBAAkB,GAAGvL,WAAW,CAAC,MAAOwL,SAAS,IAAK;IAC1D;IACA,IAAI,CAACA,SAAS,IAAIA,SAAS,KAAK,WAAW,EAAE;MAC3CpC,OAAO,CAACqC,IAAI,CAAC,oBAAoB,EAAED,SAAS,CAAC;MAC7C/G,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;MACrB4F,qBAAqB,CAAC,CAAC,CAAC,CAAC;MACzB;IACF;IAEA5F,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMiH,OAAO,GAAG,MAAMzK,UAAU,CAAC0K,iBAAiB,CAACH,SAAS,CAAC;MAC7DpC,OAAO,CAACuB,GAAG,CAAC,wCAAwCa,SAAS,GAAG,EAAEE,OAAO,CAAC;;MAE1E;MACA,IAAIE,eAAe,GAAGF,OAAO,CAACG,YAAY,IAAI,EAAE;;MAEhD;MACA,IAAID,eAAe,CAAC7C,MAAM,GAAG,CAAC,EAAE;QAC9B6C,eAAe,GAAGA,eAAe,CAAChJ,GAAG,CAAC,CAACkJ,GAAG,EAAEC,KAAK,MAAM;UACrD,GAAGD,GAAG;UACNzI,EAAE,EAAEyI,GAAG,CAACzI,EAAE,IAAI,OAAO0I,KAAK,IAAInI,IAAI,CAACoI,GAAG,CAAC,CAAC,EAAE;UAC1CC,MAAM,EAAEH,GAAG,CAACG,MAAM,KAAKF,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC;UACvDG,IAAI,EAAEJ,GAAG,CAACI,IAAI,IAAIJ,GAAG,CAACK,OAAO,IAAI,KAAK;UACtCtI,SAAS,EAAEiI,GAAG,CAACjI,SAAS,IAAI,IAAID,IAAI,CAAC,CAAC,CAAC+F,WAAW,CAAC;QACrD,CAAC,CAAC,CAAC;QACHP,OAAO,CAACuB,GAAG,CAAC,sBAAsB,EAAEiB,eAAe,CAAC;MACtD,CAAC,MAAM;QACL;QACAA,eAAe,GAAG,CAAC;UACjBvI,EAAE,EAAE,UAAUO,IAAI,CAACoI,GAAG,CAAC,CAAC,EAAE;UAC1BC,MAAM,EAAE,QAAQ;UAChBC,IAAI,EAAE,gBAAgB;UACtBrI,SAAS,EAAE,IAAID,IAAI,CAAC,CAAC,CAAC+F,WAAW,CAAC;QACpC,CAAC,CAAC;MACJ;MAEAtE,eAAe,CAACuG,eAAe,CAAC;MAChC,MAAMQ,YAAY,GAAGV,OAAO,CAACvI,KAAK,IAAI,OAAOqI,SAAS,CAAChC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;MACnEvE,YAAY,CAACmH,YAAY,CAAC;MAC1BrH,2BAA2B,CAACqH,YAAY,CAAC;MACzCtE,mBAAmB,CAAC4D,OAAO,CAAC9B,UAAU,CAAC;;MAEvC;MACAK,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEwB,OAAO,CAAC9B,UAAU,CAAC;;MAE5D;MACA,IAAI,CAACrF,QAAQ,CAAC8H,MAAM,CAACC,QAAQ,CAAC,aAAaZ,OAAO,CAAC9B,UAAU,EAAE,CAAC,EAAE;QAChEtF,QAAQ,CAAC,eAAeoH,OAAO,CAAC9B,UAAU,EAAE,EAAE;UAAE2C,OAAO,EAAE;QAAK,CAAC,CAAC;MAClE;MAEAhF,kBAAkB,CAACmE,OAAO,CAACc,oBAAoB,IAAI,CAACd,OAAO,CAAChC,MAAM,IAAI,EAAE,EAAEX,MAAM,CAAC;;MAEjF;MACA,IAAI2C,OAAO,CAAChC,MAAM,IAAIgC,OAAO,CAAChC,MAAM,CAACX,MAAM,GAAG,CAAC,EAAE;QAC/C,MAAM0D,kBAAkB,GAAGf,OAAO,CAAChC,MAAM,CAAC9G,GAAG,CAAC8J,CAAC,KAAK;UAChDrJ,EAAE,EAAEqJ,CAAC,CAACrJ,EAAE;UACRsJ,IAAI,EAAE,EAAE;UACRC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAEH,CAAC,CAACI,YAAY,GAAG,CAAC;UACzB3J,KAAK,EAAE,OAAOuJ,CAAC,CAACI,YAAY,EAAE;UAC9BtI,SAAS,EAAE;QACf,CAAC,CAAC,CAAC;QACHe,gBAAgB,CAACkH,kBAAkB,CAAC;QAEpC,MAAMM,oBAAoB,GAAGrB,OAAO,CAAChC,MAAM,CAAC9G,GAAG,CAACoK,SAAS,IACrD/L,UAAU,CAACgM,eAAe,CAACD,SAAS,CAAC3J,EAAE,CAAC,CAAC6J,KAAK,CAAC,OAAO;UAAE7J,EAAE,EAAE2J,SAAS,CAAC3J,EAAE;UAAE8F,KAAK,EAAE;QAAK,CAAC,CAAC,CAC5F,CAAC;QACD,MAAMgE,cAAc,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACN,oBAAoB,CAAC;QAE9DxH,gBAAgB,CAACD,aAAa,IAAIA,aAAa,CAAC1C,GAAG,CAAC0K,SAAS,IAAI;UAC7D,MAAMC,QAAQ,GAAGJ,cAAc,CAACK,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACpK,EAAE,KAAKiK,SAAS,CAACjK,EAAE,CAAC;UAClE,IAAIkK,QAAQ,IAAI,CAACA,QAAQ,CAACpE,KAAK,EAAE;YAC7B,OAAO;cAAE,GAAGmE,SAAS;cAAEX,IAAI,EAAEY,QAAQ,CAACZ,IAAI;cAAEC,IAAI,EAAEW,QAAQ,CAACZ,IAAI;cAAEnI,SAAS,EAAE;YAAM,CAAC;UACvF;UACA,OAAO;YAAE,GAAG8I,SAAS;YAAEX,IAAI,EAAE,mBAAmB;YAAEC,IAAI,EAAE,cAAc;YAAEpI,SAAS,EAAE;UAAM,CAAC;QAC9F,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLe,gBAAgB,CAAC,EAAE,CAAC;MACtB;IAEF,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,QAAQqC,SAAS,QAAQ,EAAErC,KAAK,CAAC;MAC/C,IAAIA,KAAK,CAACnF,OAAO,KAAKmF,KAAK,CAACnF,OAAO,CAACsI,QAAQ,CAAC,WAAW,CAAC,IAAInD,KAAK,CAACnF,OAAO,CAACsI,QAAQ,CAAC,YAAY,CAAC,CAAC,EAAE;QAClG;QACA,IAAIrC,YAAY,CAACoB,OAAO,CAAC,kBAAkB,CAAC,KAAKG,SAAS,EAAE;UAC1DvB,YAAY,CAACM,UAAU,CAAC,kBAAkB,CAAC;QAC7C;QACAF,qBAAqB,CAAC,CAAC;MACzB;IACF,CAAC,SAAS;MACR5F,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CACDA,YAAY,EAAE4F,qBAAqB,EACnChF,eAAe,EAAEJ,YAAY,EAAEF,2BAA2B,EAAE+C,mBAAmB,EAC/EvD,QAAQ,CAAC8H,MAAM,EAAE/H,QAAQ,EAAEiD,kBAAkB,EAAEhC,gBAAgB,CAChE,CAAC;;EAEF;EACAxF,SAAS,CAAC,MAAM;IACd,MAAM2N,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChCjJ,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAM+F,kBAAkB,CAAC,CAAC;;MAE1B;MACA,MAAMmD,MAAM,GAAG,IAAIC,eAAe,CAACrJ,QAAQ,CAAC8H,MAAM,CAAC;MACnD,IAAIwB,eAAe,GAAGF,MAAM,CAACG,GAAG,CAAC,WAAW,CAAC;;MAE7C;MACA,IAAI,CAACD,eAAe,IAAIA,eAAe,KAAK,WAAW,EAAE;QACvDA,eAAe,GAAG5D,YAAY,CAACoB,OAAO,CAAC,kBAAkB,CAAC;MAC5D;MAEA,IAAIwC,eAAe,IAAIA,eAAe,KAAK,WAAW,EAAE;QACtD;QACAzE,OAAO,CAACuB,GAAG,CAAC,4CAA4CkD,eAAe,EAAE,CAAC;QAC1E,MAAMtC,kBAAkB,CAACsC,eAAe,CAAC;MAC3C,CAAC,MAAM;QACL;QACA;QACA,IAAI9F,iBAAiB,CAACuC,OAAO,EAAE;UAC7BlB,OAAO,CAACuB,GAAG,CAAC,qEAAqE,CAAC;UAClF5C,iBAAiB,CAACuC,OAAO,GAAG,KAAK;UACjC7F,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;UACrB,OAAO,CAAC;QACV;;QAEA;QACA,MAAMjB,OAAO,GAAG,MAAMvC,UAAU,CAACyJ,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACvD,IAAIlH,OAAO,IAAIA,OAAO,CAACuF,MAAM,GAAG,CAAC,EAAE;UACjC,MAAMgF,aAAa,GAAGvK,OAAO,CAACwK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAItK,IAAI,CAACsK,CAAC,CAACpD,aAAa,CAAC,GAAG,IAAIlH,IAAI,CAACqK,CAAC,CAACnD,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;UACtG;UACA,IAAIiD,aAAa,IAAIA,aAAa,CAACnE,UAAU,EAAE;YAC7CR,OAAO,CAACuB,GAAG,CAAC,2CAA2CoD,aAAa,CAACnE,UAAU,EAAE,CAAC;YAClF,MAAM2B,kBAAkB,CAACwC,aAAa,CAACnE,UAAU,CAAC;UACpD,CAAC,MAAM;YACL;YACAR,OAAO,CAACuB,GAAG,CAAC,sEAAsE,CAAC;YACnFN,qBAAqB,CAAC,CAAC;UACzB;QACF,CAAC,MAAM;UACL;UACAjB,OAAO,CAACuB,GAAG,CAAC,iEAAiE,CAAC;UAC9EN,qBAAqB,CAAC,CAAC;QACzB;MACF;MACA5F,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC;IAEDiJ,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACnJ,QAAQ,CAAC8H,MAAM,EAAE7B,kBAAkB,EAAEe,kBAAkB,EAAElB,qBAAqB,EAAE5F,YAAY,CAAC,CAAC;;EAElG;EACA1E,SAAS,CAAC,MAAM;IACd;IACAqJ,OAAO,CAACuB,GAAG,CAAC,0BAA0B,CAAC;;IAEvC;IACA,MAAMwD,kBAAkB,GAAGA,CAAA,KAAM;MAC/B;MACA,IAAIzK,gBAAgB,EAAE;QACpBuG,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAExG,gBAAgB,CAAC;MAC5D;IACF,CAAC;;IAED;IACA0K,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEF,kBAAkB,CAAC;;IAE3D;IACA,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEH,kBAAkB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAACzK,gBAAgB,CAAC,CAAC,CAAC,CAAC;;EAExB;EACA3D,SAAS,CAAC,MAAM;IACd;IACA,IAAIqF,YAAY,CAAC2D,MAAM,GAAG,CAAC,IAAIzD,aAAa,CAACyD,MAAM,GAAG,CAAC,EAAE;MACvD,IAAI;QAAA,IAAAwF,kBAAA;QACFtE,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAAChF,YAAY,CAAC,CAAC;QAClE6E,YAAY,CAACC,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAAC9E,aAAa,CAAC,CAAC;QACpE2E,YAAY,CAACC,OAAO,CAAC,WAAW,EAAElF,SAAS,CAAC;QAC5CiF,YAAY,CAACC,OAAO,CAAC,mBAAmB,EAAEpF,wBAAwB,CAAC;;QAEnE;QACA,MAAM0J,qBAAqB,IAAAD,kBAAA,GAAGnJ,YAAY,CAACoI,IAAI,CAAC1B,GAAG,IAAIA,GAAG,CAAClC,UAAU,CAAC,cAAA2E,kBAAA,uBAAxCA,kBAAA,CAA0C3E,UAAU;QAClF,IAAI4E,qBAAqB,EAAE;UACzB1G,mBAAmB,CAAC0G,qBAAqB,CAAC;UAC1CvE,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEsE,qBAAqB,CAAC;QACjE;MACF,CAAC,CAAC,OAAOrF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF;EACF,CAAC,EAAE,CAAC/D,YAAY,EAAEE,aAAa,EAAEN,SAAS,EAAEF,wBAAwB,CAAC,CAAC;EAEtE,MAAM2J,cAAc,GAAGA,CAAA,KAAM5J,iBAAiB,CAAC,CAACD,cAAc,CAAC;EAE/D,MAAM8J,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,MAAMC,aAAa,GAAG5J,SAAS,CAAC,CAAC;;IAEjC;IACAC,YAAY,CAAC0J,QAAQ,CAAC;IACtB5J,2BAA2B,CAAC4J,QAAQ,CAAC;IAErC,IAAIjL,gBAAgB,EAAE;MACpB,IAAI;QACF,MAAMzC,UAAU,CAAC4N,aAAa,CAACnL,gBAAgB,EAAE;UAAEP,KAAK,EAAEwL;QAAS,CAAC,CAAC;QACrEvF,OAAO,CAACuB,GAAG,CAAC,YAAYjH,gBAAgB,kBAAkBiL,QAAQ,EAAE,CAAC;QAErEpI,cAAc,CAAC8C,IAAI,IAAIA,IAAI,CAACzG,GAAG,CAACe,IAAI,IAClCA,IAAI,CAACiG,UAAU,KAAKlG,gBAAgB,GAAG;UAAE,GAAGC,IAAI;UAAER,KAAK,EAAEwL;QAAS,CAAC,GAAGhL,IACxE,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOwF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC2F,KAAK,CAAC,WAAW3F,KAAK,CAACnF,OAAO,EAAE,CAAC;;QAEjC;QACA;QACAiB,YAAY,CAAC2J,aAAa,CAAC;QAC3B7J,2BAA2B,CAAC6J,aAAa,CAAC;MAC5C;IACF;EACF,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI7J,YAAY,EAAE;MAChB;MACA,IAAIsB,cAAc,CAAC8D,OAAO,EAAE;QAC1B9D,cAAc,CAAC8D,OAAO,CAAC0E,KAAK,CAAC,CAAC;QAC9BxI,cAAc,CAAC8D,OAAO,GAAG,IAAI;MAC/B;MACAnF,eAAe,CAAC,KAAK,CAAC;MACtBE,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAChChG,EAAE,EAAEO,IAAI,CAACoI,GAAG,CAAC,CAAC;QACd9I,IAAI,EAAE,UAAU;QAChBgJ,IAAI,EAAE,QAAQ;QACdD,MAAM,EAAE,QAAQ;QAAE;QAClBgD,IAAI,EAAE,GAAG,CAAC;MACZ,CAAC,CAAC,CAAC;MACH;IACF;;IAEA;IACA5E,qBAAqB,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC;EAED,MAAM6E,mBAAmB,GAAIC,WAAW,IAAK;IAC3C;IACA,IAAI,CAACA,WAAW,IAAI,CAACA,WAAW,CAACvF,UAAU,EAAE;MACzCR,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEgG,WAAW,CAAC;MAC1C,OAAO,CAAC;IACZ;IACA/F,OAAO,CAACuB,GAAG,CAAC,SAAS,EAAEwE,WAAW,CAAC;IACnC9H,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;;IAE7B;IACA/C,QAAQ,CAAC,eAAe6K,WAAW,CAACvF,UAAU,EAAE,CAAC;EACnD,CAAC;EAED,MAAMwF,mBAAmB,GAAG,MAAAA,CAAOC,aAAa,EAAEC,iBAAiB,KAAK;IAAA,IAAAC,iBAAA;IACtE;IACA,IAAIF,aAAa,KAAK,iBAAiB,EAAE;;IAEzC;IACA1H,iBAAiB,CAAC;MAChBtE,EAAE,EAAEgM,aAAa;MAAE;MACnB7D,SAAS,EAAE8D,iBAAiB;MAAE;MAC9BnM,KAAK,EAAE,EAAAoM,iBAAA,GAAAjJ,WAAW,CAACkH,IAAI,CAAC7J,IAAI,IAAIA,IAAI,CAACN,EAAE,KAAKgM,aAAa,CAAC,cAAAE,iBAAA,uBAAnDA,iBAAA,CAAqDpM,KAAK,KAAI;IACvE,CAAC,CAAC;IACFsE,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAM+H,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAAC9H,cAAc,IAAI,CAACA,cAAc,CAAC8D,SAAS,EAAE;MAChD/D,yBAAyB,CAAC,KAAK,CAAC;MAChCE,iBAAiB,CAAC,IAAI,CAAC;MACvB;IACF;IAEA,MAAM;MAAEtE,EAAE,EAAEoM,kBAAkB;MAAEjE,SAAS,EAAEkE;IAAgB,CAAC,GAAGhI,cAAc;IAE7E,IAAI;MACF;MACA,MAAMiI,QAAQ,GAAG,MAAM1O,UAAU,CAAC2O,aAAa,CAACF,eAAe,CAAC;MAChEtG,OAAO,CAACuB,GAAG,CAAC,MAAM+E,eAAe,QAAQ,EAAEC,QAAQ,CAAC;;MAEpD;MACA,MAAM3F,cAAc,GAAG1D,WAAW,CAACuJ,MAAM,CAAClM,IAAI,IAAIA,IAAI,CAACN,EAAE,KAAKoM,kBAAkB,CAAC;MACjFlJ,cAAc,CAACyD,cAAc,CAAC;;MAE9B;MACA,IAAItG,gBAAgB,KAAKgM,eAAe,EAAE;QACxCrF,qBAAqB,CAAC,CAAC;MACzB;MACAjB,OAAO,CAACuB,GAAG,CAAC,MAAM+E,eAAe,cAAcD,kBAAkB,UAAU,CAAC;IAE9E,CAAC,CAAC,OAAOtG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,QAAQuG,eAAe,OAAO,EAAEvG,KAAK,CAAC;MACpD2F,KAAK,CAAC,WAAW3F,KAAK,CAACnF,OAAO,EAAE,CAAC;IACnC,CAAC,SAAS;MACRyD,yBAAyB,CAAC,KAAK,CAAC;MAChCE,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAED,MAAMmI,qBAAqB,GAAGA,CAAA,KAAM;IAClCzJ,gBAAgB,CAAC,CAACD,aAAa,CAAC;IAChCT,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE,MAAMoK,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,KAAK;IAChD,IAAI,CAACvK,kBAAkB,EAAE;IACzB,MAAM;MAAEwK,OAAO;MAAEC;IAAU,CAAC,GAAGzK,kBAAkB;IACjDH,gBAAgB,CAAC6K,UAAU,IACzBA,UAAU,CAACxN,GAAG,CAACyN,KAAK,IAAI;MACtB,IAAIA,KAAK,CAAChN,EAAE,KAAK6M,OAAO,EAAE;QACxB,MAAMI,OAAO,GAAG5H,QAAQ,CAAC6H,aAAa,CAAC,KAAK,CAAC;QAC7CD,OAAO,CAACE,SAAS,GAAGH,KAAK,CAAC1D,IAAI;QAC9B,MAAM8D,aAAa,GAAGH,OAAO,CAACI,aAAa,CAAC,sBAAsBP,SAAS,IAAI,CAAC;QAChF,IAAIM,aAAa,EAAE;UACjBA,aAAa,CAACE,SAAS,GAAGX,OAAO;UACjCY,MAAM,CAACC,IAAI,CAACZ,QAAQ,CAAC,CAACa,OAAO,CAACC,GAAG,IAAI;YACnCN,aAAa,CAACO,KAAK,CAACD,GAAG,CAAC,GAAGd,QAAQ,CAACc,GAAG,CAAC;UAC1C,CAAC,CAAC;QACJ;QACA,OAAO;UAAE,GAAGV,KAAK;UAAE1D,IAAI,EAAE2D,OAAO,CAACE,SAAS;UAAE5D,IAAI,EAAE0D,OAAO,CAACE;QAAU,CAAC;MACvE;MACA,OAAOH,KAAK;IACd,CAAC,CACH,CAAC;IACD1K,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMsL,oBAAoB,GAAGA,CAAA,KAAMtL,qBAAqB,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAMuL,kBAAkB,GAAIC,CAAC,IAAK;IAChCA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB,IAAIpJ,WAAW,EAAE;MACfG,YAAY,CAAC;QAAE+H,OAAO,EAAElI,WAAW,CAACkI,OAAO;QAAEC,SAAS,EAAEnI,WAAW,CAACmI;MAAU,CAAC,CAAC;MAChFlI,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;IACxB;EACF,CAAC;EAED,MAAMoJ,oBAAoB,GAAGA,CAAA,KAAM;IACjClJ,YAAY,CAAC,IAAI,CAAC;IAClBE,kBAAkB,CAAC,EAAE,CAAC;IACtBE,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAM+I,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAClJ,eAAe,CAACmJ,IAAI,CAAC,CAAC,IAAI,CAACrJ,SAAS,EAAE;IAE3CK,mBAAmB,CAAC,IAAI,CAAC;IACzB,MAAM;MAAE2H,OAAO;MAAEC;IAAU,CAAC,GAAGjI,SAAS;IAExC,IAAI;MACF,MAAMrF,MAAM,GAAG,MAAM5B,UAAU,CAACuQ,gBAAgB,CAACtB,OAAO,EAAE,sBAAsBC,SAAS,IAAI,EAAE/H,eAAe,CAAC;MAC/G,IAAIvF,MAAM,IAAIA,MAAM,CAAC8J,IAAI,EAAE;QACzB;QACApH,gBAAgB,CAAC6K,UAAU,IACzBA,UAAU,CAACxN,GAAG,CAACyN,KAAK,IAClBA,KAAK,CAAChN,EAAE,KAAK6M,OAAO,GAAG;UAAE,GAAGG,KAAK;UAAE1D,IAAI,EAAE9J,MAAM,CAAC8J,IAAI;UAAEC,IAAI,EAAE/J,MAAM,CAAC8J;QAAK,CAAC,GAAG0D,KAC9E,CACF,CAAC;MACH;IACF,CAAC,CAAC,OAAOlH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B2F,KAAK,CAAC,SAAS3F,KAAK,CAACnF,OAAO,EAAE,CAAC;IACjC,CAAC,SAAS;MACRqN,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC;EACD;;EAEA;EACA,MAAMI,uBAAuB,GAAGA,CAAA,KAAM;IACpCnN,QAAQ,CAAC,SAAS,EAAE;MAClBoN,KAAK,EAAE;QACLhI,MAAM,EAAEpE,aAAa;QACrBqM,YAAY,EAAE,CAAC;QACfC,iBAAiB,EAAE9M;MACrB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA/E,SAAS,CAAC,MAAM;IACd,MAAM8R,2BAA2B,GAAIC,KAAK,IAAK;MAC7C,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAAC7O,IAAI,KAAK,oBAAoB,EAAE;QAC1D;QACA,IAAI4O,KAAK,CAACC,IAAI,CAACC,OAAO,IAAIF,KAAK,CAACC,IAAI,CAACC,OAAO,CAACL,YAAY,KAAKM,SAAS,EAAE;UACvE9L,8BAA8B,CAAC2L,KAAK,CAACC,IAAI,CAACC,OAAO,CAACL,YAAY,CAAC;QACjE;QACA;QACAnJ,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC;IAED4F,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEwD,2BAA2B,CAAC;IAC/D,OAAO,MAAM;MACXzD,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEuD,2BAA2B,CAAC;IACpE,CAAC;EACH,CAAC,EAAE,CAACrJ,gBAAgB,CAAC,CAAC;;EAEtB;EACAzI,SAAS,CAAC,MAAM;IACd,MAAMmS,mBAAmB,GAAIJ,KAAK,IAAK;MACrC,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAAC7O,IAAI,KAAK,iBAAiB,IAAIkD,aAAa,EAAE;QACxE,MAAM;UAAE8J,OAAO;UAAEC,SAAS;UAAEgC;QAAc,CAAC,GAAGL,KAAK,CAACC,IAAI,CAACC,OAAO;;QAEhE;QACA,MAAMI,MAAM,GAAG1J,QAAQ,CAACgI,aAAa,CAAC,kBAAkBR,OAAO,SAAS,CAAC;QACzE,IAAIkC,MAAM,EAAE;UACV,MAAMC,UAAU,GAAGD,MAAM,CAACE,qBAAqB,CAAC,CAAC;UACjD,MAAMC,KAAK,GAAGF,UAAU,CAACG,KAAK,GAAG,IAAI,CAAC,CAAC;;UAEvC,MAAMC,SAAS,GAAGJ,UAAU,CAACK,IAAI,GAAIP,aAAa,CAACQ,CAAC,GAAGJ,KAAM,GAAGnE,MAAM,CAACwE,OAAO;UAC9E,MAAMC,SAAS,GAAGR,UAAU,CAACS,GAAG,GAAIX,aAAa,CAACY,CAAC,GAAGR,KAAM,GAAGnE,MAAM,CAAC4E,OAAO;UAE7E/K,cAAc,CAAC;YACb0K,CAAC,EAAEF,SAAS;YACZM,CAAC,EAAEF,SAAS;YACZ3C,OAAO;YACPC;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC;IAED/B,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAE6D,mBAAmB,CAAC;IACvD;IACA,MAAMe,SAAS,GAAGA,CAAA,KAAMhL,cAAc,CAAC,IAAI,CAAC;IAC5CmG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAE4E,SAAS,CAAC;IAE3C,OAAO,MAAM;MACX7E,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAE4D,mBAAmB,CAAC;MAC1D9D,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAE2E,SAAS,CAAC;IAChD,CAAC;EACH,CAAC,EAAE,CAAC7M,aAAa,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEArG,SAAS,CAAC,MAAM;IACd;IACA,MAAMmT,sBAAsB,GAAGA,CAAA,KAAM;MACnC,MAAMC,qBAAqB,GAAG,CAAC,EAAEzK,QAAQ,CAACC,iBAAiB,IAAID,QAAQ,CAACE,uBAAuB,IAAIF,QAAQ,CAAC0K,oBAAoB,IAAI1K,QAAQ,CAACI,mBAAmB,CAAC;MACjK7C,eAAe,CAACkN,qBAAqB,CAAC;IACxC,CAAC;IAEDzK,QAAQ,CAAC2F,gBAAgB,CAAC,kBAAkB,EAAE6E,sBAAsB,CAAC;IACrExK,QAAQ,CAAC2F,gBAAgB,CAAC,wBAAwB,EAAE6E,sBAAsB,CAAC;IAC3ExK,QAAQ,CAAC2F,gBAAgB,CAAC,qBAAqB,EAAE6E,sBAAsB,CAAC;IACxExK,QAAQ,CAAC2F,gBAAgB,CAAC,oBAAoB,EAAE6E,sBAAsB,CAAC;IAEvE,OAAO,MAAM;MACXxK,QAAQ,CAAC4F,mBAAmB,CAAC,kBAAkB,EAAE4E,sBAAsB,CAAC;MACxExK,QAAQ,CAAC4F,mBAAmB,CAAC,wBAAwB,EAAE4E,sBAAsB,CAAC;MAC9ExK,QAAQ,CAAC4F,mBAAmB,CAAC,qBAAqB,EAAE4E,sBAAsB,CAAC;MAC3ExK,QAAQ,CAAC4F,mBAAmB,CAAC,oBAAoB,EAAE4E,sBAAsB,CAAC;IAC5E,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,0BAA0B,GAAItH,KAAK,IAAK;IAC5CtG,oBAAoB,CAACsG,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMuH,oBAAoB,GAAGA,CAACC,UAAU,EAAEC,QAAQ,KAAK;IACrD3L,kBAAkB,CAACwB,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACkK,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAI1H,KAAK,IAAK;IACxC5F,8BAA8B,CAAC4F,KAAK,CAAC;IACrCvD,gBAAgB,CAAC,CAAC;EACpB,CAAC;EAED,MAAMkL,iBAAiB,GAAG,MAAOC,WAAW,IAAK;IAC/C;IACA,IAAIC,WAAW;MAAEC,KAAK,GAAG,EAAE;IAE3B,IAAI,OAAOF,WAAW,KAAK,QAAQ,EAAE;MACnCC,WAAW,GAAGD,WAAW;IAC3B,CAAC,MAAM,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,IAAI,EAAE;MAClEC,WAAW,GAAGD,WAAW,CAAC3P,OAAO,IAAI,EAAE;MACvC6P,KAAK,GAAGF,WAAW,CAACE,KAAK,IAAI,EAAE;IACjC,CAAC,MAAM;MACLD,WAAW,GAAG,EAAE;IAClB;IAEA,IAAI,CAACA,WAAW,CAACrC,IAAI,CAAC,CAAC,IAAIrM,YAAY,EAAE;;IAEzC;IACA,MAAM4O,SAAS,GAAG,YAAYlQ,IAAI,CAACoI,GAAG,CAAC,CAAC,EAAE;;IAE1C;IACA,MAAM+H,WAAW,GAAG;MAClB1Q,EAAE,EAAEyQ,SAAS;MACb5Q,IAAI,EAAE,MAAM;MACZgJ,IAAI,EAAE0H,WAAW;MACjBC,KAAK,EAAEA,KAAK,CAAC9K,MAAM,GAAG,CAAC,GAAG8K,KAAK,GAAG5B,SAAS,CAAE;IAC/C,CAAC;;IAED;IACA,MAAM+B,iBAAiB,GAAGJ,WAAW,CAACK,WAAW,CAAC,CAAC,CAAC3H,QAAQ,CAAC,IAAI,CAAC,IACzCsH,WAAW,CAACK,WAAW,CAAC,CAAC,CAAC3H,QAAQ,CAAC,UAAU,CAAC;;IAEvE;IACA,IAAI,CAAC0H,iBAAiB,EAAE;MACtB;MACA3O,eAAe,CAAC,CAAC0O,WAAW,CAAC,CAAC,CAAC,CAAC;MAChCxO,gBAAgB,CAAC,EAAE,CAAC;MACpBgC,kBAAkB,CAAC,CAAC,CAAC;MACrBO,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;;MAE3B;MACFjB,cAAc,CAAC,IAAI,CAAC;MAClBF,QAAQ,CAAC,EAAE,CAAC;MACdI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,YAAY,CAAC,EAAE,CAAC;MAChBE,aAAa,CAAC,SAAS,CAAC;IACxB,CAAC,MAAM;MACL;MACF9B,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE0K,WAAW,CAAC,CAAC;IAC/C;IAEA,IAAIG,aAAa,GAAG,CAAC;IACrB,MAAMC,mBAAmB,GAAG,CAAC;IAE7B,MAAMC,iBAAiB,GAAG,MAAOC,cAAc,IAAK;MAClD,IAAI;QACF;QACAlP,eAAe,CAAC,IAAI,CAAC;;QAErB;QACA,IAAIqB,cAAc,CAAC8D,OAAO,EAAE;UAC1B9D,cAAc,CAAC8D,OAAO,CAAC0E,KAAK,CAAC,CAAC;QAChC;;QAEA;QACA;;QAEA;QACA,MAAMsF,YAAY,GAAG,CAAC,CAAC;;QAEvB;QACA;QACA,MAAMC,kBAAkB,GAAGF,cAAc;;QAEzC;QACA7N,cAAc,CAAC8D,OAAO,GAAG,MAAMrJ,UAAU,CAACuT,oBAAoB,CAC5DZ,WAAW;QACX;QACCa,YAAY,IAAK;UAChB;;UAEA;UACA,IAAIA,YAAY,CAACC,MAAM,KAAK,qBAAqB,IAAID,YAAY,CAACE,QAAQ,IAAIF,YAAY,CAACE,QAAQ,CAACC,iBAAiB,EAAE;YACrH,MAAMC,WAAW,GAAGJ,YAAY,CAACE,QAAQ,CAACC,iBAAiB;YAC3DxL,OAAO,CAACuB,GAAG,CAAC,4BAA4BkK,WAAW,EAAE,CAAC;YACtD/M,mBAAmB,CAAC+M,WAAW,CAAC;YAChC5K,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAE2K,WAAW,CAAC;YACrD;YACAvQ,QAAQ,CAAC,eAAeuQ,WAAW,EAAE,EAAE;cAAEtI,OAAO,EAAE;YAAK,CAAC,CAAC;YACzD,OAAO,CAAC;UACV;;UAEA;UACA,IAAI,CAAC7I,gBAAgB,IAAI+Q,YAAY,CAAC7K,UAAU,IAAI6K,YAAY,CAACC,MAAM,KAAK,YAAY,EAAE;YACxFtL,OAAO,CAACuB,GAAG,CAAC,uBAAuB,EAAE8J,YAAY,CAAC7K,UAAU,CAAC;YAC7D9B,mBAAmB,CAAC2M,YAAY,CAAC7K,UAAU,CAAC;YAC5CK,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEuK,YAAY,CAAC7K,UAAU,CAAC,CAAC,CAAC;UACrE;;UAEA;UACAsK,aAAa,GAAG,CAAC;;UAEjB;UACA,IAAIO,YAAY,CAACzQ,OAAO,EAAE;YACxB,MAAM4P,WAAW,GAAGa,YAAY,CAACzQ,OAAO,CAACkI,IAAI;;YAE7C;YACA,IAAI0H,WAAW,KAAK,sBAAsB,IACtCA,WAAW,KAAK,gBAAgB,EAAE;cACpC;YACF;;YAEA;YACA,IAAIkB,aAAa,GAAG,KAAK;YACzB,IAAIC,kBAAkB,GAAG,IAAI;YAC7B,IAAIC,cAAc,GAAG,EAAE;;YAEvB;YACA,IAAIpB,WAAW,CAACtH,QAAQ,CAAC,UAAU,CAAC,EAAE;cACpC,MAAM2I,iBAAiB,GAAGX,YAAY,CAAC,eAAe,CAAC;cACvD,IAAIW,iBAAiB,EAAE;gBACrBF,kBAAkB,GAAGE,iBAAiB;gBACtCH,aAAa,GAAG,IAAI;;gBAEpB;gBACA,MAAMI,SAAS,GAAGZ,YAAY,CAAC,0BAA0B,CAAC;gBAC1D,IAAIY,SAAS,EAAE;kBACb,MAAMC,WAAW,GAAGvR,IAAI,CAACoI,GAAG,CAAC,CAAC,GAAGkJ,SAAS;kBAC1C,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,GAAG,IAAI,CAAC;kBAC9C,IAAIC,OAAO,GAAG,EAAE,EAAE;oBAChBJ,cAAc,GAAG,QAAQI,OAAO,IAAI;kBACtC,CAAC,MAAM;oBACL,MAAMG,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;oBACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;oBACrCJ,cAAc,GAAG,QAAQO,OAAO,IAAIC,gBAAgB,IAAI;kBAC1D;gBACF;cACF;YACF;;YAEA;YACA,IAAI5B,WAAW,CAACtH,QAAQ,CAAC,UAAU,CAAC,EAAE;cACpC,MAAMmJ,oBAAoB,GAAGnB,YAAY,CAAC,kBAAkB,CAAC;cAC7D,IAAImB,oBAAoB,EAAE;gBACxBV,kBAAkB,GAAGU,oBAAoB;gBACzCX,aAAa,GAAG,IAAI;;gBAEpB;gBACA,MAAMI,SAAS,GAAGZ,YAAY,CAAC,6BAA6B,CAAC;gBAC7D,IAAIY,SAAS,EAAE;kBACb,MAAMC,WAAW,GAAGvR,IAAI,CAACoI,GAAG,CAAC,CAAC,GAAGkJ,SAAS;kBAC1C,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,GAAG,IAAI,CAAC;kBAC9C,IAAIC,OAAO,GAAG,EAAE,EAAE;oBAChBJ,cAAc,GAAG,QAAQI,OAAO,IAAI;kBACtC,CAAC,MAAM;oBACL,MAAMG,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;oBACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;oBACrCJ,cAAc,GAAG,QAAQO,OAAO,IAAIC,gBAAgB,IAAI;kBAC1D;gBACF;cACF;YACF;;YAEA;YACA,MAAME,qBAAqB,GAAG,wBAAwB;YACtD,MAAMC,UAAU,GAAG/B,WAAW,CAACgC,KAAK,CAACF,qBAAqB,CAAC;YAC3D,IAAIC,UAAU,EAAE;cACd,MAAME,QAAQ,GAAGF,UAAU,CAAC,CAAC,CAAC;cAC9B,MAAMG,oBAAoB,GAAGxB,YAAY,CAAC,oBAAoBuB,QAAQ,EAAE,CAAC;cACzE,IAAIC,oBAAoB,EAAE;gBACxBf,kBAAkB,GAAGe,oBAAoB;gBACzChB,aAAa,GAAG,IAAI;;gBAEpB;gBACA,MAAMI,SAAS,GAAGZ,YAAY,CAAC,oBAAoBuB,QAAQ,aAAa,CAAC;gBACzE,IAAIX,SAAS,EAAE;kBACb,MAAMC,WAAW,GAAGvR,IAAI,CAACoI,GAAG,CAAC,CAAC,GAAGkJ,SAAS;kBAC1C,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,GAAG,IAAI,CAAC;kBAC9C,IAAIC,OAAO,GAAG,EAAE,EAAE;oBAChBJ,cAAc,GAAG,QAAQI,OAAO,IAAI;kBACtC,CAAC,MAAM;oBACL,MAAMG,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;oBACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;oBACrCJ,cAAc,GAAG,QAAQO,OAAO,IAAIC,gBAAgB,IAAI;kBAC1D;gBACF;cACF;YACF;;YAEA;YACA,IAAI5B,WAAW,CAACtH,QAAQ,CAAC,KAAK,CAAC,IAAIsH,WAAW,CAACtH,QAAQ,CAAC,UAAU,CAAC,EAAE;cACnE,MAAMyJ,QAAQ,GAAG,UAAUnS,IAAI,CAACoI,GAAG,CAAC,CAAC,IAAIqJ,IAAI,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;cACrF5B,YAAY,CAAC,eAAe,CAAC,GAAGyB,QAAQ;cACxCzB,YAAY,CAAC,0BAA0B,CAAC,GAAG1Q,IAAI,CAACoI,GAAG,CAAC,CAAC;;cAErD;cACA3G,eAAe,CAACgE,IAAI,IAAI,CACtB,GAAGA,IAAI,EACP;gBACEhG,EAAE,EAAE0S,QAAQ;gBACZ7S,IAAI,EAAE,IAAI;gBACVgJ,IAAI,EAAE0H,WAAW;gBACjB3E,IAAI,EAAEwF,YAAY,CAACzQ,OAAO,CAACiL,IAAI,IAAIgD,SAAS;gBAC5CkE,QAAQ,EAAE1B,YAAY,CAACzQ,OAAO,CAACmS,QAAQ,IAAI,KAAK;gBAAE;gBAClDlK,MAAM,EAAEwI,YAAY,CAACzQ,OAAO,CAACiI,MAAM,IAAI,IAAI,CAAC;cAC9C,CAAC,CACF,CAAC;cACF,OAAO,CAAC;YACV;YAEA,IAAI2H,WAAW,CAACtH,QAAQ,CAAC,KAAK,CAAC,IAAIsH,WAAW,CAACtH,QAAQ,CAAC,QAAQ,CAAC,EAAE;cACjE,MAAMyJ,QAAQ,GAAG,UAAUnS,IAAI,CAACoI,GAAG,CAAC,CAAC,IAAIqJ,IAAI,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;cACrF5B,YAAY,CAAC,kBAAkB,CAAC,GAAGyB,QAAQ;cAC3CzB,YAAY,CAAC,6BAA6B,CAAC,GAAG1Q,IAAI,CAACoI,GAAG,CAAC,CAAC;;cAExD;cACA3G,eAAe,CAACgE,IAAI,IAAI,CACtB,GAAGA,IAAI,EACP;gBACEhG,EAAE,EAAE0S,QAAQ;gBACZ7S,IAAI,EAAE,IAAI;gBACVgJ,IAAI,EAAE0H,WAAW;gBACjB3E,IAAI,EAAEwF,YAAY,CAACzQ,OAAO,CAACiL,IAAI,IAAIgD,SAAS;gBAC5CkE,QAAQ,EAAE1B,YAAY,CAACzQ,OAAO,CAACmS,QAAQ,IAAI,KAAK;gBAAE;gBAClDlK,MAAM,EAAEwI,YAAY,CAACzQ,OAAO,CAACiI,MAAM,IAAI;cACzC,CAAC,CACF,CAAC;cACF,OAAO,CAAC;YACV;YAEA,MAAMmK,oBAAoB,GAAG,yBAAyB;YACtD,MAAMR,KAAK,GAAGhC,WAAW,CAACgC,KAAK,CAACQ,oBAAoB,CAAC;YACrD,IAAIR,KAAK,EAAE;cACT,MAAMC,QAAQ,GAAGD,KAAK,CAAC,CAAC,CAAC;cACzB,MAAMG,QAAQ,GAAG,UAAUnS,IAAI,CAACoI,GAAG,CAAC,CAAC,IAAIqJ,IAAI,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;cACrF5B,YAAY,CAAC,oBAAoBuB,QAAQ,EAAE,CAAC,GAAGE,QAAQ;cACvDzB,YAAY,CAAC,oBAAoBuB,QAAQ,aAAa,CAAC,GAAGjS,IAAI,CAACoI,GAAG,CAAC,CAAC;;cAEpE;cACA3G,eAAe,CAACgE,IAAI,IAAI,CACtB,GAAGA,IAAI,EACP;gBACEhG,EAAE,EAAE0S,QAAQ;gBACZ7S,IAAI,EAAE,IAAI;gBACVgJ,IAAI,EAAE0H,WAAW;gBACjB3E,IAAI,EAAEwF,YAAY,CAACzQ,OAAO,CAACiL,IAAI,IAAIgD,SAAS;gBAC5CkE,QAAQ,EAAE1B,YAAY,CAACzQ,OAAO,CAACmS,QAAQ,IAAI,KAAK;gBAAE;gBAClDlK,MAAM,EAAEwI,YAAY,CAACzQ,OAAO,CAACiI,MAAM,IAAI;cACzC,CAAC,CACF,CAAC;cACF,OAAO,CAAC;YACV;;YAEA;YACA,MAAMoK,UAAU,GAAGvB,aAAa,GAAGC,kBAAkB,GAAG,UAAUnR,IAAI,CAACoI,GAAG,CAAC,CAAC,IAAIqJ,IAAI,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAE5H7Q,eAAe,CAACgE,IAAI,IAAI;cACtB,MAAMiN,qBAAqB,GAAGjN,IAAI,CAACU,SAAS,CAAC+B,GAAG,IAAIA,GAAG,CAACzI,EAAE,KAAKgT,UAAU,CAAC;cAC1E,IAAIC,qBAAqB,KAAK,CAAC,CAAC,EAAE;gBAClC;gBACI,MAAMC,eAAe,GAAG,CAAC,GAAGlN,IAAI,CAAC;gBACjCkN,eAAe,CAACD,qBAAqB,CAAC,GAAG;kBACrC,GAAGC,eAAe,CAACD,qBAAqB,CAAC;kBACzCpK,IAAI,EAAE8I,cAAc,GAAG,GAAGpB,WAAW,IAAIoB,cAAc,EAAE,GAAGpB,WAAW;kBACvE3E,IAAI,EAAEwF,YAAY,CAACzQ,OAAO,CAACiL,IAAI,IAAIgD,SAAS;kBAC5CkE,QAAQ,EAAE1B,YAAY,CAACzQ,OAAO,CAACmS,QAAQ,IAAI,KAAK;kBAAE;kBAClDlK,MAAM,EAAEwI,YAAY,CAACzQ,OAAO,CAACiI,MAAM,IAAI;gBAC3C,CAAC;gBACD,OAAOsK,eAAe;cAC5B,CAAC,MAAM;gBACL;gBACI,OAAO,CACT,GAAGlN,IAAI,EACP;kBACEhG,EAAE,EAAEgT,UAAU;kBACdnT,IAAI,EAAE,IAAI;kBACVgJ,IAAI,EAAE0H,WAAW;kBACjB3E,IAAI,EAAEwF,YAAY,CAACzQ,OAAO,CAACiL,IAAI,IAAIgD,SAAS;kBACpCkE,QAAQ,EAAE1B,YAAY,CAACzQ,OAAO,CAACmS,QAAQ,IAAI,KAAK;kBAAE;kBAClDlK,MAAM,EAAEwI,YAAY,CAACzQ,OAAO,CAACiI,MAAM,IAAI;gBACjD,CAAC,CACE;cACP;YACA,CAAC,CAAC;UACJ;;UAEA;UACA,IAAIwI,YAAY,CAACC,MAAM,KAAK,UAAU,IAAID,YAAY,CAACzQ,OAAO,IAAIyQ,YAAY,CAACzQ,OAAO,CAACkI,IAAI,EAAE;YAC3F,MAAMsK,UAAU,GAAG/B,YAAY,CAACzQ,OAAO,CAACkI,IAAI,CAAC0J,KAAK,CAAC,WAAW,CAAC;YAC/D,IAAIY,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,EAAE;cAC7B,MAAMC,cAAc,GAAGD,UAAU,CAAC,CAAC,CAAC,CAACjF,IAAI,CAAC,CAAC;cAC3C,IAAIkF,cAAc,IAAIzR,SAAS,KAAK,SAASyR,cAAc,EAAE,EAAE;gBAC3D,MAAM9H,QAAQ,GAAG,SAAS8H,cAAc,EAAE;gBAC1CxR,YAAY,CAAC0J,QAAQ,CAAC;cAC1B;YACJ;UACF;;UAEA;UACA;UACA,IAAI8F,YAAY,CAACC,MAAM,KAAK,iBAAiB,EAAE;YAC7C;YACA,IAAID,YAAY,CAACE,QAAQ,IAAIF,YAAY,CAACE,QAAQ,CAAC+B,aAAa,EAAE;cAChE,MAAM/H,QAAQ,GAAG8F,YAAY,CAACE,QAAQ,CAAC+B,aAAa;cACpDtN,OAAO,CAACuB,GAAG,CAAC,2DAA2DgE,QAAQ,EAAE,CAAC;cAClF1J,YAAY,CAAC0J,QAAQ,CAAC;cACtB5J,2BAA2B,CAAC4J,QAAQ,CAAC;YACvC;YACA;YAAA,KACK,IAAI8F,YAAY,CAACzQ,OAAO,IAAIyQ,YAAY,CAACzQ,OAAO,CAACkI,IAAI,EAAE;cAC1D,MAAMsK,UAAU,GAAG/B,YAAY,CAACzQ,OAAO,CAACkI,IAAI,CAAC0J,KAAK,CAAC,YAAY,CAAC;cAChE,IAAIY,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,EAAE;gBAC7B,MAAMC,cAAc,GAAGD,UAAU,CAAC,CAAC,CAAC,CAACjF,IAAI,CAAC,CAAC;gBAC3C,MAAM5C,QAAQ,GAAG,SAAS8H,cAAc,EAAE;gBAC1CrN,OAAO,CAACuB,GAAG,CAAC,0EAA0EgE,QAAQ,EAAE,CAAC;gBACjG1J,YAAY,CAAC0J,QAAQ,CAAC;gBACtB5J,2BAA2B,CAAC4J,QAAQ,CAAC;cACzC;YACF;UACF;;UAEA;UACA,IAAI8F,YAAY,CAACkC,gBAAgB,EAAE;YACjC5P,iBAAiB,CAAC0N,YAAY,CAACkC,gBAAgB,CAAC;UAClD;UAEA,IAAIlC,YAAY,CAACmC,YAAY,IAAInC,YAAY,CAACmC,YAAY,GAAG,CAAC,EAAE;YAC9DrP,kBAAkB,CAACkN,YAAY,CAACmC,YAAY,CAAC;UAC/C;;UAEA;UACA,IAAInC,YAAY,CAACoC,SAAS,EAAE;YAC1B;YACA,IAAIpC,YAAY,CAACoC,SAAS,CAACC,MAAM,KAAK,YAAY,IAC9CrC,YAAY,CAACsC,mBAAmB,KAAK9E,SAAS,EAAE;cAElD;cACA,IAAIwC,YAAY,CAACoC,SAAS,CAACG,GAAG,KAAK,MAAM,EAAE;gBACzC7P,aAAa,CAAC,MAAM,CAAC;gBACrBU,kBAAkB,CAACwB,IAAI,KAAK;kBAC1B,GAAGA,IAAI;kBACP,CAACoL,YAAY,CAACsC,mBAAmB,GAAG,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;cACL,CAAC,MAAM,IAAItC,YAAY,CAACoC,SAAS,CAACG,GAAG,KAAK,SAAS,EAAE;gBACnD7P,aAAa,CAAC,SAAS,CAAC;gBACxBU,kBAAkB,CAACwB,IAAI,KAAK;kBAC1B,GAAGA,IAAI;kBACP,CAACoL,YAAY,CAACsC,mBAAmB,GAAG,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;cACL;YACF;UACF;;UAEA;UACA,IAAItC,YAAY,CAACwC,YAAY,EAAE;YAC7B,MAAMC,QAAQ,GAAG;cACf7T,EAAE,EAAEoR,YAAY,CAACwC,YAAY,CAAC5T,EAAE;cAChCsJ,IAAI,EAAE8H,YAAY,CAACwC,YAAY,CAACtK,IAAI,IAAI8H,YAAY,CAACwC,YAAY,CAACE,YAAY;cAAE;cAChFvK,IAAI,EAAE6H,YAAY,CAACwC,YAAY,CAACrK,IAAI,IAAI6H,YAAY,CAACwC,YAAY,CAACtK,IAAI,IAAI8H,YAAY,CAACwC,YAAY,CAACE,YAAY;cAChHtK,KAAK,EAAE4H,YAAY,CAACwC,YAAY,CAACnK,YAAY,KAAKmF,SAAS,GAAGwC,YAAY,CAACwC,YAAY,CAACnK,YAAY,GAAG,CAAC,GAAG,CAAC;cAAE;cAC9G3J,KAAK,EAAE,OAAOsR,YAAY,CAACwC,YAAY,CAACnK,YAAY;YACtD,CAAC;;YAED;YACAvH,gBAAgB,CAAC6K,UAAU,IAAI;cAC7B,MAAMgH,aAAa,GAAGhH,UAAU,CAACrG,SAAS,CAAC2C,CAAC,IAAIA,CAAC,CAACrJ,EAAE,KAAK6T,QAAQ,CAAC7T,EAAE,IAAIqJ,CAAC,CAACG,KAAK,KAAKqK,QAAQ,CAACrK,KAAK,CAAC;cACnG,IAAIwK,aAAa;cAEjB,IAAID,aAAa,IAAI,CAAC,EAAE;gBACtB;gBACAC,aAAa,GAAG,CAAC,GAAGjH,UAAU,CAAC;gBAC/BiH,aAAa,CAACD,aAAa,CAAC,GAAGF,QAAQ;cACzC,CAAC,MAAM;gBACL;gBACAG,aAAa,GAAG,CAAC,GAAGjH,UAAU,EAAE8G,QAAQ,CAAC;cAC3C;;cAEA;cACAG,aAAa,CAACrJ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACpB,KAAK,GAAGqB,CAAC,CAACrB,KAAK,CAAC;cAC/C,OAAOwK,aAAa;YACtB,CAAC,CAAC;;YAEF;YACA,IAAI5C,YAAY,CAACsC,mBAAmB,KAAK9E,SAAS,EAAE;cAClDxM,oBAAoB,CAACgP,YAAY,CAACsC,mBAAmB,CAAC;YACxD;;YAEA;YACA,IAAItC,YAAY,CAAC6C,YAAY,EAAE;cAC7BrQ,YAAY,CAACwN,YAAY,CAAC6C,YAAY,CAAC;YACzC;UACF;;UAEA;UACA,IAAI7C,YAAY,CAACC,MAAM,KAAK,WAAW,EAAE;YACvC;YACA,MAAM6C,eAAe,GAAG,UAAU3T,IAAI,CAACoI,GAAG,CAAC,CAAC,IAAIqJ,IAAI,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5F7Q,eAAe,CAACgE,IAAI,IAAI,CACtB,GAAGA,IAAI,EACP;cACEhG,EAAE,EAAEkU,eAAe;cACnBrU,IAAI,EAAE,IAAI;cACVgJ,IAAI,EAAE,iBAAkBuI,YAAY,CAACmC,YAAY,IAAItR,aAAa,CAACyD,MAAM,uBAAwB;cACjGkG,IAAI,EAAE,GAAG;cACTkH,QAAQ,EAAE,KAAK;cAAE;cACjBlK,MAAM,EAAE,IAAI;cAAE;cACdrC,UAAU,EAAE6K,YAAY,CAAC7K,UAAU,IAAIlG,gBAAgB,CAAC;YAC1D,CAAC,CACF,CAAC;YAEF2C,gBAAgB,CAAC,IAAI,CAAC;;YAEtB;YACA,IAAI,CAACvB,wBAAwB,IAAIA,wBAAwB,KAAK,OAAO,EAAE;cACrEC,2BAA2B,CAAC6O,WAAW,CAAC7K,MAAM,GAAG,EAAE,GAAG6K,WAAW,CAACsC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGtC,WAAW,CAAC;YAC3G;;YAEA;YACAtK,wBAAwB,CAAC,CAAC;;YAE1B;YACAkO,qBAAqB,CAAC,CAAC;UACzB;QACF,CAAC;QACD;QACCrO,KAAK,IAAK;UACTC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;UAE5C;UACA,IAAI3C,cAAc,CAAC8D,OAAO,IAAI,OAAO9D,cAAc,CAAC8D,OAAO,CAAC0E,KAAK,KAAK,UAAU,EAAE;YAChFxI,cAAc,CAAC8D,OAAO,CAAC0E,KAAK,CAAC,CAAC;UAChC;;UAEA;UACA,IAAI,CAAC7F,KAAK,CAACnF,OAAO,KAAK,uBAAuB,IAAImF,KAAK,CAACnF,OAAO,CAACsI,QAAQ,CAAC,mBAAmB,CAAC,KAAK4H,aAAa,GAAGC,mBAAmB,EAAE;YACrID,aAAa,EAAE;YACf7O,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;cAChChG,EAAE,EAAE,aAAaO,IAAI,CAACoI,GAAG,CAAC,CAAC,EAAE;cAC7B9I,IAAI,EAAE,aAAa;cACnBgJ,IAAI,EAAE,qBAAqBgI,aAAa,IAAIC,mBAAmB,SAASI,kBAAkB,IAAI,KAAK,EAAE;cACrGtI,MAAM,EAAE,QAAQ;cAChBgD,IAAI,EAAE;YACR,CAAC,CAAC,CAAC;;YAEH;YACAwI,UAAU,CAAC,MAAMrD,iBAAiB,CAAC1Q,gBAAgB,CAAC,EAAE,IAAI,GAAGwQ,aAAa,CAAC;UAC7E,CAAC,MAAM;YACL;YACA/O,eAAe,CAAC,KAAK,CAAC;YACtBE,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;cAChChG,EAAE,EAAE,SAASO,IAAI,CAACoI,GAAG,CAAC,CAAC,EAAE;cACzB9I,IAAI,EAAE,UAAU;cAChBgJ,IAAI,EAAE,YAAY/C,KAAK,CAACnF,OAAO,EAAE;cACjCiI,MAAM,EAAE,QAAQ;cAChBgD,IAAI,EAAE;YACR,CAAC,CAAC,CAAC;UACL;QACF,CAAC;QACD;QACA,MAAM;UACJ7F,OAAO,CAACuB,GAAG,CAAC,mBAAmB,CAAC;UAChCxF,eAAe,CAAC,KAAK,CAAC;UACtB;UACAmE,wBAAwB,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QACD;QACAiL,kBAAkB,GAAG;UAAE3K,UAAU,EAAE2K;QAAmB,CAAC,GAAG,CAAC,CAC7D,CAAC;MACH,CAAC,CAAC,OAAOmD,QAAQ,EAAE;QACjBtO,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEuO,QAAQ,CAAC;QAC9CvS,eAAe,CAAC,KAAK,CAAC;QACtBE,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAChChG,EAAE,EAAE,aAAaO,IAAI,CAACoI,GAAG,CAAC,CAAC,EAAE;UAC7B9I,IAAI,EAAE,UAAU;UAChBgJ,IAAI,EAAE,WAAWwL,QAAQ,CAAC1T,OAAO,EAAE;UACnCiI,MAAM,EAAE,QAAQ;UAChBgD,IAAI,EAAE;QACR,CAAC,CAAC,CAAC;MACL;IACF,CAAC;;IAED;IACA;IACAmF,iBAAiB,CAACJ,iBAAiB,GAAGtQ,gBAAgB,GAAG,IAAI,CAAC;EAChE,CAAC;;EAED;EACA,MAAM8T,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAM/M,aAAa,GAAG,MAAMxJ,UAAU,CAACyJ,kBAAkB,CAAC,CAAC;MAC3DtB,OAAO,CAACuB,GAAG,CAAC,iDAAiD,EAAEF,aAAa,CAAC;;MAE7E;MACA,MAAMkN,sBAAsB,GAAGlN,aAAa,CAAC7H,GAAG,CAACiI,IAAI,KAAK;QACxDxH,EAAE,EAAEwH,IAAI,CAACxH,EAAE;QAAE;QACbF,KAAK,EAAE0H,IAAI,CAAC1H,KAAK,IAAI,OAAO0H,IAAI,CAACxH,EAAE,GAAGwH,IAAI,CAACxH,EAAE,CAACmG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG;QAAE;QACnE3F,SAAS,EAAEgH,IAAI,CAACC,aAAa,IAAID,IAAI,CAACE,UAAU;QAChDnB,UAAU,EAAEiB,IAAI,CAACxH,EAAE;QAAE;QACrBoG,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE,EAAE;QACVsB,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;;MAEH;MACAzE,cAAc,CAAC8C,IAAI,IAAI;QACrB;QACA,MAAM4B,gBAAgB,GAAG,IAAIC,GAAG,CAACyM,sBAAsB,CAAC/U,GAAG,CAACe,IAAI,IAAIA,IAAI,CAACiG,UAAU,CAAC,CAAC,CAAC,CAAC;;QAEvF;QACA,MAAMgO,iBAAiB,GAAGvO,IAAI,CAACwG,MAAM,CAAClM,IAAI,IAAI,CAACsH,gBAAgB,CAACK,GAAG,CAAC3H,IAAI,CAACiG,UAAU,CAAC,CAAC,CAAC,CAAC;;QAEvF;QACA,MAAMiO,QAAQ,GAAG,CAAC,GAAGF,sBAAsB,EAAE,GAAGC,iBAAiB,CAAC,CAC/D5J,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAItK,IAAI,CAACsK,CAAC,CAACrK,SAAS,CAAC,GAAG,IAAID,IAAI,CAACqK,CAAC,CAACpK,SAAS,CAAC,CAAC;;QAEhE;QACA,IAAI;UACFoG,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACyN,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,OAAO1O,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QAC3C;QAEA,OAAO0O,QAAQ;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO1O,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;EAED,MAAM2O,uBAAuB,GAAI3V,OAAO,IAAK;IAC3C4D,uBAAuB,CAAC5D,OAAO,CAAC;IAChC0D,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;;EAED;EACA;AACF;AACA;AACA;AACA;AACA;AACA;;EAEE;;EAEA;EACA9F,SAAS,CAAC,MAAM;IACd,IAAIwE,QAAQ,CAACmN,KAAK,IAAInN,QAAQ,CAACmN,KAAK,CAACoF,MAAM,KAAK,uBAAuB,EAAE;MACvE,MAAM;QAAEpN,MAAM,EAAEqO,YAAY;QAAEpG,YAAY;QAAEC,iBAAiB,EAAEoG;MAAgB,CAAC,GAAGzT,QAAQ,CAACmN,KAAK;;MAEjG;MACA;MACA,IAAIqG,YAAY,IAAIA,YAAY,CAAChP,MAAM,GAAG,CAAC,EAAE;QAC1CxD,gBAAgB,CAACwS,YAAY,CAAC,CAAC,CAAC;MACnC;MACA,IAAIC,eAAe,EAAE;QAClBjT,2BAA2B,CAACiT,eAAe,CAAC;MAC/C;MAEA7R,8BAA8B,CAACwL,YAAY,IAAI,CAAC,CAAC;MACjDnJ,gBAAgB,CAAC,CAAC,CAAC,CAAC;;MAEpB;MACAlE,QAAQ,CAACC,QAAQ,CAAC0T,QAAQ,EAAE;QAAE1L,OAAO,EAAE,IAAI;QAAEmF,KAAK,EAAE,CAAC;MAAE,CAAC,CAAC;IAC3D;EACF,CAAC,EAAE,CAACnN,QAAQ,CAACmN,KAAK,EAAEpN,QAAQ,EAAEkE,gBAAgB,EAAEjE,QAAQ,CAAC0T,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAErE;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMC,cAAc,GAAG,CAAC/Q,iBAAiB;IACzCC,oBAAoB,CAAC8Q,cAAc,CAAC;IACpC,IAAIA,cAAc,EAAE;MAAE;MACpBX,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;EAED,MAAMY,wBAAwB,GAAGA,CAAA,KAAM;IACrC/Q,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMgR,uBAAuB,GAAIlJ,WAAW,IAAK;IAC/CD,mBAAmB,CAACC,WAAW,CAAC,CAAC,CAAC;IAClCiJ,wBAAwB,CAAC,CAAC;EAC5B,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B3Q,iBAAiB,CAAC,IAAI,CAAC;IACvBF,yBAAyB,CAAC,KAAK,CAAC;EAClC,CAAC;EAED,oBACEtG,OAAA;IAAKkB,SAAS,EAAC,iCAAiC;IAAAC,QAAA,EAC7CkC,SAAS,gBACRrD,OAAA;MAAKkB,SAAS,EAAC,kDAAkD;MAAAC,QAAA,gBAC/DnB,OAAA;QAAKoX,GAAG,EAAC,sBAAsB;QAACC,GAAG,EAAC,SAAS;QAACnW,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3EvB,OAAA;QAAKkB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnB,OAAA;UAAKkB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9EvB,OAAA;UAAKkB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENvB,OAAA,CAAAE,SAAA;MAAAiB,QAAA,gBACEnB,OAAA;QAAKkB,SAAS,EAAC;MAA0D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChFvB,OAAA;QAAKkB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CnB,OAAA,CAACV,OAAO;UACNmE,cAAc,EAAEA,cAAe;UAC/B6T,gBAAgB,EAAEhK,cAAe;UACjCiK,YAAY,EAAEpS,WAAY;UAC1B5C,gBAAgB,EAAEA,gBAAiB;UACnCiV,mBAAmB,EAAEN,uBAAwB;UAC7CO,mBAAmB,EAAExJ,mBAAoB;UACzC1K,gBAAgB,EAAEA;QAAiB;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACFvB,OAAA,CAACT,UAAU;UACTyC,KAAK,EAAE6B,SAAS,IAAI,QAAS;UAC7B6T,aAAa,EAAEnK,qBAAsB;UACrCxJ,YAAY,EAAEA,YAAa;UAC3BuE,QAAQ,EAAErE,YAAa;UACvB0T,aAAa,EAAEpF,iBAAkB;UACjCjN,OAAO,EAAEA,OAAQ;UACjBqR,uBAAuB,EAAEA,uBAAwB,CAAC;;UAElD;UAAA;UACAiB,iBAAiB,EAAE,IAAK;UACxBN,gBAAgB,EAAEhK,cAAe;UACjCuK,eAAe,EAAEd,yBAA0B;UAC3Ce,iBAAiB,EAAE5O;QAAsB;UAAA9H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAEFvB,OAAA,CAACR,SAAS;UACR+I,MAAM,EAAEpE,aAAc;UACtBE,iBAAiB,EAAEA,iBAAkB;UACrC0T,kBAAkB,EAAE7F,0BAA2B;UAC/C8F,gBAAgB,EAAE7F,oBAAqB;UACvC8F,WAAW,EAAE3F,sBAAuB;UACpC4F,WAAW,EAAEvJ,qBAAsB;UACnC1J,aAAa,EAAEA,aAAc;UAC7BwB,eAAe,EAAEA,eAAgB;UACjC0R,kBAAkB,EAAEpU,YAAa;UACjCwB,KAAK,EAAEA,KAAM;UACbE,WAAW,EAAEA,WAAY;UACzBE,cAAc,EAAEA,cAAe;UAC/BE,SAAS,EAAEA,SAAU;UACrBE,UAAU,EAAEA,UAAW;UACvBhB,2BAA2B,EAAEA,2BAA4B;UACzDqT,YAAY,EAAEA,CAAA,KAAM;YAClBzK,KAAK,CAAC,SAAS,CAAC;UAClB,CAAE;UACF0K,cAAc,EAAEtI,kBAAmB;UACnChJ,SAAS,EAAEA,SAAU;UACrBE,eAAe,EAAEA,eAAgB;UACjCqR,gBAAgB,EAAEpI,oBAAqB;UACvCqI,YAAY,EAAEpI,gBAAiB;UAC/BqI,YAAY,EAAElI;QAAwB;UAAAlP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLkD,sBAAsB,iBACrBzE,OAAA,CAACe,kBAAkB;QACjBC,OAAO,EAAE2D,oBAAqB;QAC9B1D,OAAO,EAAEA,CAAA,KAAMyD,yBAAyB,CAAC,KAAK;MAAE;QAAAtD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CACF,EAGA0E,iBAAiB,iBAChBjG,OAAA,CAACoC,aAAa;QACZC,OAAO,EAAE8C,WAAY;QACrBlE,OAAO,EAAEgW,wBAAyB;QAClC3U,YAAY,EAAE4U,uBAAwB;QACtC3U,gBAAgB,EAAEA;MAAiB;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACF,EAGA8E,sBAAsB,IAAIE,cAAc,iBACvCvG,OAAA,CAAC4C,kBAAkB;QACjBZ,KAAK,EAAE,MAAMuE,cAAc,CAACvE,KAAK,IAAI,IAAI,EAAG;QAC5Ca,OAAO,EAAE,qBAAsB;QAC/BC,SAAS,EAAEuL,mBAAoB;QAC/BtL,QAAQ,EAAEoU;MAAmB;QAAA/V,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CACF;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC2B,EAAA,CAt0CID,OAAO;EAAA,QACM7D,WAAW,EACXC,WAAW;AAAA;AAAAoZ,IAAA,GAFxBxV,OAAO;AAw0Cb,SAASyV,GAAGA,CAAA,EAAG;EACb,oBACE1Y,OAAA,CAACf,aAAa;IAAAkC,QAAA,eACZnB,OAAA,CAACd,MAAM;MAAAiC,QAAA,gBACLnB,OAAA,CAACb,KAAK;QAACwZ,IAAI,EAAC,GAAG;QAACC,OAAO,eAAE5Y,OAAA,CAACiD,OAAO;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxCvB,OAAA,CAACb,KAAK;QAACwZ,IAAI,EAAC,SAAS;QAACC,OAAO,eAC3B5Y,OAAA,CAAChB,QAAQ;UAAC6Z,QAAQ,eAAE7Y,OAAA;YAAKkB,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAE;UAAAJ,QAAA,eAC7FnB,OAAA,CAACM,eAAe;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACJvB,OAAA,CAACb,KAAK;QAACwZ,IAAI,EAAC,OAAO;QAACC,OAAO,eACzB5Y,OAAA,CAAChB,QAAQ;UAAC6Z,QAAQ,eAAE7Y,OAAA;YAAKkB,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAE;UAAAJ,QAAA,eAC7FnB,OAAA,CAACY,YAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACuX,IAAA,GAlBQJ,GAAG;AAoBZ,eAAeA,GAAG;AAAC,IAAAtY,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAqB,GAAA,EAAAQ,GAAA,EAAAK,GAAA,EAAAyV,IAAA,EAAAK,IAAA;AAAAC,YAAA,CAAA3Y,EAAA;AAAA2Y,YAAA,CAAA1Y,GAAA;AAAA0Y,YAAA,CAAAxY,GAAA;AAAAwY,YAAA,CAAAvY,GAAA;AAAAuY,YAAA,CAAArY,GAAA;AAAAqY,YAAA,CAAApY,GAAA;AAAAoY,YAAA,CAAAlY,GAAA;AAAAkY,YAAA,CAAAjY,GAAA;AAAAiY,YAAA,CAAA5W,GAAA;AAAA4W,YAAA,CAAApW,GAAA;AAAAoW,YAAA,CAAA/V,GAAA;AAAA+V,YAAA,CAAAN,IAAA;AAAAM,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}