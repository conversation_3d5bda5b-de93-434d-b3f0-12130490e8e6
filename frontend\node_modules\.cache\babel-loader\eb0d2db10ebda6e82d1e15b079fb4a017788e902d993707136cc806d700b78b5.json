{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\n// frontend/src/App.js\nimport React, { useState, useEffect, useCallback, useRef, lazy, Suspense } from 'react';\nimport { BrowserRouter, Routes, Route, useNavigate, useLocation } from 'react-router-dom';\nimport LeftNav from './components/LeftNav';\nimport MiddlePane from './components/MiddlePane';\nimport RightPane from './components/RightPane';\nimport { FaFilePdf, FaGlobe, FaTrash, FaFeatherAlt } from 'react-icons/fa';\nimport formatDate from './utils/formatDate';\nimport apiService from './services/api';\n\n// Lazy load components that are not needed for initial render\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InlineTextEditor = /*#__PURE__*/lazy(_c = () => import('./components/InlineTextEditor'));\n_c2 = InlineTextEditor;\nconst SlidePlayerView = /*#__PURE__*/lazy(_c3 = () => import('./views/SlidePlayerView'));\n_c4 = SlidePlayerView;\nconst FullScreenPlayer = /*#__PURE__*/lazy(_c5 = () => import('./components/FullScreenPlayer'));\n_c6 = FullScreenPlayer;\nconst ChatViewPage = /*#__PURE__*/lazy(_c7 = () => import('./views/ChatViewPage'));\n\n// MainApp component that contains all the existing functionality\n_c8 = ChatViewPage;\nconst MainApp = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation(); // 获取 location 对象\n  const [isLoading, setIsLoading] = useState(true); // 主加载状态（整体应用）\n  const [isLoadingHistory, setIsLoadingHistory] = useState(false); // 项目历史加载状态\n  const [isTaskListOpen, setIsTaskListOpen] = useState(false);\n  const [currentPresentationTitle, setCurrentPresentationTitle] = useState(\"AI 幻灯片\");\n  const [chatTitle, setChatTitle] = useState(\"AI 幻灯片\");\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [chatMessages, setChatMessages] = useState([]);\n  const [currentSlides, setCurrentSlides] = useState([]);\n  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);\n  const [editingTextElement, setEditingTextElement] = useState(null);\n  const [showSearchResultsModal, setShowSearchResultsModal] = useState(false);\n  const [currentSearchResults, setCurrentSearchResults] = useState([]);\n  const [isFullScreen, setIsFullScreen] = useState(false);\n  const [currentFullScreenSlideIndex, setCurrentFullScreenSlideIndex] = useState(0);\n  const [isEditingMode, setIsEditingMode] = useState(false); // 这个是App层面的编辑模式（例如NLP命令，或是否启用InlineTextEditor）\n  const [chatHistory, setChatHistory] = useState([]); // 存储历史聊天记录摘要\n  const eventSourceRef = useRef(null);\n\n  // 新增状态\n  const [tools, setTools] = useState([]);\n  const [currentStep, setCurrentStep] = useState(null);\n  const [outlineContent, setOutlineContent] = useState('');\n  const [slideCode, setSlideCode] = useState('');\n  const [activeStep, setActiveStep] = useState('preview');\n  const [showHistoryDrawer, setShowHistoryDrawer] = useState(false);\n  const [totalSlideCount, setTotalSlideCount] = useState(0);\n  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);\n  const [deleteItemInfo, setDeleteItemInfo] = useState(null);\n  const [slideTabIndices, setSlideTabIndices] = useState({});\n  const [currentProjectId, setCurrentProjectId] = useState(null);\n  const isNavigatingToNew = useRef(false);\n  const [contextMenu, setContextMenu] = useState(null);\n  const [editModal, setEditModal] = useState(null);\n  const [editInstruction, setEditInstruction] = useState('');\n  const [isEditingElement, setIsEditingElement] = useState(false); // 防止重复提交\n\n  // 用于播放器全屏的切换逻辑\n  const toggleFullScreen = useCallback(async () => {\n    // 检查当前是否处于浏览器全屏模式\n    const isInFullScreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement);\n    try {\n      if (!isInFullScreen) {\n        // 如果当前不处于全屏，则请求进入全屏\n        // 准备进入全屏时，设置好初始索引\n        if (currentSlides.length > 0) {\n          setCurrentFullScreenSlideIndex(currentSlideIndex);\n        }\n        // 调用浏览器原生 API\n        await document.documentElement.requestFullscreen();\n        // isFullScreen 状态将由下面的 'fullscreenchange' 事件监听器更新\n      } else {\n        // 如果当前已处于全屏，则退出全屏\n        await document.exitFullscreen();\n        // isFullScreen 状态将由下面的 'fullscreenchange' 事件监听器更新\n      }\n    } catch (error) {\n      console.error(\"全屏操作失败:\", error);\n      // 如果原生API失败，作为备用方案，仍然显示我们的模拟全屏组件\n      setIsFullScreen(prev => !prev);\n    }\n  }, [currentSlideIndex, currentSlides.length]);\n\n  // 定义 saveCurrentChatToHistory 函数，确保在适当的时机调用\n  const saveCurrentChatToHistory = useCallback(() => {\n    // 仅当有实质内容时才保存到历史列表\n    if (chatMessages.length > 1 && currentProjectId) {\n      // 确保有 project_id\n      const currentSession = {\n        id: currentProjectId,\n        // 使用 project_id 作为历史记录的唯一 ID\n        title: chatTitle || `演示 (${currentProjectId.slice(-4)})`,\n        // 确保有标题\n        messages: chatMessages,\n        slides: currentSlides,\n        timestamp: new Date().toISOString(),\n        project_id: currentProjectId\n      };\n      setChatHistory(prevHistory => {\n        const existingSessionIndex = prevHistory.findIndex(item => item.id === currentProjectId);\n        let updatedHistory;\n        if (existingSessionIndex >= 0) {\n          updatedHistory = [...prevHistory];\n          updatedHistory[existingSessionIndex] = currentSession; // 更新现有会话\n        } else {\n          updatedHistory = [currentSession, ...prevHistory]; // 添加新会话\n        }\n        // 限制历史记录数量（例如最近20条）\n        // updatedHistory = updatedHistory.slice(0, 20); \n        try {\n          localStorage.setItem('chatHistory', JSON.stringify(updatedHistory));\n        } catch (error) {\n          console.error(\"保存聊天历史列表到 localStorage 失败:\", error);\n        }\n        return updatedHistory;\n      });\n    }\n  }, [chatMessages, currentSlides, chatTitle, currentProjectId, setChatHistory]); // 添加 currentProjectId 依赖\n\n  // 新建演示文稿的函数\n  const handleNewPresentation = useCallback(() => {\n    isNavigatingToNew.current = true; // 在导航前设置标志\n\n    // 清空当前状态\n    setChatMessages([]);\n    setCurrentSlides([]);\n    setChatTitle(\"AI 幻灯片\");\n    setCurrentPresentationTitle(\"AI 幻灯片\");\n    setIsGenerating(false);\n    setCurrentStep(null);\n    setTools([]);\n    setOutlineContent('');\n    setSlideCode('');\n    setActiveStep('preview');\n\n    // 清除项目ID\n    setCurrentProjectId(null);\n    localStorage.removeItem('currentProjectId');\n\n    // 导航到根路径，清除URL中的projectId\n    navigate('/');\n  }, [setChatMessages, setCurrentSlides, setChatTitle, setCurrentPresentationTitle, setIsGenerating, setCurrentStep, setTools, setOutlineContent, setSlideCode, setActiveStep, setCurrentProjectId, navigate]);\n\n  // 专门用于加载历史列表的函数\n  const loadProjectHistory = useCallback(async () => {\n    setIsLoadingHistory(true);\n    try {\n      const serverHistory = await apiService.getProjectsHistory();\n      console.log(\"[App.js] Fetched project history summaries:\", serverHistory);\n      const formattedSummaries = serverHistory.map(proj => ({\n        id: proj.id,\n        // 修复：使用后端返回的 'id' 字段\n        title: proj.title || `项目 (${proj.id ? proj.id.slice(-4) : '未知'})`,\n        // 修复：使用 'id' 并增加健壮性检查\n        timestamp: proj.last_modified || proj.created_at,\n        project_id: proj.id,\n        // 修复：使用 'id' 字段来填充 project_id\n        isSummary: true // 标记为摘要\n      }));\n      setChatHistory(formattedSummaries);\n\n      // 清理无效的 localStorage.currentProjectId\n      const serverProjectIds = new Set(serverHistory.map(p => p.project_id));\n      const storedId = localStorage.getItem('currentProjectId');\n      if (storedId && !serverProjectIds.has(storedId)) {\n        localStorage.removeItem('currentProjectId');\n      }\n    } catch (error) {\n      console.error(\"加载项目历史失败:\", error);\n      setChatHistory([]); // 出错时清空\n    } finally {\n      setIsLoadingHistory(false);\n    }\n  }, [setChatHistory, setIsLoadingHistory]);\n\n  // loadProjectDetails 函数现在是加载单个项目详情的唯一入口\n  const loadProjectDetails = useCallback(async projectId => {\n    // 增加对无效projectId的严格检查，防止API调用失败\n    if (!projectId || projectId === 'undefined') {\n      console.warn(\"尝试加载一个无效的项目ID，已中止。\", projectId);\n      setIsLoading(false); // 确保结束加载状态\n      handleNewPresentation(); // 重置到一个安全的新建页面\n      return;\n    }\n    setIsLoading(true);\n    try {\n      const details = await apiService.getProjectDetails(projectId);\n      console.log(`[App.js] Fetched details for project ${projectId}:`, details);\n\n      // 确保聊天历史有数据，必要时进行格式化处理\n      let chatHistoryData = details.chat_history || [];\n\n      // 确保每条消息都有正确的格式和ID\n      if (chatHistoryData.length > 0) {\n        chatHistoryData = chatHistoryData.map((msg, index) => ({\n          ...msg,\n          id: msg.id || `msg-${index}-${Date.now()}`,\n          sender: msg.sender || (index % 2 === 0 ? 'user' : 'ai'),\n          text: msg.text || msg.content || '无内容',\n          timestamp: msg.timestamp || new Date().toISOString()\n        }));\n        console.log(`[App.js] 处理后的聊天历史数据:`, chatHistoryData);\n      } else {\n        // 如果没有聊天历史，可以添加一个系统消息\n        chatHistoryData = [{\n          id: `system-${Date.now()}`,\n          sender: 'system',\n          text: '项目已加载，但没有聊天记录。',\n          timestamp: new Date().toISOString()\n        }];\n      }\n      setChatMessages(chatHistoryData);\n      const displayTitle = details.title || `项目 (${projectId.slice(-4)})`;\n      setChatTitle(displayTitle);\n      setCurrentPresentationTitle(displayTitle);\n      setCurrentProjectId(details.project_id);\n\n      // 保存到localStorage以便在刷新后恢复\n      localStorage.setItem('currentProjectId', details.project_id);\n\n      // 确保URL与当前加载的项目ID同步\n      if (!location.search.includes(`projectId=${details.project_id}`)) {\n        navigate(`/?projectId=${details.project_id}`, {\n          replace: true\n        });\n      }\n      setTotalSlideCount(details.total_slides_planned || (details.slides || []).length);\n\n      // 懒加载幻灯片内容\n      if (details.slides && details.slides.length > 0) {\n        const slidesWithMetadata = details.slides.map(s => ({\n          id: s.id,\n          html: '',\n          code: '',\n          order: s.slide_number - 1,\n          title: `幻灯片 ${s.slide_number}`,\n          isLoading: true\n        }));\n        setCurrentSlides(slidesWithMetadata);\n        const slideContentPromises = details.slides.map(slideMeta => apiService.getSlideContent(slideMeta.id).catch(() => ({\n          id: slideMeta.id,\n          error: true\n        })));\n        const fullSlidesData = await Promise.all(slideContentPromises);\n        setCurrentSlides(currentSlides => currentSlides.map(metaSlide => {\n          const fullData = fullSlidesData.find(fs => fs.id === metaSlide.id);\n          if (fullData && !fullData.error) {\n            return {\n              ...metaSlide,\n              html: fullData.html,\n              code: fullData.html,\n              isLoading: false\n            };\n          }\n          return {\n            ...metaSlide,\n            html: '<div>内容加载失败</div>',\n            code: '/* 内容加载失败 */',\n            isLoading: false\n          };\n        }));\n      } else {\n        setCurrentSlides([]);\n      }\n    } catch (error) {\n      console.error(`加载项目 ${projectId} 详情失败:`, error);\n      if (error.message && (error.message.includes('not found') || error.message.includes('permission'))) {\n        // 如果项目不存在或无权限，从localStorage移除该项目ID\n        if (localStorage.getItem('currentProjectId') === projectId) {\n          localStorage.removeItem('currentProjectId');\n        }\n        handleNewPresentation();\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  }, [setIsLoading, handleNewPresentation, setChatMessages, setChatTitle, setCurrentPresentationTitle, setCurrentProjectId, location.search, navigate, setTotalSlideCount, setCurrentSlides]);\n\n  // 监听URL参数变化，当参数改变时加载对应项目\n  useEffect(() => {\n    const params = new URLSearchParams(location.search);\n    const projectIdFromUrl = params.get('projectId');\n    const chatIdFromUrl = params.get('chatId');\n\n    // 获取URL中的项目ID\n    const idToLoad = projectIdFromUrl || chatIdFromUrl;\n    if (idToLoad && idToLoad !== currentProjectId) {\n      console.log(`[URL变化] 加载项目: ${idToLoad}`);\n      loadProjectDetails(idToLoad);\n    }\n  }, [location.search, currentProjectId, loadProjectDetails]);\n\n  // 初始加载和恢复会话的 useEffect\n  useEffect(() => {\n    const initializeApp = async () => {\n      setIsLoading(true);\n\n      // 1. 总是先加载历史项目列表\n      await loadProjectHistory();\n\n      // 2. 决定要加载哪个项目的详情\n      const params = new URLSearchParams(location.search);\n      let projectIdToLoad = params.get('projectId');\n\n      // 如果URL没有ID，尝试从localStorage获取\n      if (!projectIdToLoad || projectIdToLoad === 'undefined') {\n        projectIdToLoad = localStorage.getItem('currentProjectId');\n      }\n      if (projectIdToLoad && projectIdToLoad !== 'undefined') {\n        // 如果有项目ID（来自URL或localStorage），加载它\n        console.log(`[App Init] Resuming session for project: ${projectIdToLoad}`);\n        await loadProjectDetails(projectIdToLoad);\n      } else {\n        // 如果我们是因为点击\"新建\"而导航到这里的，\n        // 只需要重置标志位，然后停止执行。\n        if (isNavigatingToNew.current) {\n          console.log(\"[App Init] Navigating to a new presentation. Skipping history load.\");\n          isNavigatingToNew.current = false;\n          setIsLoading(false); // 确保结束加载状态\n          return; // 终止此 effect 的后续执行\n        }\n\n        // 否则，加载最新的项目（如果存在）\n        const history = await apiService.getProjectsHistory(); // 再次获取以确保最新\n        if (history && history.length > 0) {\n          const latestProject = history.sort((a, b) => new Date(b.last_modified) - new Date(a.last_modified))[0];\n          // 确保在导航前，最新的项目和其ID是有效的\n          if (latestProject && latestProject.project_id) {\n            console.log(`[App Init] Loading most recent project: ${latestProject.project_id}`);\n            await loadProjectDetails(latestProject.project_id);\n          } else {\n            // 如果最新项目无效，则新建一个\n            console.log(\"[App Init] No valid recent project found. Creating new presentation.\");\n            handleNewPresentation();\n          }\n        } else {\n          // 没有历史记录，创建一个新演示\n          console.log(\"[App Init] No project history found. Creating new presentation.\");\n          handleNewPresentation();\n        }\n      }\n      setIsLoading(false);\n    };\n    initializeApp();\n  }, [location.search, loadProjectHistory, loadProjectDetails, handleNewPresentation, setIsLoading]);\n\n  // 自动保存当前会话到历史记录 - 禁用连续更新，只保留页面离开时的保存\n  useEffect(() => {\n    // 这个效果不再每次状态变化就执行，现在完全依赖beforeunload事件\n    console.log(\"Session management setup\");\n\n    // 页面即将卸载时保存会话\n    const handleBeforeUnload = () => {\n      // 只保存当前项目ID，不再保存完整的chatHistory\n      if (currentProjectId) {\n        localStorage.setItem('currentProjectId', currentProjectId);\n      }\n    };\n\n    // 监听页面即将离开事件\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, [currentProjectId]); // 只依赖currentProjectId\n\n  // 仅在特定状态变化时保存到localStorage，但不更新chatHistory状态\n  useEffect(() => {\n    // 只有当有足够的内容时才保存到localStorage\n    if (chatMessages.length > 1 && currentSlides.length > 0) {\n      try {\n        var _chatMessages$find;\n        localStorage.setItem('chatMessages', JSON.stringify(chatMessages));\n        localStorage.setItem('currentSlides', JSON.stringify(currentSlides));\n        localStorage.setItem('chatTitle', chatTitle);\n        localStorage.setItem('presentationTitle', currentPresentationTitle);\n\n        // 更新currentProjectId (如果消息中有)\n        const projectIdFromMessages = (_chatMessages$find = chatMessages.find(msg => msg.project_id)) === null || _chatMessages$find === void 0 ? void 0 : _chatMessages$find.project_id;\n        if (projectIdFromMessages) {\n          setCurrentProjectId(projectIdFromMessages);\n          localStorage.setItem('currentProjectId', projectIdFromMessages);\n        }\n      } catch (error) {\n        console.error(\"保存会话到localStorage失败:\", error);\n      }\n    }\n  }, [chatMessages, currentSlides, chatTitle, currentPresentationTitle]);\n  const toggleTaskList = () => setIsTaskListOpen(!isTaskListOpen);\n  const handleChatTitleChange = async newTitle => {\n    const originalTitle = chatTitle; // 在更新前保存原始标题\n\n    // 乐观更新UI，让用户立即看到变化\n    setChatTitle(newTitle);\n    setCurrentPresentationTitle(newTitle);\n    if (currentProjectId) {\n      try {\n        await apiService.updateProject(currentProjectId, {\n          title: newTitle\n        });\n        console.log(`[App] 项目 ${currentProjectId} 的标题已成功更新到数据库: ${newTitle}`);\n        setChatHistory(prev => prev.map(item => item.project_id === currentProjectId ? {\n          ...item,\n          title: newTitle\n        } : item));\n      } catch (error) {\n        console.error(\"更新项目标题失败:\", error);\n        alert(`标题更新失败: ${error.message}`);\n\n        // --- BUG修复 ---\n        // 如果API调用失败，将UI恢复到原始标题\n        setChatTitle(originalTitle);\n        setCurrentPresentationTitle(originalTitle);\n      }\n    }\n  };\n  const handleBackClick = () => {\n    if (isGenerating) {\n      // 如果正在生成，取消生成\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n        eventSourceRef.current = null;\n      }\n      setIsGenerating(false);\n      setChatMessages(prev => [...prev, {\n        id: Date.now(),\n        type: 'ai_error',\n        text: '生成已取消。',\n        sender: 'system',\n        // 明确sender为system\n        icon: '❌' // 添加一个图标\n      }]);\n      return;\n    }\n\n    // 如果没有在生成，则返回首页\n    handleNewPresentation(); // <-- 修复后的正确逻辑\n  };\n  const handleSelectHistory = historyItem => {\n    // FIX: 增加检查，确保 historyItem 和 project_id 存在且有效\n    if (!historyItem || !historyItem.project_id) {\n      console.error(\"无法选择无效的历史项目:\", historyItem);\n      return; // 中止操作\n    }\n    console.log(\"选择历史项目:\", historyItem);\n    setShowHistoryDrawer(false); // 关闭抽屉\n\n    // 关键：只进行导航，让 useEffect 钩子去处理数据的加载\n    navigate(`/?projectId=${historyItem.project_id}`);\n  };\n  const handleDeleteHistory = async (historyItemId, projectIdentifier) => {\n    var _chatHistory$find;\n    // 确保不删除当前会话\n    if (historyItemId === 'current-session') return;\n\n    // 准备删除确认\n    setDeleteItemInfo({\n      id: historyItemId,\n      // 这个是前端 chatHistory 数组中的项的 ID\n      projectId: projectIdentifier,\n      // 这个是后端的真实项目 ID\n      title: ((_chatHistory$find = chatHistory.find(item => item.id === historyItemId)) === null || _chatHistory$find === void 0 ? void 0 : _chatHistory$find.title) || \"该项目\"\n    });\n    setShowDeleteConfirmModal(true);\n  };\n  const handleConfirmDelete = async () => {\n    if (!deleteItemInfo || !deleteItemInfo.projectId) {\n      setShowDeleteConfirmModal(false);\n      setDeleteItemInfo(null);\n      return;\n    }\n    const {\n      id: localHistoryItemId,\n      projectId: serverProjectId\n    } = deleteItemInfo;\n    try {\n      // 1. 调用后端 API 删除服务器端数据\n      const response = await apiService.deleteProject(serverProjectId);\n      console.log(`项目 ${serverProjectId} 删除响应:`, response);\n\n      // 2. 更新本地 chatHistory 状态\n      const updatedHistory = chatHistory.filter(item => item.id !== localHistoryItemId);\n      setChatHistory(updatedHistory);\n\n      // 3. 如果删除的是当前加载的会话，则重置视图\n      if (currentProjectId === serverProjectId) {\n        handleNewPresentation();\n      }\n      console.log(`项目 ${serverProjectId} (本地历史项ID: ${localHistoryItemId}) 已成功删除。`);\n    } catch (error) {\n      console.error(`删除项目 ${serverProjectId} 时出错:`, error);\n      alert(`删除项目失败: ${error.message}`);\n    } finally {\n      setShowDeleteConfirmModal(false);\n      setDeleteItemInfo(null);\n    }\n  };\n  const handleToggleEditSlide = () => {\n    setIsEditingMode(!isEditingMode);\n    setEditingTextElement(null);\n  };\n\n  // Text Editing Handlers\n  /*\n  const handleTextEditStart = useCallback((slideId, elementId, initialText, initialStyle, position) => {\n    // Adjust position relative to the overall RightPane or a more stable parent\n    const rightPaneContentArea = document.querySelector('.right-pane-content-area');\n    let editorX = position.x;\n    let editorY = position.y;\n     if (rightPaneContentArea) {\n      const paneRect = rightPaneContentArea.getBoundingClientRect();\n      editorX = position.x + paneRect.left;\n      editorY = position.y + paneRect.top - rightPaneContentArea.scrollTop;\n    }\n    setEditingTextElement({ slideId, elementId, initialText, initialStyle, position: {x: editorX, y: editorY} });\n  }, []);\n  */\n\n  const handleTextEditSave = (newText, newStyle) => {\n    if (!editingTextElement) return;\n    const {\n      slideId,\n      elementId\n    } = editingTextElement;\n    setCurrentSlides(prevSlides => prevSlides.map(slide => {\n      if (slide.id === slideId) {\n        const tempDiv = document.createElement('div');\n        tempDiv.innerHTML = slide.html;\n        const targetElement = tempDiv.querySelector(`[data-editable-id=\"${elementId}\"]`);\n        if (targetElement) {\n          targetElement.innerText = newText;\n          Object.keys(newStyle).forEach(key => {\n            targetElement.style[key] = newStyle[key];\n          });\n        }\n        return {\n          ...slide,\n          html: tempDiv.innerHTML,\n          code: tempDiv.innerHTML\n        };\n      }\n      return slide;\n    }));\n    setEditingTextElement(null);\n  };\n  const handleTextEditCancel = () => setEditingTextElement(null);\n\n  // +++++++++++++++ 新增处理函数 +++++++++++++++\n  const handleRewriteClick = e => {\n    e.stopPropagation();\n    if (contextMenu) {\n      setEditModal({\n        slideId: contextMenu.slideId,\n        elementId: contextMenu.elementId\n      });\n      setContextMenu(null); // 关闭上下文菜单\n    }\n  };\n  const handleCloseEditModal = () => {\n    setEditModal(null);\n    setEditInstruction('');\n    setIsEditingElement(false);\n  };\n  const handleSubmitEdit = async () => {\n    if (!editInstruction.trim() || !editModal) return;\n    setIsEditingElement(true);\n    const {\n      slideId,\n      elementId\n    } = editModal;\n    try {\n      const result = await apiService.editSlideElement(slideId, `[data-editable-id=\"${elementId}\"]`, editInstruction);\n      if (result && result.html) {\n        // 更新幻灯片状态\n        setCurrentSlides(prevSlides => prevSlides.map(slide => slide.id === slideId ? {\n          ...slide,\n          html: result.html,\n          code: result.html\n        } : slide));\n      }\n    } catch (error) {\n      console.error('编辑元素失败:', error);\n      alert(`编辑失败: ${error.message}`);\n    } finally {\n      handleCloseEditModal();\n    }\n  };\n  // +++++++++++++++++++++++++++++++++++++++++++++\n\n  // Handle opening the player view\n  const handleOpenViewAndExport = () => {\n    navigate('/player', {\n      state: {\n        slides: currentSlides,\n        initialIndex: 0,\n        presentationTitle: currentPresentationTitle\n      }\n    });\n  };\n\n  // Handle messages from SlidePlayerView\n  useEffect(() => {\n    const handleMessageFromPlayerView = event => {\n      if (event.data && event.data.type === 'request-fullscreen') {\n        // Set the appropriate slide index if provided\n        if (event.data.payload && event.data.payload.initialIndex !== undefined) {\n          setCurrentFullScreenSlideIndex(event.data.payload.initialIndex);\n        }\n        // Toggle fullscreen\n        toggleFullScreen();\n      }\n    };\n    window.addEventListener('message', handleMessageFromPlayerView);\n    return () => {\n      window.removeEventListener('message', handleMessageFromPlayerView);\n    };\n  }, [toggleFullScreen]);\n\n  // +++++++++++++++ 新增useEffect来处理来自iframe的消息 +++++++++++++++\n  useEffect(() => {\n    const handleIframeMessage = event => {\n      if (event.data && event.data.type === 'element_clicked' && isEditingMode) {\n        const {\n          slideId,\n          elementId,\n          clickPosition\n        } = event.data.payload;\n\n        // 找到对应的iframe，计算屏幕上的绝对位置\n        const iframe = document.querySelector(`#slide-preview-${slideId} iframe`);\n        if (iframe) {\n          const iframeRect = iframe.getBoundingClientRect();\n          const scale = iframeRect.width / 1280; // 假设设计宽度是1280\n\n          const absoluteX = iframeRect.left + clickPosition.x * scale + window.scrollX;\n          const absoluteY = iframeRect.top + clickPosition.y * scale + window.scrollY;\n          setContextMenu({\n            x: absoluteX,\n            y: absoluteY,\n            slideId,\n            elementId\n          });\n        }\n      }\n    };\n    window.addEventListener('message', handleIframeMessage);\n    // 点击其他地方时关闭菜单\n    const closeMenu = () => setContextMenu(null);\n    window.addEventListener('click', closeMenu);\n    return () => {\n      window.removeEventListener('message', handleIframeMessage);\n      window.removeEventListener('click', closeMenu);\n    };\n  }, [isEditingMode]); // 只在编辑模式下监听\n  // ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n\n  useEffect(() => {\n    // 这个 useEffect 现在是同步 isFullScreen 状态的唯一来源\n    const handleFullscreenChange = () => {\n      const isCurrentlyFullScreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullscreenElement || document.msFullscreenElement);\n      setIsFullScreen(isCurrentlyFullScreen);\n    };\n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);\n    document.addEventListener('mozfullscreenchange', handleFullscreenChange);\n    document.addEventListener('MSFullscreenChange', handleFullscreenChange);\n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);\n      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);\n      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);\n    };\n  }, []);\n\n  // 用于 RightPane 设置当前活动/聚焦的幻灯片\n  const handleSetCurrentSlideIndex = index => {\n    setCurrentSlideIndex(index);\n  };\n  const handleSlideTabChange = (slideIndex, tabIndex) => {\n    setSlideTabIndices(prev => ({\n      ...prev,\n      [slideIndex]: tabIndex\n    }));\n  };\n\n  // 用于播放特定索引的幻灯片\n  const handlePlaySlideAtIndex = index => {\n    setCurrentFullScreenSlideIndex(index);\n    toggleFullScreen();\n  };\n  const handleSendMessage = async messageData => {\n    // 处理参数：可能是字符串或对象\n    let messageText,\n      files = [];\n    if (typeof messageData === 'string') {\n      messageText = messageData;\n    } else if (typeof messageData === 'object' && messageData !== null) {\n      messageText = messageData.message || '';\n      files = messageData.files || [];\n    } else {\n      messageText = '';\n    }\n    if (!messageText.trim() || isGenerating) return;\n\n    // 生成独特的消息ID\n    const userMsgId = `user-msg-${Date.now()}`;\n\n    // 添加用户消息到聊天\n    const userMessage = {\n      id: userMsgId,\n      type: 'user',\n      text: messageText,\n      files: files.length > 0 ? files : undefined // 只在有文件时添加files属性\n    };\n\n    // 判断是否是\"继续\"生成请求\n    const isContinueRequest = messageText.toLowerCase().includes(\"继续\") || messageText.toLowerCase().includes(\"continue\");\n\n    // 【核心修改1】优化新生成请求时的状态清空逻辑\n    if (!isContinueRequest) {\n      // 对于新的演示文稿生成请求，清除所有之前的AI消息，并重置项目相关状态\n      setChatMessages([userMessage]); // 仅保留当前用户消息\n      setCurrentSlides([]);\n      setTotalSlideCount(0);\n      setCurrentProjectId(null); // 这将强制后端创建一个新项目\n\n      // 重置其他UI相关状态，以获得一个全新的开始\n      setCurrentStep(null);\n      setTools([]);\n      setOutlineContent('');\n      setSlideCode('');\n      setActiveStep('preview');\n    } else {\n      // 对于\"继续\"请求，仅追加新的用户消息\n      setChatMessages(prev => [...prev, userMessage]);\n    }\n    let retryAttempts = 0;\n    const MAX_MESSAGE_RETRIES = 3;\n    const executeGeneration = async projectIdToUse => {\n      try {\n        // 设置生成状态为true\n        setIsGenerating(true);\n\n        // 关闭之前的EventSource连接（如果存在）\n        if (eventSourceRef.current) {\n          eventSourceRef.current.close();\n        }\n\n        // 初始化临时幻灯片数组 (这个现在不再由前端维护，后端SSE会直接推送)\n        // let tempSlidesData = []; \n\n        // 创建消息ID映射，用于后续更新或替换特定消息\n        const messageIdMap = {};\n\n        // 决定使用哪个 project_id\n        // `projectIdToUse` 会是 `null` (新生成) 或 `currentProjectId` (继续生成)\n        const effectiveProjectId = projectIdToUse;\n\n        // 使用新的简化工作流API\n        eventSourceRef.current = await apiService.generatePresentation(messageText,\n        // 进度处理\n        progressData => {\n          // console.log(\"进度更新:\", progressData);\n\n          // 处理 id_mapped_to_client 事件 - 当临时ID被映射到实际ID时\n          if (progressData.status === 'id_mapped_to_client' && progressData.metadata && progressData.metadata.actual_project_id) {\n            const newActualId = progressData.metadata.actual_project_id;\n            console.log(`[App.js] 临时项目ID已映射到实际ID: ${newActualId}`);\n            setCurrentProjectId(newActualId);\n            localStorage.setItem('currentProjectId', newActualId);\n            // 更新URL，但不触发页面重新加载\n            navigate(`/?projectId=${newActualId}`, {\n              replace: true\n            });\n            return; // 不需要进一步处理这个事件\n          }\n\n          // 保存项目ID - 如果这是首次获取项目ID (且不是 id_mapped_to_client 消息)\n          if (!currentProjectId && progressData.project_id && progressData.status !== 'id_mapping') {\n            console.log(\"[App.js] 从SSE获取到项目ID:\", progressData.project_id);\n            setCurrentProjectId(progressData.project_id);\n            localStorage.setItem('currentProjectId', progressData.project_id); // 添加这行\n          }\n\n          // 如果进度正常，重置重试次数\n          retryAttempts = 0;\n\n          // 更新AI消息\n          if (progressData.message) {\n            const messageText = progressData.message.text;\n\n            // 跳过初始化和连接建立消息\n            if (messageText === '⏳正在解析您的请求，AI引擎启动中...' || messageText === '🔌连接已建立，等待更新..') {\n              return;\n            }\n\n            // 检查是否是需要替换的消息类型\n            let shouldReplace = false;\n            let messageToReplaceId = null;\n            let additionalInfo = '';\n\n            // 处理风格确定消息\n            if (messageText.includes('的整体风格已确定')) {\n              const styleWaitingMsgId = messageIdMap['style_waiting'];\n              if (styleWaitingMsgId) {\n                messageToReplaceId = styleWaitingMsgId;\n                shouldReplace = true;\n\n                // 计算实际用时\n                const startTime = messageIdMap['style_waiting_start_time'];\n                if (startTime) {\n                  const elapsedTime = Date.now() - startTime;\n                  const seconds = Math.floor(elapsedTime / 1000);\n                  if (seconds < 60) {\n                    additionalInfo = `（实际用时${seconds}秒）`;\n                  } else {\n                    const minutes = Math.floor(seconds / 60);\n                    const remainingSeconds = seconds % 60;\n                    additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                  }\n                }\n              }\n            }\n\n            // 处理内容规划完成消息\n            if (messageText.includes('内容规划完成：共')) {\n              const contentPlanningMsgId = messageIdMap['content_planning'];\n              if (contentPlanningMsgId) {\n                messageToReplaceId = contentPlanningMsgId;\n                shouldReplace = true;\n\n                // 计算实际用时\n                const startTime = messageIdMap['content_planning_start_time'];\n                if (startTime) {\n                  const elapsedTime = Date.now() - startTime;\n                  const seconds = Math.floor(elapsedTime / 1000);\n                  if (seconds < 60) {\n                    additionalInfo = `（实际用时${seconds}秒）`;\n                  } else {\n                    const minutes = Math.floor(seconds / 60);\n                    const remainingSeconds = seconds % 60;\n                    additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                  }\n                }\n              }\n            }\n\n            // 处理幻灯片生成完成消息\n            const slideGenCompleteRegex = /第 (\\d+)\\/(\\d+) 张.*生成完毕/;\n            const slideMatch = messageText.match(slideGenCompleteRegex);\n            if (slideMatch) {\n              const slideNum = slideMatch[1];\n              const slideProcessingMsgId = messageIdMap[`slide_processing_${slideNum}`];\n              if (slideProcessingMsgId) {\n                messageToReplaceId = slideProcessingMsgId;\n                shouldReplace = true;\n\n                // 计算实际用时\n                const startTime = messageIdMap[`slide_processing_${slideNum}_start_time`];\n                if (startTime) {\n                  const elapsedTime = Date.now() - startTime;\n                  const seconds = Math.floor(elapsedTime / 1000);\n                  if (seconds < 60) {\n                    additionalInfo = `（实际用时${seconds}秒）`;\n                  } else {\n                    const minutes = Math.floor(seconds / 60);\n                    const remainingSeconds = seconds % 60;\n                    additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                  }\n                }\n              }\n            }\n\n            // 记录特殊消息的ID和时间戳，用于后续替换\n            if (messageText.includes('正在为') && messageText.includes('规划整体视觉风格')) {\n              const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n              messageIdMap['style_waiting'] = newMsgId;\n              messageIdMap['style_waiting_start_time'] = Date.now();\n\n              // 创建新消息\n              setChatMessages(prev => [...prev, {\n                id: newMsgId,\n                type: 'ai',\n                text: messageText,\n                icon: progressData.message.icon || undefined,\n                thinking: progressData.message.thinking || false,\n                // 使用 thinking 字段\n                sender: progressData.message.sender || 'ai' // 确保sender正确\n              }]);\n              return; // 直接返回，避免重复添加\n            }\n            if (messageText.includes('正在为') && messageText.includes('规划详细内容')) {\n              const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n              messageIdMap['content_planning'] = newMsgId;\n              messageIdMap['content_planning_start_time'] = Date.now();\n\n              // 创建新消息\n              setChatMessages(prev => [...prev, {\n                id: newMsgId,\n                type: 'ai',\n                text: messageText,\n                icon: progressData.message.icon || undefined,\n                thinking: progressData.message.thinking || false,\n                // 使用 thinking 字段\n                sender: progressData.message.sender || 'ai'\n              }]);\n              return; // 直接返回，避免重复添加\n            }\n            const slideProcessingRegex = /开始处理第 (\\d+)\\/(\\d+) 张幻灯片/;\n            const match = messageText.match(slideProcessingRegex);\n            if (match) {\n              const slideNum = match[1];\n              const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n              messageIdMap[`slide_processing_${slideNum}`] = newMsgId;\n              messageIdMap[`slide_processing_${slideNum}_start_time`] = Date.now();\n\n              // 创建新消息\n              setChatMessages(prev => [...prev, {\n                id: newMsgId,\n                type: 'ai',\n                text: messageText,\n                icon: progressData.message.icon || undefined,\n                thinking: progressData.message.thinking || false,\n                // 使用 thinking 字段\n                sender: progressData.message.sender || 'ai'\n              }]);\n              return; // 直接返回，避免重复添加\n            }\n\n            // 创建新消息或替换现有消息\n            const newAiMsgId = shouldReplace ? messageToReplaceId : `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n            setChatMessages(prev => {\n              const currentAiMessageIndex = prev.findIndex(msg => msg.id === newAiMsgId);\n              if (currentAiMessageIndex !== -1) {\n                // 替换现有消息\n                const updatedMessages = [...prev];\n                updatedMessages[currentAiMessageIndex] = {\n                  ...updatedMessages[currentAiMessageIndex],\n                  text: additionalInfo ? `${messageText} ${additionalInfo}` : messageText,\n                  icon: progressData.message.icon || undefined,\n                  thinking: progressData.message.thinking || false,\n                  // 使用 thinking 字段\n                  sender: progressData.message.sender || 'ai'\n                };\n                return updatedMessages;\n              } else {\n                // 添加新消息\n                return [...prev, {\n                  id: newAiMsgId,\n                  type: 'ai',\n                  text: messageText,\n                  icon: progressData.message.icon || undefined,\n                  thinking: progressData.message.thinking || false,\n                  // 使用 thinking 字段\n                  sender: progressData.message.sender || 'ai'\n                }];\n              }\n            });\n          }\n\n          // 当接收到开始状态和AI识别的主题时，更新标题\n          if (progressData.status === \"starting\" && progressData.message && progressData.message.text) {\n            const topicMatch = progressData.message.text.match(/关于「(.+?)」/);\n            if (topicMatch && topicMatch[1]) {\n              const extractedTopic = topicMatch[1].trim();\n              if (extractedTopic && chatTitle !== `AI幻灯片 ${extractedTopic}`) {\n                const newTitle = `AI幻灯片 ${extractedTopic}`;\n                setChatTitle(newTitle);\n              }\n            }\n          }\n\n          // 新增/修改：当意图分析完成时，更精确地更新标题\n          // 当意图分析完成时，根据后端元数据或消息文本更新标题\n          if (progressData.status === \"intent_analyzed\") {\n            // 优先使用 metadata 中的精确标题 (这是最可靠的方法)\n            if (progressData.metadata && progressData.metadata.updated_title) {\n              const newTitle = progressData.metadata.updated_title;\n              console.log(`[App.js] Intent analyzed, updating title from metadata: ${newTitle}`);\n              setChatTitle(newTitle);\n              setCurrentPresentationTitle(newTitle);\n            }\n            // 如果 metadata 中没有标题，则回退到从消息文本中解析 (作为备用方案)\n            else if (progressData.message && progressData.message.text) {\n              const topicMatch = progressData.message.text.match(/主题：\"(.+?)\"/);\n              if (topicMatch && topicMatch[1]) {\n                const extractedTopic = topicMatch[1].trim();\n                const newTitle = `AI幻灯片 ${extractedTopic}`;\n                console.log(`[App.js] Intent analyzed, updating title from message text (fallback): ${newTitle}`);\n                setChatTitle(newTitle);\n                setCurrentPresentationTitle(newTitle);\n              }\n            }\n          }\n\n          // 处理思考内容和总幻灯片数\n          if (progressData.thinking_content) {\n            setOutlineContent(progressData.thinking_content);\n          }\n          if (progressData.total_slides && progressData.total_slides > 0) {\n            setTotalSlideCount(progressData.total_slides);\n          }\n\n          // 处理UI交互动作\n          if (progressData.ui_action) {\n            // 处理标签页切换\n            if (progressData.ui_action.action === \"select_tab\" && progressData.current_slide_index !== undefined) {\n              // 设置当前幻灯片的标签页\n              if (progressData.ui_action.tab === \"code\") {\n                setActiveStep('code');\n                setSlideTabIndices(prev => ({\n                  ...prev,\n                  [progressData.current_slide_index]: 1 // 1 表示代码标签\n                }));\n              } else if (progressData.ui_action.tab === \"preview\") {\n                setActiveStep('preview');\n                setSlideTabIndices(prev => ({\n                  ...prev,\n                  [progressData.current_slide_index]: 0 // 0 表示预览标签\n                }));\n              }\n            }\n          }\n\n          // 处理幻灯片更新\n          if (progressData.slide_update) {\n            const newSlide = {\n              id: progressData.slide_update.id,\n              html: progressData.slide_update.html || progressData.slide_update.html_content,\n              // 兼容字段\n              code: progressData.slide_update.code || progressData.slide_update.html || progressData.slide_update.html_content,\n              order: progressData.slide_update.slide_number !== undefined ? progressData.slide_update.slide_number - 1 : 0,\n              // 确保使用slide_number转为0-based\n              title: `幻灯片 ${progressData.slide_update.slide_number}`\n            };\n\n            // 使用函数式更新来保证我们总是基于最新的幻灯片列表进行操作\n            setCurrentSlides(prevSlides => {\n              const existingIndex = prevSlides.findIndex(s => s.id === newSlide.id || s.order === newSlide.order);\n              let updatedSlides;\n              if (existingIndex >= 0) {\n                // 更新现有幻灯片\n                updatedSlides = [...prevSlides];\n                updatedSlides[existingIndex] = newSlide;\n              } else {\n                // 添加新幻灯片\n                updatedSlides = [...prevSlides, newSlide];\n              }\n\n              // 按 order 排序，以防消息乱序\n              updatedSlides.sort((a, b) => a.order - b.order);\n              return updatedSlides;\n            });\n\n            // 更新当前焦点幻灯片\n            if (progressData.current_slide_index !== undefined) {\n              setCurrentSlideIndex(progressData.current_slide_index);\n            }\n\n            // 如果有代码内容，更新代码显示\n            if (progressData.code_content) {\n              setSlideCode(progressData.code_content);\n            }\n          }\n\n          // 如果状态为完成，更新最终消息\n          if (progressData.status === 'completed') {\n            // 添加完成消息\n            const completionMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n            setChatMessages(prev => [...prev, {\n              id: completionMsgId,\n              type: 'ai',\n              text: `✅ 幻灯片已全部生成完毕 (${progressData.total_slides || currentSlides.length}张)，您可以点击每张幻灯片进行查看和编辑。`,\n              icon: '✅',\n              thinking: false,\n              // 完成时设置为非思考中\n              sender: 'ai',\n              // 确保sender正确\n              project_id: progressData.project_id || currentProjectId // 确保保存项目ID\n            }]);\n            setIsEditingMode(true);\n\n            // 设置演示文稿标题\n            if (!currentPresentationTitle || currentPresentationTitle === \"AI幻灯片\") {\n              setCurrentPresentationTitle(messageText.length > 30 ? messageText.substring(0, 30) + \"...\" : messageText);\n            }\n\n            // 保存聊天历史\n            saveCurrentChatToHistory();\n\n            // 完成后刷新项目历史列表\n            refreshProjectHistory();\n          }\n        },\n        // 错误处理\n        error => {\n          console.error('App.js - 生成幻灯片时发生错误:', error);\n\n          // 确保关闭旧的连接\n          if (eventSourceRef.current && typeof eventSourceRef.current.close === 'function') {\n            eventSourceRef.current.close();\n          }\n\n          // 处理特殊错误类型，尝试重新连接\n          if ((error.message === 'SSE_HEARTBEAT_TIMEOUT' || error.message.includes('connection failed')) && retryAttempts < MAX_MESSAGE_RETRIES) {\n            retryAttempts++;\n            setChatMessages(prev => [...prev, {\n              id: `sys-retry-${Date.now()}`,\n              type: 'system-info',\n              text: `连接中断，正在尝试重新连接... (${retryAttempts}/${MAX_MESSAGE_RETRIES}) 项目: ${effectiveProjectId || '新项目'}`,\n              sender: 'system',\n              icon: '⏳'\n            }]);\n\n            // 延迟重试，每次重试增加延迟\n            setTimeout(() => executeGeneration(currentProjectId), 3000 * retryAttempts);\n          } else {\n            // 超过最大重试次数或其他错误\n            setIsGenerating(false);\n            setChatMessages(prev => [...prev, {\n              id: `error-${Date.now()}`,\n              type: 'ai_error',\n              text: `抱歉，发生错误: ${error.message}`,\n              sender: 'system',\n              icon: '⚠️'\n            }]);\n          }\n        },\n        // 完成处理\n        () => {\n          console.log('App.js - 幻灯片生成流完成');\n          setIsGenerating(false);\n          // 不再在这里添加完成消息，依赖后端推送的 'completed' status\n          saveCurrentChatToHistory(); // 确保最终状态被保存\n        },\n        // 传递项目ID（如果是新会话，后端会创建；如果是重试，则使用 existing_project_id）\n        effectiveProjectId ? {\n          project_id: effectiveProjectId\n        } : {});\n      } catch (apiError) {\n        console.error('App.js - API调用启动错误:', apiError);\n        setIsGenerating(false);\n        setChatMessages(prev => [...prev, {\n          id: `error-api-${Date.now()}`,\n          type: 'ai_error',\n          text: `启动生成失败: ${apiError.message}`,\n          sender: 'system',\n          icon: '❌'\n        }]);\n      }\n    };\n\n    // 执行生成。如果不是\"继续\"请求，`projectIdToUse` 将是 `null`，后端将创建新项目ID。\n    // 否则，它将是 `currentProjectId`。\n    executeGeneration(isContinueRequest ? currentProjectId : null);\n  };\n\n  // 新增函数：刷新项目历史列表\n  const refreshProjectHistory = async () => {\n    try {\n      const serverHistory = await apiService.getProjectsHistory();\n      console.log(\"[App.js] Refreshed project history from server:\", serverHistory);\n\n      // 将后端返回的项目摘要转换为前端 chatHistory 格式\n      const formattedServerHistory = serverHistory.map(proj => ({\n        id: proj.id,\n        // 修复\n        title: proj.title || `项目 (${proj.id ? proj.id.slice(-4) : '未知'})`,\n        // 修复\n        timestamp: proj.last_modified || proj.created_at,\n        project_id: proj.id,\n        // 修复\n        messages: [],\n        slides: [],\n        isSummary: true\n      }));\n\n      // 更新 chatHistory 状态，保留本地项目\n      setChatHistory(prev => {\n        // 获取所有服务器项目的 ID\n        const serverProjectIds = new Set(formattedServerHistory.map(item => item.project_id)); // 这里现在可以正确获取ID了\n\n        // 保留本地项目（那些不在服务器上的）\n        const localOnlyProjects = prev.filter(item => !serverProjectIds.has(item.project_id)); // 这里的去重逻辑现在也能正常工作了\n\n        // 合并并按时间戳排序\n        const combined = [...formattedServerHistory, ...localOnlyProjects].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));\n\n        // 保存到 localStorage\n        try {\n          localStorage.setItem('chatHistory', JSON.stringify(combined));\n        } catch (error) {\n          console.error(\"保存刷新的聊天历史到本地存储失败:\", error);\n        }\n        return combined;\n      });\n    } catch (error) {\n      console.error(\"刷新项目历史列表失败:\", error);\n    }\n  };\n  const handleViewSearchResults = results => {\n    setCurrentSearchResults(results);\n    setShowSearchResultsModal(true);\n  };\n\n  // Auto-generate slides on first load - disabled for production with real backend\n  /*\n  useEffect(() => {\n    if (currentSlides.length === 0 && !isGenerating) {\n      processSimulatedGeneration(\"生成一个中国房地产市场2025年趋势的PPT\");\n    }\n  }, []);\n  */\n\n  // 移除旧的键盘事件监听代码，现在由 FullScreenPlayer 组件通过 postMessage 处理\n\n  // 监听来自 SlidePlayerView 的请求，以启动全屏播放器\n  useEffect(() => {\n    if (location.state && location.state.action === 'startFullScreenPlayer') {\n      const {\n        slides: slidesToPlay,\n        initialIndex,\n        presentationTitle: titleFromPlayer\n      } = location.state;\n\n      // 你可能需要更新 currentSlides 和 presentationTitle，如果它们与 App.js 的当前状态不同\n      // 或者，如果 App.js 是这些状态的唯一来源，SlidePlayerView 应该总是从 App.js 获取最新的\n      if (slidesToPlay && slidesToPlay.length > 0) {\n        setCurrentSlides(slidesToPlay); // 确保幻灯片数据是最新的\n      }\n      if (titleFromPlayer) {\n        setCurrentPresentationTitle(titleFromPlayer);\n      }\n      setCurrentFullScreenSlideIndex(initialIndex || 0);\n      toggleFullScreen(); // 触发全屏\n\n      // 清除 location.state 防止刷新或其他导航时再次触发\n      navigate(location.pathname, {\n        replace: true,\n        state: {}\n      });\n    }\n  }, [location.state, navigate, toggleFullScreen, location.pathname]); // 添加 location.pathname 为依赖项\n\n  // Handlers for history drawer\n  const handleToggleHistoryDrawer = () => {\n    const newDrawerState = !showHistoryDrawer;\n    setShowHistoryDrawer(newDrawerState);\n    if (newDrawerState) {\n      // 如果是打开抽屉\n      refreshProjectHistory(); // 刷新历史记录\n    }\n  };\n  const handleCloseHistoryDrawer = () => {\n    setShowHistoryDrawer(false);\n  };\n  const handleSelectHistoryItem = historyItem => {\n    handleSelectHistory(historyItem); // Assuming handleSelectHistory is defined in App.js\n    handleCloseHistoryDrawer();\n  };\n  const handleCancelDelete = () => {\n    setDeleteItemInfo(null);\n    setShowDeleteConfirmModal(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-screen bg-white\",\n    children: isLoading ?\n    /*#__PURE__*/\n    // 加载中的显示内容\n    _jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-screen bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"animate-spin h-10 w-10 text-blue-500 mx-auto mb-4\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n            className: \"opacity-25\",\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            stroke: \"currentColor\",\n            strokeWidth: \"4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1292,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n            className: \"opacity-75\",\n            fill: \"currentColor\",\n            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1293,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1291,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"\\u52A0\\u8F7D\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1295,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1290,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1289,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [isFullScreen && currentSlides.length > 0 && /*#__PURE__*/_jsxDEV(Suspense, {\n        fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fixed inset-0 bg-black flex justify-center items-center text-white\",\n          children: \"\\u5168\\u5C4F\\u64AD\\u653E\\u5668\\u52A0\\u8F7D\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1302,\n          columnNumber: 33\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(FullScreenPlayer, {\n          slides: currentSlides,\n          initialIndex: currentFullScreenSlideIndex,\n          onClose: toggleFullScreen\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1303,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1302,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `fixed inset-0 bg-black z-40 md:ml-60 transition-opacity duration-150 ease-in-out bg-opacity-0 pointer-events-none`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1312,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-1 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(LeftNav, {\n          isTaskListOpen: isTaskListOpen,\n          onToggleTaskList: toggleTaskList\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-1 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(MiddlePane, {\n            isGenerating: isGenerating,\n            chatMessages: chatMessages,\n            onSendMessage: handleSendMessage,\n            onViewSearchResults: handleViewSearchResults,\n            title: chatTitle,\n            onTitleClick: handleChatTitleChange,\n            onBackClick: handleBackClick,\n            tools: tools,\n            currentStep: currentStep,\n            className: \"w-1/3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1325,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(RightPane, {\n            allSlides: currentSlides,\n            currentFocusedSlideIndex: currentSlideIndex,\n            onSetFocusedSlideIndex: handleSetCurrentSlideIndex,\n            onPlaySlideAtIndex: handlePlaySlideAtIndex,\n            isAppEditingMode: isEditingMode,\n            onToggleAppEditMode: handleToggleEditSlide,\n            onTextEditStart: (elementId, textContent, style) => {\n              setEditingTextElement({\n                id: elementId,\n                content: textContent,\n                style: style\n              });\n            },\n            presentationTitle: currentPresentationTitle,\n            onOpenViewAndExport: handleOpenViewAndExport,\n            outlineContent: outlineContent,\n            slideCode: slideCode,\n            activeStep: activeStep,\n            className: \"w-2/3\",\n            totalSlideCount: totalSlideCount,\n            slideTabIndices: slideTabIndices,\n            onSlideTabChange: handleSlideTabChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1337,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1324,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1318,\n        columnNumber: 11\n      }, this), showDeleteConfirmModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100] p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-xl max-w-md w-full flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 text-xl font-medium text-center\",\n              children: \"\\u5220\\u9664\\u540E\\u4E0D\\u53EF\\u6062\\u590D\\uFF0C\\u786E\\u5B9A\\u5417\\uFF1F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1369,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1368,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 border-t flex justify-center space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleConfirmDelete,\n              className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 text-sm\",\n              children: \"\\u786E\\u5B9A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1374,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCancelDelete,\n              className: \"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm\",\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1380,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1373,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1367,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1366,\n        columnNumber: 13\n      }, this), showSearchResultsModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30 p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center p-4 border-b\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold\",\n              children: \"\\u641C\\u7D22\\u7ED3\\u679C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1395,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSearchResultsModal(false),\n              className: \"text-gray-500 hover:text-gray-700 text-2xl\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1396,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1394,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 overflow-y-auto custom-scrollbar\",\n            children: currentSearchResults.map(result => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4 p-3 border rounded-md hover:shadow-md transition-shadow\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: result.source,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"text-blue-500 hover:underline font-medium flex items-center\",\n                children: [result.type === 'pdf' ? /*#__PURE__*/_jsxDEV(FaFilePdf, {\n                  className: \"mr-2 text-red-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1402,\n                  columnNumber: 50\n                }, this) : /*#__PURE__*/_jsxDEV(FaGlobe, {\n                  className: \"mr-2 text-blue-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1402,\n                  columnNumber: 96\n                }, this), result.title]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1401,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mt-1\",\n                children: result.snippet\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1405,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-400 mt-1\",\n                children: result.source\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1406,\n                columnNumber: 23\n              }, this)]\n            }, result.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1400,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1398,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 border-t text-right\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSearchResultsModal(false),\n              className: \"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm\",\n              children: \"\\u5173\\u95ED\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1411,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1410,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1393,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1392,\n        columnNumber: 13\n      }, this), contextMenu && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed bg-white shadow-lg rounded-md p-1 border border-gray-200 z-[100]\",\n        style: {\n          top: contextMenu.y + 10,\n          left: contextMenu.x\n        },\n        onClick: e => e.stopPropagation() // 防止点击菜单自身时关闭\n        ,\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRewriteClick,\n          className: \"flex items-center w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(FaFeatherAlt, {\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1433,\n            columnNumber: 17\n          }, this), \"\\u91CD\\u5199\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1429,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1424,\n        columnNumber: 13\n      }, this), editModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[110]\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-xl p-6 w-full max-w-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium mb-4\",\n            children: \"\\u544A\\u8BC9AI\\u4F60\\u60F3\\u5982\\u4F55\\u4FEE\\u6539\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1444,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: editInstruction,\n            onChange: e => setEditInstruction(e.target.value),\n            className: \"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-300\",\n            rows: \"3\",\n            placeholder: \"\\u4F8B\\u5982\\uFF1A\\u8FD9\\u6BB5\\u8BDD\\u5199\\u5F97\\u66F4\\u6D3B\\u6CFC\\u4E00\\u70B9\",\n            disabled: isEditingElement\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1445,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3 mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCloseEditModal,\n              className: \"px-4 py-2 bg-gray-200 rounded-md text-sm hover:bg-gray-300\",\n              disabled: isEditingElement,\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1454,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSubmitEdit,\n              className: \"px-4 py-2 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 disabled:bg-blue-300\",\n              disabled: isEditingElement || !editInstruction.trim(),\n              children: isEditingElement ? '提交中...' : '提交'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1461,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1453,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1443,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1442,\n        columnNumber: 13\n      }, this), editingTextElement && /*#__PURE__*/_jsxDEV(Suspense, {\n        fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center\",\n          children: \"\\u7F16\\u8F91\\u5668\\u52A0\\u8F7D\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1475,\n          columnNumber: 33\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(InlineTextEditor, {\n          initialText: editingTextElement.content,\n          initialStyle: editingTextElement.style,\n          position: editingTextElement.position,\n          onSave: handleTextEditSave,\n          onCancel: handleTextEditCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1476,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1475,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1286,\n    columnNumber: 5\n  }, this);\n};\n_s(MainApp, \"He6FdjgU179MUyeb6lqFuMfa7+g=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c9 = MainApp;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(MainApp, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1495,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1495,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/player\",\n        element: /*#__PURE__*/_jsxDEV(Suspense, {\n          fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: \"\\u64AD\\u653E\\u5668\\u52A0\\u8F7D\\u4E2D...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1497,\n            columnNumber: 31\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(SlidePlayerView, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1498,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1497,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1496,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/chat\",\n        element: /*#__PURE__*/_jsxDEV(Suspense, {\n          fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: \"\\u52A0\\u8F7DAI\\u804A\\u5929...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1502,\n            columnNumber: 31\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(ChatViewPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1503,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1502,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1501,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1494,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1493,\n    columnNumber: 5\n  }, this);\n}\n_c0 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0;\n$RefreshReg$(_c, \"InlineTextEditor$lazy\");\n$RefreshReg$(_c2, \"InlineTextEditor\");\n$RefreshReg$(_c3, \"SlidePlayerView$lazy\");\n$RefreshReg$(_c4, \"SlidePlayerView\");\n$RefreshReg$(_c5, \"FullScreenPlayer$lazy\");\n$RefreshReg$(_c6, \"FullScreenPlayer\");\n$RefreshReg$(_c7, \"ChatViewPage$lazy\");\n$RefreshReg$(_c8, \"ChatViewPage\");\n$RefreshReg$(_c9, \"MainApp\");\n$RefreshReg$(_c0, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "lazy", "Suspense", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "useNavigate", "useLocation", "LeftNav", "MiddlePane", "RightPane", "FaFilePdf", "FaGlobe", "FaTrash", "FaFeatherAlt", "formatDate", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InlineTextEditor", "_c", "_c2", "SlidePlayerView", "_c3", "_c4", "FullScreenPlayer", "_c5", "_c6", "ChatViewPage", "_c7", "_c8", "MainApp", "_s", "navigate", "location", "isLoading", "setIsLoading", "isLoadingHistory", "setIsLoadingHistory", "isTaskListOpen", "setIsTaskListOpen", "currentPresentationTitle", "setCurrentPresentationTitle", "chatTitle", "setChatTitle", "isGenerating", "setIsGenerating", "chatMessages", "setChatMessages", "currentSlides", "setCurrentSlides", "currentSlideIndex", "setCurrentSlideIndex", "editingTextElement", "setEditingTextElement", "showSearchResultsModal", "setShowSearchResultsModal", "currentSearchResults", "setCurrentSearchResults", "isFullScreen", "setIsFullScreen", "currentFullScreenSlideIndex", "setCurrentFullScreenSlideIndex", "isEditingMode", "setIsEditingMode", "chatHistory", "setChatHistory", "eventSourceRef", "tools", "setTools", "currentStep", "setCurrentStep", "outlineContent", "setOutlineContent", "slideCode", "setSlideCode", "activeStep", "setActiveStep", "showHistoryDrawer", "setShowHistoryDrawer", "totalSlideCount", "setTotalSlideCount", "showDeleteConfirmModal", "setShowDeleteConfirmModal", "deleteItemInfo", "setDeleteItemInfo", "slideTabIndices", "setSlideTabIndices", "currentProjectId", "setCurrentProjectId", "isNavigatingToNew", "contextMenu", "setContextMenu", "editModal", "setEditModal", "editInstruction", "setEditInstruction", "isEditingElement", "setIsEditingElement", "toggleFullScreen", "isInFullScreen", "document", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "length", "documentElement", "requestFullscreen", "exitFullscreen", "error", "console", "prev", "saveCurrentChatToHistory", "currentSession", "id", "title", "slice", "messages", "slides", "timestamp", "Date", "toISOString", "project_id", "prevHistory", "existingSessionIndex", "findIndex", "item", "updatedHistory", "localStorage", "setItem", "JSON", "stringify", "handleNewPresentation", "current", "removeItem", "loadProjectHistory", "serverHistory", "getProjectsHistory", "log", "formattedSummaries", "map", "proj", "last_modified", "created_at", "is<PERSON>ummary", "serverProjectIds", "Set", "p", "storedId", "getItem", "has", "loadProjectDetails", "projectId", "warn", "details", "getProjectDetails", "chatHistoryData", "chat_history", "msg", "index", "now", "sender", "text", "content", "displayTitle", "search", "includes", "replace", "total_slides_planned", "slidesWithMetadata", "s", "html", "code", "order", "slide_number", "slideContentPromises", "slideMeta", "getSlideC<PERSON>nt", "catch", "fullSlidesData", "Promise", "all", "metaSlide", "fullData", "find", "fs", "message", "params", "URLSearchParams", "projectIdFromUrl", "get", "chatIdFromUrl", "idToLoad", "initializeApp", "projectIdToLoad", "history", "latestProject", "sort", "a", "b", "handleBeforeUnload", "window", "addEventListener", "removeEventListener", "_chatMessages$find", "projectIdFromMessages", "toggleTaskList", "handleChatTitleChange", "newTitle", "originalTitle", "updateProject", "alert", "handleBackClick", "close", "type", "icon", "handleSelectHistory", "historyItem", "handleDeleteHistory", "historyItemId", "projectIdentifier", "_chatHistory$find", "handleConfirmDelete", "localHistoryItemId", "serverProjectId", "response", "deleteProject", "filter", "handleToggleEditSlide", "handleTextEditSave", "newText", "newStyle", "slideId", "elementId", "prevSlides", "slide", "tempDiv", "createElement", "innerHTML", "targetElement", "querySelector", "innerText", "Object", "keys", "for<PERSON>ach", "key", "style", "handleTextEditCancel", "handleRewriteClick", "e", "stopPropagation", "handleCloseEditModal", "handleSubmitEdit", "trim", "result", "editSlideElement", "handleOpenViewAndExport", "state", "initialIndex", "presentationTitle", "handleMessageFromPlayerView", "event", "data", "payload", "undefined", "handleIframeMessage", "clickPosition", "iframe", "iframeRect", "getBoundingClientRect", "scale", "width", "absoluteX", "left", "x", "scrollX", "absoluteY", "top", "y", "scrollY", "closeMenu", "handleFullscreenChange", "isCurrentlyFullScreen", "mozFullscreenElement", "handleSetCurrentSlideIndex", "handleSlideTabChange", "slideIndex", "tabIndex", "handlePlaySlideAtIndex", "handleSendMessage", "messageData", "messageText", "files", "userMsgId", "userMessage", "isContinueRequest", "toLowerCase", "retryAttempts", "MAX_MESSAGE_RETRIES", "executeGeneration", "projectIdToUse", "messageIdMap", "effectiveProjectId", "generatePresentation", "progressData", "status", "metadata", "actual_project_id", "newActualId", "shouldReplace", "messageToReplaceId", "additionalInfo", "styleWaitingMsgId", "startTime", "elapsedTime", "seconds", "Math", "floor", "minutes", "remainingSeconds", "contentPlanningMsgId", "slideGenCompleteRegex", "slideMatch", "match", "slideNum", "slideProcessingMsgId", "newMsgId", "random", "toString", "substring", "thinking", "slideProcessingRegex", "newAiMsgId", "currentAiMessageIndex", "updatedMessages", "topicMatch", "extractedTopic", "updated_title", "thinking_content", "total_slides", "ui_action", "action", "current_slide_index", "tab", "slide_update", "newSlide", "html_content", "existingIndex", "updatedSlides", "code_content", "completionMsgId", "refreshProjectHistory", "setTimeout", "apiError", "formattedServerHistory", "localOnlyProjects", "combined", "handleViewSearchResults", "results", "slidesToPlay", "titleFromPlayer", "pathname", "handleToggleHistoryDrawer", "newDrawerState", "handleCloseHistoryDrawer", "handleSelectHistoryItem", "handleCancelDelete", "className", "children", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "fallback", "onClose", "onToggleTaskList", "onSendMessage", "onViewSearchResults", "onTitleClick", "onBackClick", "allSlides", "currentFocusedSlideIndex", "onSetFocusedSlideIndex", "onPlaySlideAtIndex", "isAppEditingMode", "onToggleAppEditMode", "onTextEditStart", "textContent", "onOpenViewAndExport", "onSlideTabChange", "onClick", "href", "source", "target", "rel", "snippet", "value", "onChange", "rows", "placeholder", "disabled", "initialText", "initialStyle", "position", "onSave", "onCancel", "_c9", "App", "path", "element", "_c0", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/App.js"], "sourcesContent": ["// frontend/src/App.js\nimport React, { useState, useEffect, useCallback, useRef, lazy, Suspense } from 'react';\nimport { BrowserRouter, Routes, Route, useNavigate, useLocation } from 'react-router-dom';\nimport LeftNav from './components/LeftNav';\nimport MiddlePane from './components/MiddlePane';\nimport RightPane from './components/RightPane';\nimport { FaFilePdf, FaGlobe, FaTrash, FaFeatherAlt } from 'react-icons/fa';\nimport formatDate from './utils/formatDate';\nimport apiService from './services/api';\n\n// Lazy load components that are not needed for initial render\nconst InlineTextEditor = lazy(() => import('./components/InlineTextEditor'));\nconst SlidePlayerView = lazy(() => import('./views/SlidePlayerView'));\nconst FullScreenPlayer = lazy(() => import('./components/FullScreenPlayer'));\n\nconst ChatViewPage = lazy(() => import('./views/ChatViewPage'));\n\n// MainApp component that contains all the existing functionality\nconst MainApp = () => {\n  const navigate = useNavigate();\n  const location = useLocation(); // 获取 location 对象\n  const [isLoading, setIsLoading] = useState(true); // 主加载状态（整体应用）\n  const [isLoadingHistory, setIsLoadingHistory] = useState(false); // 项目历史加载状态\n  const [isTaskListOpen, setIsTaskListOpen] = useState(false);\n  const [currentPresentationTitle, setCurrentPresentationTitle] = useState(\"AI 幻灯片\");\n  const [chatTitle, setChatTitle] = useState(\"AI 幻灯片\");\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [chatMessages, setChatMessages] = useState([]);\n  const [currentSlides, setCurrentSlides] = useState([]);\n  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);\n  const [editingTextElement, setEditingTextElement] = useState(null);\n  const [showSearchResultsModal, setShowSearchResultsModal] = useState(false);\n  const [currentSearchResults, setCurrentSearchResults] = useState([]);\n  const [isFullScreen, setIsFullScreen] = useState(false);\n  const [currentFullScreenSlideIndex, setCurrentFullScreenSlideIndex] = useState(0);\n  const [isEditingMode, setIsEditingMode] = useState(false); // 这个是App层面的编辑模式（例如NLP命令，或是否启用InlineTextEditor）\n  const [chatHistory, setChatHistory] = useState([]); // 存储历史聊天记录摘要\n  const eventSourceRef = useRef(null);\n  \n  // 新增状态\n  const [tools, setTools] = useState([]);\n  const [currentStep, setCurrentStep] = useState(null);\n  const [outlineContent, setOutlineContent] = useState('');\n  const [slideCode, setSlideCode] = useState('');\n  const [activeStep, setActiveStep] = useState('preview');\n  const [showHistoryDrawer, setShowHistoryDrawer] = useState(false);\n  const [totalSlideCount, setTotalSlideCount] = useState(0);\n  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);\n  const [deleteItemInfo, setDeleteItemInfo] = useState(null);\n  const [slideTabIndices, setSlideTabIndices] = useState({});\n  const [currentProjectId, setCurrentProjectId] = useState(null);\n  const isNavigatingToNew = useRef(false);\n  const [contextMenu, setContextMenu] = useState(null);\n  const [editModal, setEditModal] = useState(null);\n  const [editInstruction, setEditInstruction] = useState('');\n  const [isEditingElement, setIsEditingElement] = useState(false); // 防止重复提交\n  \n  // 用于播放器全屏的切换逻辑\n  const toggleFullScreen = useCallback(async () => {\n    // 检查当前是否处于浏览器全屏模式\n    const isInFullScreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement);\n    \n    try {\n      if (!isInFullScreen) {\n        // 如果当前不处于全屏，则请求进入全屏\n        // 准备进入全屏时，设置好初始索引\n        if (currentSlides.length > 0) {\n          setCurrentFullScreenSlideIndex(currentSlideIndex);\n        }\n        // 调用浏览器原生 API\n        await document.documentElement.requestFullscreen();\n        // isFullScreen 状态将由下面的 'fullscreenchange' 事件监听器更新\n      } else {\n        // 如果当前已处于全屏，则退出全屏\n        await document.exitFullscreen();\n        // isFullScreen 状态将由下面的 'fullscreenchange' 事件监听器更新\n      }\n    } catch (error) {\n      console.error(\"全屏操作失败:\", error);\n      // 如果原生API失败，作为备用方案，仍然显示我们的模拟全屏组件\n      setIsFullScreen(prev => !prev);\n    }\n  }, [currentSlideIndex, currentSlides.length]);\n\n  // 定义 saveCurrentChatToHistory 函数，确保在适当的时机调用\n  const saveCurrentChatToHistory = useCallback(() => {\n    // 仅当有实质内容时才保存到历史列表\n    if (chatMessages.length > 1 && currentProjectId) { // 确保有 project_id\n      const currentSession = {\n        id: currentProjectId, // 使用 project_id 作为历史记录的唯一 ID\n        title: chatTitle || `演示 (${currentProjectId.slice(-4)})`, // 确保有标题\n        messages: chatMessages,\n        slides: currentSlides,\n        timestamp: new Date().toISOString(),\n        project_id: currentProjectId\n      };\n      \n      setChatHistory(prevHistory => {\n        const existingSessionIndex = prevHistory.findIndex(item => item.id === currentProjectId);\n        let updatedHistory;\n        if (existingSessionIndex >= 0) {\n            updatedHistory = [...prevHistory];\n            updatedHistory[existingSessionIndex] = currentSession; // 更新现有会话\n          } else {\n            updatedHistory = [currentSession, ...prevHistory]; // 添加新会话\n        }\n        // 限制历史记录数量（例如最近20条）\n        // updatedHistory = updatedHistory.slice(0, 20); \n        try {\n          localStorage.setItem('chatHistory', JSON.stringify(updatedHistory));\n        } catch (error) {\n            console.error(\"保存聊天历史列表到 localStorage 失败:\", error);\n        }\n        return updatedHistory;\n      });\n    }\n  }, [chatMessages, currentSlides, chatTitle, currentProjectId, setChatHistory]); // 添加 currentProjectId 依赖\n  \n  // 新建演示文稿的函数\n  const handleNewPresentation = useCallback(() => {\n    isNavigatingToNew.current = true; // 在导航前设置标志\n    \n    // 清空当前状态\n    setChatMessages([]);\n    setCurrentSlides([]);\n    setChatTitle(\"AI 幻灯片\");\n    setCurrentPresentationTitle(\"AI 幻灯片\");\n    setIsGenerating(false);\n    setCurrentStep(null);\n    setTools([]);\n    setOutlineContent('');\n    setSlideCode('');\n    setActiveStep('preview');\n    \n    // 清除项目ID\n    setCurrentProjectId(null); \n    localStorage.removeItem('currentProjectId');\n    \n    // 导航到根路径，清除URL中的projectId\n    navigate('/');\n  }, [\n    setChatMessages, setCurrentSlides, setChatTitle, setCurrentPresentationTitle,\n    setIsGenerating, setCurrentStep, setTools, setOutlineContent, setSlideCode,\n    setActiveStep, setCurrentProjectId, navigate\n  ]);\n\n  // 专门用于加载历史列表的函数\n  const loadProjectHistory = useCallback(async () => {\n    setIsLoadingHistory(true);\n    try {\n      const serverHistory = await apiService.getProjectsHistory();\n      console.log(\"[App.js] Fetched project history summaries:\", serverHistory);\n\n      const formattedSummaries = serverHistory.map(proj => ({\n        id: proj.id, // 修复：使用后端返回的 'id' 字段\n        title: proj.title || `项目 (${proj.id ? proj.id.slice(-4) : '未知'})`, // 修复：使用 'id' 并增加健壮性检查\n        timestamp: proj.last_modified || proj.created_at,\n        project_id: proj.id, // 修复：使用 'id' 字段来填充 project_id\n        isSummary: true // 标记为摘要\n      }));\n\n      setChatHistory(formattedSummaries);\n      \n      // 清理无效的 localStorage.currentProjectId\n      const serverProjectIds = new Set(serverHistory.map(p => p.project_id));\n      const storedId = localStorage.getItem('currentProjectId');\n      if (storedId && !serverProjectIds.has(storedId)) {\n        localStorage.removeItem('currentProjectId');\n      }\n\n    } catch (error) {\n      console.error(\"加载项目历史失败:\", error);\n      setChatHistory([]); // 出错时清空\n    } finally {\n      setIsLoadingHistory(false);\n    }\n  }, [setChatHistory, setIsLoadingHistory]);\n\n  // loadProjectDetails 函数现在是加载单个项目详情的唯一入口\n  const loadProjectDetails = useCallback(async (projectId) => {\n    // 增加对无效projectId的严格检查，防止API调用失败\n    if (!projectId || projectId === 'undefined') {\n      console.warn(\"尝试加载一个无效的项目ID，已中止。\", projectId);\n      setIsLoading(false); // 确保结束加载状态\n      handleNewPresentation(); // 重置到一个安全的新建页面\n      return;\n    }\n    \n    setIsLoading(true);\n    \n    try {\n      const details = await apiService.getProjectDetails(projectId);\n      console.log(`[App.js] Fetched details for project ${projectId}:`, details);\n\n      // 确保聊天历史有数据，必要时进行格式化处理\n      let chatHistoryData = details.chat_history || [];\n      \n      // 确保每条消息都有正确的格式和ID\n      if (chatHistoryData.length > 0) {\n        chatHistoryData = chatHistoryData.map((msg, index) => ({\n          ...msg,\n          id: msg.id || `msg-${index}-${Date.now()}`,\n          sender: msg.sender || (index % 2 === 0 ? 'user' : 'ai'),\n          text: msg.text || msg.content || '无内容',\n          timestamp: msg.timestamp || new Date().toISOString()\n        }));\n        console.log(`[App.js] 处理后的聊天历史数据:`, chatHistoryData);\n      } else {\n        // 如果没有聊天历史，可以添加一个系统消息\n        chatHistoryData = [{\n          id: `system-${Date.now()}`,\n          sender: 'system',\n          text: '项目已加载，但没有聊天记录。',\n          timestamp: new Date().toISOString()\n        }];\n      }\n      \n      setChatMessages(chatHistoryData);\n      const displayTitle = details.title || `项目 (${projectId.slice(-4)})`;\n      setChatTitle(displayTitle);\n      setCurrentPresentationTitle(displayTitle);\n      setCurrentProjectId(details.project_id);\n      \n      // 保存到localStorage以便在刷新后恢复\n      localStorage.setItem('currentProjectId', details.project_id);\n      \n      // 确保URL与当前加载的项目ID同步\n      if (!location.search.includes(`projectId=${details.project_id}`)) {\n        navigate(`/?projectId=${details.project_id}`, { replace: true });\n      }\n\n      setTotalSlideCount(details.total_slides_planned || (details.slides || []).length);\n      \n      // 懒加载幻灯片内容\n      if (details.slides && details.slides.length > 0) {\n        const slidesWithMetadata = details.slides.map(s => ({\n            id: s.id,\n            html: '',\n            code: '',\n            order: s.slide_number - 1,\n            title: `幻灯片 ${s.slide_number}`,\n            isLoading: true,\n        }));\n        setCurrentSlides(slidesWithMetadata);\n\n        const slideContentPromises = details.slides.map(slideMeta =>\n            apiService.getSlideContent(slideMeta.id).catch(() => ({ id: slideMeta.id, error: true }))\n        );\n        const fullSlidesData = await Promise.all(slideContentPromises);\n\n        setCurrentSlides(currentSlides => currentSlides.map(metaSlide => {\n            const fullData = fullSlidesData.find(fs => fs.id === metaSlide.id);\n            if (fullData && !fullData.error) {\n                return { ...metaSlide, html: fullData.html, code: fullData.html, isLoading: false };\n            }\n            return { ...metaSlide, html: '<div>内容加载失败</div>', code: '/* 内容加载失败 */', isLoading: false };\n        }));\n      } else {\n        setCurrentSlides([]);\n      }\n\n    } catch (error) {\n      console.error(`加载项目 ${projectId} 详情失败:`, error);\n      if (error.message && (error.message.includes('not found') || error.message.includes('permission'))) {\n        // 如果项目不存在或无权限，从localStorage移除该项目ID\n        if (localStorage.getItem('currentProjectId') === projectId) {\n          localStorage.removeItem('currentProjectId');\n        }\n        handleNewPresentation();\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  }, [\n    setIsLoading, handleNewPresentation,\n    setChatMessages, setChatTitle, setCurrentPresentationTitle, setCurrentProjectId,\n    location.search, navigate, setTotalSlideCount, setCurrentSlides\n  ]);\n\n  // 监听URL参数变化，当参数改变时加载对应项目\n  useEffect(() => {\n    const params = new URLSearchParams(location.search);\n    const projectIdFromUrl = params.get('projectId');\n    const chatIdFromUrl = params.get('chatId');\n    \n    // 获取URL中的项目ID\n    const idToLoad = projectIdFromUrl || chatIdFromUrl;\n    \n    if (idToLoad && idToLoad !== currentProjectId) {\n      console.log(`[URL变化] 加载项目: ${idToLoad}`);\n      loadProjectDetails(idToLoad);\n    }\n  }, [location.search, currentProjectId, loadProjectDetails]);\n\n  // 初始加载和恢复会话的 useEffect\n  useEffect(() => {\n    const initializeApp = async () => {\n      setIsLoading(true);\n      \n      // 1. 总是先加载历史项目列表\n      await loadProjectHistory();\n\n      // 2. 决定要加载哪个项目的详情\n      const params = new URLSearchParams(location.search);\n      let projectIdToLoad = params.get('projectId');\n      \n      // 如果URL没有ID，尝试从localStorage获取\n      if (!projectIdToLoad || projectIdToLoad === 'undefined') {\n        projectIdToLoad = localStorage.getItem('currentProjectId');\n      }\n      \n      if (projectIdToLoad && projectIdToLoad !== 'undefined') {\n        // 如果有项目ID（来自URL或localStorage），加载它\n        console.log(`[App Init] Resuming session for project: ${projectIdToLoad}`);\n        await loadProjectDetails(projectIdToLoad);\n      } else {\n        // 如果我们是因为点击\"新建\"而导航到这里的，\n        // 只需要重置标志位，然后停止执行。\n        if (isNavigatingToNew.current) {\n          console.log(\"[App Init] Navigating to a new presentation. Skipping history load.\");\n          isNavigatingToNew.current = false;\n          setIsLoading(false); // 确保结束加载状态\n          return; // 终止此 effect 的后续执行\n        }\n        \n        // 否则，加载最新的项目（如果存在）\n        const history = await apiService.getProjectsHistory(); // 再次获取以确保最新\n        if (history && history.length > 0) {\n          const latestProject = history.sort((a, b) => new Date(b.last_modified) - new Date(a.last_modified))[0];\n          // 确保在导航前，最新的项目和其ID是有效的\n          if (latestProject && latestProject.project_id) {\n            console.log(`[App Init] Loading most recent project: ${latestProject.project_id}`);\n            await loadProjectDetails(latestProject.project_id);\n          } else {\n            // 如果最新项目无效，则新建一个\n            console.log(\"[App Init] No valid recent project found. Creating new presentation.\");\n            handleNewPresentation();\n          }\n        } else {\n          // 没有历史记录，创建一个新演示\n          console.log(\"[App Init] No project history found. Creating new presentation.\");\n          handleNewPresentation();\n        }\n      }\n      setIsLoading(false);\n    };\n\n    initializeApp();\n  }, [location.search, loadProjectHistory, loadProjectDetails, handleNewPresentation, setIsLoading]);\n  \n  // 自动保存当前会话到历史记录 - 禁用连续更新，只保留页面离开时的保存\n  useEffect(() => {\n    // 这个效果不再每次状态变化就执行，现在完全依赖beforeunload事件\n    console.log(\"Session management setup\");\n    \n    // 页面即将卸载时保存会话\n    const handleBeforeUnload = () => {\n      // 只保存当前项目ID，不再保存完整的chatHistory\n      if (currentProjectId) {\n        localStorage.setItem('currentProjectId', currentProjectId);\n      }\n    };\n    \n    // 监听页面即将离开事件\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    \n    // 清理函数\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, [currentProjectId]); // 只依赖currentProjectId\n\n  // 仅在特定状态变化时保存到localStorage，但不更新chatHistory状态\n  useEffect(() => {\n    // 只有当有足够的内容时才保存到localStorage\n    if (chatMessages.length > 1 && currentSlides.length > 0) {\n      try {\n        localStorage.setItem('chatMessages', JSON.stringify(chatMessages));\n        localStorage.setItem('currentSlides', JSON.stringify(currentSlides));\n        localStorage.setItem('chatTitle', chatTitle);\n        localStorage.setItem('presentationTitle', currentPresentationTitle);\n        \n        // 更新currentProjectId (如果消息中有)\n        const projectIdFromMessages = chatMessages.find(msg => msg.project_id)?.project_id;\n        if (projectIdFromMessages) {\n          setCurrentProjectId(projectIdFromMessages);\n          localStorage.setItem('currentProjectId', projectIdFromMessages);\n        }\n      } catch (error) {\n        console.error(\"保存会话到localStorage失败:\", error);\n      }\n    }\n  }, [chatMessages, currentSlides, chatTitle, currentPresentationTitle]);\n\n  const toggleTaskList = () => setIsTaskListOpen(!isTaskListOpen);\n\n  const handleChatTitleChange = async (newTitle) => {\n    const originalTitle = chatTitle; // 在更新前保存原始标题\n    \n    // 乐观更新UI，让用户立即看到变化\n    setChatTitle(newTitle);\n    setCurrentPresentationTitle(newTitle);\n    \n    if (currentProjectId) {\n      try {\n        await apiService.updateProject(currentProjectId, { title: newTitle });\n        console.log(`[App] 项目 ${currentProjectId} 的标题已成功更新到数据库: ${newTitle}`);\n        \n        setChatHistory(prev => prev.map(item => \n          item.project_id === currentProjectId ? { ...item, title: newTitle } : item\n        ));\n      } catch (error) {\n        console.error(\"更新项目标题失败:\", error);\n        alert(`标题更新失败: ${error.message}`);\n        \n        // --- BUG修复 ---\n        // 如果API调用失败，将UI恢复到原始标题\n        setChatTitle(originalTitle);\n        setCurrentPresentationTitle(originalTitle);\n      }\n    }\n  };\n\n  const handleBackClick = () => {\n    if (isGenerating) {\n      // 如果正在生成，取消生成\n      if (eventSourceRef.current) {\n        eventSourceRef.current.close();\n        eventSourceRef.current = null;\n      }\n      setIsGenerating(false);\n      setChatMessages(prev => [...prev, {\n        id: Date.now(),\n        type: 'ai_error',\n        text: '生成已取消。',\n        sender: 'system', // 明确sender为system\n        icon: '❌' // 添加一个图标\n      }]);\n      return;\n    }\n    \n    // 如果没有在生成，则返回首页\n    handleNewPresentation(); // <-- 修复后的正确逻辑\n  };\n  \n  const handleSelectHistory = (historyItem) => {\n    // FIX: 增加检查，确保 historyItem 和 project_id 存在且有效\n    if (!historyItem || !historyItem.project_id) {\n        console.error(\"无法选择无效的历史项目:\", historyItem);\n        return; // 中止操作\n    }\n    console.log(\"选择历史项目:\", historyItem);\n    setShowHistoryDrawer(false); // 关闭抽屉\n    \n    // 关键：只进行导航，让 useEffect 钩子去处理数据的加载\n    navigate(`/?projectId=${historyItem.project_id}`);\n  };\n\n  const handleDeleteHistory = async (historyItemId, projectIdentifier) => {\n    // 确保不删除当前会话\n    if (historyItemId === 'current-session') return;\n    \n    // 准备删除确认\n    setDeleteItemInfo({\n      id: historyItemId, // 这个是前端 chatHistory 数组中的项的 ID\n      projectId: projectIdentifier, // 这个是后端的真实项目 ID\n      title: chatHistory.find(item => item.id === historyItemId)?.title || \"该项目\"\n    });\n    setShowDeleteConfirmModal(true);\n  };\n\n  const handleConfirmDelete = async () => {\n    if (!deleteItemInfo || !deleteItemInfo.projectId) {\n      setShowDeleteConfirmModal(false);\n      setDeleteItemInfo(null);\n      return;\n    }\n\n    const { id: localHistoryItemId, projectId: serverProjectId } = deleteItemInfo;\n\n    try {\n      // 1. 调用后端 API 删除服务器端数据\n      const response = await apiService.deleteProject(serverProjectId);\n      console.log(`项目 ${serverProjectId} 删除响应:`, response);\n\n      // 2. 更新本地 chatHistory 状态\n      const updatedHistory = chatHistory.filter(item => item.id !== localHistoryItemId);\n      setChatHistory(updatedHistory);\n      \n      // 3. 如果删除的是当前加载的会话，则重置视图\n      if (currentProjectId === serverProjectId) {\n        handleNewPresentation(); \n      }\n      console.log(`项目 ${serverProjectId} (本地历史项ID: ${localHistoryItemId}) 已成功删除。`);\n\n    } catch (error) {\n      console.error(`删除项目 ${serverProjectId} 时出错:`, error);\n      alert(`删除项目失败: ${error.message}`);\n    } finally {\n      setShowDeleteConfirmModal(false);\n      setDeleteItemInfo(null);\n    }\n  };\n\n  const handleToggleEditSlide = () => {\n    setIsEditingMode(!isEditingMode);\n    setEditingTextElement(null);\n  };\n\n  // Text Editing Handlers\n  /*\n  const handleTextEditStart = useCallback((slideId, elementId, initialText, initialStyle, position) => {\n    // Adjust position relative to the overall RightPane or a more stable parent\n    const rightPaneContentArea = document.querySelector('.right-pane-content-area');\n    let editorX = position.x;\n    let editorY = position.y;\n\n    if (rightPaneContentArea) {\n      const paneRect = rightPaneContentArea.getBoundingClientRect();\n      editorX = position.x + paneRect.left;\n      editorY = position.y + paneRect.top - rightPaneContentArea.scrollTop;\n    }\n    setEditingTextElement({ slideId, elementId, initialText, initialStyle, position: {x: editorX, y: editorY} });\n  }, []);\n  */\n\n  const handleTextEditSave = (newText, newStyle) => {\n    if (!editingTextElement) return;\n    const { slideId, elementId } = editingTextElement;\n    setCurrentSlides(prevSlides =>\n      prevSlides.map(slide => {\n        if (slide.id === slideId) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = slide.html;\n          const targetElement = tempDiv.querySelector(`[data-editable-id=\"${elementId}\"]`);\n          if (targetElement) {\n            targetElement.innerText = newText;\n            Object.keys(newStyle).forEach(key => {\n              targetElement.style[key] = newStyle[key];\n            });\n          }\n          return { ...slide, html: tempDiv.innerHTML, code: tempDiv.innerHTML };\n        }\n        return slide;\n      })\n    );\n    setEditingTextElement(null);\n  };\n\n  const handleTextEditCancel = () => setEditingTextElement(null);\n\n  // +++++++++++++++ 新增处理函数 +++++++++++++++\n  const handleRewriteClick = (e) => {\n    e.stopPropagation();\n    if (contextMenu) {\n      setEditModal({ slideId: contextMenu.slideId, elementId: contextMenu.elementId });\n      setContextMenu(null); // 关闭上下文菜单\n    }\n  };\n\n  const handleCloseEditModal = () => {\n    setEditModal(null);\n    setEditInstruction('');\n    setIsEditingElement(false);\n  };\n\n  const handleSubmitEdit = async () => {\n    if (!editInstruction.trim() || !editModal) return;\n    \n    setIsEditingElement(true);\n    const { slideId, elementId } = editModal;\n    \n    try {\n      const result = await apiService.editSlideElement(slideId, `[data-editable-id=\"${elementId}\"]`, editInstruction);\n      if (result && result.html) {\n        // 更新幻灯片状态\n        setCurrentSlides(prevSlides =>\n          prevSlides.map(slide => \n            slide.id === slideId ? { ...slide, html: result.html, code: result.html } : slide\n          )\n        );\n      }\n    } catch (error) {\n      console.error('编辑元素失败:', error);\n      alert(`编辑失败: ${error.message}`);\n    } finally {\n      handleCloseEditModal();\n    }\n  };\n  // +++++++++++++++++++++++++++++++++++++++++++++\n\n  // Handle opening the player view\n  const handleOpenViewAndExport = () => {\n    navigate('/player', { \n      state: { \n        slides: currentSlides,\n        initialIndex: 0,\n        presentationTitle: currentPresentationTitle \n      } \n    });\n  };\n  \n  // Handle messages from SlidePlayerView\n  useEffect(() => {\n    const handleMessageFromPlayerView = (event) => {\n      if (event.data && event.data.type === 'request-fullscreen') {\n        // Set the appropriate slide index if provided\n        if (event.data.payload && event.data.payload.initialIndex !== undefined) {\n          setCurrentFullScreenSlideIndex(event.data.payload.initialIndex);\n        }\n        // Toggle fullscreen\n        toggleFullScreen();\n      }\n    };\n\n    window.addEventListener('message', handleMessageFromPlayerView);\n    return () => {\n      window.removeEventListener('message', handleMessageFromPlayerView);\n    };\n  }, [toggleFullScreen]);\n  \n  // +++++++++++++++ 新增useEffect来处理来自iframe的消息 +++++++++++++++\n  useEffect(() => {\n    const handleIframeMessage = (event) => {\n      if (event.data && event.data.type === 'element_clicked' && isEditingMode) {\n        const { slideId, elementId, clickPosition } = event.data.payload;\n        \n        // 找到对应的iframe，计算屏幕上的绝对位置\n        const iframe = document.querySelector(`#slide-preview-${slideId} iframe`);\n        if (iframe) {\n          const iframeRect = iframe.getBoundingClientRect();\n          const scale = iframeRect.width / 1280; // 假设设计宽度是1280\n          \n          const absoluteX = iframeRect.left + (clickPosition.x * scale) + window.scrollX;\n          const absoluteY = iframeRect.top + (clickPosition.y * scale) + window.scrollY;\n          \n          setContextMenu({\n            x: absoluteX,\n            y: absoluteY,\n            slideId,\n            elementId\n          });\n        }\n      }\n    };\n\n    window.addEventListener('message', handleIframeMessage);\n    // 点击其他地方时关闭菜单\n    const closeMenu = () => setContextMenu(null);\n    window.addEventListener('click', closeMenu);\n\n    return () => {\n      window.removeEventListener('message', handleIframeMessage);\n      window.removeEventListener('click', closeMenu);\n    };\n  }, [isEditingMode]); // 只在编辑模式下监听\n  // ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n  \n  useEffect(() => {\n    // 这个 useEffect 现在是同步 isFullScreen 状态的唯一来源\n    const handleFullscreenChange = () => {\n      const isCurrentlyFullScreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.mozFullscreenElement || document.msFullscreenElement);\n      setIsFullScreen(isCurrentlyFullScreen);\n    };\n    \n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);\n    document.addEventListener('mozfullscreenchange', handleFullscreenChange);\n    document.addEventListener('MSFullscreenChange', handleFullscreenChange);\n    \n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);\n      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);\n      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);\n    };\n  }, []);\n\n  // 用于 RightPane 设置当前活动/聚焦的幻灯片\n  const handleSetCurrentSlideIndex = (index) => {\n    setCurrentSlideIndex(index);\n  };\n\n  const handleSlideTabChange = (slideIndex, tabIndex) => {\n    setSlideTabIndices(prev => ({\n      ...prev,\n      [slideIndex]: tabIndex\n    }));\n  };\n\n  // 用于播放特定索引的幻灯片\n  const handlePlaySlideAtIndex = (index) => {\n    setCurrentFullScreenSlideIndex(index);\n    toggleFullScreen();\n  };\n\n  const handleSendMessage = async (messageData) => {\n    // 处理参数：可能是字符串或对象\n    let messageText, files = [];\n    \n    if (typeof messageData === 'string') {\n      messageText = messageData;\n    } else if (typeof messageData === 'object' && messageData !== null) {\n      messageText = messageData.message || '';\n      files = messageData.files || [];\n    } else {\n      messageText = '';\n    }\n\n    if (!messageText.trim() || isGenerating) return;\n\n    // 生成独特的消息ID\n    const userMsgId = `user-msg-${Date.now()}`;\n    \n    // 添加用户消息到聊天\n    const userMessage = {\n      id: userMsgId,\n      type: 'user',\n      text: messageText,\n      files: files.length > 0 ? files : undefined, // 只在有文件时添加files属性\n    };\n    \n    // 判断是否是\"继续\"生成请求\n    const isContinueRequest = messageText.toLowerCase().includes(\"继续\") || \n                             messageText.toLowerCase().includes(\"continue\");\n    \n    // 【核心修改1】优化新生成请求时的状态清空逻辑\n    if (!isContinueRequest) {\n      // 对于新的演示文稿生成请求，清除所有之前的AI消息，并重置项目相关状态\n      setChatMessages([userMessage]); // 仅保留当前用户消息\n      setCurrentSlides([]);\n      setTotalSlideCount(0);\n      setCurrentProjectId(null); // 这将强制后端创建一个新项目\n      \n      // 重置其他UI相关状态，以获得一个全新的开始\n    setCurrentStep(null);\n      setTools([]);\n    setOutlineContent('');\n    setSlideCode('');\n    setActiveStep('preview');\n    } else {\n      // 对于\"继续\"请求，仅追加新的用户消息\n    setChatMessages(prev => [...prev, userMessage]);\n    }\n    \n    let retryAttempts = 0;\n    const MAX_MESSAGE_RETRIES = 3;\n\n    const executeGeneration = async (projectIdToUse) => {\n      try {\n        // 设置生成状态为true\n        setIsGenerating(true);\n                \n        // 关闭之前的EventSource连接（如果存在）\n        if (eventSourceRef.current) {\n          eventSourceRef.current.close();\n        }\n        \n        // 初始化临时幻灯片数组 (这个现在不再由前端维护，后端SSE会直接推送)\n        // let tempSlidesData = []; \n        \n        // 创建消息ID映射，用于后续更新或替换特定消息\n        const messageIdMap = {};\n        \n        // 决定使用哪个 project_id\n        // `projectIdToUse` 会是 `null` (新生成) 或 `currentProjectId` (继续生成)\n        const effectiveProjectId = projectIdToUse; \n        \n        // 使用新的简化工作流API\n        eventSourceRef.current = await apiService.generatePresentation(\n          messageText,\n          // 进度处理\n          (progressData) => {\n            // console.log(\"进度更新:\", progressData);\n            \n            // 处理 id_mapped_to_client 事件 - 当临时ID被映射到实际ID时\n            if (progressData.status === 'id_mapped_to_client' && progressData.metadata && progressData.metadata.actual_project_id) {\n              const newActualId = progressData.metadata.actual_project_id;\n              console.log(`[App.js] 临时项目ID已映射到实际ID: ${newActualId}`);\n              setCurrentProjectId(newActualId);\n              localStorage.setItem('currentProjectId', newActualId);\n              // 更新URL，但不触发页面重新加载\n              navigate(`/?projectId=${newActualId}`, { replace: true });\n              return; // 不需要进一步处理这个事件\n            }\n            \n            // 保存项目ID - 如果这是首次获取项目ID (且不是 id_mapped_to_client 消息)\n            if (!currentProjectId && progressData.project_id && progressData.status !== 'id_mapping') {\n              console.log(\"[App.js] 从SSE获取到项目ID:\", progressData.project_id);\n              setCurrentProjectId(progressData.project_id);\n              localStorage.setItem('currentProjectId', progressData.project_id); // 添加这行\n            }\n            \n            // 如果进度正常，重置重试次数\n            retryAttempts = 0;\n            \n            // 更新AI消息\n            if (progressData.message) {\n              const messageText = progressData.message.text;\n              \n              // 跳过初始化和连接建立消息\n              if (messageText === '⏳正在解析您的请求，AI引擎启动中...' || \n                  messageText === '🔌连接已建立，等待更新..') {\n                return;\n              }\n              \n              // 检查是否是需要替换的消息类型\n              let shouldReplace = false;\n              let messageToReplaceId = null;\n              let additionalInfo = '';\n              \n              // 处理风格确定消息\n              if (messageText.includes('的整体风格已确定')) {\n                const styleWaitingMsgId = messageIdMap['style_waiting'];\n                if (styleWaitingMsgId) {\n                  messageToReplaceId = styleWaitingMsgId;\n                  shouldReplace = true;\n                  \n                  // 计算实际用时\n                  const startTime = messageIdMap['style_waiting_start_time'];\n                  if (startTime) {\n                    const elapsedTime = Date.now() - startTime;\n                    const seconds = Math.floor(elapsedTime / 1000);\n                    if (seconds < 60) {\n                      additionalInfo = `（实际用时${seconds}秒）`;\n                    } else {\n                      const minutes = Math.floor(seconds / 60);\n                      const remainingSeconds = seconds % 60;\n                      additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                    }\n                  }\n                }\n              }\n              \n              // 处理内容规划完成消息\n              if (messageText.includes('内容规划完成：共')) {\n                const contentPlanningMsgId = messageIdMap['content_planning'];\n                if (contentPlanningMsgId) {\n                  messageToReplaceId = contentPlanningMsgId;\n                  shouldReplace = true;\n                  \n                  // 计算实际用时\n                  const startTime = messageIdMap['content_planning_start_time'];\n                  if (startTime) {\n                    const elapsedTime = Date.now() - startTime;\n                    const seconds = Math.floor(elapsedTime / 1000);\n                    if (seconds < 60) {\n                      additionalInfo = `（实际用时${seconds}秒）`;\n                    } else {\n                      const minutes = Math.floor(seconds / 60);\n                      const remainingSeconds = seconds % 60;\n                      additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                    }\n                  }\n                }\n              }\n              \n              // 处理幻灯片生成完成消息\n              const slideGenCompleteRegex = /第 (\\d+)\\/(\\d+) 张.*生成完毕/;\n              const slideMatch = messageText.match(slideGenCompleteRegex);\n              if (slideMatch) {\n                const slideNum = slideMatch[1];\n                const slideProcessingMsgId = messageIdMap[`slide_processing_${slideNum}`];\n                if (slideProcessingMsgId) {\n                  messageToReplaceId = slideProcessingMsgId;\n                  shouldReplace = true;\n                  \n                  // 计算实际用时\n                  const startTime = messageIdMap[`slide_processing_${slideNum}_start_time`];\n                  if (startTime) {\n                    const elapsedTime = Date.now() - startTime;\n                    const seconds = Math.floor(elapsedTime / 1000);\n                    if (seconds < 60) {\n                      additionalInfo = `（实际用时${seconds}秒）`;\n                    } else {\n                      const minutes = Math.floor(seconds / 60);\n                      const remainingSeconds = seconds % 60;\n                      additionalInfo = `（实际用时${minutes}分${remainingSeconds}秒）`;\n                    }\n                  }\n                }\n              }\n              \n              // 记录特殊消息的ID和时间戳，用于后续替换\n              if (messageText.includes('正在为') && messageText.includes('规划整体视觉风格')) {\n                const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n                messageIdMap['style_waiting'] = newMsgId;\n                messageIdMap['style_waiting_start_time'] = Date.now();\n                \n                // 创建新消息\n                setChatMessages(prev => [\n                  ...prev, \n                  { \n                    id: newMsgId, \n                    type: 'ai', \n                    text: messageText,\n                    icon: progressData.message.icon || undefined,\n                    thinking: progressData.message.thinking || false, // 使用 thinking 字段\n                    sender: progressData.message.sender || 'ai' // 确保sender正确\n                  }\n                ]);\n                return; // 直接返回，避免重复添加\n              }\n              \n              if (messageText.includes('正在为') && messageText.includes('规划详细内容')) {\n                const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n                messageIdMap['content_planning'] = newMsgId;\n                messageIdMap['content_planning_start_time'] = Date.now();\n                \n                // 创建新消息\n                setChatMessages(prev => [\n                  ...prev, \n                  { \n                    id: newMsgId, \n                    type: 'ai', \n                    text: messageText,\n                    icon: progressData.message.icon || undefined,\n                    thinking: progressData.message.thinking || false, // 使用 thinking 字段\n                    sender: progressData.message.sender || 'ai'\n                  }\n                ]);\n                return; // 直接返回，避免重复添加\n              }\n              \n              const slideProcessingRegex = /开始处理第 (\\d+)\\/(\\d+) 张幻灯片/;\n              const match = messageText.match(slideProcessingRegex);\n              if (match) {\n                const slideNum = match[1];\n                const newMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n                messageIdMap[`slide_processing_${slideNum}`] = newMsgId;\n                messageIdMap[`slide_processing_${slideNum}_start_time`] = Date.now();\n                \n                // 创建新消息\n                setChatMessages(prev => [\n                  ...prev, \n                  { \n                    id: newMsgId, \n                    type: 'ai', \n                    text: messageText,\n                    icon: progressData.message.icon || undefined,\n                    thinking: progressData.message.thinking || false, // 使用 thinking 字段\n                    sender: progressData.message.sender || 'ai'\n                  }\n                ]);\n                return; // 直接返回，避免重复添加\n              }\n              \n              // 创建新消息或替换现有消息\n              const newAiMsgId = shouldReplace ? messageToReplaceId : `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n              \n              setChatMessages(prev => {\n                const currentAiMessageIndex = prev.findIndex(msg => msg.id === newAiMsgId);\n                if (currentAiMessageIndex !== -1) {\n                // 替换现有消息\n                    const updatedMessages = [...prev];\n                    updatedMessages[currentAiMessageIndex] = {\n                        ...updatedMessages[currentAiMessageIndex], \n                        text: additionalInfo ? `${messageText} ${additionalInfo}` : messageText,\n                        icon: progressData.message.icon || undefined,\n                        thinking: progressData.message.thinking || false, // 使用 thinking 字段\n                        sender: progressData.message.sender || 'ai'\n                    };\n                    return updatedMessages;\n              } else {\n                // 添加新消息\n                    return [\n                  ...prev, \n                  { \n                    id: newAiMsgId, \n                    type: 'ai', \n                    text: messageText,\n                    icon: progressData.message.icon || undefined,\n                            thinking: progressData.message.thinking || false, // 使用 thinking 字段\n                            sender: progressData.message.sender || 'ai'\n                  }\n                    ];\n              }\n              });\n            }\n            \n            // 当接收到开始状态和AI识别的主题时，更新标题\n            if (progressData.status === \"starting\" && progressData.message && progressData.message.text) {\n              const topicMatch = progressData.message.text.match(/关于「(.+?)」/);\n              if (topicMatch && topicMatch[1]) { \n                  const extractedTopic = topicMatch[1].trim();\n                  if (extractedTopic && chatTitle !== `AI幻灯片 ${extractedTopic}`) { \n                      const newTitle = `AI幻灯片 ${extractedTopic}`;\n                      setChatTitle(newTitle);\n                  }\n              }\n            }\n\n            // 新增/修改：当意图分析完成时，更精确地更新标题\n            // 当意图分析完成时，根据后端元数据或消息文本更新标题\n            if (progressData.status === \"intent_analyzed\") {\n              // 优先使用 metadata 中的精确标题 (这是最可靠的方法)\n              if (progressData.metadata && progressData.metadata.updated_title) {\n                const newTitle = progressData.metadata.updated_title;\n                console.log(`[App.js] Intent analyzed, updating title from metadata: ${newTitle}`);\n                setChatTitle(newTitle);\n                setCurrentPresentationTitle(newTitle);\n              } \n              // 如果 metadata 中没有标题，则回退到从消息文本中解析 (作为备用方案)\n              else if (progressData.message && progressData.message.text) {\n                const topicMatch = progressData.message.text.match(/主题：\"(.+?)\"/);\n                if (topicMatch && topicMatch[1]) {\n                    const extractedTopic = topicMatch[1].trim();\n                    const newTitle = `AI幻灯片 ${extractedTopic}`;\n                    console.log(`[App.js] Intent analyzed, updating title from message text (fallback): ${newTitle}`);\n                    setChatTitle(newTitle);\n                    setCurrentPresentationTitle(newTitle);\n                }\n              }\n            }\n            \n            // 处理思考内容和总幻灯片数\n            if (progressData.thinking_content) {\n              setOutlineContent(progressData.thinking_content);\n            }\n            \n            if (progressData.total_slides && progressData.total_slides > 0) {\n              setTotalSlideCount(progressData.total_slides);\n            }\n            \n            // 处理UI交互动作\n            if (progressData.ui_action) {\n              // 处理标签页切换\n              if (progressData.ui_action.action === \"select_tab\" && \n                  progressData.current_slide_index !== undefined) {\n                \n                // 设置当前幻灯片的标签页\n                if (progressData.ui_action.tab === \"code\") {\n                  setActiveStep('code');\n                  setSlideTabIndices(prev => ({\n                    ...prev,\n                    [progressData.current_slide_index]: 1 // 1 表示代码标签\n                  }));\n                } else if (progressData.ui_action.tab === \"preview\") {\n                  setActiveStep('preview');\n                  setSlideTabIndices(prev => ({\n                    ...prev,\n                    [progressData.current_slide_index]: 0 // 0 表示预览标签\n                  }));\n                }\n              }\n            }\n            \n            // 处理幻灯片更新\n            if (progressData.slide_update) {\n              const newSlide = {\n                id: progressData.slide_update.id,\n                html: progressData.slide_update.html || progressData.slide_update.html_content, // 兼容字段\n                code: progressData.slide_update.code || progressData.slide_update.html || progressData.slide_update.html_content,\n                order: progressData.slide_update.slide_number !== undefined ? progressData.slide_update.slide_number - 1 : 0, // 确保使用slide_number转为0-based\n                title: `幻灯片 ${progressData.slide_update.slide_number}`\n              };\n\n              // 使用函数式更新来保证我们总是基于最新的幻灯片列表进行操作\n              setCurrentSlides(prevSlides => {\n                const existingIndex = prevSlides.findIndex(s => s.id === newSlide.id || s.order === newSlide.order);\n                let updatedSlides;\n\n                if (existingIndex >= 0) {\n                  // 更新现有幻灯片\n                  updatedSlides = [...prevSlides];\n                  updatedSlides[existingIndex] = newSlide;\n                } else {\n                  // 添加新幻灯片\n                  updatedSlides = [...prevSlides, newSlide];\n                }\n                \n                // 按 order 排序，以防消息乱序\n                updatedSlides.sort((a, b) => a.order - b.order);\n                return updatedSlides;\n              });\n\n              // 更新当前焦点幻灯片\n              if (progressData.current_slide_index !== undefined) {\n                setCurrentSlideIndex(progressData.current_slide_index);\n              }\n              \n              // 如果有代码内容，更新代码显示\n              if (progressData.code_content) {\n                setSlideCode(progressData.code_content);\n              }\n            }\n            \n            // 如果状态为完成，更新最终消息\n            if (progressData.status === 'completed') {\n              // 添加完成消息\n              const completionMsgId = `ai-msg-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;\n              setChatMessages(prev => [\n                ...prev, \n                { \n                  id: completionMsgId, \n                  type: 'ai', \n                  text: `✅ 幻灯片已全部生成完毕 (${(progressData.total_slides || currentSlides.length)}张)，您可以点击每张幻灯片进行查看和编辑。`,\n                  icon: '✅',\n                  thinking: false, // 完成时设置为非思考中\n                  sender: 'ai', // 确保sender正确\n                  project_id: progressData.project_id || currentProjectId // 确保保存项目ID\n                }\n              ]);\n              \n              setIsEditingMode(true);\n              \n              // 设置演示文稿标题\n              if (!currentPresentationTitle || currentPresentationTitle === \"AI幻灯片\") {\n                setCurrentPresentationTitle(messageText.length > 30 ? messageText.substring(0, 30) + \"...\" : messageText);\n              }\n              \n              // 保存聊天历史\n              saveCurrentChatToHistory();\n              \n              // 完成后刷新项目历史列表\n              refreshProjectHistory();\n            }\n          },\n          // 错误处理\n          (error) => {\n            console.error('App.js - 生成幻灯片时发生错误:', error);\n            \n            // 确保关闭旧的连接\n            if (eventSourceRef.current && typeof eventSourceRef.current.close === 'function') {\n              eventSourceRef.current.close();\n            }\n            \n            // 处理特殊错误类型，尝试重新连接\n            if ((error.message === 'SSE_HEARTBEAT_TIMEOUT' || error.message.includes('connection failed')) && retryAttempts < MAX_MESSAGE_RETRIES) {\n              retryAttempts++;\n              setChatMessages(prev => [...prev, {\n                id: `sys-retry-${Date.now()}`, \n                type: 'system-info', \n                text: `连接中断，正在尝试重新连接... (${retryAttempts}/${MAX_MESSAGE_RETRIES}) 项目: ${effectiveProjectId || '新项目'}`,\n                sender: 'system',\n                icon: '⏳'\n              }]);\n              \n              // 延迟重试，每次重试增加延迟\n              setTimeout(() => executeGeneration(currentProjectId), 3000 * retryAttempts);\n            } else {\n              // 超过最大重试次数或其他错误\n              setIsGenerating(false);\n              setChatMessages(prev => [...prev, {\n                id: `error-${Date.now()}`, \n                type: 'ai_error', \n                text: `抱歉，发生错误: ${error.message}`,\n                sender: 'system',\n                icon: '⚠️'\n              }]);\n            }\n          },\n          // 完成处理\n          () => {\n            console.log('App.js - 幻灯片生成流完成');\n            setIsGenerating(false);\n            // 不再在这里添加完成消息，依赖后端推送的 'completed' status\n            saveCurrentChatToHistory(); // 确保最终状态被保存\n          },\n          // 传递项目ID（如果是新会话，后端会创建；如果是重试，则使用 existing_project_id）\n          effectiveProjectId ? { project_id: effectiveProjectId } : {}\n        );\n      } catch (apiError) {\n        console.error('App.js - API调用启动错误:', apiError);\n        setIsGenerating(false);\n        setChatMessages(prev => [...prev, {\n          id: `error-api-${Date.now()}`, \n          type: 'ai_error', \n          text: `启动生成失败: ${apiError.message}`,\n          sender: 'system',\n          icon: '❌'\n        }]);\n      }\n    };\n\n    // 执行生成。如果不是\"继续\"请求，`projectIdToUse` 将是 `null`，后端将创建新项目ID。\n    // 否则，它将是 `currentProjectId`。\n    executeGeneration(isContinueRequest ? currentProjectId : null);\n  };\n\n  // 新增函数：刷新项目历史列表\n  const refreshProjectHistory = async () => {\n    try {\n      const serverHistory = await apiService.getProjectsHistory();\n      console.log(\"[App.js] Refreshed project history from server:\", serverHistory);\n      \n      // 将后端返回的项目摘要转换为前端 chatHistory 格式\n      const formattedServerHistory = serverHistory.map(proj => ({\n        id: proj.id, // 修复\n        title: proj.title || `项目 (${proj.id ? proj.id.slice(-4) : '未知'})`, // 修复\n        timestamp: proj.last_modified || proj.created_at,\n        project_id: proj.id, // 修复\n        messages: [], \n        slides: [],\n        isSummary: true\n      }));\n      \n      // 更新 chatHistory 状态，保留本地项目\n      setChatHistory(prev => {\n        // 获取所有服务器项目的 ID\n        const serverProjectIds = new Set(formattedServerHistory.map(item => item.project_id)); // 这里现在可以正确获取ID了\n        \n        // 保留本地项目（那些不在服务器上的）\n        const localOnlyProjects = prev.filter(item => !serverProjectIds.has(item.project_id)); // 这里的去重逻辑现在也能正常工作了\n        \n        // 合并并按时间戳排序\n        const combined = [...formattedServerHistory, ...localOnlyProjects]\n          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));\n          \n        // 保存到 localStorage\n        try {\n          localStorage.setItem('chatHistory', JSON.stringify(combined));\n        } catch (error) {\n          console.error(\"保存刷新的聊天历史到本地存储失败:\", error);\n        }\n        \n        return combined;\n      });\n    } catch (error) {\n      console.error(\"刷新项目历史列表失败:\", error);\n    }\n  };\n\n  const handleViewSearchResults = (results) => {\n    setCurrentSearchResults(results);\n    setShowSearchResultsModal(true);\n  };\n\n  // Auto-generate slides on first load - disabled for production with real backend\n  /*\n  useEffect(() => {\n    if (currentSlides.length === 0 && !isGenerating) {\n      processSimulatedGeneration(\"生成一个中国房地产市场2025年趋势的PPT\");\n    }\n  }, []);\n  */\n\n  // 移除旧的键盘事件监听代码，现在由 FullScreenPlayer 组件通过 postMessage 处理\n\n  // 监听来自 SlidePlayerView 的请求，以启动全屏播放器\n  useEffect(() => {\n    if (location.state && location.state.action === 'startFullScreenPlayer') {\n      const { slides: slidesToPlay, initialIndex, presentationTitle: titleFromPlayer } = location.state;\n      \n      // 你可能需要更新 currentSlides 和 presentationTitle，如果它们与 App.js 的当前状态不同\n      // 或者，如果 App.js 是这些状态的唯一来源，SlidePlayerView 应该总是从 App.js 获取最新的\n      if (slidesToPlay && slidesToPlay.length > 0) {\n         setCurrentSlides(slidesToPlay); // 确保幻灯片数据是最新的\n      }\n      if (titleFromPlayer) {\n         setCurrentPresentationTitle(titleFromPlayer);\n      }\n\n      setCurrentFullScreenSlideIndex(initialIndex || 0);\n      toggleFullScreen(); // 触发全屏\n\n      // 清除 location.state 防止刷新或其他导航时再次触发\n      navigate(location.pathname, { replace: true, state: {} }); \n    }\n  }, [location.state, navigate, toggleFullScreen, location.pathname]); // 添加 location.pathname 为依赖项\n\n  // Handlers for history drawer\n  const handleToggleHistoryDrawer = () => {\n    const newDrawerState = !showHistoryDrawer;\n    setShowHistoryDrawer(newDrawerState);\n    if (newDrawerState) { // 如果是打开抽屉\n      refreshProjectHistory(); // 刷新历史记录\n    }\n  };\n\n  const handleCloseHistoryDrawer = () => {\n    setShowHistoryDrawer(false);\n  };\n\n  const handleSelectHistoryItem = (historyItem) => {\n    handleSelectHistory(historyItem); // Assuming handleSelectHistory is defined in App.js\n    handleCloseHistoryDrawer();\n  };\n\n  const handleCancelDelete = () => {\n    setDeleteItemInfo(null);\n    setShowDeleteConfirmModal(false);\n  };\n\n  return (\n    <div className=\"flex flex-col h-screen bg-white\">\n      {isLoading ? (\n        // 加载中的显示内容\n        <div className=\"flex items-center justify-center h-screen bg-gray-50\">\n          <div className=\"text-center\">\n            <svg className=\"animate-spin h-10 w-10 text-blue-500 mx-auto mb-4\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <p className=\"text-gray-600\">加载中...</p>\n          </div>\n        </div>\n      ) : (\n        <>\n          {/* Render fullscreen player if in fullscreen mode */}\n          {isFullScreen && currentSlides.length > 0 && (\n            <Suspense fallback={<div className=\"fixed inset-0 bg-black flex justify-center items-center text-white\">全屏播放器加载中...</div>}>\n              <FullScreenPlayer\n                slides={currentSlides}\n                initialIndex={currentFullScreenSlideIndex}\n                onClose={toggleFullScreen}\n              />\n            </Suspense>\n          )}\n          \n          {/* Overlay for content area - Always in DOM, opacity and pointer-events toggled */}\n          <div \n            className={`fixed inset-0 bg-black z-40 md:ml-60 transition-opacity duration-150 ease-in-out bg-opacity-0 pointer-events-none`}\n          />\n    \n          {/* 删除历史抽屉面板 */}\n          \n          <div className=\"flex flex-1 overflow-hidden\">\n            <LeftNav \n              isTaskListOpen={isTaskListOpen} \n              onToggleTaskList={toggleTaskList} \n            />\n            \n            <div className=\"flex flex-1 overflow-hidden\"> \n              <MiddlePane\n                isGenerating={isGenerating}\n                chatMessages={chatMessages}\n                onSendMessage={handleSendMessage}\n                onViewSearchResults={handleViewSearchResults}\n                title={chatTitle}\n                onTitleClick={handleChatTitleChange}\n                onBackClick={handleBackClick}\n                tools={tools}\n                currentStep={currentStep}\n                className=\"w-1/3\"\n              />\n              <RightPane\n                allSlides={currentSlides}\n                currentFocusedSlideIndex={currentSlideIndex}\n                onSetFocusedSlideIndex={handleSetCurrentSlideIndex}\n                onPlaySlideAtIndex={handlePlaySlideAtIndex}\n                isAppEditingMode={isEditingMode}\n                onToggleAppEditMode={handleToggleEditSlide}\n                onTextEditStart={(elementId, textContent, style) => {\n                  setEditingTextElement({ \n                    id: elementId, \n                    content: textContent, \n                    style: style \n                  });\n                }}\n                presentationTitle={currentPresentationTitle}\n                onOpenViewAndExport={handleOpenViewAndExport}\n                outlineContent={outlineContent}\n                slideCode={slideCode}\n                activeStep={activeStep}\n                className=\"w-2/3\"\n                totalSlideCount={totalSlideCount}\n                slideTabIndices={slideTabIndices}\n                onSlideTabChange={handleSlideTabChange}\n              />\n            </div>\n          </div>\n\n          {/* Delete Confirmation Modal */}\n          {showDeleteConfirmModal && (\n            <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100] p-4\">\n              <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full flex flex-col\">\n                <div className=\"p-8 flex items-center justify-center\">\n                  <p className=\"text-gray-700 text-xl font-medium text-center\">\n                    删除后不可恢复，确定吗？\n                  </p>\n                </div>\n                <div className=\"p-4 border-t flex justify-center space-x-6\">\n                  <button\n                    onClick={handleConfirmDelete}\n                    className=\"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 text-sm\"\n                  >\n                    确定\n                  </button>\n                  <button\n                    onClick={handleCancelDelete}\n                    className=\"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm\"\n                  >\n                    取消\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {showSearchResultsModal && (\n            <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30 p-4\">\n              <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] flex flex-col\">\n                <div className=\"flex justify-between items-center p-4 border-b\">\n                  <h3 className=\"text-lg font-semibold\">搜索结果</h3>\n                  <button onClick={() => setShowSearchResultsModal(false)} className=\"text-gray-500 hover:text-gray-700 text-2xl\">×</button>\n                </div>\n                <div className=\"p-4 overflow-y-auto custom-scrollbar\">\n                  {currentSearchResults.map(result => (\n                    <div key={result.id} className=\"mb-4 p-3 border rounded-md hover:shadow-md transition-shadow\">\n                      <a href={result.source} target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-500 hover:underline font-medium flex items-center\">\n                        {result.type === 'pdf' ? <FaFilePdf className=\"mr-2 text-red-500\" /> : <FaGlobe className=\"mr-2 text-blue-400\" />}\n                        {result.title}\n                      </a>\n                      <p className=\"text-sm text-gray-600 mt-1\">{result.snippet}</p>\n                      <p className=\"text-xs text-gray-400 mt-1\">{result.source}</p>\n                    </div>\n                  ))}\n                </div>\n                <div className=\"p-3 border-t text-right\">\n                  <button\n                    onClick={() => setShowSearchResultsModal(false)}\n                    className=\"px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-sm\"\n                  >\n                    关闭\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n          {/* +++++++++++++++ 新增UI组件渲染 +++++++++++++++ */}\n          {/* 上下文菜单 */}\n          {contextMenu && (\n            <div\n              className=\"fixed bg-white shadow-lg rounded-md p-1 border border-gray-200 z-[100]\"\n              style={{ top: contextMenu.y + 10, left: contextMenu.x }}\n              onClick={e => e.stopPropagation()} // 防止点击菜单自身时关闭\n            >\n              <button\n                onClick={handleRewriteClick}\n                className=\"flex items-center w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded\"\n              >\n                <FaFeatherAlt className=\"mr-2\" />\n                重写\n              </button>\n              {/* 在这里可以添加更多按钮，如\"换图\"、\"改样式\"等 */}\n            </div>\n          )}\n\n          {/* 编辑指令输入弹窗 */}\n          {editModal && (\n            <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[110]\">\n              <div className=\"bg-white rounded-lg shadow-xl p-6 w-full max-w-md\">\n                <h3 className=\"text-lg font-medium mb-4\">告诉AI你想如何修改</h3>\n                <textarea\n                  value={editInstruction}\n                  onChange={(e) => setEditInstruction(e.target.value)}\n                  className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-300\"\n                  rows=\"3\"\n                  placeholder=\"例如：这段话写得更活泼一点\"\n                  disabled={isEditingElement}\n                />\n                <div className=\"flex justify-end space-x-3 mt-4\">\n                  <button\n                    onClick={handleCloseEditModal}\n                    className=\"px-4 py-2 bg-gray-200 rounded-md text-sm hover:bg-gray-300\"\n                    disabled={isEditingElement}\n                  >\n                    取消\n                  </button>\n                  <button\n                    onClick={handleSubmitEdit}\n                    className=\"px-4 py-2 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600 disabled:bg-blue-300\"\n                    disabled={isEditingElement || !editInstruction.trim()}\n                  >\n                    {isEditingElement ? '提交中...' : '提交'}\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n          {/* +++++++++++++++++++++++++++++++++++++++++++++++++++ */}\n\n          {editingTextElement && (\n            <Suspense fallback={<div className=\"fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center\">编辑器加载中...</div>}>\n            <InlineTextEditor\n              initialText={editingTextElement.content}\n              initialStyle={editingTextElement.style}\n              position={editingTextElement.position}\n              onSave={handleTextEditSave}\n              onCancel={handleTextEditCancel}\n            />\n            </Suspense>\n          )}\n        </>\n      )}\n    </div>\n  );\n};\n\nfunction App() {\n  return (\n    <BrowserRouter>\n      <Routes>\n        <Route path=\"/\" element={<MainApp />} />\n        <Route path=\"/player\" element={\n          <Suspense fallback={<div className=\"flex justify-center items-center h-screen\">播放器加载中...</div>}>\n            <SlidePlayerView />\n          </Suspense>\n        } />\n        <Route path=\"/chat\" element={\n          <Suspense fallback={<div className=\"flex justify-center items-center h-screen\">加载AI聊天...</div>}>\n            <ChatViewPage />\n          </Suspense>\n        } />\n      </Routes>\n    </BrowserRouter>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,OAAO;AACvF,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACzF,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,SAASC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAEC,YAAY,QAAQ,gBAAgB;AAC1E,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,UAAU,MAAM,gBAAgB;;AAEvC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,gBAAgB,gBAAGpB,IAAI,CAAAqB,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC;AAACC,GAAA,GAAvEF,gBAAgB;AACtB,MAAMG,eAAe,gBAAGvB,IAAI,CAAAwB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,GAAA,GAAhEF,eAAe;AACrB,MAAMG,gBAAgB,gBAAG1B,IAAI,CAAA2B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC;AAACC,GAAA,GAAvEF,gBAAgB;AAEtB,MAAMG,YAAY,gBAAG7B,IAAI,CAAA8B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;;AAE/D;AAAAC,GAAA,GAFMF,YAAY;AAGlB,MAAMG,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,QAAQ,GAAG7B,WAAW,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACjE,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8C,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG/C,QAAQ,CAAC,QAAQ,CAAC;EAClF,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAAC0D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAAC4D,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC8D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkE,2BAA2B,EAAEC,8BAA8B,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC;EACjF,MAAM,CAACoE,aAAa,EAAEC,gBAAgB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACpD,MAAMwE,cAAc,GAAGrE,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM,CAACsE,KAAK,EAAEC,QAAQ,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+E,SAAS,EAAEC,YAAY,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiF,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC,SAAS,CAAC;EACvD,MAAM,CAACmF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACqF,eAAe,EAAEC,kBAAkB,CAAC,GAAGtF,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAACuF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACyF,cAAc,EAAEC,iBAAiB,CAAC,GAAG1F,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2F,eAAe,EAAEC,kBAAkB,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC6F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9F,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM+F,iBAAiB,GAAG5F,MAAM,CAAC,KAAK,CAAC;EACvC,MAAM,CAAC6F,WAAW,EAAEC,cAAc,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkG,SAAS,EAAEC,YAAY,CAAC,GAAGnG,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoG,eAAe,EAAEC,kBAAkB,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEjE;EACA,MAAMwG,gBAAgB,GAAGtG,WAAW,CAAC,YAAY;IAC/C;IACA,MAAMuG,cAAc,GAAG,CAAC,EAAEC,QAAQ,CAACC,iBAAiB,IAAID,QAAQ,CAACE,uBAAuB,IAAIF,QAAQ,CAACG,oBAAoB,IAAIH,QAAQ,CAACI,mBAAmB,CAAC;IAE1J,IAAI;MACF,IAAI,CAACL,cAAc,EAAE;QACnB;QACA;QACA,IAAInD,aAAa,CAACyD,MAAM,GAAG,CAAC,EAAE;UAC5B5C,8BAA8B,CAACX,iBAAiB,CAAC;QACnD;QACA;QACA,MAAMkD,QAAQ,CAACM,eAAe,CAACC,iBAAiB,CAAC,CAAC;QAClD;MACF,CAAC,MAAM;QACL;QACA,MAAMP,QAAQ,CAACQ,cAAc,CAAC,CAAC;QAC/B;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B;MACAlD,eAAe,CAACoD,IAAI,IAAI,CAACA,IAAI,CAAC;IAChC;EACF,CAAC,EAAE,CAAC7D,iBAAiB,EAAEF,aAAa,CAACyD,MAAM,CAAC,CAAC;;EAE7C;EACA,MAAMO,wBAAwB,GAAGpH,WAAW,CAAC,MAAM;IACjD;IACA,IAAIkD,YAAY,CAAC2D,MAAM,GAAG,CAAC,IAAIlB,gBAAgB,EAAE;MAAE;MACjD,MAAM0B,cAAc,GAAG;QACrBC,EAAE,EAAE3B,gBAAgB;QAAE;QACtB4B,KAAK,EAAEzE,SAAS,IAAI,OAAO6C,gBAAgB,CAAC6B,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;QAAE;QAC1DC,QAAQ,EAAEvE,YAAY;QACtBwE,MAAM,EAAEtE,aAAa;QACrBuE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,UAAU,EAAEnC;MACd,CAAC;MAEDtB,cAAc,CAAC0D,WAAW,IAAI;QAC5B,MAAMC,oBAAoB,GAAGD,WAAW,CAACE,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACZ,EAAE,KAAK3B,gBAAgB,CAAC;QACxF,IAAIwC,cAAc;QAClB,IAAIH,oBAAoB,IAAI,CAAC,EAAE;UAC3BG,cAAc,GAAG,CAAC,GAAGJ,WAAW,CAAC;UACjCI,cAAc,CAACH,oBAAoB,CAAC,GAAGX,cAAc,CAAC,CAAC;QACzD,CAAC,MAAM;UACLc,cAAc,GAAG,CAACd,cAAc,EAAE,GAAGU,WAAW,CAAC,CAAC,CAAC;QACvD;QACA;QACA;QACA,IAAI;UACFK,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACJ,cAAc,CAAC,CAAC;QACrE,CAAC,CAAC,OAAOlB,KAAK,EAAE;UACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACtD;QACA,OAAOkB,cAAc;MACvB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACjF,YAAY,EAAEE,aAAa,EAAEN,SAAS,EAAE6C,gBAAgB,EAAEtB,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEhF;EACA,MAAMmE,qBAAqB,GAAGxI,WAAW,CAAC,MAAM;IAC9C6F,iBAAiB,CAAC4C,OAAO,GAAG,IAAI,CAAC,CAAC;;IAElC;IACAtF,eAAe,CAAC,EAAE,CAAC;IACnBE,gBAAgB,CAAC,EAAE,CAAC;IACpBN,YAAY,CAAC,QAAQ,CAAC;IACtBF,2BAA2B,CAAC,QAAQ,CAAC;IACrCI,eAAe,CAAC,KAAK,CAAC;IACtByB,cAAc,CAAC,IAAI,CAAC;IACpBF,QAAQ,CAAC,EAAE,CAAC;IACZI,iBAAiB,CAAC,EAAE,CAAC;IACrBE,YAAY,CAAC,EAAE,CAAC;IAChBE,aAAa,CAAC,SAAS,CAAC;;IAExB;IACAY,mBAAmB,CAAC,IAAI,CAAC;IACzBwC,YAAY,CAACM,UAAU,CAAC,kBAAkB,CAAC;;IAE3C;IACAtG,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC,EAAE,CACDe,eAAe,EAAEE,gBAAgB,EAAEN,YAAY,EAAEF,2BAA2B,EAC5EI,eAAe,EAAEyB,cAAc,EAAEF,QAAQ,EAAEI,iBAAiB,EAAEE,YAAY,EAC1EE,aAAa,EAAEY,mBAAmB,EAAExD,QAAQ,CAC7C,CAAC;;EAEF;EACA,MAAMuG,kBAAkB,GAAG3I,WAAW,CAAC,YAAY;IACjDyC,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACF,MAAMmG,aAAa,GAAG,MAAM3H,UAAU,CAAC4H,kBAAkB,CAAC,CAAC;MAC3D3B,OAAO,CAAC4B,GAAG,CAAC,6CAA6C,EAAEF,aAAa,CAAC;MAEzE,MAAMG,kBAAkB,GAAGH,aAAa,CAACI,GAAG,CAACC,IAAI,KAAK;QACpD3B,EAAE,EAAE2B,IAAI,CAAC3B,EAAE;QAAE;QACbC,KAAK,EAAE0B,IAAI,CAAC1B,KAAK,IAAI,OAAO0B,IAAI,CAAC3B,EAAE,GAAG2B,IAAI,CAAC3B,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG;QAAE;QACnEG,SAAS,EAAEsB,IAAI,CAACC,aAAa,IAAID,IAAI,CAACE,UAAU;QAChDrB,UAAU,EAAEmB,IAAI,CAAC3B,EAAE;QAAE;QACrB8B,SAAS,EAAE,IAAI,CAAC;MAClB,CAAC,CAAC,CAAC;MAEH/E,cAAc,CAAC0E,kBAAkB,CAAC;;MAElC;MACA,MAAMM,gBAAgB,GAAG,IAAIC,GAAG,CAACV,aAAa,CAACI,GAAG,CAACO,CAAC,IAAIA,CAAC,CAACzB,UAAU,CAAC,CAAC;MACtE,MAAM0B,QAAQ,GAAGpB,YAAY,CAACqB,OAAO,CAAC,kBAAkB,CAAC;MACzD,IAAID,QAAQ,IAAI,CAACH,gBAAgB,CAACK,GAAG,CAACF,QAAQ,CAAC,EAAE;QAC/CpB,YAAY,CAACM,UAAU,CAAC,kBAAkB,CAAC;MAC7C;IAEF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC5C,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC,SAAS;MACR5B,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,CAAC4B,cAAc,EAAE5B,mBAAmB,CAAC,CAAC;;EAEzC;EACA,MAAMkH,kBAAkB,GAAG3J,WAAW,CAAC,MAAO4J,SAAS,IAAK;IAC1D;IACA,IAAI,CAACA,SAAS,IAAIA,SAAS,KAAK,WAAW,EAAE;MAC3C1C,OAAO,CAAC2C,IAAI,CAAC,oBAAoB,EAAED,SAAS,CAAC;MAC7CrH,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;MACrBiG,qBAAqB,CAAC,CAAC,CAAC,CAAC;MACzB;IACF;IAEAjG,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMuH,OAAO,GAAG,MAAM7I,UAAU,CAAC8I,iBAAiB,CAACH,SAAS,CAAC;MAC7D1C,OAAO,CAAC4B,GAAG,CAAC,wCAAwCc,SAAS,GAAG,EAAEE,OAAO,CAAC;;MAE1E;MACA,IAAIE,eAAe,GAAGF,OAAO,CAACG,YAAY,IAAI,EAAE;;MAEhD;MACA,IAAID,eAAe,CAACnD,MAAM,GAAG,CAAC,EAAE;QAC9BmD,eAAe,GAAGA,eAAe,CAAChB,GAAG,CAAC,CAACkB,GAAG,EAAEC,KAAK,MAAM;UACrD,GAAGD,GAAG;UACN5C,EAAE,EAAE4C,GAAG,CAAC5C,EAAE,IAAI,OAAO6C,KAAK,IAAIvC,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAE;UAC1CC,MAAM,EAAEH,GAAG,CAACG,MAAM,KAAKF,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC;UACvDG,IAAI,EAAEJ,GAAG,CAACI,IAAI,IAAIJ,GAAG,CAACK,OAAO,IAAI,KAAK;UACtC5C,SAAS,EAAEuC,GAAG,CAACvC,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACrD,CAAC,CAAC,CAAC;QACHX,OAAO,CAAC4B,GAAG,CAAC,sBAAsB,EAAEkB,eAAe,CAAC;MACtD,CAAC,MAAM;QACL;QACAA,eAAe,GAAG,CAAC;UACjB1C,EAAE,EAAE,UAAUM,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAE;UAC1BC,MAAM,EAAE,QAAQ;UAChBC,IAAI,EAAE,gBAAgB;UACtB3C,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC,CAAC;MACJ;MAEA1E,eAAe,CAAC6G,eAAe,CAAC;MAChC,MAAMQ,YAAY,GAAGV,OAAO,CAACvC,KAAK,IAAI,OAAOqC,SAAS,CAACpC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;MACnEzE,YAAY,CAACyH,YAAY,CAAC;MAC1B3H,2BAA2B,CAAC2H,YAAY,CAAC;MACzC5E,mBAAmB,CAACkE,OAAO,CAAChC,UAAU,CAAC;;MAEvC;MACAM,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEyB,OAAO,CAAChC,UAAU,CAAC;;MAE5D;MACA,IAAI,CAACzF,QAAQ,CAACoI,MAAM,CAACC,QAAQ,CAAC,aAAaZ,OAAO,CAAChC,UAAU,EAAE,CAAC,EAAE;QAChE1F,QAAQ,CAAC,eAAe0H,OAAO,CAAChC,UAAU,EAAE,EAAE;UAAE6C,OAAO,EAAE;QAAK,CAAC,CAAC;MAClE;MAEAvF,kBAAkB,CAAC0E,OAAO,CAACc,oBAAoB,IAAI,CAACd,OAAO,CAACpC,MAAM,IAAI,EAAE,EAAEb,MAAM,CAAC;;MAEjF;MACA,IAAIiD,OAAO,CAACpC,MAAM,IAAIoC,OAAO,CAACpC,MAAM,CAACb,MAAM,GAAG,CAAC,EAAE;QAC/C,MAAMgE,kBAAkB,GAAGf,OAAO,CAACpC,MAAM,CAACsB,GAAG,CAAC8B,CAAC,KAAK;UAChDxD,EAAE,EAAEwD,CAAC,CAACxD,EAAE;UACRyD,IAAI,EAAE,EAAE;UACRC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAEH,CAAC,CAACI,YAAY,GAAG,CAAC;UACzB3D,KAAK,EAAE,OAAOuD,CAAC,CAACI,YAAY,EAAE;UAC9B5I,SAAS,EAAE;QACf,CAAC,CAAC,CAAC;QACHe,gBAAgB,CAACwH,kBAAkB,CAAC;QAEpC,MAAMM,oBAAoB,GAAGrB,OAAO,CAACpC,MAAM,CAACsB,GAAG,CAACoC,SAAS,IACrDnK,UAAU,CAACoK,eAAe,CAACD,SAAS,CAAC9D,EAAE,CAAC,CAACgE,KAAK,CAAC,OAAO;UAAEhE,EAAE,EAAE8D,SAAS,CAAC9D,EAAE;UAAEL,KAAK,EAAE;QAAK,CAAC,CAAC,CAC5F,CAAC;QACD,MAAMsE,cAAc,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACN,oBAAoB,CAAC;QAE9D9H,gBAAgB,CAACD,aAAa,IAAIA,aAAa,CAAC4F,GAAG,CAAC0C,SAAS,IAAI;UAC7D,MAAMC,QAAQ,GAAGJ,cAAc,CAACK,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACvE,EAAE,KAAKoE,SAAS,CAACpE,EAAE,CAAC;UAClE,IAAIqE,QAAQ,IAAI,CAACA,QAAQ,CAAC1E,KAAK,EAAE;YAC7B,OAAO;cAAE,GAAGyE,SAAS;cAAEX,IAAI,EAAEY,QAAQ,CAACZ,IAAI;cAAEC,IAAI,EAAEW,QAAQ,CAACZ,IAAI;cAAEzI,SAAS,EAAE;YAAM,CAAC;UACvF;UACA,OAAO;YAAE,GAAGoJ,SAAS;YAAEX,IAAI,EAAE,mBAAmB;YAAEC,IAAI,EAAE,cAAc;YAAE1I,SAAS,EAAE;UAAM,CAAC;QAC9F,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLe,gBAAgB,CAAC,EAAE,CAAC;MACtB;IAEF,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,QAAQ2C,SAAS,QAAQ,EAAE3C,KAAK,CAAC;MAC/C,IAAIA,KAAK,CAAC6E,OAAO,KAAK7E,KAAK,CAAC6E,OAAO,CAACpB,QAAQ,CAAC,WAAW,CAAC,IAAIzD,KAAK,CAAC6E,OAAO,CAACpB,QAAQ,CAAC,YAAY,CAAC,CAAC,EAAE;QAClG;QACA,IAAItC,YAAY,CAACqB,OAAO,CAAC,kBAAkB,CAAC,KAAKG,SAAS,EAAE;UAC1DxB,YAAY,CAACM,UAAU,CAAC,kBAAkB,CAAC;QAC7C;QACAF,qBAAqB,CAAC,CAAC;MACzB;IACF,CAAC,SAAS;MACRjG,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CACDA,YAAY,EAAEiG,qBAAqB,EACnCrF,eAAe,EAAEJ,YAAY,EAAEF,2BAA2B,EAAE+C,mBAAmB,EAC/EvD,QAAQ,CAACoI,MAAM,EAAErI,QAAQ,EAAEgD,kBAAkB,EAAE/B,gBAAgB,CAChE,CAAC;;EAEF;EACAtD,SAAS,CAAC,MAAM;IACd,MAAMgM,MAAM,GAAG,IAAIC,eAAe,CAAC3J,QAAQ,CAACoI,MAAM,CAAC;IACnD,MAAMwB,gBAAgB,GAAGF,MAAM,CAACG,GAAG,CAAC,WAAW,CAAC;IAChD,MAAMC,aAAa,GAAGJ,MAAM,CAACG,GAAG,CAAC,QAAQ,CAAC;;IAE1C;IACA,MAAME,QAAQ,GAAGH,gBAAgB,IAAIE,aAAa;IAElD,IAAIC,QAAQ,IAAIA,QAAQ,KAAKzG,gBAAgB,EAAE;MAC7CuB,OAAO,CAAC4B,GAAG,CAAC,iBAAiBsD,QAAQ,EAAE,CAAC;MACxCzC,kBAAkB,CAACyC,QAAQ,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC/J,QAAQ,CAACoI,MAAM,EAAE9E,gBAAgB,EAAEgE,kBAAkB,CAAC,CAAC;;EAE3D;EACA5J,SAAS,CAAC,MAAM;IACd,MAAMsM,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC9J,YAAY,CAAC,IAAI,CAAC;;MAElB;MACA,MAAMoG,kBAAkB,CAAC,CAAC;;MAE1B;MACA,MAAMoD,MAAM,GAAG,IAAIC,eAAe,CAAC3J,QAAQ,CAACoI,MAAM,CAAC;MACnD,IAAI6B,eAAe,GAAGP,MAAM,CAACG,GAAG,CAAC,WAAW,CAAC;;MAE7C;MACA,IAAI,CAACI,eAAe,IAAIA,eAAe,KAAK,WAAW,EAAE;QACvDA,eAAe,GAAGlE,YAAY,CAACqB,OAAO,CAAC,kBAAkB,CAAC;MAC5D;MAEA,IAAI6C,eAAe,IAAIA,eAAe,KAAK,WAAW,EAAE;QACtD;QACApF,OAAO,CAAC4B,GAAG,CAAC,4CAA4CwD,eAAe,EAAE,CAAC;QAC1E,MAAM3C,kBAAkB,CAAC2C,eAAe,CAAC;MAC3C,CAAC,MAAM;QACL;QACA;QACA,IAAIzG,iBAAiB,CAAC4C,OAAO,EAAE;UAC7BvB,OAAO,CAAC4B,GAAG,CAAC,qEAAqE,CAAC;UAClFjD,iBAAiB,CAAC4C,OAAO,GAAG,KAAK;UACjClG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;UACrB,OAAO,CAAC;QACV;;QAEA;QACA,MAAMgK,OAAO,GAAG,MAAMtL,UAAU,CAAC4H,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACvD,IAAI0D,OAAO,IAAIA,OAAO,CAAC1F,MAAM,GAAG,CAAC,EAAE;UACjC,MAAM2F,aAAa,GAAGD,OAAO,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI/E,IAAI,CAAC+E,CAAC,CAACzD,aAAa,CAAC,GAAG,IAAItB,IAAI,CAAC8E,CAAC,CAACxD,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;UACtG;UACA,IAAIsD,aAAa,IAAIA,aAAa,CAAC1E,UAAU,EAAE;YAC7CZ,OAAO,CAAC4B,GAAG,CAAC,2CAA2C0D,aAAa,CAAC1E,UAAU,EAAE,CAAC;YAClF,MAAM6B,kBAAkB,CAAC6C,aAAa,CAAC1E,UAAU,CAAC;UACpD,CAAC,MAAM;YACL;YACAZ,OAAO,CAAC4B,GAAG,CAAC,sEAAsE,CAAC;YACnFN,qBAAqB,CAAC,CAAC;UACzB;QACF,CAAC,MAAM;UACL;UACAtB,OAAO,CAAC4B,GAAG,CAAC,iEAAiE,CAAC;UAC9EN,qBAAqB,CAAC,CAAC;QACzB;MACF;MACAjG,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC;IAED8J,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAChK,QAAQ,CAACoI,MAAM,EAAE9B,kBAAkB,EAAEgB,kBAAkB,EAAEnB,qBAAqB,EAAEjG,YAAY,CAAC,CAAC;;EAElG;EACAxC,SAAS,CAAC,MAAM;IACd;IACAmH,OAAO,CAAC4B,GAAG,CAAC,0BAA0B,CAAC;;IAEvC;IACA,MAAM8D,kBAAkB,GAAGA,CAAA,KAAM;MAC/B;MACA,IAAIjH,gBAAgB,EAAE;QACpByC,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAE1C,gBAAgB,CAAC;MAC5D;IACF,CAAC;;IAED;IACAkH,MAAM,CAACC,gBAAgB,CAAC,cAAc,EAAEF,kBAAkB,CAAC;;IAE3D;IACA,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,cAAc,EAAEH,kBAAkB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAACjH,gBAAgB,CAAC,CAAC,CAAC,CAAC;;EAExB;EACA5F,SAAS,CAAC,MAAM;IACd;IACA,IAAImD,YAAY,CAAC2D,MAAM,GAAG,CAAC,IAAIzD,aAAa,CAACyD,MAAM,GAAG,CAAC,EAAE;MACvD,IAAI;QAAA,IAAAmG,kBAAA;QACF5E,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEC,IAAI,CAACC,SAAS,CAACrF,YAAY,CAAC,CAAC;QAClEkF,YAAY,CAACC,OAAO,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACnF,aAAa,CAAC,CAAC;QACpEgF,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEvF,SAAS,CAAC;QAC5CsF,YAAY,CAACC,OAAO,CAAC,mBAAmB,EAAEzF,wBAAwB,CAAC;;QAEnE;QACA,MAAMqK,qBAAqB,IAAAD,kBAAA,GAAG9J,YAAY,CAAC0I,IAAI,CAAC1B,GAAG,IAAIA,GAAG,CAACpC,UAAU,CAAC,cAAAkF,kBAAA,uBAAxCA,kBAAA,CAA0ClF,UAAU;QAClF,IAAImF,qBAAqB,EAAE;UACzBrH,mBAAmB,CAACqH,qBAAqB,CAAC;UAC1C7E,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAE4E,qBAAqB,CAAC;QACjE;MACF,CAAC,CAAC,OAAOhG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C;IACF;EACF,CAAC,EAAE,CAAC/D,YAAY,EAAEE,aAAa,EAAEN,SAAS,EAAEF,wBAAwB,CAAC,CAAC;EAEtE,MAAMsK,cAAc,GAAGA,CAAA,KAAMvK,iBAAiB,CAAC,CAACD,cAAc,CAAC;EAE/D,MAAMyK,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,MAAMC,aAAa,GAAGvK,SAAS,CAAC,CAAC;;IAEjC;IACAC,YAAY,CAACqK,QAAQ,CAAC;IACtBvK,2BAA2B,CAACuK,QAAQ,CAAC;IAErC,IAAIzH,gBAAgB,EAAE;MACpB,IAAI;QACF,MAAM1E,UAAU,CAACqM,aAAa,CAAC3H,gBAAgB,EAAE;UAAE4B,KAAK,EAAE6F;QAAS,CAAC,CAAC;QACrElG,OAAO,CAAC4B,GAAG,CAAC,YAAYnD,gBAAgB,kBAAkByH,QAAQ,EAAE,CAAC;QAErE/I,cAAc,CAAC8C,IAAI,IAAIA,IAAI,CAAC6B,GAAG,CAACd,IAAI,IAClCA,IAAI,CAACJ,UAAU,KAAKnC,gBAAgB,GAAG;UAAE,GAAGuC,IAAI;UAAEX,KAAK,EAAE6F;QAAS,CAAC,GAAGlF,IACxE,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOjB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCsG,KAAK,CAAC,WAAWtG,KAAK,CAAC6E,OAAO,EAAE,CAAC;;QAEjC;QACA;QACA/I,YAAY,CAACsK,aAAa,CAAC;QAC3BxK,2BAA2B,CAACwK,aAAa,CAAC;MAC5C;IACF;EACF,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIxK,YAAY,EAAE;MAChB;MACA,IAAIsB,cAAc,CAACmE,OAAO,EAAE;QAC1BnE,cAAc,CAACmE,OAAO,CAACgF,KAAK,CAAC,CAAC;QAC9BnJ,cAAc,CAACmE,OAAO,GAAG,IAAI;MAC/B;MACAxF,eAAe,CAAC,KAAK,CAAC;MACtBE,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAChCG,EAAE,EAAEM,IAAI,CAACwC,GAAG,CAAC,CAAC;QACdsD,IAAI,EAAE,UAAU;QAChBpD,IAAI,EAAE,QAAQ;QACdD,MAAM,EAAE,QAAQ;QAAE;QAClBsD,IAAI,EAAE,GAAG,CAAC;MACZ,CAAC,CAAC,CAAC;MACH;IACF;;IAEA;IACAnF,qBAAqB,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC;EAED,MAAMoF,mBAAmB,GAAIC,WAAW,IAAK;IAC3C;IACA,IAAI,CAACA,WAAW,IAAI,CAACA,WAAW,CAAC/F,UAAU,EAAE;MACzCZ,OAAO,CAACD,KAAK,CAAC,cAAc,EAAE4G,WAAW,CAAC;MAC1C,OAAO,CAAC;IACZ;IACA3G,OAAO,CAAC4B,GAAG,CAAC,SAAS,EAAE+E,WAAW,CAAC;IACnC3I,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;;IAE7B;IACA9C,QAAQ,CAAC,eAAeyL,WAAW,CAAC/F,UAAU,EAAE,CAAC;EACnD,CAAC;EAED,MAAMgG,mBAAmB,GAAG,MAAAA,CAAOC,aAAa,EAAEC,iBAAiB,KAAK;IAAA,IAAAC,iBAAA;IACtE;IACA,IAAIF,aAAa,KAAK,iBAAiB,EAAE;;IAEzC;IACAvI,iBAAiB,CAAC;MAChB8B,EAAE,EAAEyG,aAAa;MAAE;MACnBnE,SAAS,EAAEoE,iBAAiB;MAAE;MAC9BzG,KAAK,EAAE,EAAA0G,iBAAA,GAAA7J,WAAW,CAACwH,IAAI,CAAC1D,IAAI,IAAIA,IAAI,CAACZ,EAAE,KAAKyG,aAAa,CAAC,cAAAE,iBAAA,uBAAnDA,iBAAA,CAAqD1G,KAAK,KAAI;IACvE,CAAC,CAAC;IACFjC,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAM4I,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAAC3I,cAAc,IAAI,CAACA,cAAc,CAACqE,SAAS,EAAE;MAChDtE,yBAAyB,CAAC,KAAK,CAAC;MAChCE,iBAAiB,CAAC,IAAI,CAAC;MACvB;IACF;IAEA,MAAM;MAAE8B,EAAE,EAAE6G,kBAAkB;MAAEvE,SAAS,EAAEwE;IAAgB,CAAC,GAAG7I,cAAc;IAE7E,IAAI;MACF;MACA,MAAM8I,QAAQ,GAAG,MAAMpN,UAAU,CAACqN,aAAa,CAACF,eAAe,CAAC;MAChElH,OAAO,CAAC4B,GAAG,CAAC,MAAMsF,eAAe,QAAQ,EAAEC,QAAQ,CAAC;;MAEpD;MACA,MAAMlG,cAAc,GAAG/D,WAAW,CAACmK,MAAM,CAACrG,IAAI,IAAIA,IAAI,CAACZ,EAAE,KAAK6G,kBAAkB,CAAC;MACjF9J,cAAc,CAAC8D,cAAc,CAAC;;MAE9B;MACA,IAAIxC,gBAAgB,KAAKyI,eAAe,EAAE;QACxC5F,qBAAqB,CAAC,CAAC;MACzB;MACAtB,OAAO,CAAC4B,GAAG,CAAC,MAAMsF,eAAe,cAAcD,kBAAkB,UAAU,CAAC;IAE9E,CAAC,CAAC,OAAOlH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,QAAQmH,eAAe,OAAO,EAAEnH,KAAK,CAAC;MACpDsG,KAAK,CAAC,WAAWtG,KAAK,CAAC6E,OAAO,EAAE,CAAC;IACnC,CAAC,SAAS;MACRxG,yBAAyB,CAAC,KAAK,CAAC;MAChCE,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAED,MAAMgJ,qBAAqB,GAAGA,CAAA,KAAM;IAClCrK,gBAAgB,CAAC,CAACD,aAAa,CAAC;IAChCT,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE,MAAMgL,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,KAAK;IAChD,IAAI,CAACnL,kBAAkB,EAAE;IACzB,MAAM;MAAEoL,OAAO;MAAEC;IAAU,CAAC,GAAGrL,kBAAkB;IACjDH,gBAAgB,CAACyL,UAAU,IACzBA,UAAU,CAAC9F,GAAG,CAAC+F,KAAK,IAAI;MACtB,IAAIA,KAAK,CAACzH,EAAE,KAAKsH,OAAO,EAAE;QACxB,MAAMI,OAAO,GAAGxI,QAAQ,CAACyI,aAAa,CAAC,KAAK,CAAC;QAC7CD,OAAO,CAACE,SAAS,GAAGH,KAAK,CAAChE,IAAI;QAC9B,MAAMoE,aAAa,GAAGH,OAAO,CAACI,aAAa,CAAC,sBAAsBP,SAAS,IAAI,CAAC;QAChF,IAAIM,aAAa,EAAE;UACjBA,aAAa,CAACE,SAAS,GAAGX,OAAO;UACjCY,MAAM,CAACC,IAAI,CAACZ,QAAQ,CAAC,CAACa,OAAO,CAACC,GAAG,IAAI;YACnCN,aAAa,CAACO,KAAK,CAACD,GAAG,CAAC,GAAGd,QAAQ,CAACc,GAAG,CAAC;UAC1C,CAAC,CAAC;QACJ;QACA,OAAO;UAAE,GAAGV,KAAK;UAAEhE,IAAI,EAAEiE,OAAO,CAACE,SAAS;UAAElE,IAAI,EAAEgE,OAAO,CAACE;QAAU,CAAC;MACvE;MACA,OAAOH,KAAK;IACd,CAAC,CACH,CAAC;IACDtL,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMkM,oBAAoB,GAAGA,CAAA,KAAMlM,qBAAqB,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAMmM,kBAAkB,GAAIC,CAAC,IAAK;IAChCA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB,IAAIhK,WAAW,EAAE;MACfG,YAAY,CAAC;QAAE2I,OAAO,EAAE9I,WAAW,CAAC8I,OAAO;QAAEC,SAAS,EAAE/I,WAAW,CAAC+I;MAAU,CAAC,CAAC;MAChF9I,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;IACxB;EACF,CAAC;EAED,MAAMgK,oBAAoB,GAAGA,CAAA,KAAM;IACjC9J,YAAY,CAAC,IAAI,CAAC;IAClBE,kBAAkB,CAAC,EAAE,CAAC;IACtBE,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAM2J,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC9J,eAAe,CAAC+J,IAAI,CAAC,CAAC,IAAI,CAACjK,SAAS,EAAE;IAE3CK,mBAAmB,CAAC,IAAI,CAAC;IACzB,MAAM;MAAEuI,OAAO;MAAEC;IAAU,CAAC,GAAG7I,SAAS;IAExC,IAAI;MACF,MAAMkK,MAAM,GAAG,MAAMjP,UAAU,CAACkP,gBAAgB,CAACvB,OAAO,EAAE,sBAAsBC,SAAS,IAAI,EAAE3I,eAAe,CAAC;MAC/G,IAAIgK,MAAM,IAAIA,MAAM,CAACnF,IAAI,EAAE;QACzB;QACA1H,gBAAgB,CAACyL,UAAU,IACzBA,UAAU,CAAC9F,GAAG,CAAC+F,KAAK,IAClBA,KAAK,CAACzH,EAAE,KAAKsH,OAAO,GAAG;UAAE,GAAGG,KAAK;UAAEhE,IAAI,EAAEmF,MAAM,CAACnF,IAAI;UAAEC,IAAI,EAAEkF,MAAM,CAACnF;QAAK,CAAC,GAAGgE,KAC9E,CACF,CAAC;MACH;IACF,CAAC,CAAC,OAAO9H,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BsG,KAAK,CAAC,SAAStG,KAAK,CAAC6E,OAAO,EAAE,CAAC;IACjC,CAAC,SAAS;MACRiE,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC;EACD;;EAEA;EACA,MAAMK,uBAAuB,GAAGA,CAAA,KAAM;IACpChO,QAAQ,CAAC,SAAS,EAAE;MAClBiO,KAAK,EAAE;QACL3I,MAAM,EAAEtE,aAAa;QACrBkN,YAAY,EAAE,CAAC;QACfC,iBAAiB,EAAE3N;MACrB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA7C,SAAS,CAAC,MAAM;IACd,MAAMyQ,2BAA2B,GAAIC,KAAK,IAAK;MAC7C,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAAChD,IAAI,KAAK,oBAAoB,EAAE;QAC1D;QACA,IAAI+C,KAAK,CAACC,IAAI,CAACC,OAAO,IAAIF,KAAK,CAACC,IAAI,CAACC,OAAO,CAACL,YAAY,KAAKM,SAAS,EAAE;UACvE3M,8BAA8B,CAACwM,KAAK,CAACC,IAAI,CAACC,OAAO,CAACL,YAAY,CAAC;QACjE;QACA;QACAhK,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC;IAEDuG,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAE0D,2BAA2B,CAAC;IAC/D,OAAO,MAAM;MACX3D,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEyD,2BAA2B,CAAC;IACpE,CAAC;EACH,CAAC,EAAE,CAAClK,gBAAgB,CAAC,CAAC;;EAEtB;EACAvG,SAAS,CAAC,MAAM;IACd,MAAM8Q,mBAAmB,GAAIJ,KAAK,IAAK;MACrC,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAAChD,IAAI,KAAK,iBAAiB,IAAIxJ,aAAa,EAAE;QACxE,MAAM;UAAE0K,OAAO;UAAEC,SAAS;UAAEiC;QAAc,CAAC,GAAGL,KAAK,CAACC,IAAI,CAACC,OAAO;;QAEhE;QACA,MAAMI,MAAM,GAAGvK,QAAQ,CAAC4I,aAAa,CAAC,kBAAkBR,OAAO,SAAS,CAAC;QACzE,IAAImC,MAAM,EAAE;UACV,MAAMC,UAAU,GAAGD,MAAM,CAACE,qBAAqB,CAAC,CAAC;UACjD,MAAMC,KAAK,GAAGF,UAAU,CAACG,KAAK,GAAG,IAAI,CAAC,CAAC;;UAEvC,MAAMC,SAAS,GAAGJ,UAAU,CAACK,IAAI,GAAIP,aAAa,CAACQ,CAAC,GAAGJ,KAAM,GAAGrE,MAAM,CAAC0E,OAAO;UAC9E,MAAMC,SAAS,GAAGR,UAAU,CAACS,GAAG,GAAIX,aAAa,CAACY,CAAC,GAAGR,KAAM,GAAGrE,MAAM,CAAC8E,OAAO;UAE7E5L,cAAc,CAAC;YACbuL,CAAC,EAAEF,SAAS;YACZM,CAAC,EAAEF,SAAS;YACZ5C,OAAO;YACPC;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC;IAEDhC,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAE+D,mBAAmB,CAAC;IACvD;IACA,MAAMe,SAAS,GAAGA,CAAA,KAAM7L,cAAc,CAAC,IAAI,CAAC;IAC5C8G,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAE8E,SAAS,CAAC;IAE3C,OAAO,MAAM;MACX/E,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAE8D,mBAAmB,CAAC;MAC1DhE,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAE6E,SAAS,CAAC;IAChD,CAAC;EACH,CAAC,EAAE,CAAC1N,aAAa,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEAnE,SAAS,CAAC,MAAM;IACd;IACA,MAAM8R,sBAAsB,GAAGA,CAAA,KAAM;MACnC,MAAMC,qBAAqB,GAAG,CAAC,EAAEtL,QAAQ,CAACC,iBAAiB,IAAID,QAAQ,CAACE,uBAAuB,IAAIF,QAAQ,CAACuL,oBAAoB,IAAIvL,QAAQ,CAACI,mBAAmB,CAAC;MACjK7C,eAAe,CAAC+N,qBAAqB,CAAC;IACxC,CAAC;IAEDtL,QAAQ,CAACsG,gBAAgB,CAAC,kBAAkB,EAAE+E,sBAAsB,CAAC;IACrErL,QAAQ,CAACsG,gBAAgB,CAAC,wBAAwB,EAAE+E,sBAAsB,CAAC;IAC3ErL,QAAQ,CAACsG,gBAAgB,CAAC,qBAAqB,EAAE+E,sBAAsB,CAAC;IACxErL,QAAQ,CAACsG,gBAAgB,CAAC,oBAAoB,EAAE+E,sBAAsB,CAAC;IAEvE,OAAO,MAAM;MACXrL,QAAQ,CAACuG,mBAAmB,CAAC,kBAAkB,EAAE8E,sBAAsB,CAAC;MACxErL,QAAQ,CAACuG,mBAAmB,CAAC,wBAAwB,EAAE8E,sBAAsB,CAAC;MAC9ErL,QAAQ,CAACuG,mBAAmB,CAAC,qBAAqB,EAAE8E,sBAAsB,CAAC;MAC3ErL,QAAQ,CAACuG,mBAAmB,CAAC,oBAAoB,EAAE8E,sBAAsB,CAAC;IAC5E,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,0BAA0B,GAAI7H,KAAK,IAAK;IAC5C5G,oBAAoB,CAAC4G,KAAK,CAAC;EAC7B,CAAC;EAED,MAAM8H,oBAAoB,GAAGA,CAACC,UAAU,EAAEC,QAAQ,KAAK;IACrDzM,kBAAkB,CAACyB,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAAC+K,UAAU,GAAGC;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAIjI,KAAK,IAAK;IACxClG,8BAA8B,CAACkG,KAAK,CAAC;IACrC7D,gBAAgB,CAAC,CAAC;EACpB,CAAC;EAED,MAAM+L,iBAAiB,GAAG,MAAOC,WAAW,IAAK;IAC/C;IACA,IAAIC,WAAW;MAAEC,KAAK,GAAG,EAAE;IAE3B,IAAI,OAAOF,WAAW,KAAK,QAAQ,EAAE;MACnCC,WAAW,GAAGD,WAAW;IAC3B,CAAC,MAAM,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,IAAI,EAAE;MAClEC,WAAW,GAAGD,WAAW,CAACxG,OAAO,IAAI,EAAE;MACvC0G,KAAK,GAAGF,WAAW,CAACE,KAAK,IAAI,EAAE;IACjC,CAAC,MAAM;MACLD,WAAW,GAAG,EAAE;IAClB;IAEA,IAAI,CAACA,WAAW,CAACtC,IAAI,CAAC,CAAC,IAAIjN,YAAY,EAAE;;IAEzC;IACA,MAAMyP,SAAS,GAAG,YAAY7K,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAE;;IAE1C;IACA,MAAMsI,WAAW,GAAG;MAClBpL,EAAE,EAAEmL,SAAS;MACb/E,IAAI,EAAE,MAAM;MACZpD,IAAI,EAAEiI,WAAW;MACjBC,KAAK,EAAEA,KAAK,CAAC3L,MAAM,GAAG,CAAC,GAAG2L,KAAK,GAAG5B,SAAS,CAAE;IAC/C,CAAC;;IAED;IACA,MAAM+B,iBAAiB,GAAGJ,WAAW,CAACK,WAAW,CAAC,CAAC,CAAClI,QAAQ,CAAC,IAAI,CAAC,IACzC6H,WAAW,CAACK,WAAW,CAAC,CAAC,CAAClI,QAAQ,CAAC,UAAU,CAAC;;IAEvE;IACA,IAAI,CAACiI,iBAAiB,EAAE;MACtB;MACAxP,eAAe,CAAC,CAACuP,WAAW,CAAC,CAAC,CAAC,CAAC;MAChCrP,gBAAgB,CAAC,EAAE,CAAC;MACpB+B,kBAAkB,CAAC,CAAC,CAAC;MACrBQ,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;;MAE3B;MACFlB,cAAc,CAAC,IAAI,CAAC;MAClBF,QAAQ,CAAC,EAAE,CAAC;MACdI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,YAAY,CAAC,EAAE,CAAC;MAChBE,aAAa,CAAC,SAAS,CAAC;IACxB,CAAC,MAAM;MACL;MACF7B,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEuL,WAAW,CAAC,CAAC;IAC/C;IAEA,IAAIG,aAAa,GAAG,CAAC;IACrB,MAAMC,mBAAmB,GAAG,CAAC;IAE7B,MAAMC,iBAAiB,GAAG,MAAOC,cAAc,IAAK;MAClD,IAAI;QACF;QACA/P,eAAe,CAAC,IAAI,CAAC;;QAErB;QACA,IAAIqB,cAAc,CAACmE,OAAO,EAAE;UAC1BnE,cAAc,CAACmE,OAAO,CAACgF,KAAK,CAAC,CAAC;QAChC;;QAEA;QACA;;QAEA;QACA,MAAMwF,YAAY,GAAG,CAAC,CAAC;;QAEvB;QACA;QACA,MAAMC,kBAAkB,GAAGF,cAAc;;QAEzC;QACA1O,cAAc,CAACmE,OAAO,GAAG,MAAMxH,UAAU,CAACkS,oBAAoB,CAC5DZ,WAAW;QACX;QACCa,YAAY,IAAK;UAChB;;UAEA;UACA,IAAIA,YAAY,CAACC,MAAM,KAAK,qBAAqB,IAAID,YAAY,CAACE,QAAQ,IAAIF,YAAY,CAACE,QAAQ,CAACC,iBAAiB,EAAE;YACrH,MAAMC,WAAW,GAAGJ,YAAY,CAACE,QAAQ,CAACC,iBAAiB;YAC3DrM,OAAO,CAAC4B,GAAG,CAAC,4BAA4B0K,WAAW,EAAE,CAAC;YACtD5N,mBAAmB,CAAC4N,WAAW,CAAC;YAChCpL,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEmL,WAAW,CAAC;YACrD;YACApR,QAAQ,CAAC,eAAeoR,WAAW,EAAE,EAAE;cAAE7I,OAAO,EAAE;YAAK,CAAC,CAAC;YACzD,OAAO,CAAC;UACV;;UAEA;UACA,IAAI,CAAChF,gBAAgB,IAAIyN,YAAY,CAACtL,UAAU,IAAIsL,YAAY,CAACC,MAAM,KAAK,YAAY,EAAE;YACxFnM,OAAO,CAAC4B,GAAG,CAAC,uBAAuB,EAAEsK,YAAY,CAACtL,UAAU,CAAC;YAC7DlC,mBAAmB,CAACwN,YAAY,CAACtL,UAAU,CAAC;YAC5CM,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAE+K,YAAY,CAACtL,UAAU,CAAC,CAAC,CAAC;UACrE;;UAEA;UACA+K,aAAa,GAAG,CAAC;;UAEjB;UACA,IAAIO,YAAY,CAACtH,OAAO,EAAE;YACxB,MAAMyG,WAAW,GAAGa,YAAY,CAACtH,OAAO,CAACxB,IAAI;;YAE7C;YACA,IAAIiI,WAAW,KAAK,sBAAsB,IACtCA,WAAW,KAAK,gBAAgB,EAAE;cACpC;YACF;;YAEA;YACA,IAAIkB,aAAa,GAAG,KAAK;YACzB,IAAIC,kBAAkB,GAAG,IAAI;YAC7B,IAAIC,cAAc,GAAG,EAAE;;YAEvB;YACA,IAAIpB,WAAW,CAAC7H,QAAQ,CAAC,UAAU,CAAC,EAAE;cACpC,MAAMkJ,iBAAiB,GAAGX,YAAY,CAAC,eAAe,CAAC;cACvD,IAAIW,iBAAiB,EAAE;gBACrBF,kBAAkB,GAAGE,iBAAiB;gBACtCH,aAAa,GAAG,IAAI;;gBAEpB;gBACA,MAAMI,SAAS,GAAGZ,YAAY,CAAC,0BAA0B,CAAC;gBAC1D,IAAIY,SAAS,EAAE;kBACb,MAAMC,WAAW,GAAGlM,IAAI,CAACwC,GAAG,CAAC,CAAC,GAAGyJ,SAAS;kBAC1C,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,GAAG,IAAI,CAAC;kBAC9C,IAAIC,OAAO,GAAG,EAAE,EAAE;oBAChBJ,cAAc,GAAG,QAAQI,OAAO,IAAI;kBACtC,CAAC,MAAM;oBACL,MAAMG,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;oBACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;oBACrCJ,cAAc,GAAG,QAAQO,OAAO,IAAIC,gBAAgB,IAAI;kBAC1D;gBACF;cACF;YACF;;YAEA;YACA,IAAI5B,WAAW,CAAC7H,QAAQ,CAAC,UAAU,CAAC,EAAE;cACpC,MAAM0J,oBAAoB,GAAGnB,YAAY,CAAC,kBAAkB,CAAC;cAC7D,IAAImB,oBAAoB,EAAE;gBACxBV,kBAAkB,GAAGU,oBAAoB;gBACzCX,aAAa,GAAG,IAAI;;gBAEpB;gBACA,MAAMI,SAAS,GAAGZ,YAAY,CAAC,6BAA6B,CAAC;gBAC7D,IAAIY,SAAS,EAAE;kBACb,MAAMC,WAAW,GAAGlM,IAAI,CAACwC,GAAG,CAAC,CAAC,GAAGyJ,SAAS;kBAC1C,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,GAAG,IAAI,CAAC;kBAC9C,IAAIC,OAAO,GAAG,EAAE,EAAE;oBAChBJ,cAAc,GAAG,QAAQI,OAAO,IAAI;kBACtC,CAAC,MAAM;oBACL,MAAMG,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;oBACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;oBACrCJ,cAAc,GAAG,QAAQO,OAAO,IAAIC,gBAAgB,IAAI;kBAC1D;gBACF;cACF;YACF;;YAEA;YACA,MAAME,qBAAqB,GAAG,wBAAwB;YACtD,MAAMC,UAAU,GAAG/B,WAAW,CAACgC,KAAK,CAACF,qBAAqB,CAAC;YAC3D,IAAIC,UAAU,EAAE;cACd,MAAME,QAAQ,GAAGF,UAAU,CAAC,CAAC,CAAC;cAC9B,MAAMG,oBAAoB,GAAGxB,YAAY,CAAC,oBAAoBuB,QAAQ,EAAE,CAAC;cACzE,IAAIC,oBAAoB,EAAE;gBACxBf,kBAAkB,GAAGe,oBAAoB;gBACzChB,aAAa,GAAG,IAAI;;gBAEpB;gBACA,MAAMI,SAAS,GAAGZ,YAAY,CAAC,oBAAoBuB,QAAQ,aAAa,CAAC;gBACzE,IAAIX,SAAS,EAAE;kBACb,MAAMC,WAAW,GAAGlM,IAAI,CAACwC,GAAG,CAAC,CAAC,GAAGyJ,SAAS;kBAC1C,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,GAAG,IAAI,CAAC;kBAC9C,IAAIC,OAAO,GAAG,EAAE,EAAE;oBAChBJ,cAAc,GAAG,QAAQI,OAAO,IAAI;kBACtC,CAAC,MAAM;oBACL,MAAMG,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;oBACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;oBACrCJ,cAAc,GAAG,QAAQO,OAAO,IAAIC,gBAAgB,IAAI;kBAC1D;gBACF;cACF;YACF;;YAEA;YACA,IAAI5B,WAAW,CAAC7H,QAAQ,CAAC,KAAK,CAAC,IAAI6H,WAAW,CAAC7H,QAAQ,CAAC,UAAU,CAAC,EAAE;cACnE,MAAMgK,QAAQ,GAAG,UAAU9M,IAAI,CAACwC,GAAG,CAAC,CAAC,IAAI4J,IAAI,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;cACrF5B,YAAY,CAAC,eAAe,CAAC,GAAGyB,QAAQ;cACxCzB,YAAY,CAAC,0BAA0B,CAAC,GAAGrL,IAAI,CAACwC,GAAG,CAAC,CAAC;;cAErD;cACAjH,eAAe,CAACgE,IAAI,IAAI,CACtB,GAAGA,IAAI,EACP;gBACEG,EAAE,EAAEoN,QAAQ;gBACZhH,IAAI,EAAE,IAAI;gBACVpD,IAAI,EAAEiI,WAAW;gBACjB5E,IAAI,EAAEyF,YAAY,CAACtH,OAAO,CAAC6B,IAAI,IAAIiD,SAAS;gBAC5CkE,QAAQ,EAAE1B,YAAY,CAACtH,OAAO,CAACgJ,QAAQ,IAAI,KAAK;gBAAE;gBAClDzK,MAAM,EAAE+I,YAAY,CAACtH,OAAO,CAACzB,MAAM,IAAI,IAAI,CAAC;cAC9C,CAAC,CACF,CAAC;cACF,OAAO,CAAC;YACV;YAEA,IAAIkI,WAAW,CAAC7H,QAAQ,CAAC,KAAK,CAAC,IAAI6H,WAAW,CAAC7H,QAAQ,CAAC,QAAQ,CAAC,EAAE;cACjE,MAAMgK,QAAQ,GAAG,UAAU9M,IAAI,CAACwC,GAAG,CAAC,CAAC,IAAI4J,IAAI,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;cACrF5B,YAAY,CAAC,kBAAkB,CAAC,GAAGyB,QAAQ;cAC3CzB,YAAY,CAAC,6BAA6B,CAAC,GAAGrL,IAAI,CAACwC,GAAG,CAAC,CAAC;;cAExD;cACAjH,eAAe,CAACgE,IAAI,IAAI,CACtB,GAAGA,IAAI,EACP;gBACEG,EAAE,EAAEoN,QAAQ;gBACZhH,IAAI,EAAE,IAAI;gBACVpD,IAAI,EAAEiI,WAAW;gBACjB5E,IAAI,EAAEyF,YAAY,CAACtH,OAAO,CAAC6B,IAAI,IAAIiD,SAAS;gBAC5CkE,QAAQ,EAAE1B,YAAY,CAACtH,OAAO,CAACgJ,QAAQ,IAAI,KAAK;gBAAE;gBAClDzK,MAAM,EAAE+I,YAAY,CAACtH,OAAO,CAACzB,MAAM,IAAI;cACzC,CAAC,CACF,CAAC;cACF,OAAO,CAAC;YACV;YAEA,MAAM0K,oBAAoB,GAAG,yBAAyB;YACtD,MAAMR,KAAK,GAAGhC,WAAW,CAACgC,KAAK,CAACQ,oBAAoB,CAAC;YACrD,IAAIR,KAAK,EAAE;cACT,MAAMC,QAAQ,GAAGD,KAAK,CAAC,CAAC,CAAC;cACzB,MAAMG,QAAQ,GAAG,UAAU9M,IAAI,CAACwC,GAAG,CAAC,CAAC,IAAI4J,IAAI,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;cACrF5B,YAAY,CAAC,oBAAoBuB,QAAQ,EAAE,CAAC,GAAGE,QAAQ;cACvDzB,YAAY,CAAC,oBAAoBuB,QAAQ,aAAa,CAAC,GAAG5M,IAAI,CAACwC,GAAG,CAAC,CAAC;;cAEpE;cACAjH,eAAe,CAACgE,IAAI,IAAI,CACtB,GAAGA,IAAI,EACP;gBACEG,EAAE,EAAEoN,QAAQ;gBACZhH,IAAI,EAAE,IAAI;gBACVpD,IAAI,EAAEiI,WAAW;gBACjB5E,IAAI,EAAEyF,YAAY,CAACtH,OAAO,CAAC6B,IAAI,IAAIiD,SAAS;gBAC5CkE,QAAQ,EAAE1B,YAAY,CAACtH,OAAO,CAACgJ,QAAQ,IAAI,KAAK;gBAAE;gBAClDzK,MAAM,EAAE+I,YAAY,CAACtH,OAAO,CAACzB,MAAM,IAAI;cACzC,CAAC,CACF,CAAC;cACF,OAAO,CAAC;YACV;;YAEA;YACA,MAAM2K,UAAU,GAAGvB,aAAa,GAAGC,kBAAkB,GAAG,UAAU9L,IAAI,CAACwC,GAAG,CAAC,CAAC,IAAI4J,IAAI,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAE5H1R,eAAe,CAACgE,IAAI,IAAI;cACtB,MAAM8N,qBAAqB,GAAG9N,IAAI,CAACc,SAAS,CAACiC,GAAG,IAAIA,GAAG,CAAC5C,EAAE,KAAK0N,UAAU,CAAC;cAC1E,IAAIC,qBAAqB,KAAK,CAAC,CAAC,EAAE;gBAClC;gBACI,MAAMC,eAAe,GAAG,CAAC,GAAG/N,IAAI,CAAC;gBACjC+N,eAAe,CAACD,qBAAqB,CAAC,GAAG;kBACrC,GAAGC,eAAe,CAACD,qBAAqB,CAAC;kBACzC3K,IAAI,EAAEqJ,cAAc,GAAG,GAAGpB,WAAW,IAAIoB,cAAc,EAAE,GAAGpB,WAAW;kBACvE5E,IAAI,EAAEyF,YAAY,CAACtH,OAAO,CAAC6B,IAAI,IAAIiD,SAAS;kBAC5CkE,QAAQ,EAAE1B,YAAY,CAACtH,OAAO,CAACgJ,QAAQ,IAAI,KAAK;kBAAE;kBAClDzK,MAAM,EAAE+I,YAAY,CAACtH,OAAO,CAACzB,MAAM,IAAI;gBAC3C,CAAC;gBACD,OAAO6K,eAAe;cAC5B,CAAC,MAAM;gBACL;gBACI,OAAO,CACT,GAAG/N,IAAI,EACP;kBACEG,EAAE,EAAE0N,UAAU;kBACdtH,IAAI,EAAE,IAAI;kBACVpD,IAAI,EAAEiI,WAAW;kBACjB5E,IAAI,EAAEyF,YAAY,CAACtH,OAAO,CAAC6B,IAAI,IAAIiD,SAAS;kBACpCkE,QAAQ,EAAE1B,YAAY,CAACtH,OAAO,CAACgJ,QAAQ,IAAI,KAAK;kBAAE;kBAClDzK,MAAM,EAAE+I,YAAY,CAACtH,OAAO,CAACzB,MAAM,IAAI;gBACjD,CAAC,CACE;cACP;YACA,CAAC,CAAC;UACJ;;UAEA;UACA,IAAI+I,YAAY,CAACC,MAAM,KAAK,UAAU,IAAID,YAAY,CAACtH,OAAO,IAAIsH,YAAY,CAACtH,OAAO,CAACxB,IAAI,EAAE;YAC3F,MAAM6K,UAAU,GAAG/B,YAAY,CAACtH,OAAO,CAACxB,IAAI,CAACiK,KAAK,CAAC,WAAW,CAAC;YAC/D,IAAIY,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,EAAE;cAC7B,MAAMC,cAAc,GAAGD,UAAU,CAAC,CAAC,CAAC,CAAClF,IAAI,CAAC,CAAC;cAC3C,IAAImF,cAAc,IAAItS,SAAS,KAAK,SAASsS,cAAc,EAAE,EAAE;gBAC3D,MAAMhI,QAAQ,GAAG,SAASgI,cAAc,EAAE;gBAC1CrS,YAAY,CAACqK,QAAQ,CAAC;cAC1B;YACJ;UACF;;UAEA;UACA;UACA,IAAIgG,YAAY,CAACC,MAAM,KAAK,iBAAiB,EAAE;YAC7C;YACA,IAAID,YAAY,CAACE,QAAQ,IAAIF,YAAY,CAACE,QAAQ,CAAC+B,aAAa,EAAE;cAChE,MAAMjI,QAAQ,GAAGgG,YAAY,CAACE,QAAQ,CAAC+B,aAAa;cACpDnO,OAAO,CAAC4B,GAAG,CAAC,2DAA2DsE,QAAQ,EAAE,CAAC;cAClFrK,YAAY,CAACqK,QAAQ,CAAC;cACtBvK,2BAA2B,CAACuK,QAAQ,CAAC;YACvC;YACA;YAAA,KACK,IAAIgG,YAAY,CAACtH,OAAO,IAAIsH,YAAY,CAACtH,OAAO,CAACxB,IAAI,EAAE;cAC1D,MAAM6K,UAAU,GAAG/B,YAAY,CAACtH,OAAO,CAACxB,IAAI,CAACiK,KAAK,CAAC,YAAY,CAAC;cAChE,IAAIY,UAAU,IAAIA,UAAU,CAAC,CAAC,CAAC,EAAE;gBAC7B,MAAMC,cAAc,GAAGD,UAAU,CAAC,CAAC,CAAC,CAAClF,IAAI,CAAC,CAAC;gBAC3C,MAAM7C,QAAQ,GAAG,SAASgI,cAAc,EAAE;gBAC1ClO,OAAO,CAAC4B,GAAG,CAAC,0EAA0EsE,QAAQ,EAAE,CAAC;gBACjGrK,YAAY,CAACqK,QAAQ,CAAC;gBACtBvK,2BAA2B,CAACuK,QAAQ,CAAC;cACzC;YACF;UACF;;UAEA;UACA,IAAIgG,YAAY,CAACkC,gBAAgB,EAAE;YACjC1Q,iBAAiB,CAACwO,YAAY,CAACkC,gBAAgB,CAAC;UAClD;UAEA,IAAIlC,YAAY,CAACmC,YAAY,IAAInC,YAAY,CAACmC,YAAY,GAAG,CAAC,EAAE;YAC9DnQ,kBAAkB,CAACgO,YAAY,CAACmC,YAAY,CAAC;UAC/C;;UAEA;UACA,IAAInC,YAAY,CAACoC,SAAS,EAAE;YAC1B;YACA,IAAIpC,YAAY,CAACoC,SAAS,CAACC,MAAM,KAAK,YAAY,IAC9CrC,YAAY,CAACsC,mBAAmB,KAAK9E,SAAS,EAAE;cAElD;cACA,IAAIwC,YAAY,CAACoC,SAAS,CAACG,GAAG,KAAK,MAAM,EAAE;gBACzC3Q,aAAa,CAAC,MAAM,CAAC;gBACrBU,kBAAkB,CAACyB,IAAI,KAAK;kBAC1B,GAAGA,IAAI;kBACP,CAACiM,YAAY,CAACsC,mBAAmB,GAAG,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;cACL,CAAC,MAAM,IAAItC,YAAY,CAACoC,SAAS,CAACG,GAAG,KAAK,SAAS,EAAE;gBACnD3Q,aAAa,CAAC,SAAS,CAAC;gBACxBU,kBAAkB,CAACyB,IAAI,KAAK;kBAC1B,GAAGA,IAAI;kBACP,CAACiM,YAAY,CAACsC,mBAAmB,GAAG,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC;cACL;YACF;UACF;;UAEA;UACA,IAAItC,YAAY,CAACwC,YAAY,EAAE;YAC7B,MAAMC,QAAQ,GAAG;cACfvO,EAAE,EAAE8L,YAAY,CAACwC,YAAY,CAACtO,EAAE;cAChCyD,IAAI,EAAEqI,YAAY,CAACwC,YAAY,CAAC7K,IAAI,IAAIqI,YAAY,CAACwC,YAAY,CAACE,YAAY;cAAE;cAChF9K,IAAI,EAAEoI,YAAY,CAACwC,YAAY,CAAC5K,IAAI,IAAIoI,YAAY,CAACwC,YAAY,CAAC7K,IAAI,IAAIqI,YAAY,CAACwC,YAAY,CAACE,YAAY;cAChH7K,KAAK,EAAEmI,YAAY,CAACwC,YAAY,CAAC1K,YAAY,KAAK0F,SAAS,GAAGwC,YAAY,CAACwC,YAAY,CAAC1K,YAAY,GAAG,CAAC,GAAG,CAAC;cAAE;cAC9G3D,KAAK,EAAE,OAAO6L,YAAY,CAACwC,YAAY,CAAC1K,YAAY;YACtD,CAAC;;YAED;YACA7H,gBAAgB,CAACyL,UAAU,IAAI;cAC7B,MAAMiH,aAAa,GAAGjH,UAAU,CAAC7G,SAAS,CAAC6C,CAAC,IAAIA,CAAC,CAACxD,EAAE,KAAKuO,QAAQ,CAACvO,EAAE,IAAIwD,CAAC,CAACG,KAAK,KAAK4K,QAAQ,CAAC5K,KAAK,CAAC;cACnG,IAAI+K,aAAa;cAEjB,IAAID,aAAa,IAAI,CAAC,EAAE;gBACtB;gBACAC,aAAa,GAAG,CAAC,GAAGlH,UAAU,CAAC;gBAC/BkH,aAAa,CAACD,aAAa,CAAC,GAAGF,QAAQ;cACzC,CAAC,MAAM;gBACL;gBACAG,aAAa,GAAG,CAAC,GAAGlH,UAAU,EAAE+G,QAAQ,CAAC;cAC3C;;cAEA;cACAG,aAAa,CAACvJ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzB,KAAK,GAAG0B,CAAC,CAAC1B,KAAK,CAAC;cAC/C,OAAO+K,aAAa;YACtB,CAAC,CAAC;;YAEF;YACA,IAAI5C,YAAY,CAACsC,mBAAmB,KAAK9E,SAAS,EAAE;cAClDrN,oBAAoB,CAAC6P,YAAY,CAACsC,mBAAmB,CAAC;YACxD;;YAEA;YACA,IAAItC,YAAY,CAAC6C,YAAY,EAAE;cAC7BnR,YAAY,CAACsO,YAAY,CAAC6C,YAAY,CAAC;YACzC;UACF;;UAEA;UACA,IAAI7C,YAAY,CAACC,MAAM,KAAK,WAAW,EAAE;YACvC;YACA,MAAM6C,eAAe,GAAG,UAAUtO,IAAI,CAACwC,GAAG,CAAC,CAAC,IAAI4J,IAAI,CAACW,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5F1R,eAAe,CAACgE,IAAI,IAAI,CACtB,GAAGA,IAAI,EACP;cACEG,EAAE,EAAE4O,eAAe;cACnBxI,IAAI,EAAE,IAAI;cACVpD,IAAI,EAAE,iBAAkB8I,YAAY,CAACmC,YAAY,IAAInS,aAAa,CAACyD,MAAM,uBAAwB;cACjG8G,IAAI,EAAE,GAAG;cACTmH,QAAQ,EAAE,KAAK;cAAE;cACjBzK,MAAM,EAAE,IAAI;cAAE;cACdvC,UAAU,EAAEsL,YAAY,CAACtL,UAAU,IAAInC,gBAAgB,CAAC;YAC1D,CAAC,CACF,CAAC;YAEFxB,gBAAgB,CAAC,IAAI,CAAC;;YAEtB;YACA,IAAI,CAACvB,wBAAwB,IAAIA,wBAAwB,KAAK,OAAO,EAAE;cACrEC,2BAA2B,CAAC0P,WAAW,CAAC1L,MAAM,GAAG,EAAE,GAAG0L,WAAW,CAACsC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGtC,WAAW,CAAC;YAC3G;;YAEA;YACAnL,wBAAwB,CAAC,CAAC;;YAE1B;YACA+O,qBAAqB,CAAC,CAAC;UACzB;QACF,CAAC;QACD;QACClP,KAAK,IAAK;UACTC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;UAE5C;UACA,IAAI3C,cAAc,CAACmE,OAAO,IAAI,OAAOnE,cAAc,CAACmE,OAAO,CAACgF,KAAK,KAAK,UAAU,EAAE;YAChFnJ,cAAc,CAACmE,OAAO,CAACgF,KAAK,CAAC,CAAC;UAChC;;UAEA;UACA,IAAI,CAACxG,KAAK,CAAC6E,OAAO,KAAK,uBAAuB,IAAI7E,KAAK,CAAC6E,OAAO,CAACpB,QAAQ,CAAC,mBAAmB,CAAC,KAAKmI,aAAa,GAAGC,mBAAmB,EAAE;YACrID,aAAa,EAAE;YACf1P,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;cAChCG,EAAE,EAAE,aAAaM,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAE;cAC7BsD,IAAI,EAAE,aAAa;cACnBpD,IAAI,EAAE,qBAAqBuI,aAAa,IAAIC,mBAAmB,SAASI,kBAAkB,IAAI,KAAK,EAAE;cACrG7I,MAAM,EAAE,QAAQ;cAChBsD,IAAI,EAAE;YACR,CAAC,CAAC,CAAC;;YAEH;YACAyI,UAAU,CAAC,MAAMrD,iBAAiB,CAACpN,gBAAgB,CAAC,EAAE,IAAI,GAAGkN,aAAa,CAAC;UAC7E,CAAC,MAAM;YACL;YACA5P,eAAe,CAAC,KAAK,CAAC;YACtBE,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;cAChCG,EAAE,EAAE,SAASM,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAE;cACzBsD,IAAI,EAAE,UAAU;cAChBpD,IAAI,EAAE,YAAYrD,KAAK,CAAC6E,OAAO,EAAE;cACjCzB,MAAM,EAAE,QAAQ;cAChBsD,IAAI,EAAE;YACR,CAAC,CAAC,CAAC;UACL;QACF,CAAC;QACD;QACA,MAAM;UACJzG,OAAO,CAAC4B,GAAG,CAAC,mBAAmB,CAAC;UAChC7F,eAAe,CAAC,KAAK,CAAC;UACtB;UACAmE,wBAAwB,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QACD;QACA8L,kBAAkB,GAAG;UAAEpL,UAAU,EAAEoL;QAAmB,CAAC,GAAG,CAAC,CAC7D,CAAC;MACH,CAAC,CAAC,OAAOmD,QAAQ,EAAE;QACjBnP,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEoP,QAAQ,CAAC;QAC9CpT,eAAe,CAAC,KAAK,CAAC;QACtBE,eAAe,CAACgE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAChCG,EAAE,EAAE,aAAaM,IAAI,CAACwC,GAAG,CAAC,CAAC,EAAE;UAC7BsD,IAAI,EAAE,UAAU;UAChBpD,IAAI,EAAE,WAAW+L,QAAQ,CAACvK,OAAO,EAAE;UACnCzB,MAAM,EAAE,QAAQ;UAChBsD,IAAI,EAAE;QACR,CAAC,CAAC,CAAC;MACL;IACF,CAAC;;IAED;IACA;IACAoF,iBAAiB,CAACJ,iBAAiB,GAAGhN,gBAAgB,GAAG,IAAI,CAAC;EAChE,CAAC;;EAED;EACA,MAAMwQ,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAMvN,aAAa,GAAG,MAAM3H,UAAU,CAAC4H,kBAAkB,CAAC,CAAC;MAC3D3B,OAAO,CAAC4B,GAAG,CAAC,iDAAiD,EAAEF,aAAa,CAAC;;MAE7E;MACA,MAAM0N,sBAAsB,GAAG1N,aAAa,CAACI,GAAG,CAACC,IAAI,KAAK;QACxD3B,EAAE,EAAE2B,IAAI,CAAC3B,EAAE;QAAE;QACbC,KAAK,EAAE0B,IAAI,CAAC1B,KAAK,IAAI,OAAO0B,IAAI,CAAC3B,EAAE,GAAG2B,IAAI,CAAC3B,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG;QAAE;QACnEG,SAAS,EAAEsB,IAAI,CAACC,aAAa,IAAID,IAAI,CAACE,UAAU;QAChDrB,UAAU,EAAEmB,IAAI,CAAC3B,EAAE;QAAE;QACrBG,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE,EAAE;QACV0B,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;;MAEH;MACA/E,cAAc,CAAC8C,IAAI,IAAI;QACrB;QACA,MAAMkC,gBAAgB,GAAG,IAAIC,GAAG,CAACgN,sBAAsB,CAACtN,GAAG,CAACd,IAAI,IAAIA,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAC,CAAC;;QAEvF;QACA,MAAMyO,iBAAiB,GAAGpP,IAAI,CAACoH,MAAM,CAACrG,IAAI,IAAI,CAACmB,gBAAgB,CAACK,GAAG,CAACxB,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAC,CAAC;;QAEvF;QACA,MAAM0O,QAAQ,GAAG,CAAC,GAAGF,sBAAsB,EAAE,GAAGC,iBAAiB,CAAC,CAC/D9J,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI/E,IAAI,CAAC+E,CAAC,CAAChF,SAAS,CAAC,GAAG,IAAIC,IAAI,CAAC8E,CAAC,CAAC/E,SAAS,CAAC,CAAC;;QAEhE;QACA,IAAI;UACFS,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACiO,QAAQ,CAAC,CAAC;QAC/D,CAAC,CAAC,OAAOvP,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QAC3C;QAEA,OAAOuP,QAAQ;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOvP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;EAED,MAAMwP,uBAAuB,GAAIC,OAAO,IAAK;IAC3C7S,uBAAuB,CAAC6S,OAAO,CAAC;IAChC/S,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;;EAED;EACA;AACF;AACA;AACA;AACA;AACA;AACA;;EAEE;;EAEA;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIsC,QAAQ,CAACgO,KAAK,IAAIhO,QAAQ,CAACgO,KAAK,CAACoF,MAAM,KAAK,uBAAuB,EAAE;MACvE,MAAM;QAAE/N,MAAM,EAAEiP,YAAY;QAAErG,YAAY;QAAEC,iBAAiB,EAAEqG;MAAgB,CAAC,GAAGvU,QAAQ,CAACgO,KAAK;;MAEjG;MACA;MACA,IAAIsG,YAAY,IAAIA,YAAY,CAAC9P,MAAM,GAAG,CAAC,EAAE;QAC1CxD,gBAAgB,CAACsT,YAAY,CAAC,CAAC,CAAC;MACnC;MACA,IAAIC,eAAe,EAAE;QAClB/T,2BAA2B,CAAC+T,eAAe,CAAC;MAC/C;MAEA3S,8BAA8B,CAACqM,YAAY,IAAI,CAAC,CAAC;MACjDhK,gBAAgB,CAAC,CAAC,CAAC,CAAC;;MAEpB;MACAlE,QAAQ,CAACC,QAAQ,CAACwU,QAAQ,EAAE;QAAElM,OAAO,EAAE,IAAI;QAAE0F,KAAK,EAAE,CAAC;MAAE,CAAC,CAAC;IAC3D;EACF,CAAC,EAAE,CAAChO,QAAQ,CAACgO,KAAK,EAAEjO,QAAQ,EAAEkE,gBAAgB,EAAEjE,QAAQ,CAACwU,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAErE;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMC,cAAc,GAAG,CAAC9R,iBAAiB;IACzCC,oBAAoB,CAAC6R,cAAc,CAAC;IACpC,IAAIA,cAAc,EAAE;MAAE;MACpBZ,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;EAED,MAAMa,wBAAwB,GAAGA,CAAA,KAAM;IACrC9R,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAM+R,uBAAuB,GAAIpJ,WAAW,IAAK;IAC/CD,mBAAmB,CAACC,WAAW,CAAC,CAAC,CAAC;IAClCmJ,wBAAwB,CAAC,CAAC;EAC5B,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1R,iBAAiB,CAAC,IAAI,CAAC;IACvBF,yBAAyB,CAAC,KAAK,CAAC;EAClC,CAAC;EAED,oBACEnE,OAAA;IAAKgW,SAAS,EAAC,iCAAiC;IAAAC,QAAA,EAC7C9U,SAAS;IAAA;IACR;IACAnB,OAAA;MAAKgW,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnEjW,OAAA;QAAKgW,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjW,OAAA;UAAKgW,SAAS,EAAC,mDAAmD;UAACE,KAAK,EAAC,4BAA4B;UAACC,IAAI,EAAC,MAAM;UAACC,OAAO,EAAC,WAAW;UAAAH,QAAA,gBACnIjW,OAAA;YAAQgW,SAAS,EAAC,YAAY;YAACK,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,CAAC,EAAC,IAAI;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACrG7W,OAAA;YAAMgW,SAAS,EAAC,YAAY;YAACG,IAAI,EAAC,cAAc;YAACW,CAAC,EAAC;UAAiH;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzK,CAAC,eACN7W,OAAA;UAAGgW,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAM;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAEN7W,OAAA,CAAAE,SAAA;MAAA+V,QAAA,GAEGtT,YAAY,IAAIV,aAAa,CAACyD,MAAM,GAAG,CAAC,iBACvC1F,OAAA,CAAChB,QAAQ;QAAC+X,QAAQ,eAAE/W,OAAA;UAAKgW,SAAS,EAAC,oEAAoE;UAAAC,QAAA,EAAC;QAAW;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAE;QAAAZ,QAAA,eACxHjW,OAAA,CAACS,gBAAgB;UACf8F,MAAM,EAAEtE,aAAc;UACtBkN,YAAY,EAAEtM,2BAA4B;UAC1CmU,OAAO,EAAE7R;QAAiB;UAAAuR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACX,eAGD7W,OAAA;QACEgW,SAAS,EAAE;MAAoH;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChI,CAAC,eAIF7W,OAAA;QAAKgW,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CjW,OAAA,CAACV,OAAO;UACNiC,cAAc,EAAEA,cAAe;UAC/B0V,gBAAgB,EAAElL;QAAe;UAAA2K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEF7W,OAAA;UAAKgW,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CjW,OAAA,CAACT,UAAU;YACTsC,YAAY,EAAEA,YAAa;YAC3BE,YAAY,EAAEA,YAAa;YAC3BmV,aAAa,EAAEhG,iBAAkB;YACjCiG,mBAAmB,EAAE7B,uBAAwB;YAC7ClP,KAAK,EAAEzE,SAAU;YACjByV,YAAY,EAAEpL,qBAAsB;YACpCqL,WAAW,EAAEhL,eAAgB;YAC7BjJ,KAAK,EAAEA,KAAM;YACbE,WAAW,EAAEA,WAAY;YACzB0S,SAAS,EAAC;UAAO;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACF7W,OAAA,CAACR,SAAS;YACR8X,SAAS,EAAErV,aAAc;YACzBsV,wBAAwB,EAAEpV,iBAAkB;YAC5CqV,sBAAsB,EAAE3G,0BAA2B;YACnD4G,kBAAkB,EAAExG,sBAAuB;YAC3CyG,gBAAgB,EAAE3U,aAAc;YAChC4U,mBAAmB,EAAEtK,qBAAsB;YAC3CuK,eAAe,EAAEA,CAAClK,SAAS,EAAEmK,WAAW,EAAEtJ,KAAK,KAAK;cAClDjM,qBAAqB,CAAC;gBACpB6D,EAAE,EAAEuH,SAAS;gBACbtE,OAAO,EAAEyO,WAAW;gBACpBtJ,KAAK,EAAEA;cACT,CAAC,CAAC;YACJ,CAAE;YACFa,iBAAiB,EAAE3N,wBAAyB;YAC5CqW,mBAAmB,EAAE7I,uBAAwB;YAC7CzL,cAAc,EAAEA,cAAe;YAC/BE,SAAS,EAAEA,SAAU;YACrBE,UAAU,EAAEA,UAAW;YACvBoS,SAAS,EAAC,OAAO;YACjBhS,eAAe,EAAEA,eAAgB;YACjCM,eAAe,EAAEA,eAAgB;YACjCyT,gBAAgB,EAAEjH;UAAqB;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL3S,sBAAsB,iBACrBlE,OAAA;QAAKgW,SAAS,EAAC,mFAAmF;QAAAC,QAAA,eAChGjW,OAAA;UAAKgW,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAC1EjW,OAAA;YAAKgW,SAAS,EAAC,sCAAsC;YAAAC,QAAA,eACnDjW,OAAA;cAAGgW,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAE7D;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN7W,OAAA;YAAKgW,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDjW,OAAA;cACEgY,OAAO,EAAEjL,mBAAoB;cAC7BiJ,SAAS,EAAC,oEAAoE;cAAAC,QAAA,EAC/E;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7W,OAAA;cACEgY,OAAO,EAAEjC,kBAAmB;cAC5BC,SAAS,EAAC,0EAA0E;cAAAC,QAAA,EACrF;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAtU,sBAAsB,iBACrBvC,OAAA;QAAKgW,SAAS,EAAC,gFAAgF;QAAAC,QAAA,eAC7FjW,OAAA;UAAKgW,SAAS,EAAC,2EAA2E;UAAAC,QAAA,gBACxFjW,OAAA;YAAKgW,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DjW,OAAA;cAAIgW,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C7W,OAAA;cAAQgY,OAAO,EAAEA,CAAA,KAAMxV,yBAAyB,CAAC,KAAK,CAAE;cAACwT,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EAAC;YAAC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvH,CAAC,eACN7W,OAAA;YAAKgW,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDxT,oBAAoB,CAACoF,GAAG,CAACkH,MAAM,iBAC9B/O,OAAA;cAAqBgW,SAAS,EAAC,8DAA8D;cAAAC,QAAA,gBAC3FjW,OAAA;gBAAGiY,IAAI,EAAElJ,MAAM,CAACmJ,MAAO;gBAACC,MAAM,EAAC,QAAQ;gBAACC,GAAG,EAAC,qBAAqB;gBAACpC,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,GACtIlH,MAAM,CAACxC,IAAI,KAAK,KAAK,gBAAGvM,OAAA,CAACP,SAAS;kBAACuW,SAAS,EAAC;gBAAmB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG7W,OAAA,CAACN,OAAO;kBAACsW,SAAS,EAAC;gBAAoB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChH9H,MAAM,CAAC3I,KAAK;cAAA;gBAAAsQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACJ7W,OAAA;gBAAGgW,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAElH,MAAM,CAACsJ;cAAO;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9D7W,OAAA;gBAAGgW,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAElH,MAAM,CAACmJ;cAAM;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GANrD9H,MAAM,CAAC5I,EAAE;cAAAuQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOd,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN7W,OAAA;YAAKgW,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtCjW,OAAA;cACEgY,OAAO,EAAEA,CAAA,KAAMxV,yBAAyB,CAAC,KAAK,CAAE;cAChDwT,SAAS,EAAC,0EAA0E;cAAAC,QAAA,EACrF;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAlS,WAAW,iBACV3E,OAAA;QACEgW,SAAS,EAAC,wEAAwE;QAClFzH,KAAK,EAAE;UAAE+B,GAAG,EAAE3L,WAAW,CAAC4L,CAAC,GAAG,EAAE;UAAEL,IAAI,EAAEvL,WAAW,CAACwL;QAAE,CAAE;QACxD6H,OAAO,EAAEtJ,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;QAAA;QAAAsH,QAAA,eAEnCjW,OAAA;UACEgY,OAAO,EAAEvJ,kBAAmB;UAC5BuH,SAAS,EAAC,8FAA8F;UAAAC,QAAA,gBAExGjW,OAAA,CAACJ,YAAY;YAACoW,SAAS,EAAC;UAAM;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEN,CACN,EAGAhS,SAAS,iBACR7E,OAAA;QAAKgW,SAAS,EAAC,+EAA+E;QAAAC,QAAA,eAC5FjW,OAAA;UAAKgW,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChEjW,OAAA;YAAIgW,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAU;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxD7W,OAAA;YACEsY,KAAK,EAAEvT,eAAgB;YACvBwT,QAAQ,EAAG7J,CAAC,IAAK1J,kBAAkB,CAAC0J,CAAC,CAACyJ,MAAM,CAACG,KAAK,CAAE;YACpDtC,SAAS,EAAC,+EAA+E;YACzFwC,IAAI,EAAC,GAAG;YACRC,WAAW,EAAC,gFAAe;YAC3BC,QAAQ,EAAEzT;UAAiB;YAAAyR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACF7W,OAAA;YAAKgW,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CjW,OAAA;cACEgY,OAAO,EAAEpJ,oBAAqB;cAC9BoH,SAAS,EAAC,4DAA4D;cACtE0C,QAAQ,EAAEzT,gBAAiB;cAAAgR,QAAA,EAC5B;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7W,OAAA;cACEgY,OAAO,EAAEnJ,gBAAiB;cAC1BmH,SAAS,EAAC,4FAA4F;cACtG0C,QAAQ,EAAEzT,gBAAgB,IAAI,CAACF,eAAe,CAAC+J,IAAI,CAAC,CAAE;cAAAmH,QAAA,EAErDhR,gBAAgB,GAAG,QAAQ,GAAG;YAAI;cAAAyR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAxU,kBAAkB,iBACjBrC,OAAA,CAAChB,QAAQ;QAAC+X,QAAQ,eAAE/W,OAAA;UAAKgW,SAAS,EAAC,uEAAuE;UAAAC,QAAA,EAAC;QAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAE;QAAAZ,QAAA,eAC3HjW,OAAA,CAACG,gBAAgB;UACfwY,WAAW,EAAEtW,kBAAkB,CAAC+G,OAAQ;UACxCwP,YAAY,EAAEvW,kBAAkB,CAACkM,KAAM;UACvCsK,QAAQ,EAAExW,kBAAkB,CAACwW,QAAS;UACtCC,MAAM,EAAExL,kBAAmB;UAC3ByL,QAAQ,EAAEvK;QAAqB;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CACX;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7V,EAAA,CA97CID,OAAO;EAAA,QACM3B,WAAW,EACXC,WAAW;AAAA;AAAA2Z,GAAA,GAFxBjY,OAAO;AAg8Cb,SAASkY,GAAGA,CAAA,EAAG;EACb,oBACEjZ,OAAA,CAACf,aAAa;IAAAgX,QAAA,eACZjW,OAAA,CAACd,MAAM;MAAA+W,QAAA,gBACLjW,OAAA,CAACb,KAAK;QAAC+Z,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEnZ,OAAA,CAACe,OAAO;UAAA2V,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxC7W,OAAA,CAACb,KAAK;QAAC+Z,IAAI,EAAC,SAAS;QAACC,OAAO,eAC3BnZ,OAAA,CAAChB,QAAQ;UAAC+X,QAAQ,eAAE/W,OAAA;YAAKgW,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAE;UAAAZ,QAAA,eAC7FjW,OAAA,CAACM,eAAe;YAAAoW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACJ7W,OAAA,CAACb,KAAK;QAAC+Z,IAAI,EAAC,OAAO;QAACC,OAAO,eACzBnZ,OAAA,CAAChB,QAAQ;UAAC+X,QAAQ,eAAE/W,OAAA;YAAKgW,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAE;UAAAZ,QAAA,eAC7FjW,OAAA,CAACY,YAAY;YAAA8V,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACX;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACuC,GAAA,GAlBQH,GAAG;AAoBZ,eAAeA,GAAG;AAAC,IAAA7Y,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAkY,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAjZ,EAAA;AAAAiZ,YAAA,CAAAhZ,GAAA;AAAAgZ,YAAA,CAAA9Y,GAAA;AAAA8Y,YAAA,CAAA7Y,GAAA;AAAA6Y,YAAA,CAAA3Y,GAAA;AAAA2Y,YAAA,CAAA1Y,GAAA;AAAA0Y,YAAA,CAAAxY,GAAA;AAAAwY,YAAA,CAAAvY,GAAA;AAAAuY,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}