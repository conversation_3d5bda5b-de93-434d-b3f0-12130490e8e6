import requests
import json
import time

def simple_test():
    """简单测试：启动生成，等待，然后检查结果"""
    # 第一步：启动生成
    url = "http://localhost:8000/api/v1/slides/generate_from_text_stream"
    data = {
        "prompt": "测试聊天记录保存，创建关于AI的3张幻灯片"
    }
    
    print("开始测试...")
    response = requests.post(url, json=data, stream=True)
    
    if response.status_code != 200:
        print(f"错误: {response.status_code} - {response.text}")
        return None
    
    project_id = None
    # 解析初始响应
    for line in response.iter_lines(decode_unicode=True):
        if line.startswith('data:'):
            try:
                json_data = json.loads(line[5:].strip())
                print(f"响应: {json_data.get('status')}")
                if 'project_id' in json_data:
                    project_id = json_data['project_id']
                    break
            except json.JSONDecodeError:
                continue
    
    print(f"获取到项目ID: {project_id}")
    
    # 等待生成完成
    print("等待30秒让生成完成...")
    time.sleep(30)
    
    # 检查项目详情
    url = f"http://localhost:8000/api/v1/projects/{project_id}/details"
    response = requests.get(url)
    
    if response.status_code == 200:
        data = response.json()
        print(f"项目标题: {data.get('title')}")
        print(f"聊天历史数量: {len(data.get('chat_history', []))}")
        
        chat_history = data.get('chat_history', [])
        if chat_history:
            print("✅ 聊天历史记录:")
            for i, msg in enumerate(chat_history):
                sender = msg.get('sender', 'unknown')
                text = msg.get('text', '')[:60]
                print(f"  {i+1}. [{sender}]: {text}...")
        else:
            print("❌ 没有找到聊天历史记录!")
    else:
        print(f"获取项目详情失败: {response.status_code}")
    
    return project_id

if __name__ == "__main__":
    simple_test() 