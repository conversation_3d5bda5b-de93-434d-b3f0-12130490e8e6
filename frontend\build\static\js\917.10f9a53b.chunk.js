"use strict";(self.webpackChunktiktodo_slides_clone=self.webpackChunktiktodo_slides_clone||[]).push([[917],{3917:(e,t,l)=>{l.r(t),l.d(t,{default:()=>a});var n=l(5043),r=l(6147),s=l(579);const a=e=>{let{slides:t,initialIndex:l=0,onClose:a}=e;const[i,o]=(0,n.useState)(l),[c,u]=(0,n.useState)(!0),d=(0,n.useRef)(null),w=(0,n.useRef)(null),f=(0,n.useCallback)((()=>{d.current&&clearTimeout(d.current),d.current=setTimeout((()=>u(!1)),2e3)}),[]),p=(0,n.useCallback)((()=>{o((e=>e<t.length-1?e+1:e)),u(!0),f()}),[t.length,f]),h=(0,n.useCallback)((()=>{o((e=>e>0?e-1:e)),u(!0),f()}),[f]),g=(0,n.useCallback)((()=>{u(!0),f()}),[f]);(0,n.useEffect)((()=>{const e=e=>{if(e.data&&"iframe_keydown"===e.data.type){const t=e.data.key;"ArrowRight"===t||"ArrowDown"===t||"PageDown"===t||" "===t?p():"ArrowLeft"===t||"ArrowUp"===t||"PageUp"===t?h():"Escape"===t&&a()}};return window.addEventListener("message",e),()=>{window.removeEventListener("message",e)}}),[p,h,a]),(0,n.useEffect)((()=>{const e=e=>{e.preventDefault(),e.deltaY>0?p():e.deltaY<0&&h()},t=e=>{["ArrowRight","ArrowDown","PageDown"," ","ArrowLeft","ArrowUp","PageUp","Escape"].includes(e.key)&&e.preventDefault(),"ArrowRight"===e.key||"ArrowDown"===e.key||"PageDown"===e.key||" "===e.key?p():"ArrowLeft"===e.key||"ArrowUp"===e.key||"PageUp"===e.key?h():"Escape"===e.key&&a()},l=w.current;return l&&(l.addEventListener("wheel",e,{passive:!1}),window.addEventListener("keydown",t,{passive:!1})),()=>{l&&l.removeEventListener("wheel",e),window.removeEventListener("keydown",t)}}),[p,h,a]),(0,n.useEffect)((()=>(f(),()=>{d.current&&clearTimeout(d.current)})),[i,f]);const k=t[i];return k?(0,s.jsxs)("div",{ref:w,className:"fixed inset-0 bg-black flex items-center justify-center z-[9999]",onMouseMove:g,onClick:e=>{e.target!==e.currentTarget&&e.target!==w.current.firstChild||p()},children:[(0,s.jsx)("div",{className:"relative w-full h-full flex items-center justify-center",children:(0,s.jsx)(r.A,{slideId:"fullscreen-".concat(k.id||i),slideFullHtml:k.html,isAppEditingMode:!1,isThumbnail:!1})}),(0,s.jsxs)("div",{className:"transition-opacity duration-300 ".concat(c?"opacity-100":"opacity-0"),onClick:e=>e.stopPropagation(),children:[(0,s.jsx)("button",{className:"absolute top-5 right-5 w-10 h-10 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-xl",onClick:a,title:"\u9000\u51fa\u5168\u5c4f (Esc)",children:"\xd7"}),i>0&&(0,s.jsx)("button",{className:"absolute left-5 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-2xl",onClick:h,title:"\u4e0a\u4e00\u5f20 (\u2190 / \u2191)",children:"\u2039"}),i<t.length-1&&(0,s.jsx)("button",{className:"absolute right-5 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-black bg-opacity-40 text-white flex items-center justify-center hover:bg-opacity-60 text-2xl",onClick:p,title:"\u4e0b\u4e00\u5f20 (\u2192 / \u2193)",children:"\u203a"}),(0,s.jsxs)("div",{className:"absolute bottom-5 left-1/2 -translate-x-1/2 bg-black bg-opacity-50 px-4 py-2 rounded-full text-white text-sm",children:[i+1," / ",t.length]})]})]}):null}}}]);
//# sourceMappingURL=917.10f9a53b.chunk.js.map