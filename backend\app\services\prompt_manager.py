import os
import logging
import re
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from functools import lru_cache

logger = logging.getLogger(__name__)

# PROMPTS_BASE_DIR 应该相对于 app 目录
PROMPTS_BASE_DIR = Path(__file__).resolve().parent.parent / "prompts"
logger.info(f"PromptManager: Base directory for prompts set to: {PROMPTS_BASE_DIR}")

class PromptManagerSingleton:
    def __init__(self):
        if not PROMPTS_BASE_DIR.exists():
            logger.error(f"Prompts base path does not exist: {PROMPTS_BASE_DIR}")
            raise FileNotFoundError(f"Prompts base path does not exist: {PROMPTS_BASE_DIR}")
        logger.info(f"PromptManagerSingleton initialized with base path: {PROMPTS_BASE_DIR}")

    @lru_cache(maxsize=128)
    def get_prompt_section(self, agent_name: str, section_name: str) -> Optional[str]:
        """
        从统一的Agent提示词文件中提取特定部分的内容。
        
        Args:
            agent_name: Agent名称，如 "html_editor_agent"
            section_name: 部分名称，如 "system" 或 "task:main" 或 "task:edit_element"
        
        Returns:
            提取的提示词内容，如果未找到则返回None
        """
        # 构建文件路径
        prompt_file_path = PROMPTS_BASE_DIR / f"{agent_name}_prompt.md"
        
        if not prompt_file_path.exists():
            logger.error(f"Prompt file not found: {prompt_file_path}")
            return None
        
        try:
            with open(prompt_file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 解析Markdown文件，提取指定部分
            return self._extract_section_from_markdown(content, section_name)
            
        except Exception as e:
            logger.error(f"Error loading prompt from {prompt_file_path}: {e}", exc_info=True)
            return None

    def _extract_section_from_markdown(self, content: str, section_name: str) -> Optional[str]:
        """
        从Markdown内容中提取指定部分。
        
        Args:
            content: 完整的Markdown文件内容
            section_name: 要提取的部分名称
        
        Returns:
            提取的部分内容，如果未找到则返回None
        """
        if section_name == "system":
            # 提取 "# System Prompt" 部分
            pattern = r'# System Prompt\s*\n(.*?)(?=\n---\n|\n## Task:|\Z)'
            match = re.search(pattern, content, re.DOTALL)
            if match:
                return match.group(1).strip()
        
        elif section_name.startswith("task:"):
            # 提取 "## Task: xxx" 部分 - 修复正则表达式，只在遇到下一个Task或文件结尾时停止
            task_name = section_name.split(":", 1)[1]
            pattern = rf'## Task: {re.escape(task_name)}\s*\n(.*?)(?=\n## Task:|\Z)'
            match = re.search(pattern, content, re.DOTALL)
            if match:
                return match.group(1).strip()
        
        logger.warning(f"Section '{section_name}' not found in markdown content")
        return None

    @lru_cache(maxsize=128)
    def get_prompt(self, prompt_name: str, agent_name: Optional[str] = None) -> Optional[str]:
        """
        兼容旧版本的get_prompt方法，保持向后兼容性。
        """
        if agent_name and agent_name.lower() != "common":
            if "agent" in agent_name.lower():
                agent_dir_name = agent_name.lower().replace("agent", "")
            else:
                agent_dir_name = agent_name.lower()
            sub_dir_path = PROMPTS_BASE_DIR / agent_dir_name
        else:
            sub_dir_path = PROMPTS_BASE_DIR / "common"

        # 【修改】简化文件名逻辑
        filename_with_ext = f"{prompt_name}.md"
        file_path = sub_dir_path / filename_with_ext

        try:
            if not file_path.exists():
                logger.error(f"Prompt file not found: {file_path}") # 【修改】移除回退逻辑
                return None
                
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            return content
        except Exception as e:
            logger.error(f"Error loading prompt from {file_path}: {e}", exc_info=True)
            return None

    def format_prompt(self, prompt_name: str, agent_name: Optional[str] = None, **kwargs: Any) -> Optional[str]:
        """
        获取并格式化提示词模板。
        """
        template = self.get_prompt(prompt_name, agent_name) # 【修改】移除version
        if template is None:
            return None
        try:
            return template.format(**kwargs)
        except KeyError as e:
            logger.error(f"Missing key in prompt formatting for {agent_name}/{prompt_name}: {e}. Available keys in prompt_format_args: {list(kwargs.keys())}", exc_info=True)
            return f"PROMPT_FORMATTING_ERROR: Missing key {e} for prompt {agent_name}/{prompt_name}"
        except Exception as e:
            logger.error(f"Error formatting prompt {agent_name}/{prompt_name}: {e}", exc_info=True)
            return f"PROMPT_FORMATTING_ERROR: General error for prompt {agent_name}/{prompt_name}"

    def format_prompt_section(self, agent_name: str, section_name: str, **kwargs: Any) -> Optional[str]:
        """
        获取并格式化统一提示词文件中的特定部分。
        
        Args:
            agent_name: Agent名称
            section_name: 部分名称
            **kwargs: 格式化参数
        
        Returns:
            格式化后的提示词内容
        """
        template = self.get_prompt_section(agent_name, section_name)
        if template is None:
            return None
            
        try:
            # 使用安全的字符串替换方法，避免大括号冲突
            result = template
            for key, value in kwargs.items():
                placeholder = "{" + key + "}"
                if placeholder in result:
                    result = result.replace(placeholder, str(value))
            return result
        except Exception as e:
            logger.error(f"Error formatting prompt {agent_name}/{section_name}: {e}", exc_info=True)
            return f"PROMPT_FORMATTING_ERROR: General error for prompt {agent_name}/{section_name}"

    def clear_cache(self):
        # 因为现在lru_cache在方法上，我们需要特定地清除它
        self.get_prompt.cache_clear()
        self.get_prompt_section.cache_clear()
        logger.info("PromptManager cache cleared.")

# 创建单例实例
prompt_manager_instance = PromptManagerSingleton()

# 导出方便使用的函数，而不是直接导出实例或类
def get_prompt(prompt_name: str, agent_name: Optional[str] = None) -> Optional[str]:
    return prompt_manager_instance.get_prompt(prompt_name, agent_name) # 【修改】移除version

def get_prompt_section(agent_name: str, section_name: str) -> Optional[str]:
    """
    从统一的Agent提示词文件中获取特定部分的内容。
    
    Args:
        agent_name: Agent名称，如 "html_editor_agent"
        section_name: 部分名称，如 "system" 或 "task:main" 或 "task:edit_element"
    
    Returns:
        提取的提示词内容，如果未找到则返回None
    """
    return prompt_manager_instance.get_prompt_section(agent_name, section_name)

def format_prompt(prompt_name: str, agent_name: Optional[str] = None, **kwargs: Any) -> Optional[str]:
    return prompt_manager_instance.format_prompt(prompt_name, agent_name, **kwargs) # 【修改】移除version

def format_prompt_section(agent_name: str, section_name: str, **kwargs: Any) -> Optional[str]:
    """
    获取并格式化统一提示词文件中的特定部分。
    
    Args:
        agent_name: Agent名称
        section_name: 部分名称
        **kwargs: 格式化参数
    
    Returns:
        格式化后的提示词内容
    """
    return prompt_manager_instance.format_prompt_section(agent_name, section_name, **kwargs)

def clear_prompt_cache():
    prompt_manager_instance.clear_cache()

# 【删除】整个 PromptManagerCompat 类，它不再需要 