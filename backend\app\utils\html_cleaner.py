import re
import logging
from typing import Optional

logger = logging.getLogger(__name__)

def clean_css_in_html(html_content: str) -> str:
    """
    清理HTML中的CSS，确保CSS变量正确定义和使用。
    
    Args:
        html_content: 原始HTML内容
    
    Returns:
        清理后的HTML内容
    """
    if not html_content:
        return html_content
    
    # 1. 确保:root中定义了必要的CSS变量
    root_pattern = r'(:root\s*{[^}]*})'
    root_match = re.search(root_pattern, html_content)
    
    if not root_match:
        # 如果没有:root定义，添加一个基本的:root
        basic_root = """
        :root {
            --primary-color: #0A74DA;
            --secondary-color: #6C757D;
            --accent-color: #FFC107;
            --text-dark: #212529;
            --text-light: #FFFFFF;
            --background-light: #FFFFFF;
            --background-dark: #212529;
        }
        """
        # 在<style>标签内添加:root
        html_content = re.sub(r'(<style[^>]*>)', r'\1' + basic_root, html_content)
    
    # 2. 确保定义了必要的动画
    animations = [
        "@keyframes slideIn",
        "@keyframes float",
        "@keyframes pulse"
    ]
    
    style_content_pattern = r'<style[^>]*>(.*?)</style>'
    style_match = re.search(style_content_pattern, html_content, re.DOTALL)
    
    if style_match:
        style_content = style_match.group(1)
        missing_animations = []
        
        for animation in animations:
            if animation not in style_content:
                missing_animations.append(animation)
        
        if missing_animations:
            # 添加缺失的动画
            animation_definitions = """
            @keyframes slideIn {
                from { opacity: 0; transform: translateY(30px); }
                to { opacity: 1; transform: translateY(0); }
            }
            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-10px); }
            }
            @keyframes pulse {
                0%, 100% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.05); opacity: 0.8; }
            }
            """
            # 在</style>前添加动画定义
            html_content = html_content.replace('</style>', animation_definitions + '</style>')
    
    # 3. 确保html和body有正确的溢出控制
    overflow_pattern = r'(html|body)\s*{[^}]*overflow\s*:[^;}]*;'
    if not re.search(overflow_pattern, html_content):
        # 添加溢出控制
        overflow_css = """
        html, body {
            width: 1280px !important; 
            height: 720px !important;
            margin: 0 !important; 
            padding: 0 !important;
            overflow: hidden !important; 
            box-sizing: border-box !important;
        }
        """
        # 在<style>标签内添加溢出控制
        html_content = re.sub(r'(<style[^>]*>)', r'\1' + overflow_css, html_content)
    
    return html_content

def clean_json_in_response(response_text: str) -> str:
    """
    从LLM响应中提取和清洗JSON内容。
    移除可能的Markdown代码块标记。
    
    Args:
        response_text: LLM的原始响应文本
        
    Returns:
        清洗后的JSON字符串
    """
    if not response_text:
        return response_text
    
    # 移除开头和结尾的空白字符
    cleaned = response_text.strip()
    
    # 移除可能的Markdown代码块标记
    cleaned = re.sub(r'^\s*```json\s*', '', cleaned, flags=re.IGNORECASE)
    cleaned = re.sub(r'^\s*```\s*', '', cleaned, flags=re.IGNORECASE)
    cleaned = re.sub(r'\s*```\s*$', '', cleaned, flags=re.IGNORECASE)
    
    return cleaned

def validate_and_repair_html_structure(html_content: str) -> str:
    """
    基础的HTML结构验证和修复。
    确保基本的HTML标签配对正确。
    
    Args:
        html_content: 需要验证的HTML内容
        
    Returns:
        修复后的HTML内容
    """
    if not html_content:
        return html_content
    
    # 确保HTML有基本的DOCTYPE声明（如果没有的话）
    if not html_content.strip().startswith('<!DOCTYPE'):
        if not html_content.strip().startswith('<html'):
            # 这是一个HTML片段，需要包装
            html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slide</title>
</head>
<body>
{html_content}
</body>
</html>"""
    
    return html_content

def comprehensive_html_cleanup(html_content: str) -> str:
    """
    全面清理HTML，包括CSS变量、动画定义、溢出控制等。
    
    Args:
        html_content: 原始HTML内容
    
    Returns:
        清理后的HTML内容
    """
    if not html_content:
        return html_content
    
    # --- START OF CHANGE ---
    # 0. 移除任何可能存在的外部思考过程标签
    cleaned_html = re.sub(r'<thinking_process>[\s\S]*?</thinking_process>', '', html_content, flags=re.DOTALL).strip()
    
    # 0.1 新增：更精确的中文思考过程清理 - 直接从"回答:"后提取HTML
    answer_match = re.search(r'回答:\s*(<!DOCTYPE html>.*)', cleaned_html, re.DOTALL | re.IGNORECASE)
    if answer_match:
        cleaned_html = answer_match.group(1).strip()
    else:
        # 如果没有"回答:"标记，尝试移除思考过程
        cleaned_html = re.sub(r'思考过程:[\s\S]*?(?=<!DOCTYPE|<html)', '', cleaned_html, flags=re.DOTALL).strip()
        
        # 如果HTML前面还有任何非HTML内容，直接提取HTML部分
        doctype_match = re.search(r'(<!DOCTYPE html>.*)', cleaned_html, re.DOTALL | re.IGNORECASE)
        if doctype_match:
            cleaned_html = doctype_match.group(1).strip()
    
    # 1. 移除可能存在的Markdown代码块标记 (更强的正则表达式)
    cleaned_html = re.sub(r'^\s*```(?:html)?\s*', '', cleaned_html, flags=re.IGNORECASE | re.MULTILINE)
    cleaned_html = re.sub(r'\s*```\s*$', '', cleaned_html, flags=re.IGNORECASE | re.MULTILINE)

    # 2. 清理 <style> 标签内部可能存在的Markdown标记
    def clean_style_tag(match):
        style_content = match.group(1)
        # 移除 '```css' 和 '```'
        cleaned_content = re.sub(r'^\s*```(?:css)?\s*|```\s*$', '', style_content, flags=re.MULTILINE).strip()
        return f"<style>{cleaned_content}</style>"

    cleaned_html = re.sub(r'<style[^>]*>([\s\S]*?)</style>', clean_style_tag, cleaned_html, flags=re.IGNORECASE)
    # --- END OF CHANGE ---

    # 3. 清理CSS (调用现有函数)
    cleaned_html = clean_css_in_html(cleaned_html)
    
    # 4. 确保图片有alt属性
    img_pattern = r'<img([^>]*)>'
    
    def add_alt_to_img(match):
        img_attrs = match.group(1)
        # 检查是否已经存在 alt 属性，无论其值是什么
        if 'alt=' not in img_attrs.lower():
            return f'<img{img_attrs} alt="幻灯片图片">'
        return match.group(0)
    
    cleaned_html = re.sub(img_pattern, add_alt_to_img, cleaned_html, flags=re.IGNORECASE)
    
    # 5. 确保Chart.js脚本正确放置
    if 'chart.js' in cleaned_html.lower() and '<canvas' in cleaned_html.lower():
        if 'cdn.jsdelivr.net/npm/chart.js' not in cleaned_html:
            chart_js_cdn = '<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>'
            # 优先插入到</head>前
            if '</head>' in cleaned_html:
                cleaned_html = cleaned_html.replace('</head>', f'{chart_js_cdn}</head>', 1)
            else: # 如果没有head，则添加到文档开头
                cleaned_html = chart_js_cdn + cleaned_html

    return cleaned_html.strip()

# 用于测试的函数
def test_css_cleaner():
    """测试CSS清洗功能"""
    test_cases = [
        {
            "name": "基本CSS清洗",
            "input": """<style>
```css
body { font-family: sans-serif; }
.container { max-width: 1200px; }
```
</style>""",
            "expected_cleaned": True
        },
        {
            "name": "多个style标签",
            "input": """<style>
```css
body { color: red; }
```
</style>
<style>
```scss
.header { background: blue; }
```
</style>""",
            "expected_cleaned": True
        },
        {
            "name": "已经是纯净CSS",
            "input": """<style>
body { font-family: Arial; }
.main { padding: 20px; }
</style>""",
            "expected_cleaned": False
        }
    ]
    
    for case in test_cases:
        print(f"\n测试用例: {case['name']}")
        print("输入:")
        print(case['input'])
        
        cleaned = clean_css_in_html(case['input'])
        print("输出:")
        print(cleaned)
        
        has_markdown = '```' in case['input']
        cleaned_has_markdown = '```' in cleaned
        
        print(f"原始包含Markdown: {has_markdown}")
        print(f"清洗后包含Markdown: {cleaned_has_markdown}")
        print(f"清洗成功: {has_markdown and not cleaned_has_markdown}")

if __name__ == "__main__":
    # 运行测试
    test_css_cleaner()
    