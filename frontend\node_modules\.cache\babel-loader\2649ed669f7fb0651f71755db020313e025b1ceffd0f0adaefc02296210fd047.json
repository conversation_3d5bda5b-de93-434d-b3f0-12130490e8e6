{"ast": null, "code": "// frontend/src/services/api.js\nimport config from '../config';\nclass ApiService {\n  constructor() {\n    this.baseUrl = config.api.baseUrl || 'http://localhost:8000/api/v1';\n    this.rootUrl = config.api.rootUrl || 'http://localhost:8000';\n  }\n\n  // 获取历史项目列表\n  async getProjectsHistory() {\n    try {\n      const response = await fetch(`${this.baseUrl}/projects/history`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `Error: ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('Error fetching projects history:', error);\n      throw error;\n    }\n  }\n\n  // 获取特定项目详情\n  async getProjectDetails(projectId) {\n    try {\n      console.log(`[API Service] 正在获取项目 ${projectId} 的详情...`);\n      const response = await fetch(`${this.baseUrl}/projects/${projectId}/details`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `Error: ${response.status}`);\n      }\n      const data = await response.json();\n      // 添加详细日志\n      console.log(`[API Service] 获取项目 ${projectId} 详情成功:`, data);\n      console.log(`[API Service] 聊天历史记录数量: ${data.chat_history ? data.chat_history.length : 0}`);\n      if (data.chat_history && data.chat_history.length > 0) {\n        console.log(`[API Service] 聊天历史记录第一条:`, data.chat_history[0]);\n      } else {\n        console.log(`[API Service] 警告: 项目 ${projectId} 没有聊天历史记录`);\n        // 检查数据库连接\n        console.log(`[API Service] 后端API返回的完整数据结构:`, JSON.stringify(data, null, 2));\n      }\n      return data;\n    } catch (error) {\n      console.error(`Error fetching project details for ${projectId}:`, error);\n      throw error;\n    }\n  }\n\n  // 生成幻灯片的普通API调用\n  async generateSlidesFromText(text) {\n    try {\n      const response = await fetch(`${this.baseUrl}/slides/generate_from_text`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          text\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `Error: ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('Error generating slides:', error);\n      throw error;\n    }\n  }\n\n  // 使用流式响应生成幻灯片\n  async generateSlidesFromTextStream(text, onProgress, onError, onComplete, additionalParams = {}) {\n    try {\n      // 准备请求数据对象\n      const requestData = {\n        prompt: text,\n        ...additionalParams\n      };\n      const response = await fetch(`${this.baseUrl}/slides/generate_from_text_stream`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(requestData)\n      });\n      if (!response.ok) {\n        throw new Error(`Error: ${response.status} ${response.statusText}`);\n      }\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder();\n      const processStreamData = async () => {\n        try {\n          while (true) {\n            const {\n              value,\n              done\n            } = await reader.read();\n            if (done) {\n              console.log(\"Stream complete\");\n              onComplete();\n              return;\n            }\n            const text = decoder.decode(value, {\n              stream: true\n            });\n            // 解析SSE格式的数据（data: {...}\\n\\n）\n            const messages = text.split('\\n\\n');\n            for (const message of messages) {\n              if (message.startsWith('data:')) {\n                try {\n                  const jsonStr = message.slice(5).trim();\n                  if (jsonStr) {\n                    const data = JSON.parse(jsonStr);\n                    onProgress(data);\n                  }\n                } catch (e) {\n                  console.warn('Failed to parse SSE message:', message, e);\n                }\n              }\n            }\n          }\n        } catch (err) {\n          console.error('Stream reading error:', err);\n          onError(err);\n        }\n      };\n\n      // 开始处理流数据\n      processStreamData();\n      return {\n        // 提供中止流的方法\n        abort: () => reader.cancel()\n      };\n    } catch (error) {\n      console.error('Error setting up streaming connection:', error);\n      onError(error);\n      return null;\n    }\n  }\n\n  // +++++++++++++++ 新增AI聊天流式API调用函数 +++++++++++++++\n  async streamChatResponse(userMessage, onProgress, onError, onComplete, projectId = null, turboMode = false) {\n    try {\n      const requestBody = {\n        user_message: userMessage\n      };\n\n      // 如果提供了projectId，则添加到请求中\n      if (projectId) {\n        requestBody.project_id = projectId;\n      }\n\n      // 添加极速模式参数\n      if (turboMode) {\n        requestBody.turbo_mode = true;\n      }\n      const response = await fetch(`${this.baseUrl}/chat/stream`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(requestBody)\n      });\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('Server error response for chat stream:', errorText);\n        throw new Error(`聊天服务错误 (${response.status}): ${response.statusText}.`);\n      }\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder();\n      const processStream = async () => {\n        try {\n          while (true) {\n            const {\n              value,\n              done\n            } = await reader.read();\n            if (done) {\n              console.log(\"Chat stream complete.\");\n              onComplete();\n              return;\n            }\n            const text = decoder.decode(value, {\n              stream: true\n            });\n            // SSE messages are prefixed with 'data: ' and end with '\\n\\n'\n            const messages = text.split('\\n\\n').filter(msg => msg.trim() !== ''); // Filter out empty strings\n\n            for (const message of messages) {\n              if (message.startsWith('data:')) {\n                try {\n                  const jsonStr = message.slice(5).trim();\n                  if (jsonStr) {\n                    const data = JSON.parse(jsonStr);\n                    onProgress(data); // Pass the parsed AiChatMessage object\n                  }\n                } catch (e) {\n                  console.warn('Failed to parse chat SSE message:', message, e);\n                }\n              }\n            }\n          }\n        } catch (err) {\n          console.error('Chat stream reading error:', err);\n          onError(err);\n        }\n      };\n      processStream();\n      return {\n        abort: () => reader.cancel()\n      };\n    } catch (error) {\n      console.error('Error setting up chat streaming connection:', error);\n      onError(error);\n      return null;\n    }\n  }\n  // +++++++++++++++++++++++++++++++++++++++++++++\n\n  // 上传图片\n  async uploadImage(file) {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      const response = await fetch(`${this.baseUrl}/uploads/image`, {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `Error: ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('Error uploading image:', error);\n      throw error;\n    }\n  }\n\n  // 获取图片\n  async getImage(imageId) {\n    try {\n      const response = await fetch(`${this.baseUrl}/uploads/image/${imageId}`);\n      if (!response.ok) {\n        throw new Error(`Error: ${response.status}`);\n      }\n      return response.url;\n    } catch (error) {\n      console.error('Error getting image:', error);\n      throw error;\n    }\n  }\n\n  // 生成图片\n  async generateImage(prompt, size = '1024x1024') {\n    try {\n      const response = await fetch(`${this.baseUrl}/images/generate`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          prompt,\n          size\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `Error: ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('Error generating image:', error);\n      throw error;\n    }\n  }\n\n  // 使用简化工作流生成幻灯片\n  async generatePresentation(userPrompt, onProgress, onError, onComplete, options = {}) {\n    try {\n      console.log('Starting presentation generation with prompt:', userPrompt);\n      console.log('API URL:', `${this.rootUrl}/generate_presentation/`);\n\n      // 1. 首先创建项目\n      const projectId = options.project_id; // 如果是重试，可能会提供project_id\n      const requestBody = projectId ? {\n        user_prompt: userPrompt,\n        project_id: projectId\n      } : {\n        user_prompt: userPrompt\n      };\n      const response = await fetch(`${this.rootUrl}/generate_presentation/`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(requestBody)\n      });\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('Server error response:', errorText);\n        throw new Error(`服务器错误 (${response.status}): ${response.statusText}. 请检查后端服务是否正常运行。`);\n      }\n      const {\n        project_id,\n        status\n      } = await response.json();\n      if (status !== 'started' || !project_id) {\n        throw new Error('生成过程启动失败，服务器未返回有效的项目ID');\n      }\n      console.log('创建了新的演示文稿项目，ID:', project_id);\n\n      // 2. 使用SSE监听更新 - 使用封装函数来设置EventSource\n      const setupEventSource = currentProjectId => {\n        const es = new EventSource(`${this.baseUrl}/presentation_updates/${currentProjectId}`);\n        es.onopen = () => {\n          console.log(`SSE connection established for project: ${currentProjectId}`);\n          if (onProgress) {\n            onProgress({\n              project_id: currentProjectId,\n              status: \"sse_connected\",\n              message: {\n                sender: \"system\",\n                text: \"更新通道已连接。\",\n                icon: \"🔗\"\n              }\n            });\n          }\n        };\n        es.onmessage = event => {\n          try {\n            const data = JSON.parse(event.data);\n\n            // 心跳消息直接忽略，不处理\n            if (data.status === 'heartbeat') {\n              // console.log(`SSE Heartbeat for ${currentProjectId}`);\n              return;\n            }\n            if (onProgress) onProgress(data);\n            if (data.status === 'completed' || data.status === 'error') {\n              var _data$message;\n              es.close();\n              if (onComplete && data.status === 'completed') onComplete(data);\n              if (onError && data.status === 'error') onError(new Error(((_data$message = data.message) === null || _data$message === void 0 ? void 0 : _data$message.text) || 'Unknown server error'));\n            }\n          } catch (e) {\n            console.error('SSE parsing error:', e, 'Data:', event.data);\n            if (onError) onError(new Error('Failed to parse server event.'));\n          }\n        };\n        es.onerror = error => {\n          console.error(`SSE connection error for ${currentProjectId}:`, error);\n          es.close();\n          // 让浏览器自动处理重连，我们只在最终失败时通知用户\n          if (onError) {\n            onError(new Error(`SSE connection failed. The browser will attempt to reconnect.`));\n          }\n        };\n        return es; // 返回 EventSource 实例\n      };\n      let eventSourceInstance = setupEventSource(project_id);\n      return {\n        projectId: project_id,\n        // 返回项目ID，方便前端重试时使用\n        close: () => {\n          if (eventSourceInstance) {\n            console.log(`手动关闭SSE连接: ${project_id}`);\n            eventSourceInstance.close();\n          }\n        }\n      };\n    } catch (error) {\n      console.error('生成演示文稿时出错:', error);\n      if (onError) {\n        onError(error);\n      }\n      throw error;\n    }\n  }\n\n  // 删除项目\n  async deleteProject(projectId) {\n    try {\n      const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {\n        method: 'DELETE'\n      });\n      if (!response.ok && response.status !== 204) {\n        // 204 No Content is success\n        const errorData = await response.json().catch(() => ({\n          detail: \"Failed to delete project from server.\"\n        }));\n        throw new Error(errorData.detail || `Error: ${response.status}`);\n      }\n      // No content to parse for 204\n      return {\n        success: true,\n        message: `Project ${projectId} deleted.`\n      };\n    } catch (error) {\n      console.error(`Error deleting project ${projectId}:`, error);\n      throw error;\n    }\n  }\n\n  // 获取单张幻灯片的完整内容\n  async getSlideContent(slideId) {\n    try {\n      const response = await fetch(`${this.baseUrl}/slides/${slideId}/content`);\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `Error: ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error(`Error fetching content for slide ${slideId}:`, error);\n      throw error;\n    }\n  }\n  async updateProject(projectId, updateData) {\n    try {\n      const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(updateData)\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `Error updating project: ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error(`Error updating project ${projectId}:`, error);\n      throw error;\n    }\n  }\n\n  // +++++++++++++++ 新增API调用函数 +++++++++++++++\n  async editSlideElement(slideId, elementSelector, instruction) {\n    try {\n      const response = await fetch(`${this.baseUrl}/slides/${slideId}/edit_element`, {\n        method: 'PATCH',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          element_selector: elementSelector,\n          instruction: instruction\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || `Error: ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error(`Error editing element for slide ${slideId}:`, error);\n      throw error;\n    }\n  }\n}\n\n// Create a single instance and export it\nconst apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["config", "ApiService", "constructor", "baseUrl", "api", "rootUrl", "getProjectsHistory", "response", "fetch", "method", "headers", "ok", "errorData", "json", "Error", "detail", "status", "error", "console", "getProjectDetails", "projectId", "log", "data", "chat_history", "length", "JSON", "stringify", "generateSlidesFromText", "text", "body", "generateSlidesFromTextStream", "onProgress", "onError", "onComplete", "additionalParams", "requestData", "prompt", "statusText", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "processStreamData", "value", "done", "read", "decode", "stream", "messages", "split", "message", "startsWith", "jsonStr", "slice", "trim", "parse", "e", "warn", "err", "abort", "cancel", "streamChatResponse", "userMessage", "turboMode", "requestBody", "user_message", "project_id", "turbo_mode", "errorText", "processStream", "filter", "msg", "uploadImage", "file", "formData", "FormData", "append", "getImage", "imageId", "url", "generateImage", "size", "generatePresentation", "userPrompt", "options", "user_prompt", "setupEventSource", "currentProjectId", "es", "EventSource", "onopen", "sender", "icon", "onmessage", "event", "_data$message", "close", "onerror", "eventSourceInstance", "deleteProject", "catch", "success", "getSlideC<PERSON>nt", "slideId", "updateProject", "updateData", "editSlideElement", "elementSelector", "instruction", "element_selector", "apiService"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/services/api.js"], "sourcesContent": ["// frontend/src/services/api.js\r\nimport config from '../config';\r\n\r\nclass ApiService {\r\n  constructor() {\r\n    this.baseUrl = config.api.baseUrl || 'http://localhost:8000/api/v1';\r\n    this.rootUrl = config.api.rootUrl || 'http://localhost:8000';\r\n  }\r\n\r\n  // 获取历史项目列表\r\n  async getProjectsHistory() {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/projects/history`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.detail || `Error: ${response.status}`);\r\n      }\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      console.error('Error fetching projects history:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 获取特定项目详情\r\n  async getProjectDetails(projectId) {\r\n    try {\r\n      console.log(`[API Service] 正在获取项目 ${projectId} 的详情...`);\r\n      const response = await fetch(`${this.baseUrl}/projects/${projectId}/details`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.detail || `Error: ${response.status}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n      // 添加详细日志\r\n      console.log(`[API Service] 获取项目 ${projectId} 详情成功:`, data);\r\n      console.log(`[API Service] 聊天历史记录数量: ${data.chat_history ? data.chat_history.length : 0}`);\r\n      if (data.chat_history && data.chat_history.length > 0) {\r\n        console.log(`[API Service] 聊天历史记录第一条:`, data.chat_history[0]);\r\n      } else {\r\n        console.log(`[API Service] 警告: 项目 ${projectId} 没有聊天历史记录`);\r\n        // 检查数据库连接\r\n        console.log(`[API Service] 后端API返回的完整数据结构:`, JSON.stringify(data, null, 2));\r\n      }\r\n      \r\n      return data;\r\n    } catch (error) {\r\n      console.error(`Error fetching project details for ${projectId}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 生成幻灯片的普通API调用\r\n  async generateSlidesFromText(text) {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/slides/generate_from_text`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ text }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.detail || `Error: ${response.status}`);\r\n      }\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      console.error('Error generating slides:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 使用流式响应生成幻灯片\r\n  async generateSlidesFromTextStream(text, onProgress, onError, onComplete, additionalParams = {}) {\r\n    try {\r\n      // 准备请求数据对象\r\n      const requestData = {\r\n        prompt: text,\r\n        ...additionalParams\r\n      };\r\n      \r\n      const response = await fetch(`${this.baseUrl}/slides/generate_from_text_stream`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(requestData),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Error: ${response.status} ${response.statusText}`);\r\n      }\r\n\r\n      const reader = response.body.getReader();\r\n      const decoder = new TextDecoder();\r\n      \r\n      const processStreamData = async () => {\r\n        try {\r\n          while (true) {\r\n            const { value, done } = await reader.read();\r\n            \r\n            if (done) {\r\n              console.log(\"Stream complete\");\r\n              onComplete();\r\n              return;\r\n            }\r\n            \r\n            const text = decoder.decode(value, { stream: true });\r\n            // 解析SSE格式的数据（data: {...}\\n\\n）\r\n            const messages = text.split('\\n\\n');\r\n            \r\n            for (const message of messages) {\r\n              if (message.startsWith('data:')) {\r\n                try {\r\n                  const jsonStr = message.slice(5).trim();\r\n                  if (jsonStr) {\r\n                    const data = JSON.parse(jsonStr);\r\n                    onProgress(data);\r\n                  }\r\n                } catch (e) {\r\n                  console.warn('Failed to parse SSE message:', message, e);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        } catch (err) {\r\n          console.error('Stream reading error:', err);\r\n          onError(err);\r\n        }\r\n      };\r\n      \r\n      // 开始处理流数据\r\n      processStreamData();\r\n      \r\n      return {\r\n        // 提供中止流的方法\r\n        abort: () => reader.cancel()\r\n      };\r\n    } catch (error) {\r\n      console.error('Error setting up streaming connection:', error);\r\n      onError(error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // +++++++++++++++ 新增AI聊天流式API调用函数 +++++++++++++++\r\n  async streamChatResponse(userMessage, onProgress, onError, onComplete, projectId = null, turboMode = false) {\r\n    try {\r\n      const requestBody = {\r\n        user_message: userMessage\r\n      };\r\n      \r\n      // 如果提供了projectId，则添加到请求中\r\n      if (projectId) {\r\n        requestBody.project_id = projectId;\r\n      }\r\n      \r\n      // 添加极速模式参数\r\n      if (turboMode) {\r\n        requestBody.turbo_mode = true;\r\n      }\r\n      \r\n      const response = await fetch(`${this.baseUrl}/chat/stream`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(requestBody),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorText = await response.text();\r\n        console.error('Server error response for chat stream:', errorText);\r\n        throw new Error(`聊天服务错误 (${response.status}): ${response.statusText}.`);\r\n      }\r\n\r\n      const reader = response.body.getReader();\r\n      const decoder = new TextDecoder();\r\n      \r\n      const processStream = async () => {\r\n        try {\r\n          while (true) {\r\n            const { value, done } = await reader.read();\r\n            if (done) {\r\n              console.log(\"Chat stream complete.\");\r\n              onComplete();\r\n              return;\r\n            }\r\n            const text = decoder.decode(value, { stream: true });\r\n            // SSE messages are prefixed with 'data: ' and end with '\\n\\n'\r\n            const messages = text.split('\\n\\n').filter(msg => msg.trim() !== ''); // Filter out empty strings\r\n            \r\n            for (const message of messages) {\r\n              if (message.startsWith('data:')) {\r\n                try {\r\n                  const jsonStr = message.slice(5).trim();\r\n                  if (jsonStr) {\r\n                    const data = JSON.parse(jsonStr);\r\n                    onProgress(data); // Pass the parsed AiChatMessage object\r\n                  }\r\n                } catch (e) {\r\n                  console.warn('Failed to parse chat SSE message:', message, e);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        } catch (err) {\r\n          console.error('Chat stream reading error:', err);\r\n          onError(err);\r\n        }\r\n      };\r\n      \r\n      processStream();\r\n      \r\n      return {\r\n        abort: () => reader.cancel()\r\n      };\r\n    } catch (error) {\r\n      console.error('Error setting up chat streaming connection:', error);\r\n      onError(error);\r\n      return null;\r\n    }\r\n  }\r\n  // +++++++++++++++++++++++++++++++++++++++++++++\r\n\r\n  // 上传图片\r\n  async uploadImage(file) {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n\r\n      const response = await fetch(`${this.baseUrl}/uploads/image`, {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.detail || `Error: ${response.status}`);\r\n      }\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      console.error('Error uploading image:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 获取图片\r\n  async getImage(imageId) {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/uploads/image/${imageId}`);\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Error: ${response.status}`);\r\n      }\r\n\r\n      return response.url;\r\n    } catch (error) {\r\n      console.error('Error getting image:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 生成图片\r\n  async generateImage(prompt, size = '1024x1024') {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/images/generate`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ prompt, size }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.detail || `Error: ${response.status}`);\r\n      }\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      console.error('Error generating image:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 使用简化工作流生成幻灯片\r\n  async generatePresentation(userPrompt, onProgress, onError, onComplete, options = {}) {\r\n    try {\r\n      console.log('Starting presentation generation with prompt:', userPrompt);\r\n      console.log('API URL:', `${this.rootUrl}/generate_presentation/`);\r\n      \r\n      // 1. 首先创建项目\r\n      const projectId = options.project_id; // 如果是重试，可能会提供project_id\r\n      const requestBody = projectId \r\n        ? { user_prompt: userPrompt, project_id: projectId }\r\n        : { user_prompt: userPrompt };\r\n        \r\n      const response = await fetch(`${this.rootUrl}/generate_presentation/`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(requestBody),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorText = await response.text();\r\n        console.error('Server error response:', errorText);\r\n        throw new Error(`服务器错误 (${response.status}): ${response.statusText}. 请检查后端服务是否正常运行。`);\r\n      }\r\n\r\n      const { project_id, status } = await response.json();\r\n      \r\n      if (status !== 'started' || !project_id) {\r\n        throw new Error('生成过程启动失败，服务器未返回有效的项目ID');\r\n      }\r\n      \r\n      console.log('创建了新的演示文稿项目，ID:', project_id);\r\n      \r\n      // 2. 使用SSE监听更新 - 使用封装函数来设置EventSource\r\n      const setupEventSource = (currentProjectId) => {\r\n        const es = new EventSource(`${this.baseUrl}/presentation_updates/${currentProjectId}`);\r\n\r\n        es.onopen = () => {\r\n          console.log(`SSE connection established for project: ${currentProjectId}`);\r\n          if (onProgress) {\r\n            onProgress({\r\n              project_id: currentProjectId,\r\n              status: \"sse_connected\",\r\n              message: { sender: \"system\", text: \"更新通道已连接。\", icon: \"🔗\" }\r\n            });\r\n          }\r\n        };\r\n\r\n        es.onmessage = (event) => {\r\n          try {\r\n            const data = JSON.parse(event.data);\r\n            \r\n            // 心跳消息直接忽略，不处理\r\n            if (data.status === 'heartbeat') {\r\n              // console.log(`SSE Heartbeat for ${currentProjectId}`);\r\n              return;\r\n            }\r\n\r\n            if (onProgress) onProgress(data);\r\n\r\n            if (data.status === 'completed' || data.status === 'error') {\r\n              es.close();\r\n              if (onComplete && data.status === 'completed') onComplete(data);\r\n              if (onError && data.status === 'error') onError(new Error(data.message?.text || 'Unknown server error'));\r\n            }\r\n          } catch (e) {\r\n            console.error('SSE parsing error:', e, 'Data:', event.data);\r\n            if (onError) onError(new Error('Failed to parse server event.'));\r\n          }\r\n        };\r\n\r\n        es.onerror = (error) => {\r\n          console.error(`SSE connection error for ${currentProjectId}:`, error);\r\n          es.close();\r\n          // 让浏览器自动处理重连，我们只在最终失败时通知用户\r\n          if (onError) {\r\n            onError(new Error(`SSE connection failed. The browser will attempt to reconnect.`));\r\n          }\r\n        };\r\n\r\n        return es; // 返回 EventSource 实例\r\n      };\r\n\r\n      let eventSourceInstance = setupEventSource(project_id);\r\n\r\n      return {\r\n        projectId: project_id, // 返回项目ID，方便前端重试时使用\r\n        close: () => {\r\n          if (eventSourceInstance) {\r\n            console.log(`手动关闭SSE连接: ${project_id}`);\r\n            eventSourceInstance.close();\r\n          }\r\n        }\r\n      };\r\n      \r\n    } catch (error) {\r\n      console.error('生成演示文稿时出错:', error);\r\n      if (onError) {\r\n        onError(error);\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 删除项目\r\n  async deleteProject(projectId) {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {\r\n        method: 'DELETE',\r\n      });\r\n      if (!response.ok && response.status !== 204) { // 204 No Content is success\r\n        const errorData = await response.json().catch(() => ({ detail: \"Failed to delete project from server.\" }));\r\n        throw new Error(errorData.detail || `Error: ${response.status}`);\r\n      }\r\n      // No content to parse for 204\r\n      return { success: true, message: `Project ${projectId} deleted.` };\r\n    } catch (error) {\r\n      console.error(`Error deleting project ${projectId}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 获取单张幻灯片的完整内容\r\n  async getSlideContent(slideId) {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/slides/${slideId}/content`);\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.detail || `Error: ${response.status}`);\r\n      }\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      console.error(`Error fetching content for slide ${slideId}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async updateProject(projectId, updateData) {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {\r\n        method: 'PATCH',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(updateData),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.detail || `Error updating project: ${response.status}`);\r\n      }\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      console.error(`Error updating project ${projectId}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // +++++++++++++++ 新增API调用函数 +++++++++++++++\r\n  async editSlideElement(slideId, elementSelector, instruction) {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/slides/${slideId}/edit_element`, {\r\n        method: 'PATCH',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          element_selector: elementSelector,\r\n          instruction: instruction,\r\n        }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json();\r\n        throw new Error(errorData.detail || `Error: ${response.status}`);\r\n      }\r\n\r\n      return await response.json();\r\n    } catch (error) {\r\n      console.error(`Error editing element for slide ${slideId}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Create a single instance and export it\r\nconst apiService = new ApiService();\r\nexport default apiService; "], "mappings": "AAAA;AACA,OAAOA,MAAM,MAAM,WAAW;AAE9B,MAAMC,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGH,MAAM,CAACI,GAAG,CAACD,OAAO,IAAI,8BAA8B;IACnE,IAAI,CAACE,OAAO,GAAGL,MAAM,CAACI,GAAG,CAACC,OAAO,IAAI,uBAAuB;EAC9D;;EAEA;EACA,MAAMC,kBAAkBA,CAAA,EAAG;IACzB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,mBAAmB,EAAE;QAC/DM,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,UAAUR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAClE;MAEA,OAAO,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAME,iBAAiBA,CAACC,SAAS,EAAE;IACjC,IAAI;MACFF,OAAO,CAACG,GAAG,CAAC,wBAAwBD,SAAS,SAAS,CAAC;MACvD,MAAMb,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,aAAaiB,SAAS,UAAU,EAAE;QAC5EX,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACH,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,UAAUR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAClE;MAEA,MAAMM,IAAI,GAAG,MAAMf,QAAQ,CAACM,IAAI,CAAC,CAAC;MAClC;MACAK,OAAO,CAACG,GAAG,CAAC,sBAAsBD,SAAS,QAAQ,EAAEE,IAAI,CAAC;MAC1DJ,OAAO,CAACG,GAAG,CAAC,2BAA2BC,IAAI,CAACC,YAAY,GAAGD,IAAI,CAACC,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE,CAAC;MAC1F,IAAIF,IAAI,CAACC,YAAY,IAAID,IAAI,CAACC,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;QACrDN,OAAO,CAACG,GAAG,CAAC,0BAA0B,EAAEC,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC,CAAC;MAC/D,CAAC,MAAM;QACLL,OAAO,CAACG,GAAG,CAAC,wBAAwBD,SAAS,WAAW,CAAC;QACzD;QACAF,OAAO,CAACG,GAAG,CAAC,+BAA+B,EAAEI,IAAI,CAACC,SAAS,CAACJ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAC7E;MAEA,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsCG,SAAS,GAAG,EAAEH,KAAK,CAAC;MACxE,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMU,sBAAsBA,CAACC,IAAI,EAAE;IACjC,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,4BAA4B,EAAE;QACxEM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDmB,IAAI,EAAEJ,IAAI,CAACC,SAAS,CAAC;UAAEE;QAAK,CAAC;MAC/B,CAAC,CAAC;MAEF,IAAI,CAACrB,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,UAAUR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAClE;MAEA,OAAO,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMa,4BAA4BA,CAACF,IAAI,EAAEG,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEC,gBAAgB,GAAG,CAAC,CAAC,EAAE;IAC/F,IAAI;MACF;MACA,MAAMC,WAAW,GAAG;QAClBC,MAAM,EAAER,IAAI;QACZ,GAAGM;MACL,CAAC;MAED,MAAM3B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,mCAAmC,EAAE;QAC/EM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDmB,IAAI,EAAEJ,IAAI,CAACC,SAAS,CAACS,WAAW;MAClC,CAAC,CAAC;MAEF,IAAI,CAAC5B,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIG,KAAK,CAAC,UAAUP,QAAQ,CAACS,MAAM,IAAIT,QAAQ,CAAC8B,UAAU,EAAE,CAAC;MACrE;MAEA,MAAMC,MAAM,GAAG/B,QAAQ,CAACsB,IAAI,CAACU,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;MAEjC,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpC,IAAI;UACF,OAAO,IAAI,EAAE;YACX,MAAM;cAAEC,KAAK;cAAEC;YAAK,CAAC,GAAG,MAAMN,MAAM,CAACO,IAAI,CAAC,CAAC;YAE3C,IAAID,IAAI,EAAE;cACR1B,OAAO,CAACG,GAAG,CAAC,iBAAiB,CAAC;cAC9BY,UAAU,CAAC,CAAC;cACZ;YACF;YAEA,MAAML,IAAI,GAAGY,OAAO,CAACM,MAAM,CAACH,KAAK,EAAE;cAAEI,MAAM,EAAE;YAAK,CAAC,CAAC;YACpD;YACA,MAAMC,QAAQ,GAAGpB,IAAI,CAACqB,KAAK,CAAC,MAAM,CAAC;YAEnC,KAAK,MAAMC,OAAO,IAAIF,QAAQ,EAAE;cAC9B,IAAIE,OAAO,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;gBAC/B,IAAI;kBACF,MAAMC,OAAO,GAAGF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;kBACvC,IAAIF,OAAO,EAAE;oBACX,MAAM9B,IAAI,GAAGG,IAAI,CAAC8B,KAAK,CAACH,OAAO,CAAC;oBAChCrB,UAAU,CAACT,IAAI,CAAC;kBAClB;gBACF,CAAC,CAAC,OAAOkC,CAAC,EAAE;kBACVtC,OAAO,CAACuC,IAAI,CAAC,8BAA8B,EAAEP,OAAO,EAAEM,CAAC,CAAC;gBAC1D;cACF;YACF;UACF;QACF,CAAC,CAAC,OAAOE,GAAG,EAAE;UACZxC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEyC,GAAG,CAAC;UAC3C1B,OAAO,CAAC0B,GAAG,CAAC;QACd;MACF,CAAC;;MAED;MACAhB,iBAAiB,CAAC,CAAC;MAEnB,OAAO;QACL;QACAiB,KAAK,EAAEA,CAAA,KAAMrB,MAAM,CAACsB,MAAM,CAAC;MAC7B,CAAC;IACH,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9De,OAAO,CAACf,KAAK,CAAC;MACd,OAAO,IAAI;IACb;EACF;;EAEA;EACA,MAAM4C,kBAAkBA,CAACC,WAAW,EAAE/B,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEb,SAAS,GAAG,IAAI,EAAE2C,SAAS,GAAG,KAAK,EAAE;IAC1G,IAAI;MACF,MAAMC,WAAW,GAAG;QAClBC,YAAY,EAAEH;MAChB,CAAC;;MAED;MACA,IAAI1C,SAAS,EAAE;QACb4C,WAAW,CAACE,UAAU,GAAG9C,SAAS;MACpC;;MAEA;MACA,IAAI2C,SAAS,EAAE;QACbC,WAAW,CAACG,UAAU,GAAG,IAAI;MAC/B;MAEA,MAAM5D,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,cAAc,EAAE;QAC1DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDmB,IAAI,EAAEJ,IAAI,CAACC,SAAS,CAACsC,WAAW;MAClC,CAAC,CAAC;MAEF,IAAI,CAACzD,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMyD,SAAS,GAAG,MAAM7D,QAAQ,CAACqB,IAAI,CAAC,CAAC;QACvCV,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEmD,SAAS,CAAC;QAClE,MAAM,IAAItD,KAAK,CAAC,WAAWP,QAAQ,CAACS,MAAM,MAAMT,QAAQ,CAAC8B,UAAU,GAAG,CAAC;MACzE;MAEA,MAAMC,MAAM,GAAG/B,QAAQ,CAACsB,IAAI,CAACU,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;MAEjC,MAAM4B,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC,IAAI;UACF,OAAO,IAAI,EAAE;YACX,MAAM;cAAE1B,KAAK;cAAEC;YAAK,CAAC,GAAG,MAAMN,MAAM,CAACO,IAAI,CAAC,CAAC;YAC3C,IAAID,IAAI,EAAE;cACR1B,OAAO,CAACG,GAAG,CAAC,uBAAuB,CAAC;cACpCY,UAAU,CAAC,CAAC;cACZ;YACF;YACA,MAAML,IAAI,GAAGY,OAAO,CAACM,MAAM,CAACH,KAAK,EAAE;cAAEI,MAAM,EAAE;YAAK,CAAC,CAAC;YACpD;YACA,MAAMC,QAAQ,GAAGpB,IAAI,CAACqB,KAAK,CAAC,MAAM,CAAC,CAACqB,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACjB,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;;YAEtE,KAAK,MAAMJ,OAAO,IAAIF,QAAQ,EAAE;cAC9B,IAAIE,OAAO,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;gBAC/B,IAAI;kBACF,MAAMC,OAAO,GAAGF,OAAO,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;kBACvC,IAAIF,OAAO,EAAE;oBACX,MAAM9B,IAAI,GAAGG,IAAI,CAAC8B,KAAK,CAACH,OAAO,CAAC;oBAChCrB,UAAU,CAACT,IAAI,CAAC,CAAC,CAAC;kBACpB;gBACF,CAAC,CAAC,OAAOkC,CAAC,EAAE;kBACVtC,OAAO,CAACuC,IAAI,CAAC,mCAAmC,EAAEP,OAAO,EAAEM,CAAC,CAAC;gBAC/D;cACF;YACF;UACF;QACF,CAAC,CAAC,OAAOE,GAAG,EAAE;UACZxC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEyC,GAAG,CAAC;UAChD1B,OAAO,CAAC0B,GAAG,CAAC;QACd;MACF,CAAC;MAEDW,aAAa,CAAC,CAAC;MAEf,OAAO;QACLV,KAAK,EAAEA,CAAA,KAAMrB,MAAM,CAACsB,MAAM,CAAC;MAC7B,CAAC;IACH,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnEe,OAAO,CAACf,KAAK,CAAC;MACd,OAAO,IAAI;IACb;EACF;EACA;;EAEA;EACA,MAAMuD,WAAWA,CAACC,IAAI,EAAE;IACtB,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;MAE7B,MAAMlE,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,gBAAgB,EAAE;QAC5DM,MAAM,EAAE,MAAM;QACdoB,IAAI,EAAE6C;MACR,CAAC,CAAC;MAEF,IAAI,CAACnE,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,UAAUR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAClE;MAEA,OAAO,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAM4D,QAAQA,CAACC,OAAO,EAAE;IACtB,IAAI;MACF,MAAMvE,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,kBAAkB2E,OAAO,EAAE,CAAC;MAExE,IAAI,CAACvE,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIG,KAAK,CAAC,UAAUP,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC9C;MAEA,OAAOT,QAAQ,CAACwE,GAAG;IACrB,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAM+D,aAAaA,CAAC5C,MAAM,EAAE6C,IAAI,GAAG,WAAW,EAAE;IAC9C,IAAI;MACF,MAAM1E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,kBAAkB,EAAE;QAC9DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDmB,IAAI,EAAEJ,IAAI,CAACC,SAAS,CAAC;UAAEU,MAAM;UAAE6C;QAAK,CAAC;MACvC,CAAC,CAAC;MAEF,IAAI,CAAC1E,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,UAAUR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAClE;MAEA,OAAO,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMiE,oBAAoBA,CAACC,UAAU,EAAEpD,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEmD,OAAO,GAAG,CAAC,CAAC,EAAE;IACpF,IAAI;MACFlE,OAAO,CAACG,GAAG,CAAC,+CAA+C,EAAE8D,UAAU,CAAC;MACxEjE,OAAO,CAACG,GAAG,CAAC,UAAU,EAAE,GAAG,IAAI,CAAChB,OAAO,yBAAyB,CAAC;;MAEjE;MACA,MAAMe,SAAS,GAAGgE,OAAO,CAAClB,UAAU,CAAC,CAAC;MACtC,MAAMF,WAAW,GAAG5C,SAAS,GACzB;QAAEiE,WAAW,EAAEF,UAAU;QAAEjB,UAAU,EAAE9C;MAAU,CAAC,GAClD;QAAEiE,WAAW,EAAEF;MAAW,CAAC;MAE/B,MAAM5E,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACH,OAAO,yBAAyB,EAAE;QACrEI,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDmB,IAAI,EAAEJ,IAAI,CAACC,SAAS,CAACsC,WAAW;MAClC,CAAC,CAAC;MAEF,IAAI,CAACzD,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMyD,SAAS,GAAG,MAAM7D,QAAQ,CAACqB,IAAI,CAAC,CAAC;QACvCV,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEmD,SAAS,CAAC;QAClD,MAAM,IAAItD,KAAK,CAAC,UAAUP,QAAQ,CAACS,MAAM,MAAMT,QAAQ,CAAC8B,UAAU,kBAAkB,CAAC;MACvF;MAEA,MAAM;QAAE6B,UAAU;QAAElD;MAAO,CAAC,GAAG,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;MAEpD,IAAIG,MAAM,KAAK,SAAS,IAAI,CAACkD,UAAU,EAAE;QACvC,MAAM,IAAIpD,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MAEAI,OAAO,CAACG,GAAG,CAAC,iBAAiB,EAAE6C,UAAU,CAAC;;MAE1C;MACA,MAAMoB,gBAAgB,GAAIC,gBAAgB,IAAK;QAC7C,MAAMC,EAAE,GAAG,IAAIC,WAAW,CAAC,GAAG,IAAI,CAACtF,OAAO,yBAAyBoF,gBAAgB,EAAE,CAAC;QAEtFC,EAAE,CAACE,MAAM,GAAG,MAAM;UAChBxE,OAAO,CAACG,GAAG,CAAC,2CAA2CkE,gBAAgB,EAAE,CAAC;UAC1E,IAAIxD,UAAU,EAAE;YACdA,UAAU,CAAC;cACTmC,UAAU,EAAEqB,gBAAgB;cAC5BvE,MAAM,EAAE,eAAe;cACvBkC,OAAO,EAAE;gBAAEyC,MAAM,EAAE,QAAQ;gBAAE/D,IAAI,EAAE,UAAU;gBAAEgE,IAAI,EAAE;cAAK;YAC5D,CAAC,CAAC;UACJ;QACF,CAAC;QAEDJ,EAAE,CAACK,SAAS,GAAIC,KAAK,IAAK;UACxB,IAAI;YACF,MAAMxE,IAAI,GAAGG,IAAI,CAAC8B,KAAK,CAACuC,KAAK,CAACxE,IAAI,CAAC;;YAEnC;YACA,IAAIA,IAAI,CAACN,MAAM,KAAK,WAAW,EAAE;cAC/B;cACA;YACF;YAEA,IAAIe,UAAU,EAAEA,UAAU,CAACT,IAAI,CAAC;YAEhC,IAAIA,IAAI,CAACN,MAAM,KAAK,WAAW,IAAIM,IAAI,CAACN,MAAM,KAAK,OAAO,EAAE;cAAA,IAAA+E,aAAA;cAC1DP,EAAE,CAACQ,KAAK,CAAC,CAAC;cACV,IAAI/D,UAAU,IAAIX,IAAI,CAACN,MAAM,KAAK,WAAW,EAAEiB,UAAU,CAACX,IAAI,CAAC;cAC/D,IAAIU,OAAO,IAAIV,IAAI,CAACN,MAAM,KAAK,OAAO,EAAEgB,OAAO,CAAC,IAAIlB,KAAK,CAAC,EAAAiF,aAAA,GAAAzE,IAAI,CAAC4B,OAAO,cAAA6C,aAAA,uBAAZA,aAAA,CAAcnE,IAAI,KAAI,sBAAsB,CAAC,CAAC;YAC1G;UACF,CAAC,CAAC,OAAO4B,CAAC,EAAE;YACVtC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEuC,CAAC,EAAE,OAAO,EAAEsC,KAAK,CAACxE,IAAI,CAAC;YAC3D,IAAIU,OAAO,EAAEA,OAAO,CAAC,IAAIlB,KAAK,CAAC,+BAA+B,CAAC,CAAC;UAClE;QACF,CAAC;QAED0E,EAAE,CAACS,OAAO,GAAIhF,KAAK,IAAK;UACtBC,OAAO,CAACD,KAAK,CAAC,4BAA4BsE,gBAAgB,GAAG,EAAEtE,KAAK,CAAC;UACrEuE,EAAE,CAACQ,KAAK,CAAC,CAAC;UACV;UACA,IAAIhE,OAAO,EAAE;YACXA,OAAO,CAAC,IAAIlB,KAAK,CAAC,+DAA+D,CAAC,CAAC;UACrF;QACF,CAAC;QAED,OAAO0E,EAAE,CAAC,CAAC;MACb,CAAC;MAED,IAAIU,mBAAmB,GAAGZ,gBAAgB,CAACpB,UAAU,CAAC;MAEtD,OAAO;QACL9C,SAAS,EAAE8C,UAAU;QAAE;QACvB8B,KAAK,EAAEA,CAAA,KAAM;UACX,IAAIE,mBAAmB,EAAE;YACvBhF,OAAO,CAACG,GAAG,CAAC,cAAc6C,UAAU,EAAE,CAAC;YACvCgC,mBAAmB,CAACF,KAAK,CAAC,CAAC;UAC7B;QACF;MACF,CAAC;IAEH,CAAC,CAAC,OAAO/E,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAIe,OAAO,EAAE;QACXA,OAAO,CAACf,KAAK,CAAC;MAChB;MACA,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMkF,aAAaA,CAAC/E,SAAS,EAAE;IAC7B,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,aAAaiB,SAAS,EAAE,EAAE;QACpEX,MAAM,EAAE;MACV,CAAC,CAAC;MACF,IAAI,CAACF,QAAQ,CAACI,EAAE,IAAIJ,QAAQ,CAACS,MAAM,KAAK,GAAG,EAAE;QAAE;QAC7C,MAAMJ,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC,CAACuF,KAAK,CAAC,OAAO;UAAErF,MAAM,EAAE;QAAwC,CAAC,CAAC,CAAC;QAC1G,MAAM,IAAID,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,UAAUR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAClE;MACA;MACA,OAAO;QAAEqF,OAAO,EAAE,IAAI;QAAEnD,OAAO,EAAE,WAAW9B,SAAS;MAAY,CAAC;IACpE,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0BG,SAAS,GAAG,EAAEH,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMqF,eAAeA,CAACC,OAAO,EAAE;IAC7B,IAAI;MACF,MAAMhG,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,WAAWoG,OAAO,UAAU,CAAC;MAEzE,IAAI,CAAChG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,UAAUR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAClE;MAEA,OAAO,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoCsF,OAAO,GAAG,EAAEtF,KAAK,CAAC;MACpE,MAAMA,KAAK;IACb;EACF;EAEA,MAAMuF,aAAaA,CAACpF,SAAS,EAAEqF,UAAU,EAAE;IACzC,IAAI;MACF,MAAMlG,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,aAAaiB,SAAS,EAAE,EAAE;QACpEX,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDmB,IAAI,EAAEJ,IAAI,CAACC,SAAS,CAAC+E,UAAU;MACjC,CAAC,CAAC;MAEF,IAAI,CAAClG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,2BAA2BR,QAAQ,CAACS,MAAM,EAAE,CAAC;MACnF;MAEA,OAAO,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0BG,SAAS,GAAG,EAAEH,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMyF,gBAAgBA,CAACH,OAAO,EAAEI,eAAe,EAAEC,WAAW,EAAE;IAC5D,IAAI;MACF,MAAMrG,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACL,OAAO,WAAWoG,OAAO,eAAe,EAAE;QAC7E9F,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDmB,IAAI,EAAEJ,IAAI,CAACC,SAAS,CAAC;UACnBmF,gBAAgB,EAAEF,eAAe;UACjCC,WAAW,EAAEA;QACf,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACrG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,UAAUR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAClE;MAEA,OAAO,MAAMT,QAAQ,CAACM,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmCsF,OAAO,GAAG,EAAEtF,KAAK,CAAC;MACnE,MAAMA,KAAK;IACb;EACF;AACF;;AAGA;AACA,MAAM6F,UAAU,GAAG,IAAI7G,UAAU,CAAC,CAAC;AACnC,eAAe6G,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}