{"version": 3, "file": "static/js/935.30dfd622.chunk.js", "mappings": "2OAOA,MAoKA,EApKiBA,IAAwB,IAAvB,cAAEC,GAAeD,EAC/B,MAAOE,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAAcC,IAAmBF,EAAAA,EAAAA,WAAS,GAC3CG,GAAiBC,EAAAA,EAAAA,QAAO,OAG9BC,EAAAA,EAAAA,YAAU,KAAO,IAADC,EACU,QAAtBA,EAAAH,EAAeI,eAAO,IAAAD,GAAtBA,EAAwBE,eAAe,CAAEC,SAAU,aACpD,CAACX,IA+GJ,OACIY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,EAErDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kFAAiFC,SAAA,EAC5FC,EAAAA,EAAAA,KAAA,UACEC,QAVQC,KAClBhB,EAAY,IACZG,GAAgB,IASNS,UAAU,gFACVK,MAAM,qBAAKJ,UAETC,EAAAA,EAAAA,KAACI,EAAAA,IAAM,CAACC,KAAM,QAElBR,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+DAA8DC,SAAA,EACzEC,EAAAA,EAAAA,KAACM,EAAAA,GAAU,CAACR,UAAU,uBAAuB,0BAKrDD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wDAAuDC,SAAA,CAC7C,IAApBd,EAASsB,SACNV,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mFAAkFC,SAAA,EAC7FC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,8BAA6BC,SAAC,kBAC9CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uBAAsBC,SAAC,4CACvCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kDAAiDC,SAAC,4HAKtEd,EAASuB,KAAI,CAACC,EAAKC,KAChBV,EAAAA,EAAAA,KAACW,EAAAA,EAAW,CAAuBC,QAASH,GAA1BA,EAAII,IAAMH,MAEhCV,EAAAA,EAAAA,KAAA,OAAKc,IAAKxB,QAIdU,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACpEC,EAAAA,EAAAA,KAACe,EAAAA,EAAY,CACTC,cAjJUC,UAEtB,MAAMC,EAAqC,kBAAhBC,EAA2BA,EAAcA,EAAYP,QAC1EQ,EAA+B,kBAAhBD,EAA2BA,EAAYC,MAAQ,GAEpE,IAAiB,OAAXF,QAAW,IAAXA,IAAAA,EAAaG,WAAYD,GAA0B,IAAjBA,EAAMb,SAAkBnB,EAAc,OAG9E,MAAMkC,EAAc,CAChBT,GAAG,QAADU,OAAUC,KAAKC,OACjBC,OAAQ,OACRC,KAAMT,GAAe,iCACrBE,MAAOA,GAAS,GAChBQ,WAAW,IAAIJ,MAAOK,eAE1B3C,GAAY4C,GAAQ,IAAIA,EAAMR,KAC9BjC,GAAgB,GAEhB,UAEU0C,EAAAA,EAAWC,mBACbd,GACCe,IACG/C,GAAYgD,IACR,MAAMC,EAAc,IAAID,GAGxB,GAA4B,WAAxBD,EAAaP,OAMb,OALAS,EAAYC,MAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACXJ,GAAY,IACfK,KAAM,cACNV,UAAWK,EAAaL,YAAa,IAAIJ,MAAOK,iBAE7CM,EAIX,GAA4B,OAAxBF,EAAaP,OAAiB,CAE9B,MAAMa,EAAiBJ,EAAYK,WAAU/B,GAAOA,EAAII,KAAOoB,EAAapB,KAE5E,IAAwB,IAApB0B,EAAuB,CAEvB,MAAME,EAAkBN,EAAYI,GAEhCN,EAAaS,UAEbP,EAAYI,IAAeF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACpBI,GAAe,IAClBd,MAAOc,EAAgBd,MAAQ,KAAOM,EAAaN,MAAQ,IAC3DgB,kBAA4CC,IAA9BX,EAAaU,aAA6BV,EAAaU,aAAeF,EAAgBE,aACpGf,UAAWK,EAAaL,WAAaa,EAAgBb,YAIzDO,EAAYI,IAAeF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACpBI,GAAe,IAClBd,UAA4BiB,IAAtBX,EAAaN,KAAqBM,EAAaN,KAAOc,EAAgBd,KAC5EgB,kBAA4CC,IAA9BX,EAAaU,aAA6BV,EAAaU,aAAeF,EAAgBE,aACpGE,qBAAkDD,IAAjCX,EAAaY,gBAAgCZ,EAAaY,gBAAkBJ,EAAgBI,gBAC7GjB,UAAWK,EAAaL,WAAaa,EAAgBb,WAGjE,MAEIO,EAAYC,MAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EACXJ,GAAY,IACfK,KAAM,KACNV,UAAWK,EAAaL,YAAa,IAAIJ,MAAOK,gBAG5D,CACA,OAAOM,QAGdW,IACGC,QAAQD,MAAM,wBAAyBA,GACvCzD,GAAgB,GAChBH,GAAY4C,GAAQ,IAAIA,EAAM,CAC1BjB,GAAG,SAADU,OAAWC,KAAKC,OAClBC,OAAQ,SACRY,KAAM,WACNX,KAAK,6BAADJ,OAAWuB,EAAMlC,SACrBgB,WAAW,IAAIJ,MAAOK,qBAG9B,KAEIxC,GAAgB,KAG5B,CAAE,MAAO2D,GACLD,QAAQD,MAAM,4BAA6BE,GAC3C3D,GAAgB,GAChBH,GAAY4C,GAAQ,IAAIA,EAAM,CAC1BjB,GAAG,aAADU,OAAeC,KAAKC,OACtBC,OAAQ,SACRY,KAAM,WACNX,KAAK,6DAADJ,OAAkByB,EAASpC,SAC/BgB,WAAW,IAAIJ,MAAOK,iBAE9B,GA6CYzC,aAAcA,EACd6D,YAAY,kDCtIhC,EA1BqBC,KACnB,MAAOlE,EAAemE,IAAoBhE,EAAAA,EAAAA,UAAS,OAC5CiE,EAAgBC,IAAqBlE,EAAAA,EAAAA,WAAS,GAYrD,OACEU,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uCAAsCC,SAAA,EACnDC,EAAAA,EAAAA,KAACsD,EAAAA,EAAO,CACNF,eAAgBA,EAChBG,iBARiBC,KACrBH,GAAmBD,IAQfK,aAfoBC,IACxBP,EAAiBO,GACjBX,QAAQY,IAAI,4BAASD,OAenB1D,EAAAA,EAAAA,KAAC4D,EAAQ,CAAC5E,cAAeA,O", "sources": ["views/ChatView.js", "views/ChatViewPage.js"], "sourcesContent": ["// frontend/src/views/ChatView.js\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport MessageInput from '../components/MessageInput';\r\nimport ChatMessage from '../components/ChatMessage';\r\nimport apiService from '../services/api';\r\nimport { FaPlus, FaComments } from 'react-icons/fa';\r\n\r\nconst ChatView = ({ currentChatId }) => {\r\n    const [messages, setMessages] = useState([]);\r\n    const [isGenerating, setIsGenerating] = useState(false);\r\n    const messagesEndRef = useRef(null);\r\n\r\n    // 自动滚动到最新消息\r\n    useEffect(() => {\r\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n    }, [messages]);\r\n\r\n    const handleSendMessage = async (messageData) => {\r\n        // 兼容旧版本调用（直接传字符串）和新版本（传对象）\r\n        const messageText = typeof messageData === 'string' ? messageData : messageData.message;\r\n        const files = typeof messageData === 'object' ? messageData.files : [];\r\n        \r\n        if ((!messageText?.trim() && (!files || files.length === 0)) || isGenerating) return;\r\n\r\n        // 添加用户消息\r\n        const userMessage = {\r\n            id: `user-${Date.now()}`,\r\n            sender: 'user',\r\n            text: messageText || '发送了文件',\r\n            files: files || [],\r\n            timestamp: new Date().toISOString()\r\n        };\r\n        setMessages(prev => [...prev, userMessage]);\r\n        setIsGenerating(true);\r\n\r\n        try {\r\n            // 开始流式AI响应\r\n            await apiService.streamChatResponse(\r\n                messageText,\r\n                (progressData) => {\r\n                    setMessages(prevMessages => {\r\n                        const newMessages = [...prevMessages];\r\n\r\n                        // 处理系统消息 - 总是作为新的、独立的系统消息添加\r\n                        if (progressData.sender === 'system') {\r\n                            newMessages.push({\r\n                                ...progressData,\r\n                                type: 'system-info',\r\n                                timestamp: progressData.timestamp || new Date().toISOString()\r\n                            });\r\n                            return newMessages;\r\n                        }\r\n\r\n                        // 处理AI消息 (sender === 'ai')\r\n                        if (progressData.sender === 'ai') {\r\n                            // 查找具有相同ID的现有AI消息\r\n                            const aiMessageIndex = newMessages.findIndex(msg => msg.id === progressData.id);\r\n\r\n                            if (aiMessageIndex !== -1) {\r\n                                // 找到现有消息\r\n                                const existingMessage = newMessages[aiMessageIndex];\r\n                                \r\n                                if (progressData.is_append) {\r\n                                    // 如果是追加内容，将文本追加到现有消息\r\n                                    newMessages[aiMessageIndex] = {\r\n                                        ...existingMessage,\r\n                                        text: (existingMessage.text || '') + (progressData.text || ''),\r\n                                        is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,\r\n                                        timestamp: progressData.timestamp || existingMessage.timestamp\r\n                                    };\r\n                                } else {\r\n                                    // 如果不是追加，更新整个消息状态\r\n                                    newMessages[aiMessageIndex] = {\r\n                                        ...existingMessage,\r\n                                        text: progressData.text !== undefined ? progressData.text : existingMessage.text,\r\n                                        is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,\r\n                                        stream_complete: progressData.stream_complete !== undefined ? progressData.stream_complete : existingMessage.stream_complete,\r\n                                        timestamp: progressData.timestamp || existingMessage.timestamp\r\n                                    };\r\n                                }\r\n                            } else {\r\n                                // 如果未找到现有AI消息，作为新消息添加\r\n                                newMessages.push({\r\n                                    ...progressData,\r\n                                    type: 'ai',\r\n                                    timestamp: progressData.timestamp || new Date().toISOString()\r\n                                });\r\n                            }\r\n                        }\r\n                        return newMessages;\r\n                    });\r\n                },\r\n                (error) => {\r\n                    console.error('Chat streaming error:', error);\r\n                    setIsGenerating(false);\r\n                    setMessages(prev => [...prev, {\r\n                        id: `error-${Date.now()}`,\r\n                        sender: 'system',\r\n                        type: 'ai_error',\r\n                        text: `发生错误: ${error.message}`,\r\n                        timestamp: new Date().toISOString()\r\n                    }]);\r\n                },\r\n                () => {\r\n                    // 完成回调\r\n                    setIsGenerating(false);\r\n                }\r\n            );\r\n        } catch (apiError) {\r\n            console.error('API call failed to start:', apiError);\r\n            setIsGenerating(false);\r\n            setMessages(prev => [...prev, {\r\n                id: `error-api-${Date.now()}`,\r\n                sender: 'system',\r\n                type: 'ai_error',\r\n                text: `无法连接到AI聊天服务: ${apiError.message}`,\r\n                timestamp: new Date().toISOString()\r\n            }]);\r\n        }\r\n    };\r\n\r\n    const handleNewChat = () => {\r\n        setMessages([]);\r\n        setIsGenerating(false);\r\n    };\r\n\r\n    return (\r\n        <div className=\"flex flex-col h-screen bg-gray-50 flex-1\">\r\n            {/* Header for Chat View */}\r\n            <div className=\"h-16 px-3 border-b border-gray-200 bg-white flex items-center sticky top-0 z-10\">\r\n                <button \r\n                  onClick={handleNewChat}\r\n                  className=\"p-2 mr-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100\"\r\n                  title=\"新聊天\"\r\n                >\r\n                    <FaPlus size={16} />\r\n                </button>\r\n                <div className=\"flex-1 text-base font-medium text-gray-800 flex items-center\">\r\n                    <FaComments className=\"mr-2 text-blue-500\" /> AI 聊天\r\n                </div>\r\n            </div>\r\n\r\n            {/* Chat Messages Area */}\r\n            <div className=\"flex-1 overflow-y-auto p-4 space-y-5 custom-scrollbar\">\r\n                {messages.length === 0 && (\r\n                    <div className=\"flex flex-col items-center justify-center h-full text-gray-400 p-8 min-h-[400px]\">\r\n                        <span className=\"text-6xl mb-6 text-gray-300\">💬</span>\r\n                        <span className=\"text-2xl font-medium\">开始你的AI对话</span>\r\n                        <p className=\"text-sm text-gray-400 mt-2 text-center max-w-md\">\r\n                            在这里与AI自由交流，提出问题或寻求帮助。\r\n                        </p>\r\n                    </div>\r\n                )}\r\n                {messages.map((msg, index) => (\r\n                    <ChatMessage key={msg.id || index} message={msg} /> \r\n                ))}\r\n                <div ref={messagesEndRef} />\r\n            </div>\r\n\r\n            {/* Message Input Area */}\r\n            <div className=\"p-2 bg-gray-50 sticky bottom-0 border-t border-gray-200\">\r\n                <MessageInput \r\n                    onSendMessage={handleSendMessage} \r\n                    isGenerating={isGenerating}\r\n                    placeholder=\"输入你的消息...\"\r\n                />\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default ChatView;", "import React, { useState } from 'react';\r\nimport LeftNav from '../components/LeftNav';\r\nimport ChatView from './ChatView';\r\n\r\nconst ChatViewPage = () => {\r\n  const [currentChatId, setCurrentChatId] = useState(null);\r\n  const [isTaskListOpen, setIsTaskListOpen] = useState(false);\r\n\r\n  const handleSelectChat = (chatId) => {\r\n    setCurrentChatId(chatId);\r\n    console.log('选择聊天:', chatId);\r\n    // 这里可以加载对应的聊天内容\r\n  };\r\n\r\n  const toggleTaskList = () => {\r\n    setIsTaskListOpen(!isTaskListOpen);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-1 overflow-hidden h-screen\">\r\n      <LeftNav \r\n        isTaskListOpen={isTaskListOpen} \r\n        onToggleTaskList={toggleTaskList}\r\n        onSelectChat={handleSelectChat}\r\n      />\r\n      <ChatView currentChatId={currentChatId} />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatViewPage; "], "names": ["_ref", "currentChatId", "messages", "setMessages", "useState", "isGenerating", "setIsGenerating", "messagesEndRef", "useRef", "useEffect", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "_jsxs", "className", "children", "_jsx", "onClick", "handleNewChat", "title", "FaPlus", "size", "FaComments", "length", "map", "msg", "index", "ChatMessage", "message", "id", "ref", "MessageInput", "onSendMessage", "async", "messageText", "messageData", "files", "trim", "userMessage", "concat", "Date", "now", "sender", "text", "timestamp", "toISOString", "prev", "apiService", "streamChatResponse", "progressData", "prevMessages", "newMessages", "push", "_objectSpread", "type", "aiMessageIndex", "findIndex", "existingMessage", "is_append", "is_streaming", "undefined", "stream_complete", "error", "console", "apiError", "placeholder", "ChatViewPage", "setCurrentChatId", "isTaskListOpen", "setIsTaskListOpen", "LeftNav", "onToggleTaskList", "toggleTaskList", "onSelectChat", "chatId", "log", "ChatView"], "sourceRoot": ""}