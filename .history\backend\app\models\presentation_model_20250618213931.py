from __future__ import annotations  # 允许在Python 3.7+中使用Type | None语法
from pydantic import BaseModel, Field, ConfigDict, AfterValidator, AliasChoices, RootModel
from typing import List, Dict, Any, Literal, Annotated, Optional, Union
import uuid
from datetime import datetime

# --- 新增：自定义验证器，用于确保数据质量 ---
def check_hex_code(v: str) -> str:
    """验证并修复HEX颜色码，确保以'#'开头。也可以处理简单的rgba转换。"""
    if isinstance(v, str):
        # 标准HEX格式
        if v.startswith('#') and len(v) in [4, 7]:
            return v
        # 如果模型忘记加'#'，我们自动补上
        if not v.startswith('#') and len(v) in [3, 6]:
            return f"#{v}"
        # 处理rgba格式 - 简单转换为HEX（忽略透明度）
        if v.startswith('rgba(') and v.endswith(')'):
            try:
                # 提取RGB值：rgba(196, 166, 107, 0.3) -> [196, 166, 107, 0.3]
                rgba_values = v[5:-1].split(',')
                if len(rgba_values) >= 3:
                    r = int(rgba_values[0].strip())
                    g = int(rgba_values[1].strip())
                    b = int(rgba_values[2].strip())
                    # 转换为HEX
                    return f"#{r:02x}{g:02x}{b:02x}"
            except (ValueError, IndexError):
                pass
        # 处理rgb格式 - rgb(196, 166, 107)
        if v.startswith('rgb(') and v.endswith(')'):
            try:
                rgb_values = v[4:-1].split(',')
                if len(rgb_values) >= 3:
                    r = int(rgb_values[0].strip())
                    g = int(rgb_values[1].strip())
                    b = int(rgb_values[2].strip())
                    return f"#{r:02x}{g:02x}{b:02x}"
            except (ValueError, IndexError):
                pass
    # 如果格式完全错误，抛出异常，instructor会尝试让LLM重新生成
    raise ValueError("Invalid hex color format. Must be a valid 3 or 6-digit hex code.")

def validate_chart_config(v: Dict[str, Any]) -> Dict[str, Any]:
    """验证Chart.js配置对象的基本结构"""
    if not isinstance(v, dict):
        raise ValueError("Chart config must be a dictionary")
    
    # 确保必需的键存在
    required_keys = ["type", "data"]
    for key in required_keys:
        if key not in v:
            # 尝试自动修复
            if key == "type":
                v["type"] = "bar"  # 默认类型
            elif key == "data":
                v["data"] = {"labels": [], "datasets": []}  # 默认空数据
    
    # 确保options存在
    if "options" not in v:
        v["options"] = {"responsive": True, "maintainAspectRatio": False}
    
    return v

# 使用Annotated来应用验证器
ColorHex = Annotated[str, AfterValidator(check_hex_code)]
ChartConfig = Annotated[Dict[str, Any], AfterValidator(validate_chart_config)]

class UserRequestInfo(BaseModel):
    raw_input: str
    topic: Optional[str] = None
    target_audience: Optional[str] = None
    presentation_style: Optional[str] = None # e.g., "professional", "creative", "data-driven"
    duration_minutes: Optional[int] = None
    key_content_points: Optional[List[str]] = None
    constraints_exclusions: Optional[List[str]] = None
    source_document_urls: Optional[List[str]] = None # 用户提供的URL
    num_slides: Optional[int] = None  # Added for OutlineAgent
    detected_language: Optional[Literal["zh-CN", "en-US"]] = "zh-CN"  # Default to Chinese
    inferred_visual_style_profile: Optional[Dict[str, Any]] = None  # Added for InputProcessingAgent
    # Note: inferred_visual_style_profile now includes:
    # - visual_metaphor: Optional[str] - e.g., "水墨画卷展开" or "熔岩与玄铁"
    # - element_style_preferences: Optional[Dict[str, str]] - e.g., {"buttons": "rounded_pill_gradient_shadow", "dividers": "thin_gradient_line_subtle_glow"}

class SlideOutline(BaseModel):
    slide_title: str
    slide_type: str  # e.g., "Title Slide", "Content Slide", "DataFocusSlide"
    key_points: List[str]
    potential_visual: Optional[str] = None # Description of potential chart or visual text emphasis
    chart_type_suggestion: Optional[str] = None
    layout_archetype: Optional[str] = None # e.g., "title_two_column", "full_chart_with_summary"
    slide_tone: Optional[str] = None # e.g., "data_driven_analytical", "persuasive_summary"
    must_include_visualization: Optional[bool] = False # True if a chart is essential
    target_research_labels: Optional[List[str]] = None # Links to specific research points

class PresentationOutline(BaseModel):
    title: str
    slides: List[SlideOutline]
    error_message: Optional[str] = None

class ResearchedDataPoint(BaseModel):
    label: str
    value: Any # Can be number, string, list for trends
    unit: Optional[str] = None
    source: Optional[str] = None
    relevance_score: Optional[float] = Field(default=0.0, description="How relevant this data is to the overall topic")
    data_type: Optional[Literal["statistic", "trend", "quote", "fact"]] = None
    insight_value_description: Optional[str] = None # Description of the insight value this data point provides
    raw_data_for_chart: Optional[Dict[str, Any]] = None # Structured data for chart generation
    suggested_visualization_type: Optional[str] = None # Suggested chart type for this data

class ResearchOutput(BaseModel):
    summary: Optional[str] = None
    key_subtopics: Optional[List[str]] = None
    structured_data_points: Optional[List[ResearchedDataPoint]] = []
    cited_urls: Optional[List[str]] = [] # URLs found and used by ResearchAgent
    potential_chart_ideas: Optional[List[str]] = []
    error_message: Optional[str] = None # Added for error reporting

class SlideElement(BaseModel):
    element_id: str = Field(default_factory=lambda: f"elem_{uuid.uuid4().hex[:6]}")
    element_type: Literal["title", "subtitle", "text_block", "bullet_points", "image_placeholder", "chart_placeholder", "speaker_notes", "heading_main", "heading_sub", "paragraph", "bullet_point_item", "quote"] # Added 'quote'
    content: Optional[str] = None # For text, or image query, or chart data description
    position_hint: Optional[str] = None # e.g., "top_center", "left_column" (from LayoutAgent)
    style_hints: Optional[Dict[str, Any]] = None # Renamed from style_hint to style_hints
    target_template_area: Optional[str] = None # e.g., "main_title_area", "left_column", "chart_summary_area"
    html_structure_suggestion: Optional[str] = None # AI can suggest HTML structure for this element

class ChartConfig(BaseModel):
    chart_canvas_id: str
    chart_type: Literal["bar", "line", "pie", "radar", "scatter", "doughnut"]
    chart_js_data: Dict[str, Any] # Corresponds to Chart.js 'data' object
    chart_js_options: Optional[Dict[str, Any]] = None # Corresponds to Chart.js 'options'
    chart_title: Optional[str] = None
    data_source_description: Optional[str] = None # Where the data for this chart came from
    
    @property
    def chart_js_config(self) -> Dict[str, Any]:
        """
        Returns a complete Chart.js configuration object merging type, data and options.
        """
        return {
            "type": self.chart_type,
            "data": self.chart_js_data,
            "options": self.chart_js_options or {"responsive": True, "maintainAspectRatio": False}
        }

class VisualAsset(BaseModel):
    asset_type: Literal["chart"] # 目前只支持图表
    alt_text: Optional[str] = None # 对于图表，可以是图表标题或简短描述
    chart_config: Optional[ChartConfig] = None # If it's a chart
    target_template_area: Optional[str] = None # e.g., "CHART_AREA_1", "card_1_chart_area"

class SlideLayout(BaseModel):
    layout_name: str # e.g., "title_and_content", "two_column_with_chart"
    css_classes_map: Optional[Dict[str, str]] = None # Maps element_id to specific CSS classes for styling
    font_family: Optional[str] = None
    color_palette_hints: Optional[Dict[str, str]] = None

class SlideObject(BaseModel):
    slide_id: str = Field(default_factory=lambda: f"slide_{uuid.uuid4().hex[:8]}")
    order: int
    slide_type_suggestion: Optional[str] = None # e.g., "title", "content", "section_header", "data_visualization"
    title_suggestion: str
    focus_suggestion: str # Key message or purpose of this slide
    
    final_title: Optional[str] = None
    text_elements: Optional[List[SlideElement]] = [] # For bullet points, paragraphs
    speaker_notes: Optional[str] = None
    visual_assets_data: Optional[List[VisualAsset]] = [] # Holds image URLs or chart configs
    layout_properties: Optional[SlideLayout] = None
    
    html_content: Optional[str] = None # Final rendered HTML for this slide
    
    critique_feedback: Optional[List[Dict[str, Any]]] = None
    user_feedback_history: Optional[List[str]] = None
    version: int = 0 # Start version at 0

    # Temporary fields from outline to be used by downstream agents
    _temp_suggested_chart_type: Optional[str] = None
    _temp_relevant_data_keys: Optional[List[str]] = None
    _temp_speaker_notes_prompt: Optional[str] = None
    _temp_potential_visual: Optional[str] = None
    _temp_html_structure_json: Optional[Dict[str, Any]] = None # Stores the JSON structure from ContentAgent
    
    # Fields from SlideOutline for layout and styling
    layout_archetype: Optional[str] = None # e.g., "title_two_column", "full_chart_with_summary"
    slide_tone: Optional[str] = None # e.g., "data_driven_analytical", "persuasive_summary"
    target_research_labels: Optional[List[str]] = None # Links to specific research points
    must_include_visualization: Optional[bool] = False # True if a chart is essential

class PresentationObject(BaseModel):
    project_id: str
    user_request: UserRequestInfo
    creation_timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    last_updated_timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    
    overall_theme_settings: Optional[Dict[str, Any]] = None # e.g., font, color palette name
    research_data: Optional[ResearchOutput] = None
    presentation_outline: Optional[PresentationOutline] = None # Full outline object
    slides: List[SlideObject] = []
    
    current_status: str = "initializing" # e.g., "researching", "outlining", "content_generation", "designing", "review", "completed"
    error_log: Optional[List[str]] = []
    
    project_temp_path: Optional[str] = None  # Path for storing project temporary files
    project_output_path: Optional[str] = None  # Path for storing project output files
    project_files_manifest: Optional[Dict[str, str]] = None  # Map of file identifiers to their paths

    def get_slide_by_id(self, slide_id: str) -> Optional[SlideObject]:
        for slide in self.slides:
            if slide.slide_id == slide_id:
                return slide
        return None

    def update_slide(self, updated_slide: SlideObject):
        for i, slide in enumerate(self.slides):
            if slide.slide_id == updated_slide.slide_id:
                self.slides[i] = updated_slide
                self.last_updated_timestamp = datetime.now().isoformat()
                return
        # If not found, maybe add it - though typically should exist
        # self.slides.append(updated_slide)

# Models for OrchestratorProgress and AiChatMessage mentioned in OrchestrationAgent
# These might be in a different file like 'slide_models.py' or 'agent_io_models.py'
# For now, defining them here for completeness if not defined elsewhere.

class AiChatMessage(BaseModel):
    id: Optional[str] = Field(default_factory=lambda: f"msg_{uuid.uuid4().hex[:8]}")
    sender: str
    text: str
    icon: Optional[str] = None
    thinking: Optional[bool] = None
    thinking_status: Optional[str] = None  # 思考状态描述，如"正在思考..."
    is_streaming: Optional[bool] = None  # 是否为流式输出
    is_append: Optional[bool] = None  # 是否为追加内容
    stream_complete: Optional[bool] = None  # 流式输出是否完成
    timestamp: Optional[str] = Field(default_factory=lambda: datetime.now().isoformat())
    data: Optional[Dict[str, Any]] = None  # 附加数据字段，用于搜索建议等特殊功能

class Slide(BaseModel):
    id: str
    slide_number: Optional[int] = None  # 1-based slide number
    html: Optional[str] = None  # HTML content
    code: Optional[str] = None  # HTML code (for compatibility)
    blueprint_json: Optional[str] = None  # 详细的幻灯片蓝图JSON (原 original_prompt)
    image: Optional[Dict[str, str]] = None  # Image information with url and description
    
    model_config = ConfigDict(from_attributes=True)

class OrchestratorProgress(BaseModel):
    project_id: str
    status: str
    message: Optional[AiChatMessage] = None
    slide_update: Optional[Slide] = None
    code_content: Optional[str] = None
    current_slide_index: Optional[int] = None
    total_slides: Optional[int] = None
    ui_action: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None  # 添加元数据字段，可用于传递特殊信息如项目ID映射

# Models for other agents if not fully defined elsewhere (based on OrchestrationAgent needs)
class InputProcessingAgentOutput(BaseModel): # Conceptual model, actual might differ
    user_request: Optional[UserRequestInfo] = None
    error_message: Optional[str] = None

class VisualAssetAgentOutput(BaseModel): # Conceptual model
    image_url: Optional[str] = None
    image_prompt: Optional[str] = None
    error_message: Optional[str] = None

class CritiqueAgentOutput(BaseModel): # Conceptual model
    overall_score: float = 0.0
    summary_of_issues: Optional[str] = None
    detailed_feedback: List[Dict[str, Any]] = Field(default_factory=list)
    refinement_suggestions: List[Dict[str, Any]] = Field(default_factory=list)
    needs_revision: bool = False
    error_message: Optional[str] = None
    # Pydantic model_dump() will be used from the agent
    # For now, ensure it's a BaseModel for OrchestrationAgent
    model_config = ConfigDict(extra='allow')  # To allow model_dump() even if not all fields defined here 

class GeneratedSlideContent(BaseModel):
    slide_html_structure: Dict[str, Any] # A JSON representation of the slide's HTML structure
    required_chart_configs: Optional[List[Dict[str, Any]]] = None # Descriptions for charts needed
    error_message: Optional[str] = None 

# New models for structured output with Gemini
class UserIntentSchema(BaseModel):
    topic: str = Field(description="演示文稿的核心主题。")
    suggested_slide_count: int = Field(description="建议的幻灯片数量（在1-20之间），基于主题复杂度的估计值。")
    style_keywords: List[str] = Field(description="风格关键词。如果用户未提及，则返回一个空列表。")
    detected_language: Literal["zh-CN", "en-US"] = Field(description="检测到的用户语言，zh-CN表示中文，en-US表示英文。")

class StyleAndDetailsSchema(BaseModel):
    overall_style_text: str = Field(description="详细的整体风格要求文本，用于指导整个演示文稿的视觉设计。")
    all_slides_detail_instructions_block: str = Field(description="一个包含所有幻灯片具体要求的完整文本块，其中每张幻灯片的指令以 '--- 第N张幻灯片 ---' 作为明确的起始标记。")

class SlideEvaluationRepairSchema(BaseModel):
    action_taken: str = Field(description="描述执行的操作：'no_issues_found' 或 'html_repaired'。")
    html_content: str = Field(description="最终的HTML内容。如果 no_issues_found，则是原始HTML；如果 html_repaired，则是修复后的HTML。")
    issues_found_summary: Optional[List[str]] = Field(default_factory=list, description="如果 action_taken 是 'html_repaired'，列出主要修复的问题摘要。如果 'no_issues_found'，则此字段为空列表。")

class SlideOutlineItemSchema(BaseModel): # 单个幻灯片大纲条目
    slide_number: int = Field(description="幻灯片的顺序号，从1开始")
    title: str = Field(description="幻灯片的建议标题")
    key_points: List[str] = Field(description="这张幻灯片应包含的关键要点或核心内容描述列表，每个要点是字符串")
    slide_type_suggestion: str = Field(default="Content Slide", description="建议的幻灯片类型，例如：'Title Slide', 'Content Slide', 'Data Analysis Slide', 'Summary Slide'。")
    visual_element_hint: str = Field(default="文本内容", description="关于这张幻灯片视觉元素的初步想法或类型提示")

class OverallStyleAndOutlinesSchema(BaseModel):
    overall_style_text: str = Field(description="详细的整体风格要求文本。")
    slide_outlines: List[SlideOutlineItemSchema] = Field(description="一个列表，包含演示文稿中每张幻灯片的简要大纲。") 

# 简化版的数据模型 - 用于更高效地从LLM获取风格和大纲
class PresentationStyleSchema(BaseModel):
    style_description: str = Field(description="详细的整体风格描述文本，包括颜色、字体、设计元素等")

class SlidePromptSchema(BaseModel):
    """单张幻灯片的生成提示信息"""
    slide_order: int = Field(description="幻灯片的顺序号，从1开始")
    gen_prompt: str = Field(description="用于生成这张幻灯片的提示词")
    suggested_layout_type: str = Field(default="content_slide", description="建议的布局类型，例如：'title_slide', 'two_column_text_and_image'。")
    visual_focus_element: str = Field(default="text_content", description="这张幻灯片上建议的视觉焦点元素类型，例如：'data_chart', 'key_iconography'。")

class SimpleOverallStyleAndOutlinesSchema(BaseModel):
    """简化版的风格和大纲模型，避免复杂的嵌套结构"""
    style: PresentationStyleSchema = Field(description="演示文稿的整体风格")
    slide_prompts: List[SlidePromptSchema] = Field(description="每张幻灯片的生成提示") 

# Add new models for HTML checking
class HtmlCheckIssue(BaseModel):
    """HTML检查发现的问题"""
    severity: Literal["error", "warning", "critical", "major", "minor"] = Field(description="问题严重性: 'error'(关键问题如溢出、布局损坏、规范违反)、'warning'(次要审美/可读性问题)、'critical'(最高级别严重问题)、'major'(主要问题)、'minor'(次要问题)")
    description: str = Field(description="清晰简洁地描述发现的问题")
    suggestion: Optional[str] = Field(None, description="解决问题的可行建议(如果适用)")
    location: Optional[str] = Field(None, description="问题在HTML中的位置，例如元素选择器或描述")
    issue_type: Optional[str] = Field(None, description="问题类型分类，如'overflow', 'layout', 'styling', 'content', 'accessibility'")
    priority: Optional[int] = Field(None, description="问题修复优先级(1-5)，1为最高优先级")

class HtmlCheckResult(BaseModel):
    """HTML检查结果"""
    passed: bool = Field(description="如果没有严重性为'error'的问题，则为true(警告允许通过)")
    issues: List[HtmlCheckIssue] = Field(default_factory=list, description="发现的问题列表")
    raw_checker_response: Optional[str] = Field(None, description="用于调试检查器本身的原始响应")
    summary: Optional[str] = Field(None, description="检查结果的总体摘要，简述主要问题和结论")
    html_quality_score: Optional[float] = Field(None, description="0-10的HTML质量分数，考虑整体质量、可访问性和视觉一致性")
    check_timestamp: Optional[str] = Field(default_factory=lambda: datetime.now().isoformat(), description="检查的时间戳")

class ProjectSummarySchema(BaseModel):
    project_id: str = Field(alias="id")
    title: Optional[str] = "未命名演示"
    num_slides: Optional[int] = Field(alias="total_slides_planned", default=0)
    created_at: Optional[datetime] = None
    last_modified: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)

class PresentationDataSchema(BaseModel):
    project_id: str
    user_intent: Optional[UserIntentSchema] = None  # 将从DB的JSON字段反序列化
    style_outline: Optional[SimpleOverallStyleAndOutlinesSchema] = None  # 将从DB的JSON字段反序列化
    # detailed_prompts: Optional[List[str]] = None  # 这个可以考虑也存到DB的Project表某个JSON字段，或者拆分到Slide表
    slides: Optional[List[Slide]] = Field(default_factory=list)  # Slide Pydantic 模型
    chat_history: Optional[List[AiChatMessage]] = Field(default_factory=list)  # AiChatMessage Pydantic 模型
    title: Optional[str] = None  # 项目标题，从Project.title获取
    total_slides_planned: Optional[int] = 0  # 从Project.total_slides_planned获取

    model_config = ConfigDict(from_attributes=True)

# --- 强化设计细节的核心Schema ---
class ColorDetailSchema(BaseModel):
    name: str = Field(description="颜色的语义化名称，例如'品牌主红', '深科技蓝', '强调亮黄'")
    hex: ColorHex = Field(description="颜色的HEX值，例如'#DE2910', '#1A3B4D', '#FFD700'")
    usage_suggestion: str = Field(description="此颜色的建议用途，例如'页面主背景', '标题文字', '按钮和高亮元素', '图表数据系列1'")

class ColorPaletteSchema(BaseModel):
    theme_name: str = Field(description="此色板的主题名称，例如'中国红·荣耀金', '深海科技蓝·活力橙', '清新乙女粉·梦幻紫'")
    primary: ColorDetailSchema = Field(description="主色调，定义了演示文稿的整体基调。")
    secondary: ColorDetailSchema = Field(description="辅色调，用于搭配主色，增加视觉层次。")
    accent: ColorDetailSchema = Field(description="强调色，用于突出重要元素、按钮、图表数据等。")
    
    text_on_dark_bg: ColorHex = Field(description="在深色背景上使用的主要文本颜色HEX值。")
    text_on_light_bg: ColorHex = Field(description="在浅色背景上使用的主要文本颜色HEX值。")
    
    background_main: ColorHex = Field(description="幻灯片主体背景色（或渐变起始色）。")
    background_gradient_end: ColorHex = Field(default="#F8FAFC", description="如果背景是渐变，则为渐变结束色。如果不需要渐变，可设为与主背景相同。")
    background_gradient_direction: str = Field(default="to bottom right", description="渐变方向，例如 'to bottom right', '135deg'。")

    card_background: ColorHex = Field(description="内容卡片的背景颜色。")
    card_border: ColorHex = Field(default="#E2E8F0", description="内容卡片的边框颜色。")
    card_shadow_color_rgba: str = Field(default="rgba(0,0,0,0.1)", description="卡片阴影颜色和透明度，格式为 'rgba(0,0,0,0.1)'。")

    chart_colors: List[ColorHex] = Field(default_factory=list, description="用于图表数据系列的颜色列表，至少提供3-5个和谐的颜色。")

class TypographySchema(BaseModel):
    heading_font_family_css: str = Field(description="标题字体的CSS font-family值, e.g., \"'Noto Sans SC', sans-serif\"")
    body_font_family_css: str = Field(description="正文字体的CSS font-family值, e.g., \"'Open Sans', 'Noto Sans SC', sans-serif\"")
    heading_font_cdn_url: str = Field(default="", description="标题字体CDN链接 (如果使用Google Fonts等)")
    body_font_cdn_url: str = Field(default="", description="正文字体CDN链接")
    heading_font_weight: str = Field("700", description="标题字重, e.g., '700', 'bold', 'black'")
    body_font_weight: str = Field("400", description="正文字重, e.g., '400', 'normal'")
    
    font_size_h1: str = Field("36px", description="主标题字体大小")
    font_size_h2: str = Field("28px", description="二级标题字体大小")
    font_size_h3: str = Field("24px", description="三级标题字体大小")
    font_size_body: str = Field("16px", description="正文字体大小")
    font_size_caption: str = Field("14px", description="说明文字字体大小")
    line_height_heading: str = Field("1.3", description="标题行高")
    line_height_body: str = Field("1.6", description="正文行高")

class DesignElementFeaturesSchema(BaseModel):
    overall_feel_keywords: List[str] = Field(description="描述整体感觉的关键词列表，如['现代', '简约', '科技感']")
    card_style: str = Field(description="卡片样式描述。必须非常具体，例如：'圆角12px，背景色var(--card-background)，边框1px solid var(--card-border-color)，阴影0 4px 12px var(--card-shadow-color-rgba)'。")
    background_details: str = Field(description="背景细节。例如：'主体背景使用 var(--background-main) 到 var(--background-gradient-end) 的 var(--background-gradient-direction) 渐变。可选：叠加一个透明度为5%的 subtle_geometric_pattern.svg 作为纹理。'")
    icon_style_suggestion: str = Field(description="图标风格建议，如'使用FontAwesome的细线条(light)风格图标'")
    animation_suggestion: str = Field(description="元素入场动画的统一建议，例如 'fade-in-up 0.5s ease-out forwards'。明确动画名称和参数。")
    space_xs: str = Field("4px", description="极小间距")
    space_sm: str = Field("8px", description="小间距")
    space_md: str = Field("16px", description="中等间距")
    space_lg: str = Field("24px", description="大间距")
    space_xl: str = Field("32px", description="极大间距")
    divider_style: str = Field(description="分隔线样式描述，例如：'1px solid var(--secondary-color)' 或 '2px dashed var(--accent-color)'。")
    chart_style: str = Field(default="扁平化图表，色彩参考强调色", description="图表风格描述, e.g., '扁平化图表，色彩参考强调色，带有轻微的入场动画'")
    border_radius_suggestion: str = Field("12px", description="通用圆角建议, e.g., '8px', '16px', '9999px' for pills")
    visual_balance_principles: List[str] = Field(
        default_factory=list,
        description="视觉平衡原则列表，如'左右对称', '上轻下重', '色彩呼应'"
    )

class StructuredPresentationStyleSchema(BaseModel):
    style_summary_text: str = Field(description="对整体风格的自然语言总结性描述。")
    color_palette: ColorPaletteSchema
    typography: TypographySchema
    design_elements: DesignElementFeaturesSchema
    primary_color_var: str = Field(description="主色调CSS变量值，例如'#0A74DA'")
    secondary_color_var: str = Field(description="辅色调CSS变量值，例如'#5A6B7C'")
    accent_color_var: str = Field(description="强调色CSS变量值，例如'#FF6B35'")
    background_color_var: str = Field(description="背景色CSS变量值，例如'#F8FAFC'")
    text_color_var: str = Field(description="文本色CSS变量值，例如'#1A202C'")
    heading_font_var: str = Field(description="标题字体CSS变量值，例如'Arial, sans-serif'")
    body_font_var: str = Field(description="正文字体CSS变量值，例如'Open Sans, sans-serif'")
    presentation_outlines: List[SlideOutlineItemSchema] = Field(
        description="整个演示文稿的大纲列表，包含每张幻灯片的标题、关键点和建议类型。"
    )

class CombinedStyleAndOutlinesSchema(BaseModel): # 用于StyleOutlineAgent的最终输出
    structured_style: StructuredPresentationStyleSchema = Field(description="结构化的演示文稿风格定义。")
    slide_prompts: List[SlidePromptSchema] = Field(description="每张幻灯片的生成提示列表。")

class ImageRequestDetails(BaseModel):
    description: str = Field(description="Detailed textual description of the image content, subject, scene, and mood.")
    style_keywords: List[str] = Field(default_factory=list, description="Keywords describing the desired image style (e.g., 'photorealistic', 'abstract', 'minimalist', 'vintage', 'illustration').")
    search_query_keywords: Optional[str] = Field(None, description="Extracted or generated concise keyword query for image search engines from the detailed description. For example: 'future city traffic flying cars'.")
    # Not including desired_formats as we'll likely standardize on web-friendly formats like JPEG/PNG.
    target_aspect_ratio: Optional[str] = Field(default="16:9", description="Preferred aspect ratio like '16:9', '4:3', '1:1'. 'any' for flexible.")
    orientation: Optional[Literal["landscape", "portrait", "square", "any"]] = Field(default="landscape", description="Preferred image orientation.")
    
    # --- 核心修改：使用新的尺寸档位 ---
    required_size: Literal[
        "ICON", 
        "SMALL_CONTENT", 
        "MEDIUM_CONTENT", 
        "LARGE_CONTENT", 
        "FULL_BACKGROUND"
    ] = Field(
        default="MEDIUM_CONTENT",
        description="图片所需的尺寸档位，用于决定搜索时的尺寸参数。ICON(64x64-128x128), SMALL_CONTENT(400x300-640x480), MEDIUM_CONTENT(800x600-1024x768), LARGE_CONTENT(1280x720-1920x1080), FULL_BACKGROUND(1920x1080+)"
    )
    
    # --- Existing color preference, can map to imgDominantColor ---
    color_preference: Optional[Literal[
        "black", "blue", "brown", "gray", "green", "orange", 
        "pink", "purple", "red", "teal", "white", "yellow", "any"
    ]] = Field(default="any", description="Preferred dominant color of the image. 'any' means no specific preference.")

    # --- 核心修改：扩展图片类型选项 ---
    image_type_preference: Optional[Literal[
        "photo",         # 真实照片
        "clipart",       # 剪贴画/插图
        "lineart",       # 线条艺术
        "stock",         # 专业图库照片
        "face",          # 人物面部特写
        "illustration",  # 插画
        "any"            # 任何类型
    ]] = Field(
        default="photo",
        description="图片类型偏好，用于精确控制视觉风格。"
    )

    # --- New fields to leverage more API parameters ---
    image_color_type: Optional[Literal["color", "gray", "mono", "trans", "any"]] = Field(default="color", description="Image color type: 'color', 'gray'(grayscale), 'mono'(black and white), 'trans'(transparent background), 'any'(any type).")
    image_file_type: Optional[Literal["jpg", "gif", "png", "any"]] = Field(default="any", description="Preferred image file format: 'jpg', 'gif', 'png', 'any'(any format).")
    image_usage_rights: Optional[Literal[
        "cc_publicdomain", "cc_attribute", "cc_sharealike", 
        "cc_noncommercial", "cc_nonderived", "any"
    ]] = Field(default="any", description="Image usage rights filter: 'cc_publicdomain', 'cc_attribute', 'cc_sharealike', 'cc_noncommercial', 'cc_nonderived', 'any'(any license).")
    image_exclude_terms: Optional[List[str]] = Field(default_factory=list, description="List of terms to exclude from image search results.")
    image_date_restrict: Optional[str] = Field(None, description="Date restriction for images, e.g.: 'd[7]'(past 7 days), 'w[4]'(past 4 weeks), 'm[6]'(past 6 months), 'y[1]'(past year). Format is type[N].")
    # --- End of new fields ---

    usage_rights: str = Field(default="royalty-free", description="[Consider deprecating or merging with image_usage_rights] Traditional usage rights description.") # Keeping old field for compatibility, but suggest migration
    placement_hint: Optional[str] = Field(None, description="Hint for where the image will be placed or its role, e.g., 'full_background', 'right_column_content', 'hero_image'.")
    # These will be used to fetch an image that can be suitably cropped/resized for the slide.
    # The actual display size on slide will be determined by HTML/CSS.
    desired_final_width_on_slide_approx: Optional[int] = Field(default=600, description="Approximate final width image should occupy on slide, for quality selection.")
    desired_final_height_on_slide_approx: Optional[int] = Field(default=400, description="Approximate final height image should occupy on slide, for quality selection.")

# --- ADD THIS NEW SCHEMA ---
class ChartJsRequestSchema(BaseModel):
    chart_type: Literal["bar", "line", "pie", "radar", "doughnut"] = Field(description="图表类型")
    chart_title: str = Field(description="图表的标题")
    data_description: str = Field(description="对图表数据的自然语言描述，包括标签(labels)和数据集(datasets)的内容。例如：'一个条形图，X轴为城市，Y轴为房价，数据为北京:50000, 上海:60000...'")
    canvas_id: str = Field(default_factory=lambda: f"chart_{uuid.uuid4().hex[:6]}", description="将要渲染图表的canvas元素的唯一ID")

class SelectedImageInfo(BaseModel):
    original_url: str
    local_path: Optional[str] = None # Path within project_storage
    base64_data_uri: Optional[str] = None
    description: str # The description used to find this image
    style_keywords: List[str]
    width: Optional[int] = None # Processed image width
    height: Optional[int] = None # Processed image height
    format: Optional[str] = None # e.g., JPEG, PNG
    placement_hint: Optional[str] = None # Added

# --- MODIFY SlideDetailedOutput SCHEMA ---
class SlideDetailedOutput(BaseModel):
    text_instructions: str
    image_requests: Optional[List[ImageRequestDetails]] = None
    # --- ADD THIS NEW FIELD ---
    chart_requests: Optional[List[ChartJsRequestSchema]] = Field(None, description="一个列表，包含此幻灯片需要的所有Chart.js图表请求。")

# Add to Slide model in presentation_model.py and db/models.py if you want to persist image info
# For Slide in presentation_model.py:
# class Slide(BaseModel):
# ...
# selected_image_info: Optional[SelectedImageInfo] = None

# Add SlideMetadataSchema after Slide model definition
class SlideMetadataSchema(BaseModel):
    """轻量级幻灯片元数据模型，不包含HTML内容"""
    id: str
    slide_number: Optional[int] = None  # 1-based slide number
    status: Optional[str] = "ready"  # 状态，如 "ready", "generating", "error"
    title: Optional[str] = None  # 幻灯片标题（如果有的话）
    
    model_config = ConfigDict(from_attributes=True)

# 修改ProjectDetailsResponse定义
class ProjectDetailsResponse(BaseModel):
    """项目详情响应，包含元数据但不包含幻灯片HTML内容"""
    project_id: str
    title: Optional[str] = "未命名演示"
    num_slides: Optional[int] = 0
    created_at: Optional[datetime] = None
    last_modified: Optional[datetime] = None
    user_intent: Optional[UserIntentSchema] = None
    style_outline: Optional[SimpleOverallStyleAndOutlinesSchema] = None
    slides: List[SlideMetadataSchema] = Field(default_factory=list)  # 使用轻量级模型
    chat_history: Optional[List[AiChatMessage]] = Field(default_factory=list)

    model_config = ConfigDict(from_attributes=True)

class HtmlEditorInput(BaseModel):
    """HTML编辑器的输入"""
    original_html: str = Field(description="原始HTML内容")
    issues: List[HtmlCheckIssue] = Field(description="需要修复的问题列表")
    repair_instructions: str = Field(description="修复指令，详细说明需要进行的修改")

class HtmlEditorOutput(BaseModel):
    """HTML编辑器的输出"""
    edited_html: str = Field(description="编辑后的HTML内容")
    changes_made: List[str] = Field(default_factory=list, description="所做更改的描述列表")
    edit_summary: Optional[str] = Field(None, description="编辑的总体摘要")
    edit_timestamp: Optional[str] = Field(default_factory=lambda: datetime.now().isoformat(), description="编辑的时间戳")

# --- 开始新增的、高度详细的蓝图模型 ---

class TextElementSchema(BaseModel):
    type: Literal["title", "subtitle", "paragraph", "bullet_point", "kicker"] = Field(
        description="文本元素的具体类型。可选值：'title', 'subtitle', 'paragraph', 'bullet_point', 'kicker'。title=主标题，subtitle=副标题，paragraph=段落文本，bullet_point=要点列表项，kicker=引导性文字"
    )
    content: str = Field(
        description="该文本元素的具体内容。必须是简洁、有力、符合商业演示标准的文案。避免使用占位符文本。",
        min_length=1,
        max_length=500
    )
    target_area: str = Field(
        validation_alias=AliasChoices("target_area", "targetArea"),
        description="此元素在布局模板中应放置的目标区域ID，例如 'title_area', 'main_content_area', 'subtitle_area'。必须与选定的layout_template_name兼容。"
    )
    animation_style: str = Field(
        default="fade-in",
        validation_alias=AliasChoices("animation_style", "animationStyle"),
        description="建议的入场动画CSS类名，例如 'slide-in-up', 'fade-in', 'zoom-in'。选择应与内容重要性匹配。"
    )

class KpiCardSchema(BaseModel):
    """KPI数据卡片的详细定义"""
    type: Literal["kpi_card"] = Field(
        default="kpi_card",
        description="KPI卡片的具体类型。"
    )
    title: str = Field(
        description="KPI卡片的标题，例如'新房销售额'、'市场增长率'。应简洁明了，不超过10个字符。",
        max_length=20
    )
    value: str = Field(
        description="KPI的核心数值，必须是一个引人注目的、格式化的字符串，例如'9.4万亿元'、'+15.2%'、'1,234万套'。必须包含单位和具体数值，避免使用占位符。数值应该逼真且符合行业常识。",
        min_length=1
    )
    change: str = Field(
        default="持平", 
        description="与前期对比的变化值，例如'-3.2%'、'+15.8%'、'↑12.5%'、'↓8.1%'。必须包含方向指示符（+/-/↑/↓）和具体的百分比数值，不能使用模糊的描述。"
    )
    icon_fontawesome_class: str = Field(
        validation_alias=AliasChoices("icon_fontawesome_class", "iconFontawesomeClass"),
        description="用于表示此KPI的Font Awesome 6的图标完整类名，例如'fa-solid fa-chart-line'、'fa-solid fa-money-bill-trend-up'。必须是有效的FA6类名。"
    )
    target_area: str = Field(
        validation_alias=AliasChoices("target_area", "targetArea"),
        description="此元素在布局模板中的目标区域ID，例如 'kpi_card_1', 'kpi_card_2'。"
    )
    animation_style: str = Field(
        default="fade-in",
        validation_alias=AliasChoices("animation_style", "animationStyle"),
        description="入场动画建议，例如 'fade-in', 'slide-in-left'。KPI卡片建议使用渐显效果。"
    )

class ChartBlueprintSchema(BaseModel):
    """图表蓝图的详细定义，包含完整的Chart.js配置"""
    type: Literal["chart"] = Field(
        default="chart",
        description="图表的具体类型。"
    )
    title: str = Field(
        description="图表的清晰、描述性标题，例如'销售趋势 (2022-2025E)'、'市场份额分布'。",
        max_length=50
    )
    chart_type: Literal["bar", "line", "pie", "radar", "doughnut"] = Field(
        validation_alias=AliasChoices("chart_type", "chartType"),
        description="图表的类型，必须是这几种之一。选择应基于数据特性：bar=对比数据，line=趋势数据，pie=占比数据，radar=多维评估，doughnut=占比数据（中心可放文字）。"
    )
    data_fabrication_instruction: str = Field(
        validation_alias=AliasChoices("data_fabrication_instruction", "dataFabricationInstruction"),
        description="用于生成图表数据的详细文字描述。必须包含足够的信息来创造出逼真的、符合逻辑的数据。例如：'创建一个双Y轴折线图，展示2022到2025年的销售额和销售面积趋势。销售额数据为13.3, 11.7, 9.7, 9.4万亿元；销售面积数据为13.6, 11.2, 9.7, 15.0亿平米。使用蓝色和橙色作为线条颜色。'",
        min_length=50
    )
    final_chart_js_config: ChartConfig = Field(
        validation_alias=AliasChoices("final_chart_js_config", "finalChartJsConfig"),
        description="[此字段由AI填充] 最终生成的、完整的、可直接被Chart.js使用的配置对象，必须包含type, data, options三个键。data必须包含labels和datasets数组。"
    )
    target_area: str = Field(
        validation_alias=AliasChoices("target_area", "targetArea"),
        description="此图表在布局模板中的目标区域ID，例如 'chart_area_1', 'main_chart_area'。"
    )
    animation_style: str = Field(
        default="slide-in-up",
        validation_alias=AliasChoices("animation_style", "animationStyle"),
        description="入场动画建议，例如 'zoom-in', 'slide-in-up'。图表建议使用缩放或滑入效果。"
    )

class ImageElementSchema(BaseModel):
    """图片元素的详细定义"""
    type: Literal["image"] = Field(
        default="image",
        description="图片的具体类型。"
    )
    generation_prompt: str = Field(
        validation_alias=AliasChoices("generation_prompt", "generationPrompt"),
        description="用于AI绘画或图片搜索的高质量、详细的英文描述。需要包含场景、主体、风格、氛围等信息。例如：'A modern office building with glass facade at sunset, professional business atmosphere, architectural photography style, warm lighting, urban skyline in background'。",
        min_length=20
    )
    alt_text: str = Field(
        validation_alias=AliasChoices("alt_text", "altText"),
        description="图片的alt属性文本，用于无障碍访问。应简洁描述图片内容。",
        max_length=100
    )
    target_area: str = Field(
        validation_alias=AliasChoices("target_area", "targetArea"),
        description="此图片在布局模板中的目标区域ID，例如 'image_area', 'hero_image_area'。"
    )
    animation_style: str = Field(
        default="fade-in",
        validation_alias=AliasChoices("animation_style", "animationStyle"),
        description="入场动画建议，例如 'fade-in', 'slide-in-right'。图片建议使用渐显效果。"
    )

# --- 定义可辨识联合类型 ---
# 使用Pydantic V2的可辨识联合类型来精确描述每一种幻灯片元素

# 使用可辨识联合类型 (Discriminated Unions) 来精确描述每种元素
Element = Annotated[
    Union[TextElementSchema, KpiCardSchema, ChartBlueprintSchema, ImageElementSchema],
    Field(discriminator="type")
]

# 为了保持向下兼容，保留SlideElementSchema作为旧接口的别名
# 但强烈建议迁移到新的Element联合类型
SlideElementSchema = Element
HtmlElementTypes = Element

class DetailedSlideBlueprintSchema(BaseModel):
    """
    一份极其详尽的、用于生成单张幻灯片的"视觉施工蓝图"。
    这是 SlideDetailerAgent 的最终输出，也是 SlideGeneratorAgent 的唯一输入源。
    
    此模型遵循instructor最佳实践，将约束和指令直接编码到Schema中。
    """
    slide_number: int = Field(
        validation_alias=AliasChoices("slide_number", "slideNumber"),
        description="幻灯片的顺序号，从1开始。",
        ge=1,
        le=50
    )
    layout_template_name: str = Field(
        validation_alias=AliasChoices("layout_template_name", "layoutTemplateName"),
        description="布局模板的名称，必须从预定义的模板中选择。可选值：'TitleSlideLayout'(封面页), 'DataDashboardLayout'(数据仪表板), 'ContentSlideLayout'(内容页), 'PolicyAnalysisLayout'(政策分析), 'ComparisonLayout'(对比分析), 'TimelineLayout'(时间线), 'ProcessFlowLayout'(流程图)。选择应基于幻灯片内容类型。"
    )
    background_style_description: str = Field(
        validation_alias=AliasChoices("background_style_description", "backgroundStyleDescription"),
        description="对背景的详细CSS描述，例如 'linear-gradient(135deg, #0A1931 0%, #1E293B 100%)' 或 'radial-gradient(circle at center, #f8fafc 0%, #e2e8f0 100%)'。应与整体风格保持一致。",
        min_length=10
    )
    # --- 临时兼容性解决方案：使用Dict但在运行时转换为强类型 ---
    key_elements: List[Dict[str, Any]] = Field(
        validation_alias=AliasChoices("key_elements", "keyElements"),
        description="构成此幻灯片核心内容的所有元素的列表。每个元素都必须有'type'字段以进行区分。支持的类型：TextElement(title, subtitle, paragraph, bullet_point, kicker)、KpiCardSchema(kpi_card)、ChartBlueprintSchema(chart)、ImageElementSchema(image)。列表不能为空，至少包含一个元素。",
        min_length=1
    )
    speaker_notes: str = Field(
        validation_alias=AliasChoices("speaker_notes", "speakerNotes"),
        description="演讲者的备注，用于对幻灯片内容进行补充说明或提供演讲提示。内容需要有深度和见解，不少于50字符。必须包含：1)关键数据的深度解读和背景分析；2)数据趋势的商业意义；3)具体的演讲建议和重点强调内容。避免简单复述幻灯片内容。",
        min_length=50,
        max_length=1000
    )
    
    def get_typed_elements(self) -> List[Union[TextElementSchema, KpiCardSchema, ChartBlueprintSchema, ImageElementSchema]]:
        """
        将key_elements中的Dict转换为强类型的元素对象
        
        Returns:
            强类型元素对象的列表
        """
        from pydantic import ValidationError
        import logging
        
        logger = logging.getLogger(__name__)
        typed_elements = []
        
        for element_dict in self.key_elements:
            element_type = element_dict.get("type")
            try:
                if element_type in ["title", "subtitle", "paragraph", "bullet_point", "kicker"]:
                    typed_elements.append(TextElementSchema.model_validate(element_dict))
                elif element_type == "kpi_card":
                    typed_elements.append(KpiCardSchema.model_validate(element_dict))
                elif element_type == "chart":
                    typed_elements.append(ChartBlueprintSchema.model_validate(element_dict))
                elif element_type == "image":
                    typed_elements.append(ImageElementSchema.model_validate(element_dict))
                else:
                    # 未知类型，跳过
                    logger.warning(f"未知元素类型: {element_type}")
                    continue
            except ValidationError as e:
                # 验证失败，跳过该元素并记录错误
                logger.warning(f"元素类型 {element_type} 验证失败: {e}")
                continue
        return typed_elements

    # 添加一个Config，让Pydantic在生成JSON Schema时使用更清晰的引用
    model_config = ConfigDict(
        json_schema_extra={
            "title": "Detailed Slide Blueprint",
            "description": "A comprehensive blueprint for generating a single presentation slide with enhanced validation and constraints.",
        },
        # 启用严格模式，提高数据质量
        str_strip_whitespace=True,
        validate_assignment=True,
        # 【重要】添加这个配置，让Pydantic在处理别名时更灵活
        populate_by_name=True,
        # 【关键修复】禁用additionalProperties，避免Gemini API错误
        extra='forbid'
    )

class BlueprintListSchema(BaseModel): # 修改为继承 BaseModel
    """用于包装DetailedSlideBlueprintSchema列表的Pydantic模型"""
    # 添加一个明确的列表字段
    blueprints: List['DetailedSlideBlueprintSchema'] = Field(
        description="一个包含详细幻灯片蓝图的列表。"
    )

# Pydantic v2 的解决方案：在所有模型定义完成后调用 model_rebuild()
# 这解决了前向引用和循环依赖问题
UserIntentSchema.model_rebuild()
StyleAndDetailsSchema.model_rebuild()
SlideEvaluationRepairSchema.model_rebuild()
SlideOutlineItemSchema.model_rebuild()
OverallStyleAndOutlinesSchema.model_rebuild()
SimpleOverallStyleAndOutlinesSchema.model_rebuild()
SlidePromptSchema.model_rebuild()
PresentationStyleSchema.model_rebuild()
HtmlCheckResult.model_rebuild()
HtmlCheckIssue.model_rebuild()
ProjectSummarySchema.model_rebuild()
PresentationDataSchema.model_rebuild()
SlideMetadataSchema.model_rebuild()
ProjectDetailsResponse.model_rebuild()
KpiCardSchema.model_rebuild()
ChartBlueprintSchema.model_rebuild()
ImageElementSchema.model_rebuild()
TextElementSchema.model_rebuild()
# Element联合类型会在其组成模型重建时自动重建
DetailedSlideBlueprintSchema.model_rebuild()
BlueprintListSchema.model_rebuild()
HtmlEditorOutput.model_rebuild()
AiChatMessage.model_rebuild()
Slide.model_rebuild()
ChartConfig.model_rebuild()
UserRequestInfo.model_rebuild()
SlideOutline.model_rebuild()
PresentationOutline.model_rebuild()
ResearchedDataPoint.model_rebuild()
ResearchOutput.model_rebuild()
SlideElement.model_rebuild()
VisualAsset.model_rebuild()
SlideLayout.model_rebuild()
SlideObject.model_rebuild()
PresentationObject.model_rebuild()
OrchestratorProgress.model_rebuild()
InputProcessingAgentOutput.model_rebuild()
VisualAssetAgentOutput.model_rebuild()
CritiqueAgentOutput.model_rebuild()
GeneratedSlideContent.model_rebuild()
ColorDetailSchema.model_rebuild()
ColorPaletteSchema.model_rebuild()
TypographySchema.model_rebuild()
DesignElementFeaturesSchema.model_rebuild()
StructuredPresentationStyleSchema.model_rebuild()
CombinedStyleAndOutlinesSchema.model_rebuild()
ImageRequestDetails.model_rebuild()
ChartJsRequestSchema.model_rebuild()
SelectedImageInfo.model_rebuild()
SlideDetailedOutput.model_rebuild()
HtmlEditorInput.model_rebuild()
DetailedSlideBlueprintSchema.model_rebuild()
BlueprintListSchema.model_rebuild()

