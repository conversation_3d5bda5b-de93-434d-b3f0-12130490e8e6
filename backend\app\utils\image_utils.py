import logging
import httpx
import os
import uuid
from io import BytesIO
from PIL import Image, ImageOps # Pillow for image processing
import base64
from typing import Optional, Dict, Any, Tuple

from app.core.config import settings

logger = logging.getLogger(__name__)

# Define a subdirectory for images within each project's storage
IMAGES_SUBDIR = "images"
DEFAULT_IMAGE_FORMAT = "JPEG" # Or "PNG" if transparency is needed
DEFAULT_IMAGE_QUALITY = 85 # For JPEG

async def download_image(url: str) -> Optional[bytes]:
    """Downloads image data from a URL."""
    try:
        # 1. 预检查URL格式，过滤掉非图片链接
        # 检查剥离查询参数后的URL是否以常见图片扩展名结尾
        url_path = url.split('?')[0].lower()
        if not any(url_path.endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.webp', '.gif']):
            logger.warning(f"Skipping download, URL does not appear to be a direct image link: {url}")
            return None

        if not url.startswith(('http://', 'https://')):
            logger.error(f"Invalid URL scheme for image download: {url}")
            return None

        async with httpx.AsyncClient(timeout=20.0, follow_redirects=True) as client: # Increased timeout and follow redirects
            response = await client.get(url)
            response.raise_for_status() # Raise an exception for bad status codes
            logger.info(f"Successfully downloaded image from {url}, size: {len(response.content)} bytes")
            return response.content
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error downloading image {url}: {e.response.status_code} - {e.response.text}")
    except httpx.RequestError as e:
        logger.error(f"Request error downloading image {url}: {e}")
    except Exception as e:
        logger.error(f"Unexpected error downloading image {url}: {e}", exc_info=True)
    return None

async def process_image(
    image_data: bytes,
    target_width: Optional[int] = None,
    target_height: Optional[int] = None,
    target_aspect_ratio: Optional[str] = None, # e.g., "16:9"
    output_format: str = "JPEG"
) -> Tuple[Optional[bytes], Optional[int], Optional[int]]:
    """
    Processes image data (resize, aspect ratio) and returns processed bytes and dimensions.
    This function does NOT save the image.
    """
    try:
        img = Image.open(BytesIO(image_data))
        
        # Convert to RGB if it's RGBA and output is JPEG (to avoid errors with JPEG saving alpha)
        if img.mode == 'RGBA' and output_format.upper() == 'JPEG':
            img = img.convert('RGB')
        elif img.mode == 'P' and output_format.upper() == 'JPEG': # Handle palette mode for JPEG
             img = img.convert('RGB')

        current_width, current_height = img.size

        if target_width and target_height and target_aspect_ratio:
            # If all three are given, prioritize aspect ratio, then fit within width/height
            num, den = map(int, target_aspect_ratio.split(':'))
            aspect = num / den
            
            # Fit to width, calculate height
            h_from_w = int(target_width / aspect)
            # Fit to height, calculate width
            w_from_h = int(target_height * aspect)

            if target_width / target_height > aspect: # Target box is wider than aspect ratio
                # Use target_height, calculate width based on aspect
                final_height = target_height
                final_width = w_from_h
            else: # Target box is taller or same aspect
                # Use target_width, calculate height based on aspect
                final_width = target_width
                final_height = h_from_w
            
            # Ensure it doesn't exceed the other dimension
            final_width = min(final_width, target_width)
            final_height = min(final_height, target_height)

            img = ImageOps.fit(img, (final_width, final_height), Image.Resampling.LANCZOS)

        elif target_width or target_height:
            # Simplified resize logic if only one dimension or no aspect ratio is key
            # This uses thumbnail which preserves aspect ratio and resizes *down* if larger.
            # If you need to resize *up* or have more complex logic, this needs adjustment.
            size_to_fit = [current_width, current_height]
            if target_width:
                size_to_fit[0] = target_width
            if target_height:
                size_to_fit[1] = target_height
            
            img.thumbnail(tuple(size_to_fit), Image.Resampling.LANCZOS)

        final_width, final_height = img.size
        
        output_buffer = BytesIO()
        save_params = {}
        if output_format.upper() == 'JPEG':
            save_params['quality'] = DEFAULT_IMAGE_QUALITY
        
        img.save(output_buffer, format=output_format, **save_params)
        processed_bytes = output_buffer.getvalue()
        
        logger.info(f"Processed image to {output_format} (Final Size: {final_width}x{final_height})")
        return processed_bytes, final_width, final_height
    except Exception as e:
        logger.error(f"Error processing image: {e}", exc_info=True)
        return None, None, None

def process_and_save_image(
    image_data: bytes,
    project_id: str,
    slide_order_index: int, # 0-based
    # target_width: int, # The width the image should roughly fill on the slide
    # target_height: int, # The height the image should roughly fill on the slide
    image_request: "ImageRequestDetails", # Use forward reference if ImageRequestDetails is in presentation_model
    original_image_metadata: Dict[str, Any] # Metadata from search (like original width/height)
) -> Optional[Dict[str, Any]]: # Returns dict with path, final_width, final_height, format
    """
    Processes (resizes/crops if needed) and saves the image locally.
    Returns info about the saved image.
    """
    try:
        img = Image.open(BytesIO(image_data))
        original_format = img.format or DEFAULT_IMAGE_FORMAT.upper()
        
        # Determine target dimensions for processing based on request or defaults
        # This is the size we aim for BEFORE placing on slide, ensuring good quality
        # The actual display on slide is handled by CSS.
        # We aim to get a good quality source image close to what might be displayed.
        process_width = getattr(image_request, 'desired_final_width_on_slide_approx', 600)
        process_height = getattr(image_request, 'desired_final_height_on_slide_approx', 400)
        
        # If image is smaller than target, don't upscale, use as is (or only crop if aspect ratio differs)
        # If larger, resize it down while maintaining aspect ratio, then crop if necessary.
        
        # Strategy: Resize to fit within process_width x process_height, then crop to exact dimensions if needed.
        # Or, more commonly for slides, resize to fill (cover) the area, then crop the excess.
        
        # Let's use Pillow's ImageOps.fit for "cover" like behavior:
        # Resize and crop to fill the bounds.
        # img = ImageOps.fit(img, (process_width, process_height), Image.Resampling.LANCZOS)

        # Simpler strategy for now: just resize to fit within the bounds, maintaining aspect ratio.
        # The HTML/CSS will handle the final display (e.g., object-fit: cover).
        # This means we provide a high-quality source image to the browser.
        # We just need to ensure it's not excessively large. Max dimension (e.g. 1280 for slide width)
        max_dimension = max(process_width, process_height, 1280) # Ensure it's at least slide width for full-bg potential
        img.thumbnail((max_dimension, max_dimension), Image.Resampling.LANCZOS)
        
        # Ensure storage directory exists
        project_image_dir = os.path.join(settings.BASE_PROJECT_STORAGE_DIR, project_id, IMAGES_SUBDIR)
        os.makedirs(project_image_dir, exist_ok=True)

        # Generate a unique filename
        image_uuid = uuid.uuid4().hex[:8]
        # Ensure output format is derived correctly, especially for JPEG
        output_fmt_for_filename = original_format.lower()
        if output_fmt_for_filename == 'jpeg' and img.mode == 'RGBA':
             img = img.convert('RGB') # Convert RGBA to RGB for JPEG
        elif output_fmt_for_filename == 'jpeg' and img.mode == 'P':
             img = img.convert('RGB') # Convert Palette to RGB for JPEG

        output_filename = f"slide_{slide_order_index}_image_{image_uuid}.{output_fmt_for_filename}"
        output_path = os.path.join(project_image_dir, output_filename)

        save_params = {}
        if original_format.upper() == 'JPEG':
            save_params['quality'] = DEFAULT_IMAGE_QUALITY
        
        img.save(output_path, format=original_format, **save_params)
        
        final_width, final_height = img.size
        logger.info(f"Processed and saved image to {output_path} (Size: {final_width}x{final_height})")
        
        return {
            "local_path": output_path,
            "relative_path": os.path.join(project_id, IMAGES_SUBDIR, output_filename), # Relative to BASE_PROJECT_STORAGE_DIR
            "filename": output_filename,
            "width": final_width,
            "height": final_height,
            "format": original_format.upper()
        }
    except Exception as e:
        logger.error(f"Error processing and saving image: {e}", exc_info=True)
        return None

def encode_image_path_to_base64(image_path: str) -> Optional[str]:
    """Reads an image file from a path and returns its Base64 encoded data URI."""
    try:
        with open(image_path, "rb") as image_file:
            image_bytes = image_file.read()
        
        ext = os.path.splitext(image_path)[1].lower()
        mime_type = "image/jpeg" # Default
        if ext == ".png":
            mime_type = "image/png"
        elif ext == ".gif":
            mime_type = "image/gif"
        elif ext == ".svg":
            mime_type = "image/svg+xml"
        elif ext == ".webp":
            mime_type = "image/webp"
            
        return encode_image_bytes_to_base64(image_bytes, mime_type=mime_type, as_data_uri=True)
    except Exception as e:
        logger.error(f"Error encoding image path {image_path} to Base64: {e}", exc_info=True)
        return None

def encode_image_bytes_to_base64(
    image_bytes: bytes,
    mime_type: str = "image/jpeg",
    as_data_uri: bool = True
) -> Optional[str]:
    """Encodes image bytes to a Base64 string, optionally as a data URI."""
    try:
        encoded_string = base64.b64encode(image_bytes).decode('utf-8')
        if as_data_uri:
            return f"data:{mime_type};base64,{encoded_string}"
        return encoded_string
    except Exception as e:
        logger.error(f"Error encoding image bytes to Base64: {e}", exc_info=True)
        return None

# For backward compatibility
encode_image_base64 = encode_image_path_to_base64