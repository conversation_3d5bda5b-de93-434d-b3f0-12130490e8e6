import requests
import json
import time
import sseclient  # pip install sseclient-py

def test_slide_generation_with_sse():
    """测试完整的幻灯片生成过程"""
    # 第一步：启动生成
    url = "http://localhost:8000/api/v1/slides/generate_from_text_stream"
    data = {
        "prompt": "测试聊天记录保存，创建关于Python编程的3张幻灯片"
    }
    
    print("开始测试幻灯片生成...")
    response = requests.post(url, json=data, stream=True)
    
    if response.status_code != 200:
        print(f"错误: {response.status_code} - {response.text}")
        return None
    
    project_id = None
    # 解析初始响应
    for line in response.iter_lines(decode_unicode=True):
        if line.startswith('data:'):
            try:
                json_data = json.loads(line[5:].strip())
                print(f"初始响应: {json_data.get('status')} - {json_data.get('message', {}).get('text', '')}")
                if 'project_id' in json_data:
                    project_id = json_data['project_id']
                    break
            except json.JSONDecodeError:
                continue
    
    if not project_id:
        print("无法获取项目ID")
        return None
    
    print(f"项目ID: {project_id}")
    
    # 第二步：监听SSE更新
    sse_url = f"http://localhost:8000/api/v1/presentation_updates/{project_id}"
    print(f"监听SSE: {sse_url}")
    
    try:
        response = requests.get(sse_url, stream=True)
        client = sseclient.SSEClient(response)
        
        for event in client.events():
            try:
                data = json.loads(event.data)
                status = data.get('status', 'unknown')
                message_text = data.get('message', {}).get('text', '')
                
                print(f"SSE: {status} - {message_text}")
                
                if status in ['completed', 'error']:
                    print(f"生成完成，状态: {status}")
                    break
                    
            except json.JSONDecodeError:
                continue
                
    except Exception as e:
        print(f"SSE监听错误: {e}")
    
    return project_id

def test_get_project_details(project_id):
    """测试获取项目详情API"""
    url = f"http://localhost:8000/api/v1/projects/{project_id}/details"
    
    print(f"\n获取项目 {project_id} 详情...")
    response = requests.get(url)
    
    if response.status_code != 200:
        print(f"错误: {response.status_code} - {response.text}")
        return
    
    data = response.json()
    print(f"项目标题: {data.get('title')}")
    print(f"聊天历史数量: {len(data.get('chat_history', []))}")
    
    chat_history = data.get('chat_history', [])
    if chat_history:
        print("聊天历史记录:")
        for i, msg in enumerate(chat_history):
            print(f"  {i+1}. {msg.get('sender')}: {msg.get('text')[:50]}...")
    else:
        print("⚠️ 没有找到聊天历史记录!")
    
    return data

if __name__ == "__main__":
    # 测试生成幻灯片并监听完整过程
    project_id = test_slide_generation_with_sse()
    
    if project_id:
        # 等待数据保存
        time.sleep(2)
        # 测试获取项目详情
        test_get_project_details(project_id)
    else:
        print("幻灯片生成失败，无法获取项目ID") 