"""
backend/app/main.py - TikTodo AI Slides 主应用入口文件

使用了统一的日志工具 (app.utils.log_util) 来避免重复日志输出。
日志管理通过单例模式实现，确保即使在不同模块中多次导入也只会输出一次相同的日志消息。
"""

import sys
import asyncio
import os
import uuid
from pydantic import BaseModel

import json
import datetime
import traceback
import logging
from typing import Optional
from fastapi import FastAPI, Request, status, Response, BackgroundTasks
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware # For Frontend communication
import uvicorn
import glob # 用于查找文件
from sqlalchemy import text # 导入 text 函数
from contextlib import asynccontextmanager # 导入异步上下文管理器
from app.db import models as db_models
from app.db.session import engine 

# 确保导入路径设置在所有模块导入之前
# Add the parent directory to sys.path to allow imports to work
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 在Windows上，默认的asyncio事件循环策略 (ProactorEventLoop)
# 与Playwright启动子进程的方式不兼容，会导致NotImplementedError。
# 我们需要手动将其设置为兼容的SelectorEventLoop策略。
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
# 动态获取当前虚拟环境的site-packages路径
# 这段代码必须在其他可能依赖虚拟环境库的import语句之前
current_script_dir = os.path.dirname(os.path.abspath(__file__))
venv_root = os.path.join(current_script_dir, "..", "..", "venv")
site_packages_path = os.path.join(venv_root, "Lib", "site-packages")

if os.path.exists(site_packages_path) and site_packages_path not in sys.path:
    sys.path.insert(0, site_packages_path) # 将其插入到sys.path的最前面
    print(f"Debug: Added {site_packages_path} to sys.path for prioritization.")

# 不再需要兼容性补丁，现在使用原生的google-genai API
    
# 导入统一的日志工具
from app.utils.log_util import log_once

# 显示基本环境信息
log_once(f"PYTHONIOENCODING before set: {os.environ.get('PYTHONIOENCODING')}")
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONLEGACYWINDOWSSTDIO'] = 'utf-8'
log_once(f"PYTHONIOENCODING after set: {os.environ.get('PYTHONIOENCODING')}")
log_once(f"HTTP_PROXY from env: {os.environ.get('HTTP_PROXY')}")
log_once(f"HTTPS_PROXY from env: {os.environ.get('HTTPS_PROXY')}")
if os.environ.get('HTTP_PROXY') or os.environ.get('HTTPS_PROXY'):
    log_once("Using proxy configuration from environment variables")

# 全局变量，用于跟踪此进程的工作流实例
_workflow_instance = None
workflow = None # 声明全局变量，将在lifespan中初始化

# 现在可以安全地导入app模块
from app.models.presentation_model import OrchestratorProgress, AiChatMessage, Slide
from app.core.config import settings

# 导入数据库相关模块
from sqlalchemy.orm import Session
from app.db.session import get_db, SessionLocal
from app.crud import crud_project
from app.db import models as db_models

# 导入路由
from app.routers import file_upload, chat_with_files

# 设置自定义日志处理器
log_once("[MainApp Pre-Init] Using project-specific chat logging instead of error log handler.")

# 创建异步上下文管理器用于lifespan事件
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行的代码
    log_once("[MainApp Startup] Application startup event triggered.")
    
    log_once("[MainApp Startup] Initializing database and creating tables if they don't exist...")
    try:
        db_models.Base.metadata.create_all(bind=engine)
        log_once("[MainApp Startup] Database tables checked/created successfully.")
    except Exception as e:
        log_once(f"[MainApp Startup] CRITICAL: Could not create database tables: {e}", level=logging.ERROR)
        # 在这种严重错误下，可以选择不让应用继续运行或抛出异常
        
    # 1. 初始化工作流实例 (这个过程现在很快)
    global workflow
    workflow = initialize_workflow()

    if workflow is None:
        log_once("[MainApp Startup] CRITICAL: Workflow instance could not be initialized!")
        # 在这种严重错误下，可以选择不让应用继续运行
        # raise RuntimeError("Workflow initialization failed.")
    else:
        log_once("[MainApp Startup] Workflow instance created.")

    # 2. 异步执行耗时的健康检查
    async def run_health_checks():
        # 测试数据库连接
        try:
            with SessionLocal() as db:
                db.execute(text("SELECT 1"))
                log_once("[MainApp Health Check] Database connection test successful.")
        except Exception as e:
            log_once(f"[MainApp Health Check] CRITICAL: Database connection test failed: {e}")

        # 【核心修复】注释或删除以下模型连接测试代码块
        # 这个测试在启动时消耗了宝贵的API调用次数，并且不是必需的。
        # 实际的连接问题会在第一次调用时暴露出来。
        # if workflow and hasattr(workflow, '_test_model_connection'):
        #     log_once("[MainApp Health Check] Performing async model connection test...")
        #     # 使用 asyncio.to_thread 在后台线程中运行同步函数，避免阻塞事件循环
        #     test_successful = await asyncio.to_thread(workflow._test_model_connection)
        #     if test_successful:
        #         log_once("[MainApp Health Check] Model connection test successful.")
        #     else:
        #         log_once("[MainApp Health Check] WARNING: Model connection test failed.")
    
    # 启动后台任务执行健康检查，不阻塞应用启动
    asyncio.create_task(run_health_checks())
    
    log_once("[MainApp Startup] Lifespan setup complete. Application is ready to accept requests.")
    
    yield # 应用在此运行时点运行
    
    # 关闭时执行的代码
    log_once("[MainApp Shutdown] Application shutdown.")

# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan # 使用异步上下文管理器
)

# CORS (Cross-Origin Resource Sharing)
# Allow specific origins for better security
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"], # Allow all origins for testing
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(file_upload.router)
app.include_router(chat_with_files.router)

# 异常处理器
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": exc.errors(), "body": exc.body},
    )

@app.get("/", tags=["Root"])
async def read_root():
    return {"message": f"Welcome to {settings.PROJECT_NAME}!"}

# 初始化工作流
def initialize_workflow():
    """初始化工作流实例，确保每个进程只创建一个实例"""
    global _workflow_instance
    
    # 如果已经初始化，直接返回现有实例
    if _workflow_instance is not None:
        return _workflow_instance
    
    log_once("[MainApp Pre-Init] Attempting to instantiate PresentationWorkflow...")
    
    try:
        # 导入这里，避免循环引用
        from app.agents.presentation_workflow import PresentationWorkflow
        workflow = PresentationWorkflow()
        log_once("[MainApp Pre-Init] PresentationWorkflow instantiated successfully.")
        _workflow_instance = workflow
        return workflow
    except Exception as e:
        log_once(f"[MainApp Pre-Init] CRITICAL ERROR instantiating PresentationWorkflow: {e}")
        traceback.print_exc()
        return None

log_once("[MainApp] Skipping global Gemini API configuration. API keys will be passed per-request.")

# 请求模型
class GeneratePresentationRequest(BaseModel):
    user_prompt: str
    project_id: Optional[str] = None

@app.post("/generate_presentation/")
async def generate_presentation_endpoint(
    request: GeneratePresentationRequest,
    response: Response,
    background_tasks: BackgroundTasks,
):
    """
    生成幻灯片演示文稿
    
    - 使用简化的整体风格和大纲生成方法，将复杂的JSON格式替换为自然语言处理
    - 拆分LLM调用以提高成功率和鲁棒性
    - 当检测到WARNING或ERROR日志时，自动将相关内容保存到debug目录
    """
    is_new_project = not request.project_id # 标记是否是新项目
    
    # 如果是新项目，API层生成一个临时ID用于SSE连接
    # 后台任务将在创建数据库记录时获取真实的DB生成ID
    project_id_for_task = request.project_id # 如果前端提供了ID，就用它
    
    if is_new_project:
        # 为新项目生成临时ID，仅用于初始SSE连接
        project_id_for_task = f"proj_{uuid.uuid4().hex[:8]}"
        log_once(f"API层为新项目生成临时ID: {project_id_for_task}")
    
    # 启动后台任务
    background_tasks.add_task(
        handle_presentation_generation,
        request.user_prompt,
        project_id_for_task,
        is_new_project  # 传递这个标记，指示是否为API层生成的新项目ID
    )

    return {"project_id": project_id_for_task, "status": "started"}

async def handle_presentation_generation(
    user_prompt: str, 
    project_id_from_api: str, 
    is_new_project_id_generated_by_api: bool
):
    """
    后台处理演示文稿生成
    
    Args:
        user_prompt: 用户输入的提示文本
        project_id_from_api: 由API层传递的项目ID (可能是前端提供的已存在ID，也可能是API层为新项目生成的临时ID)
        is_new_project_id_generated_by_api: 标记这个ID是否是API层为新项目生成的临时ID
    """
    # 在后台任务中，创建独立的数据库会话
    db: Session = SessionLocal()
    current_user = None  # 暂时不处理用户，或后续从其他地方获取
    actual_db_project_id = project_id_from_api  # 初始化，可能会被更新为DB生成的ID
    
    try:
        if workflow is None:
            log_once(f"[Project {project_id_from_api}] ERROR: PresentationWorkflow is not initialized.")
            return
        
        user_id_for_db = current_user.id if current_user else None
        
        # --- 核心修改：在开始生成前增加一个等待 ---
        # 无论项目是新建的还是已存在的，在开始推送任何实际进度之前，
        # 我们都等待一小段时间，确保前端有足够的时间完成SSE连接。
        log_once(f"[ID管理] 等待 1.0 秒，以便前端建立SSE连接...")
        await asyncio.sleep(1.0)
        # +++++++++++++++++++++++++++++++++++++++++++

        if is_new_project_id_generated_by_api:
            # 这是API层生成的临时ID project_id_from_api，需要在数据库中创建实际记录
            log_once(f"[ID管理] 新项目（API临时ID {project_id_from_api}），在DB中创建记录")
            try:
                # 创建数据库项目记录
                db_project_record = crud_project.create_project(
                    db=db,
                    title=f"处理中 - {user_prompt[:20]}...",
                    user_id=user_id_for_db,
                    total_slides_planned=0  # 初始值，后续会更新
                )
                # 获取数据库生成的真实ID
                actual_db_project_id = db_project_record.id
                log_once(f"[ID管理] 新项目DB记录创建成功，真实DB ID: {actual_db_project_id}")

                # >>> 确保这个延迟存在 <<<
                await asyncio.sleep(0.1) # 例如，0.1秒
                
                # 发送ID映射消息给前端，使用API临时ID作为消息的project_id，
                # metadata中包含真实的DB ID和临时ID
                if actual_db_project_id != project_id_from_api: # 只有当ID不同时才需要映射
                    log_once(f"[ID管理] 需要ID映射: API临时ID {project_id_from_api} -> DB真实ID {actual_db_project_id}")
                    try:
                        id_mapping_progress = OrchestratorProgress(
                            project_id=project_id_from_api, # 消息发给用临时ID连接的客户端
                            status="id_mapping",
                            message=AiChatMessage(
                                sender="system", 
                                text=f"项目内部ID已确认为 {actual_db_project_id}", 
                                icon="🔗"
                            ),
                            metadata={
                                "temp_project_id": project_id_from_api, # 明确传递临时ID
                                "actual_project_id": actual_db_project_id # 明确传递真实DB ID
                            }
                        )
                        from app.apis.v1.slides_router import push_progress_to_client
                        await push_progress_to_client(id_mapping_progress) 
                        await asyncio.sleep(0.5) # 给更多时间处理映射
                        log_once(f"[ID管理] ID映射消息已发送，临时等待0.5秒")
                    except Exception as map_err:
                        log_once(f"[ID管理] 发送ID映射消息失败: {map_err}", exc_info=True)
            except Exception as e:
                log_once(f"[ID管理] 创建DB项目记录失败 for API temp ID {project_id_from_api}: {e}", exc_info=True)
                return
        else:
            # project_id_from_api 是前端提供的已存在ID，应该是真实的DB ID
            actual_db_project_id = project_id_from_api
            log_once(f"[ID管理] 尝试加载现有项目 {actual_db_project_id}")
            db_project_record = crud_project.get_project(db, project_id=actual_db_project_id, user_id=user_id_for_db)
            if not db_project_record:
                log_once(f"[ID管理] 项目 {actual_db_project_id} 未找到或用户无权限")
                
                # 尝试发送错误消息到前端
                try:
                    error_progress = OrchestratorProgress(
                        project_id=project_id_from_api,  # 使用原始ID，确保消息能送达
                        status="error",
                        message=AiChatMessage(
                            sender="system",
                            text=f"项目 {actual_db_project_id} 未找到或无访问权限",
                            icon="⚠️"
                        )
                    )
                    
                    # 导入slides_router中的推送函数
                    from app.apis.v1.slides_router import push_progress_to_client
                    await push_progress_to_client(error_progress)
                except Exception as push_err:
                    log_once(f"[ID管理] 发送错误消息失败: {push_err}")
                
                return
            log_once(f"[ID管理] 成功加载现有项目 {actual_db_project_id}")
        
        # 确保我们有有效的项目ID和记录
        if not actual_db_project_id or not db_project_record:
            log_once(f"[ID管理] 严重错误：未能确定有效的项目ID或数据库记录")
            return

        # 如果到这里，actual_db_project_id 已经是真实的数据库ID了
        # 提前发送一个准备就绪的消息，以防映射有任何问题，此消息直接使用actual_db_project_id
        try:
            ready_progress = OrchestratorProgress(
                project_id=actual_db_project_id,  # 使用真实DB ID
                status="preparing",
                message=AiChatMessage(
                    sender="system",
                    text=f"准备开始生成 (项目ID: {actual_db_project_id})",
                    icon="⚙️"
                )
            )
            from app.apis.v1.slides_router import push_progress_to_client
            await push_progress_to_client(ready_progress)
        except Exception as ready_err:
            log_once(f"[ID管理] 发送准备就绪消息失败: {ready_err}")
        
        # 现在使用实际的数据库项目ID生成演示文稿
        log_once(f"[ID管理] 开始使用真实DB ID {actual_db_project_id} 调用工作流")
        
        # 定义推送进度的函数
        async def push_progress_func(progress: OrchestratorProgress):
            # 确保progress中的project_id是实际数据库ID
            if progress.project_id != actual_db_project_id:
                progress.project_id = actual_db_project_id
            # 推送到客户端
            from app.apis.v1.slides_router import push_progress_to_client
            await push_progress_to_client(progress)
        
        # 调用简化的工作流
        await workflow.generate_async(
            user_prompt=user_prompt, 
            project_id=actual_db_project_id,  # 使用实际的数据库项目ID
            push_progress_func=push_progress_func
        )
            
    except Exception as e:
        effective_id = actual_db_project_id if 'actual_db_project_id' in locals() else project_id_from_api
        error_msg = f"未捕获异常：{str(e)}"
        log_once(f"[Project {effective_id}] ERROR: {error_msg}")
        traceback.print_exc()
        
        # 尝试更新项目状态并发送错误消息
        try:
            # 更新项目状态
            if 'actual_db_project_id' in locals() and actual_db_project_id:
                crud_project.update_project(db, actual_db_project_id, {
                    "status": "error", 
                    "error_message": error_msg[:255]  # 限制长度
                })
            
            # 发送错误消息到前端 - 使用project_id_from_api确保消息能送达
            error_progress = OrchestratorProgress(
                project_id=project_id_from_api,  # 使用原始ID，确保能送达
                status="error",
                message=AiChatMessage(
                    sender="system",
                    text=f"系统错误：{error_msg[:100]}...",
                    icon="⚠️"
                ),
                metadata=(
                    {"temp_project_id": project_id_from_api, "actual_project_id": actual_db_project_id} 
                    if 'actual_db_project_id' in locals() and actual_db_project_id != project_id_from_api 
                    else None
                )
            )
            
            from app.apis.v1.slides_router import push_progress_to_client
            await push_progress_to_client(error_progress)
        except Exception as update_err:
            log_once(f"[ID管理] 更新项目状态或发送错误消息失败: {update_err}")
    finally:
        # 确保关闭数据库会话
        db.close()

def main():
    """启动应用的主入口点，用于独立运行模式"""
    uvicorn.run(
        "app.main:app",
        host=settings.SERVER_HOST, 
        port=settings.SERVER_PORT,
        reload=settings.DEV_MODE,  # 开发模式下启用热重载
        log_level="info"
    )

# 导入API路由 - 放在最后，避免循环导入
from app.apis import v1
from app.apis.v1 import chat_router # 导入chat_router

# 包含API路由
app.include_router(v1.slides_router.router, prefix=settings.API_V1_STR, tags=["Slides"])
app.include_router(v1.auth_router.router, prefix=f"{settings.API_V1_STR}/auth", tags=["Authentication"])
app.include_router(chat_router.router, prefix=f"{settings.API_V1_STR}", tags=["Chat"]) # 添加chat_router


if __name__ == "__main__":
    main()