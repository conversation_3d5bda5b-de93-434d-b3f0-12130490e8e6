from fastapi import APIRouter, Depends, HTTPException, status, Body
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import AsyncGenerator, Optional, Dict, Any
import uuid
import json
import logging
from datetime import datetime, timezone
from pydantic import BaseModel

from app.db.session import get_db
from app.crud import crud_project, crud_chat
from app.db import models as db_models
from app.models.presentation_model import AiChatMessage # Reusing AiChatMessage
from app.agents.chat_agent import ChatAgent # Import the new ChatAgent
from .auth_router import optional_current_user # To get optional user context

router = APIRouter()
chat_router_logger = logging.getLogger(__name__)

# 定义请求模型
class ChatMessageRequest(BaseModel):
    user_message: str
    project_id: Optional[str] = None  # 可选的项目ID，如果提供则使用现有项目
    turbo_mode: Optional[bool] = False  # 新增极速模式参数，默认为False

@router.post("/chat/stream")
async def chat_stream(
    request: ChatMessageRequest,
    db: Session = Depends(get_db),
    current_user: Optional[Dict[str, Any]] = Depends(optional_current_user)
):
    """
    流式AI聊天API。
    """
    user_message = request.user_message
    chat_project_id = request.project_id
    turbo_mode = request.turbo_mode  # 获取极速模式参数
    
    chat_router_logger.info(f"接收到聊天请求，用户消息前50个字符: {user_message[:50]}...")
    chat_router_logger.info(f"项目ID: {chat_project_id or '新项目'}, 极速模式: {'开启' if turbo_mode else '关闭'}")

    # 如果没有提供项目ID，创建一个新的项目
    if not chat_project_id:
        # 创建一个新的项目
        project_name = f"Chat_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        new_project = crud_project.create_project(
            db=db,
            title=project_name,
            user_id=current_user.get("id") if current_user else None
        )
        chat_project_id = str(new_project.id)
        chat_router_logger.info(f"为聊天创建了新项目: {chat_project_id}")
    
    # 保存用户消息到数据库
    try:
        user_chat_message = AiChatMessage(
            sender="user",
            text=user_message,
            icon="👤"
        )
        crud_chat.add_chat_message(
            db=db,
            project_id=chat_project_id,
            message=user_chat_message,
            user_id=current_user.get("id") if current_user else None
        )
        chat_router_logger.info(f"已保存用户消息到数据库，项目ID: {chat_project_id}")
    except Exception as e:
        chat_router_logger.error(f"保存用户消息到数据库失败: {e}", exc_info=True)

    # Initialize chat agent
    chat_agent = ChatAgent()

    async def event_generator() -> AsyncGenerator[str, None]:
        try:
            # 获取聊天历史
            chat_history = None
            if chat_project_id:
                try:
                    # 获取该项目的历史聊天记录
                    db_messages = crud_chat.get_chat_messages_for_project(db=db, project_id=chat_project_id)
                    if db_messages:
                        chat_history = []
                        for msg in db_messages:
                            chat_history.append({
                                "role": "user" if msg.sender == "user" else "model",
                                "content": msg.text,
                                "thinking": getattr(msg, "thinking", False)
                            })
                        chat_router_logger.info(f"已加载 {len(chat_history)} 条聊天历史记录")
                except Exception as e:
                    chat_router_logger.error(f"加载聊天历史记录失败: {e}", exc_info=True)
            
            # 调用ChatAgent处理请求，传递turbo_mode参数
            async for event in chat_agent.process_chat_stream(
                user_message=user_message,
                project_id_for_logging=chat_project_id,
                chat_history=chat_history,
                turbo_mode=turbo_mode  # 传递极速模式参数
            ):
                yield event
        except Exception as e:
            error_str = str(e)
            chat_router_logger.error(f"生成聊天响应时发生错误: {error_str}", exc_info=True)
            error_message = AiChatMessage(
                sender="system",
                text=f"Error: Failed to generate response: {error_str}",
                icon="⚠️",
                id=f"err-{uuid.uuid4().hex[:8]}",
                project_id=chat_project_id
            )
            yield f"data: {error_message.model_dump_json()}\n\n"
        finally:
            # Ensure DB session is closed. This is crucial for FastAPI's Depends.
            db.close() # Ensure the session from get_db is closed after generator completes

    return StreamingResponse(event_generator(), media_type="text/event-stream") 