#!/usr/bin/env python3
"""
支持文件上传的聊天API路由
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Form
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from app.agents.chat_agent import ChatAgent
from app.models.presentation_model import AiChatMessage

router = APIRouter(prefix="/api/chat", tags=["chat-with-files"])
logger = logging.getLogger(__name__)

class ChatWithFilesRequest(BaseModel):
    message: str
    file_ids: Optional[List[str]] = []
    session_id: Optional[str] = None

@router.post("/with-files")
async def chat_with_files(request: ChatWithFilesRequest):
    """
    支持文件的聊天接口
    """
    try:
        # 初始化聊天代理
        chat_agent = ChatAgent()
        
        # 构建用户消息，包含文件引用
        user_message = request.message
        if request.file_ids:
            file_references = " ".join([f"[FILE:{file_id}]" for file_id in request.file_ids])
            user_message = f"{request.message} {file_references}"
        
        # 创建异步生成器包装器，将AiChatMessage转换为SSE格式
        async def generate_response():
            try:
                async for response in chat_agent.process_chat_stream(
                    user_message=user_message,
                    project_id_for_logging=request.session_id or "chat-session"
                ):
                    # 如果是AiChatMessage对象，转换为SSE格式
                    if hasattr(response, 'model_dump_json'):
                        yield f"data: {response.model_dump_json()}\n\n"
                    elif isinstance(response, str):
                        # 如果已经是SSE格式的字符串，直接输出
                        if not response.startswith('data: '):
                            yield f"data: {response}\n\n"
                        else:
                            yield response
                    else:
                        # 其他类型，包装成消息
                        message = AiChatMessage(
                            sender="ai",
                            text=str(response)
                        )
                        yield f"data: {message.model_dump_json()}\n\n"
                
                # 发送结束标记
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                logger.error(f"聊天流生成错误: {str(e)}")
                error_message = AiChatMessage(
                    sender="system",
                    text=f"聊天过程中发生错误: {str(e)}",
                    icon="⚠️"
                )
                yield f"data: {error_message.model_dump_json()}\n\n"
                yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
            }
        )
    
    except Exception as e:
        logger.error(f"聊天接口错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"聊天失败: {str(e)}")

@router.post("/simple")
async def simple_chat(
    message: str = Form(...),
    session_id: str = Form(default=None)
):
    """
    简单聊天接口（无文件）
    """
    try:
        chat_agent = ChatAgent()
        
        async def generate_response():
            try:
                async for response in chat_agent.process_chat_stream(
                    user_message=message,
                    project_id_for_logging=session_id or "simple-chat"
                ):
                    if hasattr(response, 'model_dump_json'):
                        yield f"data: {response.model_dump_json()}\n\n"
                    elif isinstance(response, str):
                        if not response.startswith('data: '):
                            yield f"data: {response}\n\n"
                        else:
                            yield response
                    else:
                        message_obj = AiChatMessage(
                            sender="ai",
                            text=str(response)
                        )
                        yield f"data: {message_obj.model_dump_json()}\n\n"
                
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                logger.error(f"简单聊天流生成错误: {str(e)}")
                error_message = AiChatMessage(
                    sender="system",
                    text=f"聊天过程中发生错误: {str(e)}",
                    icon="⚠️"
                )
                yield f"data: {error_message.model_dump_json()}\n\n"
                yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
            }
        )
    
    except Exception as e:
        logger.error(f"简单聊天接口错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"聊天失败: {str(e)}") 