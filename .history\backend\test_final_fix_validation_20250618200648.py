#!/usr/bin/env python3
"""
最终修复验证测试脚本 - 验证SlideElementSchema和DetailedSlideBlueprintSchema的JSON Schema兼容性
"""

import json
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.models.presentation_model import (
    SlideElementSchema,
    DetailedSlideBlueprintSchema
)

def test_slide_element_schema():
    """测试SlideElementSchema的JSON Schema生成"""
    print("=== 测试 SlideElementSchema ===")
    
    try:
        # 生成JSON Schema
        schema = SlideElementSchema.model_json_schema()
        print("✅ SlideElementSchema JSON Schema生成成功")
        
        # 检查是否存在anyOf结构
        schema_str = json.dumps(schema)
        if 'anyOf' in schema_str:
            print("❌ SlideElementSchema包含anyOf结构")
            print(f"Schema片段: {schema_str[:500]}...")
            return False
        else:
            print("✅ SlideElementSchema不包含anyOf结构")
            
        # 检查properties
        if 'properties' in schema:
            properties = schema['properties']
            print(f"✅ SlideElementSchema包含 {len(properties)} 个属性")
            
            # 检查关键字段
            required_fields = ['type', 'content', 'target_area']
            for field in required_fields:
                if field in properties:
                    field_def = properties[field]
                    if 'type' in field_def and field_def['type'] == 'string':
                        print(f"✅ {field} 字段定义正确")
                    else:
                        print(f"❌ {field} 字段定义异常: {field_def}")
                        return False
                else:
                    print(f"❌ 缺少必要字段: {field}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ SlideElementSchema测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_detailed_slide_blueprint_schema():
    """测试DetailedSlideBlueprintSchema的JSON Schema生成"""
    print("\n=== 测试 DetailedSlideBlueprintSchema ===")
    
    try:
        # 生成JSON Schema
        schema = DetailedSlideBlueprintSchema.model_json_schema()
        print("✅ DetailedSlideBlueprintSchema JSON Schema生成成功")
        
        # 检查是否存在anyOf结构
        schema_str = json.dumps(schema)
        if 'anyOf' in schema_str:
            print("❌ DetailedSlideBlueprintSchema包含anyOf结构")
            print(f"Schema片段: {schema_str[:500]}...")
            return False
        else:
            print("✅ DetailedSlideBlueprintSchema不包含anyOf结构")
            
        # 检查key_elements字段
        if 'properties' in schema and 'key_elements' in schema['properties']:
            key_elements_def = schema['properties']['key_elements']
            print(f"✅ key_elements字段定义: {json.dumps(key_elements_def, indent=2)[:200]}...")
            
            # 检查是否为数组类型
            if key_elements_def.get('type') == 'array':
                print("✅ key_elements定义为数组类型")
                
                # 检查items定义
                if 'items' in key_elements_def:
                    items_def = key_elements_def['items']
                    if '$ref' in items_def:
                        print("✅ key_elements.items使用$ref引用SlideElementSchema")
                    else:
                        print(f"✅ key_elements.items定义: {type(items_def)}")
                else:
                    print("❌ key_elements缺少items定义")
                    return False
            else:
                print(f"❌ key_elements不是数组类型: {key_elements_def}")
                return False
        else:
            print("❌ 缺少key_elements字段")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ DetailedSlideBlueprintSchema测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_slide_element_creation():
    """测试SlideElementSchema实例创建"""
    print("\n=== 测试 SlideElementSchema 实例创建 ===")
    
    try:
        # 测试标题元素
        title_element = SlideElementSchema(
            type="title",
            content="珍珠港事件",
            target_area="title_area"
        )
        print("✅ 标题元素创建成功")
        print(f"   元素数据: type={title_element.type}, content={title_element.content}")
        
        # 测试KPI卡片元素
        kpi_element = SlideElementSchema(
            type="kpi_card",
            content="战舰损失",
            target_area="kpi_area",
            kpi_value="8艘",
            kpi_label="战舰沉没",
            kpi_trend="up"
        )
        print("✅ KPI卡片元素创建成功")
        print(f"   元素数据: type={kpi_element.type}, kpi_value={kpi_element.kpi_value}")
        
        # 测试图表元素
        chart_element = SlideElementSchema(
            type="chart",
            content="损失统计",
            target_area="chart_area",
            chart_type="bar",
            chart_data='{"labels": ["战舰", "飞机"], "datasets": [{"data": [8, 188]}]}'
        )
        print("✅ 图表元素创建成功")
        print(f"   元素数据: type={chart_element.type}, chart_type={chart_element.chart_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ SlideElementSchema实例创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_detailed_blueprint_creation():
    """测试DetailedSlideBlueprintSchema实例创建"""
    print("\n=== 测试 DetailedSlideBlueprintSchema 实例创建 ===")
    
    try:
        # 创建幻灯片元素
        elements = [
            SlideElementSchema(
                type="title",
                content="珍珠港事件背景",
                target_area="title_area"
            ),
            SlideElementSchema(
                type="paragraph",
                content="1941年12月7日，日本海军偷袭美国珍珠港海军基地，成为太平洋战争的导火索。",
                target_area="content_area"
            )
        ]
        
        # 创建详细蓝图
        blueprint = DetailedSlideBlueprintSchema(
            slide_number=1,
            layout_template_name="ContentSlideLayout",
            background_style_description="linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)",
            key_elements=elements,
            speaker_notes="这张幻灯片介绍了珍珠港事件的历史背景。演讲者应该强调这个事件对于美国参与二战的重要性，需要详细阐述其历史意义和影响。"
        )
        print("✅ DetailedSlideBlueprintSchema实例创建成功")
        print(f"   蓝图数据: slide_number={blueprint.slide_number}, elements_count={len(blueprint.key_elements)}")
        
        # 测试序列化
        blueprint_dict = blueprint.model_dump()
        print("✅ 蓝图序列化成功")
        
        # 测试从字典重建
        blueprint_rebuilt = DetailedSlideBlueprintSchema(**blueprint_dict)
        print("✅ 蓝图从字典重建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ DetailedSlideBlueprintSchema实例创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("开始最终修复验证测试...\n")
    
    tests = [
        test_slide_element_schema,
        test_detailed_slide_blueprint_schema,
        test_slide_element_creation,
        test_detailed_blueprint_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！SlideElementSchema修复成功！")
        print("现在应该能够解决Gemini API兼容性问题。")
        return True
    else:
        print("❌ 仍有测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 