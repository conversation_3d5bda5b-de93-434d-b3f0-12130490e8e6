from sqlalchemy.orm import Session
from typing import List, Optional
from app.db import models as db_models
from app.models import user_model as pydantic_user_models

def get_project(db: Session, project_id: str, user_id: Optional[str] = None) -> Optional[db_models.Project]:
    """获取特定ID的项目，如果提供了user_id则检查所有权"""
    query = db.query(db_models.Project).filter(db_models.Project.id == project_id)
    if user_id:
        query = query.filter(db_models.Project.user_id == user_id)
    return query.first()

def get_projects_by_user(db: Session, user_id: Optional[str] = None, skip: int = 0, limit: int = 100) -> List[db_models.Project]:
    """获取用户的所有项目，如果user_id为None则获取所有项目"""
    query = db.query(db_models.Project)
    if user_id:
        query = query.filter(db_models.Project.user_id == user_id)
    return query.order_by(db_models.Project.last_modified.desc()).offset(skip).limit(limit).all()

def create_project(db: Session, title: str, user_id: Optional[str] = None, total_slides_planned: int = 0) -> db_models.Project:
    """创建新项目"""
    db_project = db_models.Project(
        title=title,
        user_id=user_id,
        total_slides_planned=total_slides_planned
    )
    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    return db_project

def update_project(db: Session, project_id: str, update_data: dict, user_id: Optional[str] = None) -> Optional[db_models.Project]:
    """更新项目信息"""
    db_project = get_project(db, project_id=project_id, user_id=user_id)
    if not db_project:
        return None
    
    for field, value in update_data.items():
        setattr(db_project, field, value)
    
    db.commit()
    db.refresh(db_project)
    return db_project

def delete_project(db: Session, project_id: str, user_id: Optional[str] = None) -> bool:
    """删除项目及其所有相关数据"""
    db_project = get_project(db, project_id=project_id, user_id=user_id)
    if db_project:
        db.delete(db_project)  # SQLAlchemy 会处理级联删除 (chat_messages, slides)
        db.commit()
        return True
    return False 