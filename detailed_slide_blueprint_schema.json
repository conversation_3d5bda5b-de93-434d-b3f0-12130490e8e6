{"description": "A comprehensive blueprint for generating a single presentation slide with enhanced validation and constraints.", "properties": {"slide_number": {"description": "幻灯片的顺序号，从1开始。", "maximum": 50, "minimum": 1, "title": "Slide Number", "type": "integer"}, "layout_template_name": {"description": "布局模板的名称，必须从预定义的模板中选择。可选值：'TitleSlideLayout'(封面页), 'DataDashboardLayout'(数据仪表板), 'ContentSlideLayout'(内容页), 'PolicyAnalysisLayout'(政策分析), 'ComparisonLayout'(对比分析), 'TimelineLayout'(时间线), 'ProcessFlowLayout'(流程图)。选择应基于幻灯片内容类型。", "title": "Layout Template Name", "type": "string"}, "background_style_description": {"description": "对背景的详细CSS描述，例如 'linear-gradient(135deg, #0A1931 0%, #1E293B 100%)' 或 'radial-gradient(circle at center, #f8fafc 0%, #e2e8f0 100%)'。应与整体风格保持一致。", "minLength": 10, "title": "Background Style Description", "type": "string"}, "key_elements": {"description": "构成此幻灯片核心内容的所有元素的列表。每个元素都必须有'type'字段以进行区分。支持的类型：TextElement(title, subtitle, paragraph, bullet_point, kicker)、KpiCardSchema(kpi_card)、ChartBlueprintSchema(chart)、ImageElementSchema(image)。列表不能为空，至少包含一个元素。", "items": {"properties": {"type": {"description": "元素类型，用于区分不同的元素类型", "enum": ["title", "subtitle", "paragraph", "bullet_point", "kicker", "kpi_card", "chart", "image"], "type": "string"}, "content": {"description": "文本内容（适用于文本元素：title, subtitle, paragraph, bullet_point, kicker）", "type": "string"}, "target_area": {"description": "目标区域ID，指定元素在幻灯片中的位置", "type": "string"}, "animation_style": {"description": "动画样式，可选的动画效果", "type": "string"}, "title": {"description": "标题（适用于KPI卡片和图表元素）", "type": "string"}, "value": {"description": "数值（适用于KPI卡片元素）", "type": "string"}, "change": {"description": "变化值（适用于KPI卡片元素，如 +5% 或 -2%）", "type": "string"}, "icon_fontawesome_class": {"description": "Font Awesome图标类名（适用于KPI卡片元素，如 fas fa-chart-line）", "type": "string"}, "chart_type": {"description": "图表类型（适用于图表元素）", "enum": ["bar", "line", "pie", "radar", "doughnut"], "type": "string"}, "data_fabrication_instruction": {"description": "数据生成指令（适用于图表元素，描述如何生成图表数据）", "type": "string"}, "final_chart_js_config": {"description": "Chart.js配置对象（适用于图表元素）", "type": "object"}, "generation_prompt": {"description": "图片生成提示（适用于图片元素，用于AI图片生成）", "type": "string"}, "alt_text": {"description": "图片alt文本（适用于图片元素，用于可访问性）", "type": "string"}}, "required": ["type", "target_area"], "type": "object"}, "minItems": 1, "type": "array"}, "speaker_notes": {"description": "演讲者的备注，用于对幻灯片内容进行补充说明或提供演讲提示。内容需要有深度和见解，不少于50字符。必须包含：1)关键数据的深度解读和背景分析；2)数据趋势的商业意义；3)具体的演讲建议和重点强调内容。避免简单复述幻灯片内容。", "maxLength": 1000, "minLength": 50, "title": "Speaker Notes", "type": "string"}}, "required": ["slide_number", "layout_template_name", "background_style_description", "key_elements", "speaker_notes"], "title": "Detailed Slide Blueprint", "type": "object"}