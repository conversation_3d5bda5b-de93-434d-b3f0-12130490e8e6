{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\RightPane.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\n// import { useNavigate } from 'react-router-dom'; // 引入 useNavigate\nimport SlideRenderer from './SlideRenderer';\nimport SlideThumbnails from './SlideThumbnails';\nimport { FaPlay, FaPencilAlt, FaArrowsAlt, FaSpinner, FaCode, FaEye } from 'react-icons/fa';\n// FullScreenPlayer 不再由此组件直接管理，由 App.js 管理\n// import FullScreenPlayer from './FullScreenPlayer';\n// We will add edit controls later\n// import EditControls from './EditControls';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RightPane = ({\n  allSlides,\n  // Array of {id, html (full), code, thinking, title}\n  currentFocusedSlideIndex,\n  // 由 App.js 传入，表示当前 NLP 编辑焦点的幻灯片\n  onSetFocusedSlideIndex,\n  // 回调，当用户在 RightPane 中点击幻灯片时，更新 App.js 中的 currentFocusedSlideIndex\n  onPlaySlideAtIndex,\n  // 回调，当用户点击某个幻灯片的播放按钮时，触发 App.js 中的全屏播放\n  isAppEditingMode,\n  // App 级的编辑模式\n  onToggleAppEditMode,\n  // 切换 App 级编辑模式的按钮\n  onTextEditStart,\n  // 传递给 SlideRenderer -> iframe\n  presentationTitle,\n  // 从 App.js 传入演示文稿标题\n  onOpenViewAndExport,\n  // 从 App.js 传入查看导出函数\n  slideCode = \"\",\n  // HTML代码\n  activeStep = \"preview\",\n  // 当前活动步骤\n  totalSlideCount = 0,\n  // 总幻灯片数量\n  slideTabIndices = {},\n  // 每个幻灯片的标签页索引\n  onSlideTabChange = () => {},\n  // 设置标签页索引的回调\n  className = \"\" // 添加className属性，默认为空字符串\n}) => {\n  _s();\n  const slideListRef = useRef(null); // 用于滚动聚焦\n  // const navigate = useNavigate(); // 初始化 useNavigate\n  const rightPaneRef = useRef(null);\n\n  // 为每个 preview-slide-outer-container 创建 refs 数组或对象\n  const previewContainerRefs = useRef({});\n\n  // 初始化 refs\n  useEffect(() => {\n    if (allSlides && allSlides.length > 0) {\n      previewContainerRefs.current = allSlides.reduce((acc, slide, index) => {\n        if (!slide) return acc; // 添加防御性检查，跳过undefined的slide\n        const slideKey = slide.id || `slide-${index}`;\n        acc[slideKey] = acc[slideKey] || /*#__PURE__*/React.createRef();\n        return acc;\n      }, {});\n    }\n  }, [allSlides]);\n\n  // useEffect 来动态设置每个 preview-slide-outer-container 的尺寸\n  useEffect(() => {\n    if (!allSlides || allSlides.length === 0) return;\n    const observers = [];\n    allSlides.forEach((slide, index) => {\n      var _previewContainerRefs;\n      if (!slide) return; // 添加防御性检查，跳过undefined的slide\n\n      const slideKey = slide.id || `slide-${index}`;\n      const container = (_previewContainerRefs = previewContainerRefs.current[slideKey]) === null || _previewContainerRefs === void 0 ? void 0 : _previewContainerRefs.current;\n      if (!container) return;\n      const wrapper = container.parentElement; // .slide-item-wrapper\n\n      if (container && wrapper) {\n        const updateSize = () => {\n          const availableWidth = wrapper.offsetWidth; // 获取父容器的宽度\n          if (availableWidth > 0) {\n            const height = availableWidth * 9 / 16;\n            container.style.width = `${availableWidth}px`;\n            container.style.height = `${height}px`;\n          }\n        };\n        updateSize(); // 初始设置\n        const resizeObserver = new ResizeObserver(updateSize);\n        resizeObserver.observe(wrapper); // 观察父容器的尺寸变化\n        observers.push({\n          observer: resizeObserver,\n          element: wrapper\n        });\n      }\n    });\n    return () => {\n      observers.forEach(({\n        observer,\n        element\n      }) => {\n        observer.unobserve(element);\n      });\n    };\n  }, [allSlides]);\n\n  // 根据activeStep自动切换标签页\n  useEffect(() => {\n    if (activeStep === 'code') {\n      // 为当前聚焦的幻灯片设置代码标签，但仅当没有现有选择时\n      if (slideTabIndices[currentFocusedSlideIndex] === undefined) {\n        onSlideTabChange(currentFocusedSlideIndex, 1);\n      }\n    } else if (activeStep === 'preview') {\n      // 默认为预览标签，但仅当没有现有选择时\n      if (slideTabIndices[currentFocusedSlideIndex] === undefined) {\n        onSlideTabChange(currentFocusedSlideIndex, 0);\n      }\n    }\n  }, [activeStep, currentFocusedSlideIndex, onSlideTabChange, slideTabIndices]);\n\n  // 当 currentFocusedSlideIndex (来自App.js) 改变时，滚动到对应的幻灯片\n  useEffect(() => {\n    // +++++++++++++++ BUG修复：添加防御性检查 +++++++++++++++\n    if (!allSlides || allSlides.length === 0 || currentFocusedSlideIndex < 0 || currentFocusedSlideIndex >= allSlides.length) {\n      return; // 如果幻灯片数组为空或索引无效，则不执行任何操作\n    }\n\n    // 检查当前幻灯片是否存在\n    if (!allSlides[currentFocusedSlideIndex]) {\n      console.warn(`无法滚动到索引 ${currentFocusedSlideIndex} 处的幻灯片，因为它是undefined`);\n      return;\n    }\n    // +++++++++++++++++++++++++++++++++++++++++++++++++++++\n\n    // 使用setTimeout确保DOM更新后再滚动\n    setTimeout(() => {\n      // 这里的代码现在是安全的，因为我们已经检查过 allSlides[currentFocusedSlideIndex] 的有效性\n      const slideId = allSlides[currentFocusedSlideIndex].id || `slide-${currentFocusedSlideIndex}`;\n      const slideElement = document.getElementById(`slide-item-wrapper-${slideId}`);\n      if (slideElement && slideListRef.current) {\n        const container = document.querySelector('.right-pane-content-area');\n        if (container) {\n          const containerRect = container.getBoundingClientRect();\n          const elementRect = slideElement.getBoundingClientRect();\n          const targetScrollTop = slideElement.offsetTop - container.offsetTop - (containerRect.height - elementRect.height) / 2;\n          container.scrollTo({\n            top: targetScrollTop,\n            behavior: 'auto'\n          });\n        } else {\n          slideElement.scrollIntoView({\n            behavior: 'auto',\n            block: 'center'\n          });\n        }\n      }\n    }, 50);\n  }, [currentFocusedSlideIndex, allSlides]);\n\n  // 点击幻灯片预览区域时，设置其为当前App的焦点幻灯片\n  const handleSlideFocus = index => {\n    if (index < 0 || index >= allSlides.length) {\n      console.warn(`Invalid slide index: ${index}`);\n      return;\n    }\n\n    // 检查当前幻灯片是否存在\n    if (!allSlides[index]) {\n      console.warn(`无法聚焦索引 ${index} 处的幻灯片，因为它是undefined`);\n      return;\n    }\n    if (onSetFocusedSlideIndex) {\n      onSetFocusedSlideIndex(index);\n    }\n  };\n\n  // 添加全局样式，确保iframe内的所有元素在预览模式下都显示为可点击状态\n  useEffect(() => {\n    const styleTag = document.createElement('style');\n    styleTag.innerHTML = `\n      .slide-preview-container {\n        cursor: pointer !important;\n      }\n      .slide-preview-container * {\n        cursor: pointer !important;\n      }\n    `;\n    document.head.appendChild(styleTag);\n    return () => {\n      document.head.removeChild(styleTag);\n    };\n  }, []);\n\n  // 复制幻灯片代码到剪贴板\n  const handleCopySlideCode = slide => {\n    if (!slide) {\n      console.warn('尝试复制undefined幻灯片的代码');\n      return;\n    }\n    const codeToCopy = slide.code || (slide.html ? slide.html : '/* 此幻灯片暂无代码 */');\n    navigator.clipboard.writeText(codeToCopy).then(() => {}).catch(err => {\n      console.error('复制失败:', err);\n    });\n  };\n\n  // 渲染幻灯片占位符\n  const renderSlidePlaceholders = () => {\n    const placeholders = [];\n    for (let i = 0; i < totalSlideCount; i++) {\n      const isActive = allSlides.length === i;\n      const isCompleted = allSlides.length > i;\n      placeholders.push(/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-full mx-auto bg-white shadow-xl rounded-lg overflow-hidden border-2 ${isActive ? 'border-blue-400 animate-pulse' : isCompleted ? 'border-green-200' : 'border-gray-200'} cursor-wait mb-8`,\n        style: {\n          maxWidth: '1280px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center bg-white border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-4 py-3 text-sm font-medium ${isActive ? 'text-blue-500' : isCompleted ? 'text-green-500' : 'text-gray-400'}`,\n            children: isCompleted ? '✓ 已完成' : isActive ? '⟳ 生成中...' : '预览'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full mx-auto bg-gray-100 relative\",\n          style: {\n            position: 'relative',\n            paddingTop: '56.25%',\n            margin: '1rem 0'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 left-0 w-full h-full flex items-center justify-center\",\n            children: isCompleted ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center justify-center text-green-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-12 h-12\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M5 13l4 4L19 7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-2\",\n                children: \"\\u5DF2\\u751F\\u6210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this) : isActive ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center justify-center space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n                className: \"animate-spin text-blue-500\",\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-blue-500 text-sm\",\n                children: \"\\u6B63\\u5728\\u751F\\u6210\\u5E7B\\u706F\\u7247...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center justify-center space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(FaSpinner, {\n                className: \"animate-spin text-gray-400\",\n                size: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 text-sm\",\n                children: \"\\u7B49\\u5F85\\u751F\\u6210...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center bg-gray-50 px-4 py-2 border-t\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: i === 0 ? \"标题页\" : `幻灯片 ${i + 1}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-400\",\n            children: [i + 1, \" / \", totalSlideCount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, `placeholder-${i}`, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this));\n    }\n    return placeholders;\n  };\n  const renderSlides = () => {\n    if ((!allSlides || allSlides.length === 0) && totalSlideCount > 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-8 p-4 md:p-6 flex flex-col items-center\",\n        children: renderSlidePlaceholders()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this);\n    }\n    if (!allSlides || allSlides.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center h-full text-gray-400 p-8 min-h-[400px]\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-eye text-6xl mb-6 text-gray-300\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-2xl font-medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 mt-2 text-center max-w-md\",\n          children: \"\\u5728\\u5DE6\\u4FA7\\u804A\\u5929\\u6846\\u8F93\\u5165\\u60A8\\u7684\\u9700\\u6C42\\uFF0CAI\\u5C06\\u5F00\\u59CB\\u4E3A\\u60A8\\u521B\\u5EFA\\u5E7B\\u706F\\u7247\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: slideListRef,\n      className: \"space-y-6 p-4 md:p-8 flex flex-col items-center\",\n      children: allSlides.map((slide, index) => {\n        if (!slide) {\n          console.warn(`渲染时发现索引 ${index} 处的幻灯片数据为 undefined，已跳过。`);\n          return null; // 返回 null，不渲染任何内容，防止崩溃\n        }\n        const slideId = slide.id || `slide-${index}`;\n        const slideKey = slideId;\n        const isFocused = index === currentFocusedSlideIndex;\n        const currentTabIndex = slideTabIndices[index] || 0;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          id: `slide-item-wrapper-${slideId}`,\n          className: `slide-item-wrapper w-full flex flex-col justify-center items-center \n                         ${isFocused ? 'focused-slide-item' : ''}`,\n          style: {\n            minHeight: '300px',\n            padding: '1rem 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"slide-tabs w-full bg-gray-200 p-1 flex rounded-t-lg border border-gray-300 border-b-0\",\n            style: {\n              maxWidth: '1280px',\n              margin: '0 auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: `px-3 py-2 text-sm font-medium rounded-md mr-1 flex items-center\n                             ${currentTabIndex === 0 ? 'bg-white text-blue-600 shadow' : 'text-gray-700 hover:bg-gray-300'}`,\n              onClick: e => {\n                e.stopPropagation();\n                onSlideTabChange(index, 0);\n                handleSlideFocus(index);\n              },\n              children: [/*#__PURE__*/_jsxDEV(FaEye, {\n                className: \"mr-1\",\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), \"\\u9884\\u89C8\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `px-3 py-2 text-sm font-medium rounded-md flex items-center\n                             ${currentTabIndex === 1 ? 'bg-white text-blue-600 shadow' : 'text-gray-700 hover:bg-gray-300'}`,\n              onClick: e => {\n                e.stopPropagation();\n                onSlideTabChange(index, 1);\n                handleSlideFocus(index);\n              },\n              children: [/*#__PURE__*/_jsxDEV(FaCode, {\n                className: \"mr-1\",\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), \"\\u4EE3\\u7801\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-auto text-xs text-gray-500 self-center mr-2\",\n              children: [index + 1, \" / \", allSlides.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: previewContainerRefs.current[slideKey],\n            id: `slide-preview-${slideId}`,\n            className: `preview-slide-outer-container rounded-b-lg overflow-hidden shadow-xl border-2\n                           ${isFocused ? 'border-blue-500' : 'border-gray-300 hover:border-gray-400'}\n                           transition-all duration-150 bg-white`,\n            style: {\n              width: '100%',\n              maxWidth: '1280px',\n              margin: '0 auto',\n              position: 'relative',\n              overflow: 'hidden'\n            },\n            onClick: e => {\n              e.stopPropagation();\n              handleSlideFocus(index);\n            },\n            children: [currentTabIndex === 0 && /*#__PURE__*/_jsxDEV(SlideRenderer, {\n              slideId: slideId,\n              slideFullHtml: slide.html,\n              isAppEditingMode: isAppEditingMode && isFocused,\n              isThumbnail: false\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 19\n            }, this), currentTabIndex === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full h-full relative\",\n              style: {\n                overflowY: 'auto',\n                background: '#282c34'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"absolute top-2 right-2 bg-gray-800 bg-opacity-70 text-white px-3 py-1 rounded-md hover:bg-opacity-100 transition-opacity z-10 text-sm\",\n                onClick: e => {\n                  e.stopPropagation();\n                  handleCopySlideCode(slide);\n                },\n                title: \"\\u590D\\u5236\\u4EE3\\u7801\",\n                children: \"\\u590D\\u5236\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                className: \"p-4 text-sm text-gray-300 font-mono whitespace-pre-wrap\",\n                children: /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: slide.code || (slide.html ? slide.html.replace(/</g, '<').replace(/>/g, '>') : '/* 此幻灯片暂无代码 */')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 15\n          }, this)]\n        }, slideId, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this);\n  };\n  const titleText = presentationTitle || \"\";\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: rightPaneRef,\n    className: `right-pane flex flex-col bg-gray-100 border-l border-gray-200 h-full ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center bg-white border-b border-gray-200 h-16 px-4 sticky top-0 z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-medium text-gray-700 truncate max-w-[250px]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [allSlides && allSlides.length > 0 || totalSlideCount > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => onPlaySlideAtIndex(currentFocusedSlideIndex),\n            disabled: currentFocusedSlideIndex < 0 || allSlides && currentFocusedSlideIndex >= allSlides.length,\n            className: \"px-[0.825rem] py-[0.3rem] bg-blue-500 text-white rounded-md text-[0.825rem] flex items-center hover:bg-blue-600 disabled:opacity-50\",\n            children: [/*#__PURE__*/_jsxDEV(FaPlay, {\n              className: \"mr-1\",\n              size: 10\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this), \"\\u64AD\\u653E\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onToggleAppEditMode,\n            disabled: !allSlides || allSlides.length === 0,\n            className: `px-[0.825rem] py-[0.3rem] rounded-md text-[0.825rem] flex items-center ${isAppEditingMode ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'} ${!allSlides || allSlides.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(FaPencilAlt, {\n              className: \"mr-1\",\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this), isAppEditingMode ? '退出编辑模式' : '编辑幻灯片']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-400\",\n          children: \"\\u7B49\\u5F85\\u751F\\u6210\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onOpenViewAndExport,\n          className: \"px-[0.93rem] py-[0.3rem] bg-gray-100 text-gray-700 rounded-md text-[0.93rem] flex items-center hover:bg-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(FaArrowsAlt, {\n            className: \"mr-1\",\n            size: 10\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), \"\\u67E5\\u770B\\u548C\\u5BFC\\u51FA\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col h-full\",\n      style: {\n        height: \"calc(100% - 64px)\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto custom-scrollbar right-pane-content-area overscroll-contain\",\n        children: renderSlides()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this), allSlides.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0 pt-2 pb-2 px-2 sticky bottom-0 bg-gray-50 border-t border-gray-200 shadow-inner\",\n        style: {\n          height: '140px'\n        },\n        children: /*#__PURE__*/_jsxDEV(SlideThumbnails, {\n          slides: allSlides,\n          currentSlideIndex: currentFocusedSlideIndex,\n          onThumbnailClick: index => {\n            handleSlideFocus(index);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 384,\n    columnNumber: 5\n  }, this);\n};\n_s(RightPane, \"zSXAKfHXK3dXDH/k1RVhCl/K+bs=\");\n_c = RightPane;\nexport default RightPane;\nvar _c;\n$RefreshReg$(_c, \"RightPane\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SlideThumbnails", "FaPlay", "FaPencilAlt", "FaArrowsAlt", "FaSpinner", "FaCode", "FaEye", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RightPane", "allSlides", "currentFocusedSlideIndex", "onSetFocusedSlideIndex", "onPlaySlideAtIndex", "isAppEditingMode", "onToggleAppEditMode", "onTextEditStart", "presentationTitle", "onOpenViewAndExport", "slideCode", "activeStep", "totalSlideCount", "slideTabIndices", "onSlideTabChange", "className", "_s", "slideListRef", "rightPaneRef", "previewContainerRefs", "length", "current", "reduce", "acc", "slide", "index", "<PERSON><PERSON><PERSON>", "id", "createRef", "observers", "for<PERSON>ach", "_previewContainerRefs", "container", "wrapper", "parentElement", "updateSize", "availableWidth", "offsetWidth", "height", "style", "width", "resizeObserver", "ResizeObserver", "observe", "push", "observer", "element", "unobserve", "undefined", "console", "warn", "setTimeout", "slideId", "slideElement", "document", "getElementById", "querySelector", "containerRect", "getBoundingClientRect", "elementRect", "targetScrollTop", "offsetTop", "scrollTo", "top", "behavior", "scrollIntoView", "block", "handleSlideFocus", "styleTag", "createElement", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "handleCopySlideCode", "codeToCopy", "code", "html", "navigator", "clipboard", "writeText", "then", "catch", "err", "error", "renderSlidePlaceholders", "placeholders", "i", "isActive", "isCompleted", "max<PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "paddingTop", "margin", "fill", "stroke", "viewBox", "xmlns", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "size", "renderSlides", "ref", "map", "isFocused", "currentTabIndex", "minHeight", "padding", "onClick", "e", "stopPropagation", "overflow", "slideFullHtml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overflowY", "background", "title", "replace", "titleText", "disabled", "slides", "currentSlideIndex", "onThumbnailClick", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/RightPane.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n// import { useNavigate } from 'react-router-dom'; // 引入 useNavigate\nimport SlideRenderer from './SlideRenderer';\nimport SlideThumbnails from './SlideThumbnails';\nimport { FaPlay, FaPencilAlt, FaArrowsAlt, FaSpinner, FaCode, FaEye } from 'react-icons/fa';\n// FullScreenPlayer 不再由此组件直接管理，由 App.js 管理\n// import FullScreenPlayer from './FullScreenPlayer';\n// We will add edit controls later\n// import EditControls from './EditControls';\n\nconst RightPane = ({\n  allSlides, // Array of {id, html (full), code, thinking, title}\n  currentFocusedSlideIndex, // 由 App.js 传入，表示当前 NLP 编辑焦点的幻灯片\n  onSetFocusedSlideIndex, // 回调，当用户在 RightPane 中点击幻灯片时，更新 App.js 中的 currentFocusedSlideIndex\n  onPlaySlideAtIndex, // 回调，当用户点击某个幻灯片的播放按钮时，触发 App.js 中的全屏播放\n  isAppEditingMode, // App 级的编辑模式\n  onToggleAppEditMode, // 切换 App 级编辑模式的按钮\n  onTextEditStart, // 传递给 SlideRenderer -> iframe\n  presentationTitle, // 从 App.js 传入演示文稿标题\n  onOpenViewAndExport, // 从 App.js 传入查看导出函数\n  slideCode = \"\", // HTML代码\n  activeStep = \"preview\", // 当前活动步骤\n  totalSlideCount = 0, // 总幻灯片数量\n  slideTabIndices = {}, // 每个幻灯片的标签页索引\n  onSlideTabChange = () => {}, // 设置标签页索引的回调\n  className = \"\" // 添加className属性，默认为空字符串\n}) => {\n  const slideListRef = useRef(null); // 用于滚动聚焦\n  // const navigate = useNavigate(); // 初始化 useNavigate\n  const rightPaneRef = useRef(null);\n  \n  // 为每个 preview-slide-outer-container 创建 refs 数组或对象\n  const previewContainerRefs = useRef({});\n\n  // 初始化 refs\n  useEffect(() => {\n    if (allSlides && allSlides.length > 0) {\n      previewContainerRefs.current = allSlides.reduce((acc, slide, index) => {\n        if (!slide) return acc; // 添加防御性检查，跳过undefined的slide\n        const slideKey = slide.id || `slide-${index}`;\n        acc[slideKey] = acc[slideKey] || React.createRef();\n        return acc;\n      }, {});\n    }\n  }, [allSlides]);\n  \n  // useEffect 来动态设置每个 preview-slide-outer-container 的尺寸\n  useEffect(() => {\n    if (!allSlides || allSlides.length === 0) return;\n    \n    const observers = [];\n    \n    allSlides.forEach((slide, index) => {\n      if (!slide) return; // 添加防御性检查，跳过undefined的slide\n      \n      const slideKey = slide.id || `slide-${index}`;\n      const container = previewContainerRefs.current[slideKey]?.current;\n      if (!container) return;\n      \n      const wrapper = container.parentElement; // .slide-item-wrapper\n      \n      if (container && wrapper) {\n        const updateSize = () => {\n          const availableWidth = wrapper.offsetWidth; // 获取父容器的宽度\n          if (availableWidth > 0) {\n            const height = (availableWidth * 9) / 16;\n            container.style.width = `${availableWidth}px`;\n            container.style.height = `${height}px`;\n          }\n        };\n        \n        updateSize(); // 初始设置\n        const resizeObserver = new ResizeObserver(updateSize);\n        resizeObserver.observe(wrapper); // 观察父容器的尺寸变化\n        observers.push({ observer: resizeObserver, element: wrapper });\n      }\n    });\n    \n    return () => {\n      observers.forEach(({ observer, element }) => {\n        observer.unobserve(element);\n      });\n    };\n  }, [allSlides]);\n  \n  // 根据activeStep自动切换标签页\n  useEffect(() => {\n    if (activeStep === 'code') {\n      // 为当前聚焦的幻灯片设置代码标签，但仅当没有现有选择时\n      if (slideTabIndices[currentFocusedSlideIndex] === undefined) {\n        onSlideTabChange(currentFocusedSlideIndex, 1);\n      }\n    } else if (activeStep === 'preview') {\n      // 默认为预览标签，但仅当没有现有选择时\n      if (slideTabIndices[currentFocusedSlideIndex] === undefined) {\n        onSlideTabChange(currentFocusedSlideIndex, 0);\n      }\n    }\n  }, [activeStep, currentFocusedSlideIndex, onSlideTabChange, slideTabIndices]);\n  \n  // 当 currentFocusedSlideIndex (来自App.js) 改变时，滚动到对应的幻灯片\n  useEffect(() => {\n    // +++++++++++++++ BUG修复：添加防御性检查 +++++++++++++++\n    if (!allSlides || allSlides.length === 0 || currentFocusedSlideIndex < 0 || currentFocusedSlideIndex >= allSlides.length) {\n      return; // 如果幻灯片数组为空或索引无效，则不执行任何操作\n    }\n    \n    // 检查当前幻灯片是否存在\n    if (!allSlides[currentFocusedSlideIndex]) {\n      console.warn(`无法滚动到索引 ${currentFocusedSlideIndex} 处的幻灯片，因为它是undefined`);\n      return;\n    }\n    // +++++++++++++++++++++++++++++++++++++++++++++++++++++\n\n    // 使用setTimeout确保DOM更新后再滚动\n    setTimeout(() => {\n      // 这里的代码现在是安全的，因为我们已经检查过 allSlides[currentFocusedSlideIndex] 的有效性\n      const slideId = allSlides[currentFocusedSlideIndex].id || `slide-${currentFocusedSlideIndex}`;\n      const slideElement = document.getElementById(`slide-item-wrapper-${slideId}`);\n      if (slideElement && slideListRef.current) {\n        const container = document.querySelector('.right-pane-content-area');\n        if (container) {\n          const containerRect = container.getBoundingClientRect();\n          const elementRect = slideElement.getBoundingClientRect();\n          const targetScrollTop = slideElement.offsetTop - container.offsetTop - (containerRect.height - elementRect.height) / 2;\n          container.scrollTo({\n            top: targetScrollTop,\n            behavior: 'auto'\n          });\n        } else {\n          slideElement.scrollIntoView({ \n            behavior: 'auto', \n            block: 'center'\n          });\n        }\n      }\n    }, 50);\n  }, [currentFocusedSlideIndex, allSlides]);\n\n  // 点击幻灯片预览区域时，设置其为当前App的焦点幻灯片\n  const handleSlideFocus = (index) => {\n    if (index < 0 || index >= allSlides.length) {\n      console.warn(`Invalid slide index: ${index}`);\n      return;\n    }\n    \n    // 检查当前幻灯片是否存在\n    if (!allSlides[index]) {\n      console.warn(`无法聚焦索引 ${index} 处的幻灯片，因为它是undefined`);\n      return;\n    }\n    \n    if (onSetFocusedSlideIndex) {\n      onSetFocusedSlideIndex(index);\n    }\n  };\n\n  // 添加全局样式，确保iframe内的所有元素在预览模式下都显示为可点击状态\n  useEffect(() => {\n    const styleTag = document.createElement('style');\n    styleTag.innerHTML = `\n      .slide-preview-container {\n        cursor: pointer !important;\n      }\n      .slide-preview-container * {\n        cursor: pointer !important;\n      }\n    `;\n    document.head.appendChild(styleTag);\n    return () => {\n      document.head.removeChild(styleTag);\n    };\n  }, []);\n\n  // 复制幻灯片代码到剪贴板\n  const handleCopySlideCode = (slide) => {\n    if (!slide) {\n      console.warn('尝试复制undefined幻灯片的代码');\n      return;\n    }\n    \n    const codeToCopy = slide.code || (slide.html ? slide.html : '/* 此幻灯片暂无代码 */');\n    navigator.clipboard.writeText(codeToCopy)\n      .then(() => {})\n      .catch(err => {\n        console.error('复制失败:', err);\n      });\n  };\n\n  // 渲染幻灯片占位符\n  const renderSlidePlaceholders = () => {\n    const placeholders = [];\n    for (let i = 0; i < totalSlideCount; i++) {\n      const isActive = allSlides.length === i;\n      const isCompleted = allSlides.length > i;\n      \n      placeholders.push(\n        <div\n          key={`placeholder-${i}`}\n          className={`w-full mx-auto bg-white shadow-xl rounded-lg overflow-hidden border-2 ${\n            isActive ? 'border-blue-400 animate-pulse' : isCompleted ? 'border-green-200' : 'border-gray-200'\n          } cursor-wait mb-8`}\n          style={{ maxWidth: '1280px' }}\n        >\n          <div className=\"flex justify-between items-center bg-white border-b border-gray-200\">\n            <div className={`px-4 py-3 text-sm font-medium ${isActive ? 'text-blue-500' : isCompleted ? 'text-green-500' : 'text-gray-400'}`}>\n              {isCompleted ? '✓ 已完成' : isActive ? '⟳ 生成中...' : '预览'}\n            </div>\n          </div>\n          <div \n            className=\"w-full mx-auto bg-gray-100 relative\" \n            style={{\n              position: 'relative',\n              paddingTop: '56.25%',\n              margin: '1rem 0',\n            }}\n          >\n            <div className=\"absolute top-0 left-0 w-full h-full flex items-center justify-center\">\n              {isCompleted ? (\n                <div className=\"flex flex-col items-center justify-center text-green-500\">\n                  <svg className=\"w-12 h-12\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  <p className=\"text-sm mt-2\">已生成</p>\n                </div>\n              ) : isActive ? (\n                <div className=\"flex flex-col items-center justify-center space-y-4\">\n                  <FaSpinner className=\"animate-spin text-blue-500\" size={32} />\n                  <p className=\"text-blue-500 text-sm\">正在生成幻灯片...</p>\n                </div>\n              ) : (\n                <div className=\"flex flex-col items-center justify-center space-y-4\">\n                  <FaSpinner className=\"animate-spin text-gray-400\" size={32} />\n                  <p className=\"text-gray-500 text-sm\">等待生成...</p>\n                </div>\n              )}\n            </div>\n          </div>\n          <div className=\"flex justify-between items-center bg-gray-50 px-4 py-2 border-t\">\n            <div className=\"text-sm text-gray-500\">\n              {i === 0 ? \"标题页\" : `幻灯片 ${i + 1}`}\n            </div>\n            <div className=\"text-xs text-gray-400\">\n              {i + 1} / {totalSlideCount}\n            </div>\n          </div>\n        </div>\n      );\n    }\n    return placeholders;\n  };\n\n  const renderSlides = () => {\n    if ((!allSlides || allSlides.length === 0) && totalSlideCount > 0) {\n      return (\n        <div className=\"space-y-8 p-4 md:p-6 flex flex-col items-center\">\n          {renderSlidePlaceholders()}\n        </div>\n      );\n    }\n    \n    if (!allSlides || allSlides.length === 0) {\n      return (\n        <div className=\"flex flex-col items-center justify-center h-full text-gray-400 p-8 min-h-[400px]\">\n          <i className=\"fas fa-eye text-6xl mb-6 text-gray-300\"></i>\n          <span className=\"text-2xl font-medium\"></span>\n          <p className=\"text-sm text-gray-400 mt-2 text-center max-w-md\">\n            在左侧聊天框输入您的需求，AI将开始为您创建幻灯片。\n          </p>\n        </div>\n      );\n    }\n    \n    return (\n      <div ref={slideListRef} className=\"space-y-6 p-4 md:p-8 flex flex-col items-center\"> \n        {allSlides.map((slide, index) => {\n          if (!slide) {\n            console.warn(`渲染时发现索引 ${index} 处的幻灯片数据为 undefined，已跳过。`);\n            return null; // 返回 null，不渲染任何内容，防止崩溃\n          }\n\n          const slideId = slide.id || `slide-${index}`;\n          const slideKey = slideId;\n          const isFocused = index === currentFocusedSlideIndex;\n          const currentTabIndex = slideTabIndices[index] || 0;\n          \n          return (\n            <div\n              key={slideId}\n              id={`slide-item-wrapper-${slideId}`}\n              className={`slide-item-wrapper w-full flex flex-col justify-center items-center \n                         ${isFocused ? 'focused-slide-item' : ''}`}\n              style={{ \n                minHeight: '300px',\n                padding: '1rem 0',\n              }}\n            >\n              <div className=\"slide-tabs w-full bg-gray-200 p-1 flex rounded-t-lg border border-gray-300 border-b-0\" \n                   style={{ maxWidth: '1280px', margin: '0 auto' }}>\n                <button\n                  className={`px-3 py-2 text-sm font-medium rounded-md mr-1 flex items-center\n                             ${currentTabIndex === 0 ? 'bg-white text-blue-600 shadow' : 'text-gray-700 hover:bg-gray-300'}`}\n                  onClick={(e) => { \n                    e.stopPropagation(); \n                    onSlideTabChange(index, 0); \n                    handleSlideFocus(index); \n                  }}\n                >\n                  <FaEye className=\"mr-1\" size={14} />\n                  预览\n                </button>\n                <button\n                  className={`px-3 py-2 text-sm font-medium rounded-md flex items-center\n                             ${currentTabIndex === 1 ? 'bg-white text-blue-600 shadow' : 'text-gray-700 hover:bg-gray-300'}`}\n                  onClick={(e) => { \n                    e.stopPropagation(); \n                    onSlideTabChange(index, 1); \n                    handleSlideFocus(index); \n                  }}\n                >\n                  <FaCode className=\"mr-1\" size={14} />\n                  代码\n                </button>\n                <div className=\"ml-auto text-xs text-gray-500 self-center mr-2\">\n                  {index + 1} / {allSlides.length}\n                </div>\n              </div>\n\n              <div\n              ref={previewContainerRefs.current[slideKey]}\n              id={`slide-preview-${slideId}`}\n              className={`preview-slide-outer-container rounded-b-lg overflow-hidden shadow-xl border-2\n                           ${isFocused ? 'border-blue-500' : 'border-gray-300 hover:border-gray-400'}\n                           transition-all duration-150 bg-white`}\n              style={{\n                  width: '100%',\n                  maxWidth: '1280px',\n                  margin: '0 auto',\n                  position: 'relative',\n                  overflow: 'hidden'\n                }}\n              onClick={(e) => { e.stopPropagation(); handleSlideFocus(index); }}\n              >\n                {currentTabIndex === 0 && (\n                  <SlideRenderer\n                    slideId={slideId}\n                    slideFullHtml={slide.html}\n                    isAppEditingMode={isAppEditingMode && isFocused}\n                    isThumbnail={false}\n                  />\n                )}\n                {currentTabIndex === 1 && (\n                  <div className=\"w-full h-full relative\" style={{ overflowY: 'auto', background: '#282c34' }}>\n                    <button\n                      className=\"absolute top-2 right-2 bg-gray-800 bg-opacity-70 text-white px-3 py-1 rounded-md hover:bg-opacity-100 transition-opacity z-10 text-sm\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleCopySlideCode(slide);\n                      }}\n                      title=\"复制代码\"\n                    >\n                      复制\n                    </button>\n                    <pre className=\"p-4 text-sm text-gray-300 font-mono whitespace-pre-wrap\">\n                      <code>\n                        {slide.code || (slide.html\n                          ? slide.html.replace(/</g, '<').replace(/>/g, '>')\n                          : '/* 此幻灯片暂无代码 */')}\n                      </code>\n                    </pre>\n                  </div>\n                )}\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  const titleText = presentationTitle || \"\";\n\n  return (\n    <div ref={rightPaneRef} className={`right-pane flex flex-col bg-gray-100 border-l border-gray-200 h-full ${className}`}>\n      <div className=\"flex justify-between items-center bg-white border-b border-gray-200 h-16 px-4 sticky top-0 z-10\">\n        <div className=\"font-medium text-gray-700 truncate max-w-[250px]\">\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          {(allSlides && allSlides.length > 0) || (totalSlideCount > 0) ? (\n            <>\n              <button\n                onClick={() => onPlaySlideAtIndex(currentFocusedSlideIndex)}\n                disabled={currentFocusedSlideIndex < 0 || (allSlides && currentFocusedSlideIndex >= allSlides.length)}\n                className=\"px-[0.825rem] py-[0.3rem] bg-blue-500 text-white rounded-md text-[0.825rem] flex items-center hover:bg-blue-600 disabled:opacity-50\"\n              >\n                <FaPlay className=\"mr-1\" size={10} />\n                播放\n              </button>\n              <button\n                onClick={onToggleAppEditMode}\n                disabled={!allSlides || allSlides.length === 0}\n                className={`px-[0.825rem] py-[0.3rem] rounded-md text-[0.825rem] flex items-center ${\n                  isAppEditingMode\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                  } ${(!allSlides || allSlides.length === 0) ? 'opacity-50 cursor-not-allowed' : ''}`}\n              >\n                <FaPencilAlt className=\"mr-1\" size={12} />\n                {isAppEditingMode ? '退出编辑模式' : '编辑幻灯片'}\n              </button>\n            </>\n          ) : (\n            <div className=\"text-sm text-gray-400\">等待生成中...</div>\n          )}\n          <button\n            onClick={onOpenViewAndExport}\n            className=\"px-[0.93rem] py-[0.3rem] bg-gray-100 text-gray-700 rounded-md text-[0.93rem] flex items-center hover:bg-gray-200\"\n          >\n            <FaArrowsAlt className=\"mr-1\" size={10} />\n            查看和导出\n          </button>\n        </div>\n      </div>\n      \n      <div className=\"flex flex-col h-full\" style={{ height: \"calc(100% - 64px)\" }}>\n        <div className=\"flex-1 overflow-y-auto custom-scrollbar right-pane-content-area overscroll-contain\">\n          {renderSlides()}\n        </div>\n        \n        {allSlides.length > 0 && (\n          <div className=\"flex-shrink-0 pt-2 pb-2 px-2 sticky bottom-0 bg-gray-50 border-t border-gray-200 shadow-inner\" style={{ height: '140px' }}>\n            <SlideThumbnails\n              slides={allSlides}\n              currentSlideIndex={currentFocusedSlideIndex}\n              onThumbnailClick={(index) => {\n                handleSlideFocus(index);\n              }}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default RightPane;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD;AACA,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,gBAAgB;AAC3F;AACA;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,SAAS,GAAGA,CAAC;EACjBC,SAAS;EAAE;EACXC,wBAAwB;EAAE;EAC1BC,sBAAsB;EAAE;EACxBC,kBAAkB;EAAE;EACpBC,gBAAgB;EAAE;EAClBC,mBAAmB;EAAE;EACrBC,eAAe;EAAE;EACjBC,iBAAiB;EAAE;EACnBC,mBAAmB;EAAE;EACrBC,SAAS,GAAG,EAAE;EAAE;EAChBC,UAAU,GAAG,SAAS;EAAE;EACxBC,eAAe,GAAG,CAAC;EAAE;EACrBC,eAAe,GAAG,CAAC,CAAC;EAAE;EACtBC,gBAAgB,GAAGA,CAAA,KAAM,CAAC,CAAC;EAAE;EAC7BC,SAAS,GAAG,EAAE,CAAC;AACjB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,YAAY,GAAG9B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EACnC;EACA,MAAM+B,YAAY,GAAG/B,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACA,MAAMgC,oBAAoB,GAAGhC,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEvC;EACAD,SAAS,CAAC,MAAM;IACd,IAAIe,SAAS,IAAIA,SAAS,CAACmB,MAAM,GAAG,CAAC,EAAE;MACrCD,oBAAoB,CAACE,OAAO,GAAGpB,SAAS,CAACqB,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,EAAEC,KAAK,KAAK;QACrE,IAAI,CAACD,KAAK,EAAE,OAAOD,GAAG,CAAC,CAAC;QACxB,MAAMG,QAAQ,GAAGF,KAAK,CAACG,EAAE,IAAI,SAASF,KAAK,EAAE;QAC7CF,GAAG,CAACG,QAAQ,CAAC,GAAGH,GAAG,CAACG,QAAQ,CAAC,iBAAIzC,KAAK,CAAC2C,SAAS,CAAC,CAAC;QAClD,OAAOL,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACR;EACF,CAAC,EAAE,CAACtB,SAAS,CAAC,CAAC;;EAEf;EACAf,SAAS,CAAC,MAAM;IACd,IAAI,CAACe,SAAS,IAAIA,SAAS,CAACmB,MAAM,KAAK,CAAC,EAAE;IAE1C,MAAMS,SAAS,GAAG,EAAE;IAEpB5B,SAAS,CAAC6B,OAAO,CAAC,CAACN,KAAK,EAAEC,KAAK,KAAK;MAAA,IAAAM,qBAAA;MAClC,IAAI,CAACP,KAAK,EAAE,OAAO,CAAC;;MAEpB,MAAME,QAAQ,GAAGF,KAAK,CAACG,EAAE,IAAI,SAASF,KAAK,EAAE;MAC7C,MAAMO,SAAS,IAAAD,qBAAA,GAAGZ,oBAAoB,CAACE,OAAO,CAACK,QAAQ,CAAC,cAAAK,qBAAA,uBAAtCA,qBAAA,CAAwCV,OAAO;MACjE,IAAI,CAACW,SAAS,EAAE;MAEhB,MAAMC,OAAO,GAAGD,SAAS,CAACE,aAAa,CAAC,CAAC;;MAEzC,IAAIF,SAAS,IAAIC,OAAO,EAAE;QACxB,MAAME,UAAU,GAAGA,CAAA,KAAM;UACvB,MAAMC,cAAc,GAAGH,OAAO,CAACI,WAAW,CAAC,CAAC;UAC5C,IAAID,cAAc,GAAG,CAAC,EAAE;YACtB,MAAME,MAAM,GAAIF,cAAc,GAAG,CAAC,GAAI,EAAE;YACxCJ,SAAS,CAACO,KAAK,CAACC,KAAK,GAAG,GAAGJ,cAAc,IAAI;YAC7CJ,SAAS,CAACO,KAAK,CAACD,MAAM,GAAG,GAAGA,MAAM,IAAI;UACxC;QACF,CAAC;QAEDH,UAAU,CAAC,CAAC,CAAC,CAAC;QACd,MAAMM,cAAc,GAAG,IAAIC,cAAc,CAACP,UAAU,CAAC;QACrDM,cAAc,CAACE,OAAO,CAACV,OAAO,CAAC,CAAC,CAAC;QACjCJ,SAAS,CAACe,IAAI,CAAC;UAAEC,QAAQ,EAAEJ,cAAc;UAAEK,OAAO,EAAEb;QAAQ,CAAC,CAAC;MAChE;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACXJ,SAAS,CAACC,OAAO,CAAC,CAAC;QAAEe,QAAQ;QAAEC;MAAQ,CAAC,KAAK;QAC3CD,QAAQ,CAACE,SAAS,CAACD,OAAO,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAAC7C,SAAS,CAAC,CAAC;;EAEf;EACAf,SAAS,CAAC,MAAM;IACd,IAAIyB,UAAU,KAAK,MAAM,EAAE;MACzB;MACA,IAAIE,eAAe,CAACX,wBAAwB,CAAC,KAAK8C,SAAS,EAAE;QAC3DlC,gBAAgB,CAACZ,wBAAwB,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC,MAAM,IAAIS,UAAU,KAAK,SAAS,EAAE;MACnC;MACA,IAAIE,eAAe,CAACX,wBAAwB,CAAC,KAAK8C,SAAS,EAAE;QAC3DlC,gBAAgB,CAACZ,wBAAwB,EAAE,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,EAAE,CAACS,UAAU,EAAET,wBAAwB,EAAEY,gBAAgB,EAAED,eAAe,CAAC,CAAC;;EAE7E;EACA3B,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACe,SAAS,IAAIA,SAAS,CAACmB,MAAM,KAAK,CAAC,IAAIlB,wBAAwB,GAAG,CAAC,IAAIA,wBAAwB,IAAID,SAAS,CAACmB,MAAM,EAAE;MACxH,OAAO,CAAC;IACV;;IAEA;IACA,IAAI,CAACnB,SAAS,CAACC,wBAAwB,CAAC,EAAE;MACxC+C,OAAO,CAACC,IAAI,CAAC,WAAWhD,wBAAwB,sBAAsB,CAAC;MACvE;IACF;IACA;;IAEA;IACAiD,UAAU,CAAC,MAAM;MACf;MACA,MAAMC,OAAO,GAAGnD,SAAS,CAACC,wBAAwB,CAAC,CAACyB,EAAE,IAAI,SAASzB,wBAAwB,EAAE;MAC7F,MAAMmD,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,sBAAsBH,OAAO,EAAE,CAAC;MAC7E,IAAIC,YAAY,IAAIpC,YAAY,CAACI,OAAO,EAAE;QACxC,MAAMW,SAAS,GAAGsB,QAAQ,CAACE,aAAa,CAAC,0BAA0B,CAAC;QACpE,IAAIxB,SAAS,EAAE;UACb,MAAMyB,aAAa,GAAGzB,SAAS,CAAC0B,qBAAqB,CAAC,CAAC;UACvD,MAAMC,WAAW,GAAGN,YAAY,CAACK,qBAAqB,CAAC,CAAC;UACxD,MAAME,eAAe,GAAGP,YAAY,CAACQ,SAAS,GAAG7B,SAAS,CAAC6B,SAAS,GAAG,CAACJ,aAAa,CAACnB,MAAM,GAAGqB,WAAW,CAACrB,MAAM,IAAI,CAAC;UACtHN,SAAS,CAAC8B,QAAQ,CAAC;YACjBC,GAAG,EAAEH,eAAe;YACpBI,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLX,YAAY,CAACY,cAAc,CAAC;YAC1BD,QAAQ,EAAE,MAAM;YAChBE,KAAK,EAAE;UACT,CAAC,CAAC;QACJ;MACF;IACF,CAAC,EAAE,EAAE,CAAC;EACR,CAAC,EAAE,CAAChE,wBAAwB,EAAED,SAAS,CAAC,CAAC;;EAEzC;EACA,MAAMkE,gBAAgB,GAAI1C,KAAK,IAAK;IAClC,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAIxB,SAAS,CAACmB,MAAM,EAAE;MAC1C6B,OAAO,CAACC,IAAI,CAAC,wBAAwBzB,KAAK,EAAE,CAAC;MAC7C;IACF;;IAEA;IACA,IAAI,CAACxB,SAAS,CAACwB,KAAK,CAAC,EAAE;MACrBwB,OAAO,CAACC,IAAI,CAAC,UAAUzB,KAAK,sBAAsB,CAAC;MACnD;IACF;IAEA,IAAItB,sBAAsB,EAAE;MAC1BA,sBAAsB,CAACsB,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACAvC,SAAS,CAAC,MAAM;IACd,MAAMkF,QAAQ,GAAGd,QAAQ,CAACe,aAAa,CAAC,OAAO,CAAC;IAChDD,QAAQ,CAACE,SAAS,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IACDhB,QAAQ,CAACiB,IAAI,CAACC,WAAW,CAACJ,QAAQ,CAAC;IACnC,OAAO,MAAM;MACXd,QAAQ,CAACiB,IAAI,CAACE,WAAW,CAACL,QAAQ,CAAC;IACrC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,mBAAmB,GAAIlD,KAAK,IAAK;IACrC,IAAI,CAACA,KAAK,EAAE;MACVyB,OAAO,CAACC,IAAI,CAAC,qBAAqB,CAAC;MACnC;IACF;IAEA,MAAMyB,UAAU,GAAGnD,KAAK,CAACoD,IAAI,KAAKpD,KAAK,CAACqD,IAAI,GAAGrD,KAAK,CAACqD,IAAI,GAAG,gBAAgB,CAAC;IAC7EC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACL,UAAU,CAAC,CACtCM,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CACdC,KAAK,CAACC,GAAG,IAAI;MACZlC,OAAO,CAACmC,KAAK,CAAC,OAAO,EAAED,GAAG,CAAC;IAC7B,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,YAAY,GAAG,EAAE;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3E,eAAe,EAAE2E,CAAC,EAAE,EAAE;MACxC,MAAMC,QAAQ,GAAGvF,SAAS,CAACmB,MAAM,KAAKmE,CAAC;MACvC,MAAME,WAAW,GAAGxF,SAAS,CAACmB,MAAM,GAAGmE,CAAC;MAExCD,YAAY,CAAC1C,IAAI,cACf/C,OAAA;QAEEkB,SAAS,EAAE,yEACTyE,QAAQ,GAAG,+BAA+B,GAAGC,WAAW,GAAG,kBAAkB,GAAG,iBAAiB,mBAC/E;QACpBlD,KAAK,EAAE;UAAEmD,QAAQ,EAAE;QAAS,CAAE;QAAAC,QAAA,gBAE9B9F,OAAA;UAAKkB,SAAS,EAAC,qEAAqE;UAAA4E,QAAA,eAClF9F,OAAA;YAAKkB,SAAS,EAAE,iCAAiCyE,QAAQ,GAAG,eAAe,GAAGC,WAAW,GAAG,gBAAgB,GAAG,eAAe,EAAG;YAAAE,QAAA,EAC9HF,WAAW,GAAG,OAAO,GAAGD,QAAQ,GAAG,UAAU,GAAG;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlG,OAAA;UACEkB,SAAS,EAAC,qCAAqC;UAC/CwB,KAAK,EAAE;YACLyD,QAAQ,EAAE,UAAU;YACpBC,UAAU,EAAE,QAAQ;YACpBC,MAAM,EAAE;UACV,CAAE;UAAAP,QAAA,eAEF9F,OAAA;YAAKkB,SAAS,EAAC,sEAAsE;YAAA4E,QAAA,EAClFF,WAAW,gBACV5F,OAAA;cAAKkB,SAAS,EAAC,0DAA0D;cAAA4E,QAAA,gBACvE9F,OAAA;gBAAKkB,SAAS,EAAC,WAAW;gBAACoF,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAACC,KAAK,EAAC,4BAA4B;gBAAAX,QAAA,eACjH9F,OAAA;kBAAM0G,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAgB;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC,eACNlG,OAAA;gBAAGkB,SAAS,EAAC,cAAc;gBAAA4E,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,GACJP,QAAQ,gBACV3F,OAAA;cAAKkB,SAAS,EAAC,qDAAqD;cAAA4E,QAAA,gBAClE9F,OAAA,CAACJ,SAAS;gBAACsB,SAAS,EAAC,4BAA4B;gBAAC4F,IAAI,EAAE;cAAG;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DlG,OAAA;gBAAGkB,SAAS,EAAC,uBAAuB;gBAAA4E,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,gBAENlG,OAAA;cAAKkB,SAAS,EAAC,qDAAqD;cAAA4E,QAAA,gBAClE9F,OAAA,CAACJ,SAAS;gBAACsB,SAAS,EAAC,4BAA4B;gBAAC4F,IAAI,EAAE;cAAG;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DlG,OAAA;gBAAGkB,SAAS,EAAC,uBAAuB;gBAAA4E,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlG,OAAA;UAAKkB,SAAS,EAAC,iEAAiE;UAAA4E,QAAA,gBAC9E9F,OAAA;YAAKkB,SAAS,EAAC,uBAAuB;YAAA4E,QAAA,EACnCJ,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,OAAOA,CAAC,GAAG,CAAC;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACNlG,OAAA;YAAKkB,SAAS,EAAC,uBAAuB;YAAA4E,QAAA,GACnCJ,CAAC,GAAG,CAAC,EAAC,KAAG,EAAC3E,eAAe;UAAA;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GA/CD,eAAeR,CAAC,EAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgDpB,CACP,CAAC;IACH;IACA,OAAOT,YAAY;EACrB,CAAC;EAED,MAAMsB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAC,CAAC3G,SAAS,IAAIA,SAAS,CAACmB,MAAM,KAAK,CAAC,KAAKR,eAAe,GAAG,CAAC,EAAE;MACjE,oBACEf,OAAA;QAAKkB,SAAS,EAAC,iDAAiD;QAAA4E,QAAA,EAC7DN,uBAAuB,CAAC;MAAC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAEV;IAEA,IAAI,CAAC9F,SAAS,IAAIA,SAAS,CAACmB,MAAM,KAAK,CAAC,EAAE;MACxC,oBACEvB,OAAA;QAAKkB,SAAS,EAAC,kFAAkF;QAAA4E,QAAA,gBAC/F9F,OAAA;UAAGkB,SAAS,EAAC;QAAwC;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DlG,OAAA;UAAMkB,SAAS,EAAC;QAAsB;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9ClG,OAAA;UAAGkB,SAAS,EAAC,iDAAiD;UAAA4E,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAEV;IAEA,oBACElG,OAAA;MAAKgH,GAAG,EAAE5F,YAAa;MAACF,SAAS,EAAC,iDAAiD;MAAA4E,QAAA,EAChF1F,SAAS,CAAC6G,GAAG,CAAC,CAACtF,KAAK,EAAEC,KAAK,KAAK;QAC/B,IAAI,CAACD,KAAK,EAAE;UACVyB,OAAO,CAACC,IAAI,CAAC,WAAWzB,KAAK,0BAA0B,CAAC;UACxD,OAAO,IAAI,CAAC,CAAC;QACf;QAEA,MAAM2B,OAAO,GAAG5B,KAAK,CAACG,EAAE,IAAI,SAASF,KAAK,EAAE;QAC5C,MAAMC,QAAQ,GAAG0B,OAAO;QACxB,MAAM2D,SAAS,GAAGtF,KAAK,KAAKvB,wBAAwB;QACpD,MAAM8G,eAAe,GAAGnG,eAAe,CAACY,KAAK,CAAC,IAAI,CAAC;QAEnD,oBACE5B,OAAA;UAEE8B,EAAE,EAAE,sBAAsByB,OAAO,EAAG;UACpCrC,SAAS,EAAE;AACzB,2BAA2BgG,SAAS,GAAG,oBAAoB,GAAG,EAAE,EAAG;UACrDxE,KAAK,EAAE;YACL0E,SAAS,EAAE,OAAO;YAClBC,OAAO,EAAE;UACX,CAAE;UAAAvB,QAAA,gBAEF9F,OAAA;YAAKkB,SAAS,EAAC,uFAAuF;YACjGwB,KAAK,EAAE;cAAEmD,QAAQ,EAAE,QAAQ;cAAEQ,MAAM,EAAE;YAAS,CAAE;YAAAP,QAAA,gBACnD9F,OAAA;cACEkB,SAAS,EAAE;AAC7B,+BAA+BiG,eAAe,KAAK,CAAC,GAAG,+BAA+B,GAAG,iCAAiC,EAAG;cAC3GG,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBvG,gBAAgB,CAACW,KAAK,EAAE,CAAC,CAAC;gBAC1B0C,gBAAgB,CAAC1C,KAAK,CAAC;cACzB,CAAE;cAAAkE,QAAA,gBAEF9F,OAAA,CAACF,KAAK;gBAACoB,SAAS,EAAC,MAAM;gBAAC4F,IAAI,EAAE;cAAG;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlG,OAAA;cACEkB,SAAS,EAAE;AAC7B,+BAA+BiG,eAAe,KAAK,CAAC,GAAG,+BAA+B,GAAG,iCAAiC,EAAG;cAC3GG,OAAO,EAAGC,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBvG,gBAAgB,CAACW,KAAK,EAAE,CAAC,CAAC;gBAC1B0C,gBAAgB,CAAC1C,KAAK,CAAC;cACzB,CAAE;cAAAkE,QAAA,gBAEF9F,OAAA,CAACH,MAAM;gBAACqB,SAAS,EAAC,MAAM;gBAAC4F,IAAI,EAAE;cAAG;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlG,OAAA;cAAKkB,SAAS,EAAC,gDAAgD;cAAA4E,QAAA,GAC5DlE,KAAK,GAAG,CAAC,EAAC,KAAG,EAACxB,SAAS,CAACmB,MAAM;YAAA;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlG,OAAA;YACAgH,GAAG,EAAE1F,oBAAoB,CAACE,OAAO,CAACK,QAAQ,CAAE;YAC5CC,EAAE,EAAE,iBAAiByB,OAAO,EAAG;YAC/BrC,SAAS,EAAE;AACzB,6BAA6BgG,SAAS,GAAG,iBAAiB,GAAG,uCAAuC;AACpG,gEAAiE;YACnDxE,KAAK,EAAE;cACHC,KAAK,EAAE,MAAM;cACbkD,QAAQ,EAAE,QAAQ;cAClBQ,MAAM,EAAE,QAAQ;cAChBF,QAAQ,EAAE,UAAU;cACpBsB,QAAQ,EAAE;YACZ,CAAE;YACJH,OAAO,EAAGC,CAAC,IAAK;cAAEA,CAAC,CAACC,eAAe,CAAC,CAAC;cAAElD,gBAAgB,CAAC1C,KAAK,CAAC;YAAE,CAAE;YAAAkE,QAAA,GAE/DqB,eAAe,KAAK,CAAC,iBACpBnH,OAAA,CAACT,aAAa;cACZgE,OAAO,EAAEA,OAAQ;cACjBmE,aAAa,EAAE/F,KAAK,CAACqD,IAAK;cAC1BxE,gBAAgB,EAAEA,gBAAgB,IAAI0G,SAAU;cAChDS,WAAW,EAAE;YAAM;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CACF,EACAiB,eAAe,KAAK,CAAC,iBACpBnH,OAAA;cAAKkB,SAAS,EAAC,wBAAwB;cAACwB,KAAK,EAAE;gBAAEkF,SAAS,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAU,CAAE;cAAA/B,QAAA,gBAC1F9F,OAAA;gBACEkB,SAAS,EAAC,uIAAuI;gBACjJoG,OAAO,EAAGC,CAAC,IAAK;kBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;kBACnB3C,mBAAmB,CAAClD,KAAK,CAAC;gBAC5B,CAAE;gBACFmG,KAAK,EAAC,0BAAM;gBAAAhC,QAAA,EACb;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlG,OAAA;gBAAKkB,SAAS,EAAC,yDAAyD;gBAAA4E,QAAA,eACtE9F,OAAA;kBAAA8F,QAAA,EACGnE,KAAK,CAACoD,IAAI,KAAKpD,KAAK,CAACqD,IAAI,GACtBrD,KAAK,CAACqD,IAAI,CAAC+C,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAChD,gBAAgB;gBAAC;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GApFD3C,OAAO;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqFT,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,MAAM8B,SAAS,GAAGrH,iBAAiB,IAAI,EAAE;EAEzC,oBACEX,OAAA;IAAKgH,GAAG,EAAE3F,YAAa;IAACH,SAAS,EAAE,wEAAwEA,SAAS,EAAG;IAAA4E,QAAA,gBACrH9F,OAAA;MAAKkB,SAAS,EAAC,iGAAiG;MAAA4E,QAAA,gBAC9G9F,OAAA;QAAKkB,SAAS,EAAC;MAAkD;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNlG,OAAA;QAAKkB,SAAS,EAAC,6BAA6B;QAAA4E,QAAA,GACxC1F,SAAS,IAAIA,SAAS,CAACmB,MAAM,GAAG,CAAC,IAAMR,eAAe,GAAG,CAAE,gBAC3Df,OAAA,CAAAE,SAAA;UAAA4F,QAAA,gBACE9F,OAAA;YACEsH,OAAO,EAAEA,CAAA,KAAM/G,kBAAkB,CAACF,wBAAwB,CAAE;YAC5D4H,QAAQ,EAAE5H,wBAAwB,GAAG,CAAC,IAAKD,SAAS,IAAIC,wBAAwB,IAAID,SAAS,CAACmB,MAAQ;YACtGL,SAAS,EAAC,qIAAqI;YAAA4E,QAAA,gBAE/I9F,OAAA,CAACP,MAAM;cAACyB,SAAS,EAAC,MAAM;cAAC4F,IAAI,EAAE;YAAG;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlG,OAAA;YACEsH,OAAO,EAAE7G,mBAAoB;YAC7BwH,QAAQ,EAAE,CAAC7H,SAAS,IAAIA,SAAS,CAACmB,MAAM,KAAK,CAAE;YAC/CL,SAAS,EAAE,0EACTV,gBAAgB,GACZ,wBAAwB,GACxB,6CAA6C,IAC5C,CAACJ,SAAS,IAAIA,SAAS,CAACmB,MAAM,KAAK,CAAC,GAAI,+BAA+B,GAAG,EAAE,EAAG;YAAAuE,QAAA,gBAEtF9F,OAAA,CAACN,WAAW;cAACwB,SAAS,EAAC,MAAM;cAAC4F,IAAI,EAAE;YAAG;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACzC1F,gBAAgB,GAAG,QAAQ,GAAG,OAAO;UAAA;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA,eACT,CAAC,gBAEHlG,OAAA;UAAKkB,SAAS,EAAC,uBAAuB;UAAA4E,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACrD,eACDlG,OAAA;UACEsH,OAAO,EAAE1G,mBAAoB;UAC7BM,SAAS,EAAC,kHAAkH;UAAA4E,QAAA,gBAE5H9F,OAAA,CAACL,WAAW;YAACuB,SAAS,EAAC,MAAM;YAAC4F,IAAI,EAAE;UAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kCAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlG,OAAA;MAAKkB,SAAS,EAAC,sBAAsB;MAACwB,KAAK,EAAE;QAAED,MAAM,EAAE;MAAoB,CAAE;MAAAqD,QAAA,gBAC3E9F,OAAA;QAAKkB,SAAS,EAAC,oFAAoF;QAAA4E,QAAA,EAChGiB,YAAY,CAAC;MAAC;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,EAEL9F,SAAS,CAACmB,MAAM,GAAG,CAAC,iBACnBvB,OAAA;QAAKkB,SAAS,EAAC,+FAA+F;QAACwB,KAAK,EAAE;UAAED,MAAM,EAAE;QAAQ,CAAE;QAAAqD,QAAA,eACxI9F,OAAA,CAACR,eAAe;UACd0I,MAAM,EAAE9H,SAAU;UAClB+H,iBAAiB,EAAE9H,wBAAyB;UAC5C+H,gBAAgB,EAAGxG,KAAK,IAAK;YAC3B0C,gBAAgB,CAAC1C,KAAK,CAAC;UACzB;QAAE;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/E,EAAA,CAjbIhB,SAAS;AAAAkI,EAAA,GAATlI,SAAS;AAmbf,eAAeA,SAAS;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}