"""
LLM通信日志记录器
用于记录所有与LLM的通信内容到项目特定的日志文件中
"""
import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from logging.handlers import RotatingFileHandler

# 设置日志目录
LOGS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "logs")
os.makedirs(LOGS_DIR, exist_ok=True)

class LLMCommunicationLogger:
    """LLM通信日志记录器"""
    
    _loggers_cache = {}  # 缓存已创建的logger
    
    @classmethod
    def get_project_logger(cls, project_id: str) -> logging.Logger:
        """
        获取指定项目的LLM通信日志记录器
        
        Args:
            project_id: 项目ID
            
        Returns:
            logging.Logger: 项目专用的logger
        """
        if project_id in cls._loggers_cache:
            return cls._loggers_cache[project_id]
        
        # 创建项目专用的logger
        logger_name = f"llm_comm_{project_id}"
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.INFO)
        
        # 清除现有的处理器，防止重复添加
        logger.handlers.clear()
        
        # 创建文件处理器
        log_file_path = os.path.join(LOGS_DIR, f"{project_id}.log")
        file_handler = RotatingFileHandler(
            log_file_path,
            maxBytes=50*1024*1024,  # 50MB
            backupCount=5,
            encoding='utf-8'
        )
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        # 添加处理器
        logger.addHandler(file_handler)
        
        # 防止日志向上传播到根logger
        logger.propagate = False
        
        # 缓存logger
        cls._loggers_cache[project_id] = logger
        
        return logger
    
    @classmethod
    def log_llm_request(
        cls,
        project_id: str,
        agent_name: str,
        prompt_content: str,
        temperature: float,
        model_name: str,
        expected_response_type: Optional[str] = None,
        context: Optional[str] = None
    ):
        """
        记录LLM请求
        
        Args:
            project_id: 项目ID
            agent_name: Agent名称
            prompt_content: 提示词内容
            temperature: 温度参数
            model_name: 模型名称
            expected_response_type: 期望的响应类型
            context: 调用上下文
        """
        if not project_id:
            return
            
        logger = cls.get_project_logger(project_id)
        
        request_data = {
            "type": "LLM_REQUEST",
            "agent": agent_name,
            "model": model_name,
            "temperature": temperature,
            "context": context,
            "expected_response_type": expected_response_type,
            "prompt_length": len(prompt_content),
            "prompt_preview": prompt_content[:500] + "..." if len(prompt_content) > 500 else prompt_content,
            "full_prompt": prompt_content
        }
        
        logger.info(f"=== LLM REQUEST START ===")
        logger.info(f"Agent: {agent_name}")
        logger.info(f"Model: {model_name}")
        logger.info(f"Temperature: {temperature}")
        logger.info(f"Context: {context}")
        logger.info(f"Expected Response Type: {expected_response_type}")
        logger.info(f"Prompt Length: {len(prompt_content)} characters")
        logger.info(f"--- FULL PROMPT ---")
        logger.info(prompt_content)
        logger.info(f"--- END PROMPT ---")
        logger.info(f"=== LLM REQUEST END ===\n")
    
    @classmethod
    def log_llm_response(
        cls,
        project_id: str,
        agent_name: str,
        response_text: Optional[str],
        raw_response: Optional[Dict[str, Any]],
        duration_ms: int,
        success: bool,
        error_message: Optional[str] = None,
        thinking_summary: Optional[str] = None,
        context: Optional[str] = None
    ):
        """
        记录LLM响应
        
        Args:
            project_id: 项目ID
            agent_name: Agent名称
            response_text: 响应文本
            raw_response: 原始响应数据
            duration_ms: 请求持续时间（毫秒）
            success: 是否成功
            error_message: 错误信息
            thinking_summary: 思考过程摘要
            context: 调用上下文
        """
        if not project_id:
            return
            
        logger = cls.get_project_logger(project_id)
        
        logger.info(f"=== LLM RESPONSE START ===")
        logger.info(f"Agent: {agent_name}")
        logger.info(f"Context: {context}")
        logger.info(f"Duration: {duration_ms}ms")
        logger.info(f"Success: {success}")
        
        if error_message:
            logger.info(f"Error: {error_message}")
        
        if thinking_summary:
            logger.info(f"--- THINKING PROCESS ---")
            logger.info(thinking_summary)
            logger.info(f"--- END THINKING ---")
        
        if response_text:
            logger.info(f"Response Length: {len(response_text)} characters")
            logger.info(f"--- RESPONSE CONTENT ---")
            logger.info(response_text)
            logger.info(f"--- END RESPONSE ---")
        
        if raw_response:
            logger.info(f"--- RAW RESPONSE DATA ---")
            try:
                logger.info(json.dumps(raw_response, ensure_ascii=False, indent=2))
            except Exception as e:
                logger.info(f"Raw response (non-JSON): {str(raw_response)}")
            logger.info(f"--- END RAW RESPONSE ---")
        
        logger.info(f"=== LLM RESPONSE END ===\n")
    
    @classmethod
    def log_llm_parsing_attempt(
        cls,
        project_id: str,
        agent_name: str,
        response_text: str,
        expected_schema: str,
        validation_success: bool,
        validation_error: Optional[str] = None,
        parsed_result: Optional[str] = None,
        context: Optional[str] = None
    ):
        """
        记录LLM响应解析尝试
        
        Args:
            project_id: 项目ID
            agent_name: Agent名称
            response_text: 原始响应文本
            expected_schema: 期望的模式
            validation_success: 验证是否成功
            validation_error: 验证错误信息
            parsed_result: 解析结果
            context: 调用上下文
        """
        if not project_id:
            return
            
        logger = cls.get_project_logger(project_id)
        
        logger.info(f"=== PARSING ATTEMPT START ===")
        logger.info(f"Agent: {agent_name}")
        logger.info(f"Context: {context}")
        logger.info(f"Expected Schema: {expected_schema}")
        logger.info(f"Validation Success: {validation_success}")
        
        if validation_error:
            logger.info(f"Validation Error: {validation_error}")
        
        logger.info(f"--- ORIGINAL RESPONSE ---")
        logger.info(response_text)
        logger.info(f"--- END ORIGINAL RESPONSE ---")
        
        if parsed_result:
            logger.info(f"--- PARSED RESULT ---")
            logger.info(parsed_result)
            logger.info(f"--- END PARSED RESULT ---")
        
        logger.info(f"=== PARSING ATTEMPT END ===\n")

# 单例实例
llm_comm_logger = LLMCommunicationLogger() 