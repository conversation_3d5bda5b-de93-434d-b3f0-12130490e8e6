{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\views\\\\ChatViewPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport LeftNav from '../components/LeftNav';\nimport ChatView from './ChatView';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatViewPage = () => {\n  _s();\n  const [currentChatId, setCurrentChatId] = useState(null);\n  const [isTaskListOpen, setIsTaskListOpen] = useState(false);\n  const [chatMessages, setChatMessages] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  // 从URL获取chatId参数\n  useEffect(() => {\n    const params = new URLSearchParams(location.search);\n    const chatId = params.get('chatId');\n    if (chatId) {\n      setCurrentChatId(chatId);\n      loadChatMessages(chatId);\n    }\n  }, [location.search]);\n\n  // 加载聊天消息\n  const loadChatMessages = async chatId => {\n    if (!chatId) return;\n    setIsLoading(true);\n    try {\n      const details = await apiService.getProjectDetails(chatId);\n      console.log(`[ChatViewPage] 加载聊天记录:`, details);\n      if (details && details.chat_history) {\n        setChatMessages(details.chat_history);\n      } else {\n        setChatMessages([{\n          id: `system-${Date.now()}`,\n          sender: 'system',\n          text: `该项目\"${(details === null || details === void 0 ? void 0 : details.title) || '未命名项目'}\"已加载，但没有找到聊天记录。这可能是因为：\n1. 项目是通过其他方式创建的\n2. 聊天记录可能已被清除\n3. 数据库中项目ID为 ${chatId} 的聊天记录表(chat_messages)可能为空`,\n          timestamp: new Date().toISOString(),\n          icon: 'ℹ️'\n        }]);\n      }\n    } catch (error) {\n      console.error('加载聊天记录失败:', error);\n      setChatMessages([{\n        id: `error-${Date.now()}`,\n        sender: 'system',\n        text: `加载聊天记录失败: ${error.message}`,\n        timestamp: new Date().toISOString(),\n        icon: '⚠️'\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSelectChat = chatId => {\n    setCurrentChatId(chatId);\n    console.log('选择聊天:', chatId);\n    navigate(`/chat?chatId=${chatId}`);\n    loadChatMessages(chatId);\n  };\n  const refreshChatHistory = () => {\n    // 触发LeftNav组件刷新聊天历史列表\n    window.dispatchEvent(new CustomEvent('refreshChatHistory'));\n  };\n  const toggleTaskList = () => {\n    setIsTaskListOpen(!isTaskListOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-1 overflow-hidden h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(LeftNav, {\n      isTaskListOpen: isTaskListOpen,\n      onToggleTaskList: toggleTaskList,\n      onSelectChat: handleSelectChat\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChatView, {\n      currentChatId: currentChatId,\n      initialMessages: chatMessages\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatViewPage, \"/nZ7qTWHyGOZJnnVeg67kf1W9Ls=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = ChatViewPage;\nexport default ChatViewPage;\nvar _c;\n$RefreshReg$(_c, \"ChatViewPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useLocation", "useNavigate", "LeftNav", "ChatView", "apiService", "jsxDEV", "_jsxDEV", "ChatViewPage", "_s", "currentChatId", "setCurrentChatId", "isTaskListOpen", "setIsTaskListOpen", "chatMessages", "setChatMessages", "isLoading", "setIsLoading", "location", "navigate", "params", "URLSearchParams", "search", "chatId", "get", "loadChatMessages", "details", "getProjectDetails", "console", "log", "chat_history", "id", "Date", "now", "sender", "text", "title", "timestamp", "toISOString", "icon", "error", "message", "handleSelectChat", "refreshChatHistory", "window", "dispatchEvent", "CustomEvent", "toggleTaskList", "className", "children", "onToggleTaskList", "onSelectChat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initialMessages", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/views/ChatViewPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport LeftNav from '../components/LeftNav';\nimport ChatView from './ChatView';\nimport apiService from '../services/api';\n\nconst ChatViewPage = () => {\n  const [currentChatId, setCurrentChatId] = useState(null);\n  const [isTaskListOpen, setIsTaskListOpen] = useState(false);\n  const [chatMessages, setChatMessages] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  \n  const location = useLocation();\n  const navigate = useNavigate();\n\n  // 从URL获取chatId参数\n  useEffect(() => {\n    const params = new URLSearchParams(location.search);\n    const chatId = params.get('chatId');\n    \n    if (chatId) {\n      setCurrentChatId(chatId);\n      loadChatMessages(chatId);\n    }\n  }, [location.search]);\n\n  // 加载聊天消息\n  const loadChatMessages = async (chatId) => {\n    if (!chatId) return;\n    \n    setIsLoading(true);\n    try {\n      const details = await apiService.getProjectDetails(chatId);\n      console.log(`[ChatViewPage] 加载聊天记录:`, details);\n      \n      if (details && details.chat_history) {\n        setChatMessages(details.chat_history);\n      } else {\n        setChatMessages([{\n          id: `system-${Date.now()}`,\n          sender: 'system',\n          text: `该项目\"${details?.title || '未命名项目'}\"已加载，但没有找到聊天记录。这可能是因为：\n1. 项目是通过其他方式创建的\n2. 聊天记录可能已被清除\n3. 数据库中项目ID为 ${chatId} 的聊天记录表(chat_messages)可能为空`,\n          timestamp: new Date().toISOString(),\n          icon: 'ℹ️'\n        }]);\n      }\n    } catch (error) {\n      console.error('加载聊天记录失败:', error);\n      setChatMessages([{\n        id: `error-${Date.now()}`,\n        sender: 'system',\n        text: `加载聊天记录失败: ${error.message}`,\n        timestamp: new Date().toISOString(),\n        icon: '⚠️'\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSelectChat = (chatId) => {\n    setCurrentChatId(chatId);\n    console.log('选择聊天:', chatId);\n    navigate(`/chat?chatId=${chatId}`);\n    loadChatMessages(chatId);\n  };\n\n  const refreshChatHistory = () => {\n    // 触发LeftNav组件刷新聊天历史列表\n    window.dispatchEvent(new CustomEvent('refreshChatHistory'));\n  };\n\n  const toggleTaskList = () => {\n    setIsTaskListOpen(!isTaskListOpen);\n  };\n\n  return (\n    <div className=\"flex flex-1 overflow-hidden h-screen\">\n      <LeftNav \n        isTaskListOpen={isTaskListOpen} \n        onToggleTaskList={toggleTaskList}\n        onSelectChat={handleSelectChat}\n      />\n      <ChatView \n        currentChatId={currentChatId} \n        initialMessages={chatMessages} \n      />\n    </div>\n  );\n};\n\nexport default ChatViewPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMmB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd,MAAMoB,MAAM,GAAG,IAAIC,eAAe,CAACH,QAAQ,CAACI,MAAM,CAAC;IACnD,MAAMC,MAAM,GAAGH,MAAM,CAACI,GAAG,CAAC,QAAQ,CAAC;IAEnC,IAAID,MAAM,EAAE;MACVZ,gBAAgB,CAACY,MAAM,CAAC;MACxBE,gBAAgB,CAACF,MAAM,CAAC;IAC1B;EACF,CAAC,EAAE,CAACL,QAAQ,CAACI,MAAM,CAAC,CAAC;;EAErB;EACA,MAAMG,gBAAgB,GAAG,MAAOF,MAAM,IAAK;IACzC,IAAI,CAACA,MAAM,EAAE;IAEbN,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMS,OAAO,GAAG,MAAMrB,UAAU,CAACsB,iBAAiB,CAACJ,MAAM,CAAC;MAC1DK,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEH,OAAO,CAAC;MAE9C,IAAIA,OAAO,IAAIA,OAAO,CAACI,YAAY,EAAE;QACnCf,eAAe,CAACW,OAAO,CAACI,YAAY,CAAC;MACvC,CAAC,MAAM;QACLf,eAAe,CAAC,CAAC;UACfgB,EAAE,EAAE,UAAUC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UAC1BC,MAAM,EAAE,QAAQ;UAChBC,IAAI,EAAE,OAAO,CAAAT,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,KAAK,KAAI,OAAO;AAChD;AACA;AACA,eAAeb,MAAM,4BAA4B;UACvCc,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;UACnCC,IAAI,EAAE;QACR,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCzB,eAAe,CAAC,CAAC;QACfgB,EAAE,EAAE,SAASC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QACzBC,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE,aAAaK,KAAK,CAACC,OAAO,EAAE;QAClCJ,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;QACnCC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRtB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMyB,gBAAgB,GAAInB,MAAM,IAAK;IACnCZ,gBAAgB,CAACY,MAAM,CAAC;IACxBK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEN,MAAM,CAAC;IAC5BJ,QAAQ,CAAC,gBAAgBI,MAAM,EAAE,CAAC;IAClCE,gBAAgB,CAACF,MAAM,CAAC;EAC1B,CAAC;EAED,MAAMoB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACAC,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,oBAAoB,CAAC,CAAC;EAC7D,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BlC,iBAAiB,CAAC,CAACD,cAAc,CAAC;EACpC,CAAC;EAED,oBACEL,OAAA;IAAKyC,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBACnD1C,OAAA,CAACJ,OAAO;MACNS,cAAc,EAAEA,cAAe;MAC/BsC,gBAAgB,EAAEH,cAAe;MACjCI,YAAY,EAAET;IAAiB;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,eACFhD,OAAA,CAACH,QAAQ;MACPM,aAAa,EAAEA,aAAc;MAC7B8C,eAAe,EAAE1C;IAAa;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9C,EAAA,CAtFID,YAAY;EAAA,QAMCP,WAAW,EACXC,WAAW;AAAA;AAAAuD,EAAA,GAPxBjD,YAAY;AAwFlB,eAAeA,YAAY;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}