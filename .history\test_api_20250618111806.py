import requests
import json
import time

def test_slide_generation():
    """测试幻灯片生成API"""
    url = "http://localhost:8000/slides/generate_from_text_stream"
    data = {
        "prompt": "测试聊天记录保存功能，创建关于AI技术的3张幻灯片"
    }
    
    print("开始测试幻灯片生成...")
    response = requests.post(url, json=data, stream=True)
    
    if response.status_code != 200:
        print(f"错误: {response.status_code} - {response.text}")
        return None
    
    project_id = None
    for line in response.iter_lines(decode_unicode=True):
        if line.startswith('data:'):
            try:
                json_data = json.loads(line[5:].strip())
                print(f"收到消息: {json_data}")
                if 'project_id' in json_data:
                    project_id = json_data['project_id']
                if json_data.get('status') == 'completed':
                    break
            except json.JSONDecodeError:
                continue
    
    return project_id

def test_get_project_details(project_id):
    """测试获取项目详情API"""
    url = f"http://localhost:8000/projects/{project_id}/details"
    
    print(f"\n获取项目 {project_id} 详情...")
    response = requests.get(url)
    
    if response.status_code != 200:
        print(f"错误: {response.status_code} - {response.text}")
        return
    
    data = response.json()
    print(f"项目标题: {data.get('title')}")
    print(f"聊天历史数量: {len(data.get('chat_history', []))}")
    
    chat_history = data.get('chat_history', [])
    if chat_history:
        print("聊天历史记录:")
        for i, msg in enumerate(chat_history):
            print(f"  {i+1}. {msg.get('sender')}: {msg.get('text')[:50]}...")
    else:
        print("⚠️ 没有找到聊天历史记录!")
    
    return data

if __name__ == "__main__":
    # 等待后端服务启动
    time.sleep(2)
    
    # 测试生成幻灯片
    project_id = test_slide_generation()
    
    if project_id:
        # 等待数据保存
        time.sleep(2)
        # 测试获取项目详情
        test_get_project_details(project_id)
    else:
        print("幻灯片生成失败，无法获取项目ID") 