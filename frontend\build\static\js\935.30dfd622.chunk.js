"use strict";(self.webpackChunktiktodo_slides_clone=self.webpackChunktiktodo_slides_clone||[]).push([[935],{3935:(e,t,s)=>{s.r(t),s.d(t,{default:()=>x});var r=s(5043),a=s(165),i=s(9379),n=s(1019),o=s(7052),l=s(1200),m=s(2774),c=s(579);const d=e=>{let{currentChatId:t}=e;const[s,a]=(0,r.useState)([]),[d,x]=(0,r.useState)(!1),p=(0,r.useRef)(null);(0,r.useEffect)((()=>{var e;null===(e=p.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})}),[s]);return(0,c.jsxs)("div",{className:"flex flex-col h-screen bg-gray-50 flex-1",children:[(0,c.jsxs)("div",{className:"h-16 px-3 border-b border-gray-200 bg-white flex items-center sticky top-0 z-10",children:[(0,c.jsx)("button",{onClick:()=>{a([]),x(!1)},className:"p-2 mr-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100",title:"\u65b0\u804a\u5929",children:(0,c.jsx)(m.OiG,{size:16})}),(0,c.jsxs)("div",{className:"flex-1 text-base font-medium text-gray-800 flex items-center",children:[(0,c.jsx)(m.uN,{className:"mr-2 text-blue-500"})," AI \u804a\u5929"]})]}),(0,c.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-5 custom-scrollbar",children:[0===s.length&&(0,c.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-gray-400 p-8 min-h-[400px]",children:[(0,c.jsx)("span",{className:"text-6xl mb-6 text-gray-300",children:"\ud83d\udcac"}),(0,c.jsx)("span",{className:"text-2xl font-medium",children:"\u5f00\u59cb\u4f60\u7684AI\u5bf9\u8bdd"}),(0,c.jsx)("p",{className:"text-sm text-gray-400 mt-2 text-center max-w-md",children:"\u5728\u8fd9\u91cc\u4e0eAI\u81ea\u7531\u4ea4\u6d41\uff0c\u63d0\u51fa\u95ee\u9898\u6216\u5bfb\u6c42\u5e2e\u52a9\u3002"})]}),s.map(((e,t)=>(0,c.jsx)(o.A,{message:e},e.id||t))),(0,c.jsx)("div",{ref:p})]}),(0,c.jsx)("div",{className:"p-2 bg-gray-50 sticky bottom-0 border-t border-gray-200",children:(0,c.jsx)(n.A,{onSendMessage:async e=>{const t="string"===typeof e?e:e.message,s="object"===typeof e?e.files:[];if((null===t||void 0===t||!t.trim())&&(!s||0===s.length)||d)return;const r={id:"user-".concat(Date.now()),sender:"user",text:t||"\u53d1\u9001\u4e86\u6587\u4ef6",files:s||[],timestamp:(new Date).toISOString()};a((e=>[...e,r])),x(!0);try{await l.A.streamChatResponse(t,(e=>{a((t=>{const s=[...t];if("system"===e.sender)return s.push((0,i.A)((0,i.A)({},e),{},{type:"system-info",timestamp:e.timestamp||(new Date).toISOString()})),s;if("ai"===e.sender){const t=s.findIndex((t=>t.id===e.id));if(-1!==t){const r=s[t];e.is_append?s[t]=(0,i.A)((0,i.A)({},r),{},{text:(r.text||"")+(e.text||""),is_streaming:void 0!==e.is_streaming?e.is_streaming:r.is_streaming,timestamp:e.timestamp||r.timestamp}):s[t]=(0,i.A)((0,i.A)({},r),{},{text:void 0!==e.text?e.text:r.text,is_streaming:void 0!==e.is_streaming?e.is_streaming:r.is_streaming,stream_complete:void 0!==e.stream_complete?e.stream_complete:r.stream_complete,timestamp:e.timestamp||r.timestamp})}else s.push((0,i.A)((0,i.A)({},e),{},{type:"ai",timestamp:e.timestamp||(new Date).toISOString()}))}return s}))}),(e=>{console.error("Chat streaming error:",e),x(!1),a((t=>[...t,{id:"error-".concat(Date.now()),sender:"system",type:"ai_error",text:"\u53d1\u751f\u9519\u8bef: ".concat(e.message),timestamp:(new Date).toISOString()}]))}),(()=>{x(!1)}))}catch(n){console.error("API call failed to start:",n),x(!1),a((e=>[...e,{id:"error-api-".concat(Date.now()),sender:"system",type:"ai_error",text:"\u65e0\u6cd5\u8fde\u63a5\u5230AI\u804a\u5929\u670d\u52a1: ".concat(n.message),timestamp:(new Date).toISOString()}]))}},isGenerating:d,placeholder:"\u8f93\u5165\u4f60\u7684\u6d88\u606f..."})})]})},x=()=>{const[e,t]=(0,r.useState)(null),[s,i]=(0,r.useState)(!1);return(0,c.jsxs)("div",{className:"flex flex-1 overflow-hidden h-screen",children:[(0,c.jsx)(a.A,{isTaskListOpen:s,onToggleTaskList:()=>{i(!s)},onSelectChat:e=>{t(e),console.log("\u9009\u62e9\u804a\u5929:",e)}}),(0,c.jsx)(d,{currentChatId:e})]})}}}]);
//# sourceMappingURL=935.30dfd622.chunk.js.map