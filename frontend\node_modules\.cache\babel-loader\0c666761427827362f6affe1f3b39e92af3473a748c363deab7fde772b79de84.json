{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\nvar userAgent = require('../internals/environment-user-agent');\nvar process = globalThis.process;\nvar Deno = globalThis.Deno;\nvar versions = process && process.versions || Deno && Deno.version;\nvar v8 = versions && versions.v8;\nvar match, version;\nif (v8) {\n  match = v8.split('.');\n  // in old Chrome, versions of V8 isn't V8 = Chrome / 10\n  // but their correct versions are not interesting for us\n  version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);\n}\n\n// BrowserFS NodeJS `process` polyfill incorrectly set `.v8` to `0.0`\n// so check `userAgent` even if `.v8` exists, but 0\nif (!version && userAgent) {\n  match = userAgent.match(/Edge\\/(\\d+)/);\n  if (!match || match[1] >= 74) {\n    match = userAgent.match(/Chrome\\/(\\d+)/);\n    if (match) version = +match[1];\n  }\n}\nmodule.exports = version;", "map": {"version": 3, "names": ["globalThis", "require", "userAgent", "process", "<PERSON><PERSON>", "versions", "version", "v8", "match", "split", "module", "exports"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/node_modules/core-js-pure/internals/environment-v8-version.js"], "sourcesContent": ["'use strict';\nvar globalThis = require('../internals/global-this');\nvar userAgent = require('../internals/environment-user-agent');\n\nvar process = globalThis.process;\nvar Deno = globalThis.Deno;\nvar versions = process && process.versions || Deno && Deno.version;\nvar v8 = versions && versions.v8;\nvar match, version;\n\nif (v8) {\n  match = v8.split('.');\n  // in old Chrome, versions of V8 isn't V8 = Chrome / 10\n  // but their correct versions are not interesting for us\n  version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);\n}\n\n// BrowserFS NodeJS `process` polyfill incorrectly set `.v8` to `0.0`\n// so check `userAgent` even if `.v8` exists, but 0\nif (!version && userAgent) {\n  match = userAgent.match(/Edge\\/(\\d+)/);\n  if (!match || match[1] >= 74) {\n    match = userAgent.match(/Chrome\\/(\\d+)/);\n    if (match) version = +match[1];\n  }\n}\n\nmodule.exports = version;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIC,SAAS,GAAGD,OAAO,CAAC,qCAAqC,CAAC;AAE9D,IAAIE,OAAO,GAAGH,UAAU,CAACG,OAAO;AAChC,IAAIC,IAAI,GAAGJ,UAAU,CAACI,IAAI;AAC1B,IAAIC,QAAQ,GAAGF,OAAO,IAAIA,OAAO,CAACE,QAAQ,IAAID,IAAI,IAAIA,IAAI,CAACE,OAAO;AAClE,IAAIC,EAAE,GAAGF,QAAQ,IAAIA,QAAQ,CAACE,EAAE;AAChC,IAAIC,KAAK,EAAEF,OAAO;AAElB,IAAIC,EAAE,EAAE;EACNC,KAAK,GAAGD,EAAE,CAACE,KAAK,CAAC,GAAG,CAAC;EACrB;EACA;EACAH,OAAO,GAAGE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAEA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;AACrE;;AAEA;AACA;AACA,IAAI,CAACF,OAAO,IAAIJ,SAAS,EAAE;EACzBM,KAAK,GAAGN,SAAS,CAACM,KAAK,CAAC,aAAa,CAAC;EACtC,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE;IAC5BA,KAAK,GAAGN,SAAS,CAACM,KAAK,CAAC,eAAe,CAAC;IACxC,IAAIA,KAAK,EAAEF,OAAO,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC;EAChC;AACF;AAEAE,MAAM,CAACC,OAAO,GAAGL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}