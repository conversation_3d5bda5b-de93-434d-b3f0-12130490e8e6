{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\SlideRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useCallback } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DESIGNED_WIDTH = 1280;\nconst DESIGNED_HEIGHT = 720;\n\n// prepareSlideHtmlForIframe 函数 (请确保这里是你之前确认过的好版本，\n// 即能够先移除所有旧的 viewport meta 标签，再添加我们需要的那个)\nconst prepareSlideHtmlForIframe = slideHtmlContent => {\n  if (!slideHtmlContent) {\n    return `<!DOCTYPE html>\n      <html>\n        <head>\n          <meta name=\"viewport\" content=\"width=${DESIGNED_WIDTH}, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">\n          <style>\n            html, body {\n              width: ${DESIGNED_WIDTH}px !important;\n              height: ${DESIGNED_HEIGHT}px !important;\n              margin: 0 !important;\n              padding: 0 !important;\n              overflow: hidden !important;\n              box-sizing: border-box !important;\n            }\n            body {\n              display: flex;\n              justify-content: center;\n              align-items: center;\n              background: #eee;\n              font-family: sans-serif;\n              color: #888;\n              position: relative !important;\n            }\n          </style>\n        </head>\n        <body>\n          <div>(No Content)</div>\n        </body>\n      </html>`;\n  }\n\n  // 1. 稳定地移除已存在的 viewport meta 标签\n  let processedHtml = slideHtmlContent.replace(/<meta[^>]*name=[\"']viewport[\"'][^>]*>/gi, '');\n  const viewportMeta = `<meta name=\"viewport\" content=\"width=${DESIGNED_WIDTH}, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">`;\n  const baseStyles = `\n    <style>\n      html, body {\n        width: ${DESIGNED_WIDTH}px !important;\n        height: ${DESIGNED_HEIGHT}px !important;\n        margin: 0 !important;\n        padding: 0 !important;\n        overflow: hidden !important; /* 禁止内部滚动 */\n        box-sizing: border-box !important; /* 确保盒模型一致性 */\n      }\n      body {\n        position: relative !important; /* 确保 body 是定位上下文 */\n        display: block !important; /* 覆盖LLM可能在body上设置的flex/grid */\n        justify-content: normal !important;\n        align-items: normal !important;\n        transform-origin: 0 0 !important; /* 确保缩放基点正确 */\n        transform: scale(1) !important; /* body自身不应被其内部的transform再次缩放 */\n        box-sizing: border-box !important; /* 确保盒模型一致性 */\n      }\n      /* 确保幻灯片内常见的顶层容器也使用相同的盒模型 */\n      .slide-container, .slide-content, .slide-wrapper, .content-wrapper, main, section {\n        box-sizing: border-box !important;\n      }\n      /* 确保所有元素都使用相同的盒模型 */\n      * {\n        box-sizing: inherit !important;\n      }\n    </style>\n  `;\n\n  // 2. 在处理后的HTML中注入我们的meta和styles\n  if (processedHtml.trim().toLowerCase().startsWith('<!doctype html') || processedHtml.trim().toLowerCase().startsWith('<html')) {\n    // 处理完整的HTML文档\n    if (processedHtml.includes('</head>')) {\n      processedHtml = processedHtml.replace('</head>', `${viewportMeta}${baseStyles}</head>`);\n    } else if (processedHtml.includes('<head>')) {\n      processedHtml = processedHtml.replace('<head>', `<head>${viewportMeta}${baseStyles}`);\n    } else {\n      // 有<html>标签但没有<head>标签的情况\n      processedHtml = processedHtml.replace(/<html[^>]*>/, match => `${match}<head>${viewportMeta}${baseStyles}</head>`);\n    }\n  } else {\n    // 如果是HTML片段，则构建完整结构\n    processedHtml = `\n      <!DOCTYPE html>\n      <html>\n        <head>\n          ${viewportMeta}\n          ${baseStyles}\n        </head>\n        <body>\n          ${processedHtml}\n        </body>\n      </html>\n    `;\n  }\n  return processedHtml;\n};\nconst SlideRenderer = ({\n  slideId,\n  slideFullHtml,\n  isAppEditingMode = false,\n  isThumbnail = false,\n  // isFullScreen = false, // 此组件主要用于预览和缩略图，全屏由FullScreenPlayer处理\n  onClick = null\n}) => {\n  _s();\n  const rendererRootRef = useRef(null); // SlideRenderer的根DOM元素\n  const iframeRef = useRef(null);\n\n  // injectEnvironmentScript 函数 (保持和你原来版本一致，确保 SLIDE_IS_APP_EDITING_MODE 等变量注入)\n  const injectEnvironmentScript = useCallback(iframeDocument => {\n    const oldScript = iframeDocument.getElementById('slide-environment-script');\n    if (oldScript) oldScript.remove();\n    const script = iframeDocument.createElement('script');\n    script.id = 'slide-environment-script';\n    script.textContent = `\n      window.SLIDE_IS_APP_EDITING_MODE = ${isAppEditingMode};\n      window.SLIDE_IS_THUMBNAIL = ${isThumbnail};\n      window.SLIDE_ID = \"${slideId}\"; // 确保slideId可用于postMessage\n\n      document.addEventListener('DOMContentLoaded', function() {\n        const html = document.documentElement;\n        const body = document.body;\n        const designedWidth = ${DESIGNED_WIDTH};\n        const designedHeight = ${DESIGNED_HEIGHT};\n\n        // 再次确保 html/body 尺寸正确 (与 prepareSlideHtmlForIframe 中的有点重复，但安全起见)\n        if (html) {\n          html.style.setProperty('overflow', 'hidden', 'important');\n          html.style.setProperty('width', designedWidth + 'px', 'important');\n          html.style.setProperty('height', designedHeight + 'px', 'important');\n        }\n\n        if (body) {\n          body.style.setProperty('overflow', 'hidden', 'important');\n          body.style.setProperty('width', designedWidth + 'px', 'important');\n          body.style.setProperty('height', designedHeight + 'px', 'important');\n          body.style.setProperty('position', 'relative', 'important');\n          body.style.setProperty('display', 'block', 'important');\n          body.style.setProperty('justify-content', 'normal', 'important');\n          body.style.setProperty('align-items', 'normal', 'important');\n          body.style.setProperty('transform-origin', '0 0', 'important'); \n          body.style.setProperty('transform', 'scale(1)', 'important'); \n        }\n        \n        // 【新增】为 iframe 的 document 添加键盘事件监听器\n        document.addEventListener('keydown', function(e) {\n          // 定义需要转发的按键\n          const relevantKeys = [\n            'ArrowRight', 'ArrowLeft', 'ArrowUp', 'ArrowDown',\n            'PageDown', 'PageUp', ' ', 'Escape', 'f'\n          ];\n\n          // 如果按下的键是我们关心的，就通过 postMessage 发送给父窗口\n          if (relevantKeys.includes(e.key)) {\n            // 阻止默认行为，例如按空格键滚动页面\n            e.preventDefault();\n            window.parent.postMessage({\n              type: 'iframe_keydown', // 自定义消息类型\n              key: e.key,\n              slideId: window.SLIDE_ID\n            }, '*');\n          }\n        });\n        \n        // Chart.js 图表重绘逻辑\n        if (window.Chart && typeof window.Chart.instances === 'object') {\n          setTimeout(() => {\n            Object.values(window.Chart.instances).forEach(chartInstance => {\n              if (chartInstance && typeof chartInstance.resize === 'function') {\n                try { chartInstance.resize(); chartInstance.update('none'); } catch(e) { console.warn('重绘图表时出错:', e); }\n              }\n            });\n          }, 150); // 延迟以确保DOM和样式已应用\n        }\n\n        // +++++++++++++++ 新增：全局点击监听器 +++++++++++++++\n        if (window.SLIDE_IS_APP_EDITING_MODE && !window.SLIDE_IS_THUMBNAIL) {\n          document.addEventListener('click', function(event) {\n            if (!window.SLIDE_IS_APP_EDITING_MODE) return;\n\n            const editableElement = event.target.closest('[data-editable-id]');\n            if (editableElement) {\n                event.preventDefault();\n                event.stopPropagation();\n\n                const elementId = editableElement.dataset.editableId;\n                // 使用相对于页面的坐标，而不是视口坐标，以避免滚动条影响\n                const clickX = event.pageX;\n                const clickY = event.pageY;\n                \n              window.parent.postMessage({\n                    type: 'element_clicked',\n                payload: {\n                        slideId: window.SLIDE_ID,\n                        elementId: elementId,\n                        // 将相对于iframe的点击坐标发送出去\n                        clickPosition: { x: clickX, y: clickY } \n                }\n                }, '*');\n            }\n          }, true); // 使用捕获阶段以确保能截获所有点击\n        }\n        // +++++++++++++++++++++++++++++++++++++++++++++++++++\n      });\n    `;\n    if (iframeDocument && iframeDocument.head) {\n      iframeDocument.head.appendChild(script);\n    } else if (iframeDocument && iframeDocument.body) {\n      iframeDocument.body.insertAdjacentElement('afterbegin', script);\n    } else if (iframeDocument) {\n      iframeDocument.appendChild(script);\n    }\n  }, [isAppEditingMode, isThumbnail, slideId]); // 依赖项\n\n  // 当幻灯片HTML内容变化时，或相关状态变化时，重新加载iframe内容\n  useEffect(() => {\n    const iframe = iframeRef.current;\n    let handleLoad = null;\n    if (iframe) {\n      const preparedHtml = prepareSlideHtmlForIframe(slideFullHtml);\n      handleLoad = () => {\n        if (iframe.contentWindow && iframe.contentWindow.document) {\n          injectEnvironmentScript(iframe.contentWindow.document);\n        }\n      };\n      iframe.addEventListener('load', handleLoad);\n      iframe.srcdoc = preparedHtml; // 使用 srcdoc 赋值\n    }\n    return () => {\n      if (iframe && handleLoad) {\n        iframe.removeEventListener('load', handleLoad);\n      }\n    };\n  }, [slideFullHtml, injectEnvironmentScript, slideId]); // 依赖项\n\n  // External scaling and positioning of scalingWrapperRef\n  useEffect(() => {\n    const rootElement = rendererRootRef.current;\n    const iframe = iframeRef.current;\n    if (!rootElement || !iframe) return;\n    const updateIframeScaleAndPosition = () => {\n      // 确保iframe默认可见状态，除非特定条件下需要隐藏\n      iframe.style.visibility = 'visible';\n      let availableWidth = rootElement.offsetWidth;\n      let availableHeight = rootElement.offsetHeight;\n\n      // 如果容器尺寸为零，但有父元素，尝试从父元素获取尺寸\n      if ((availableWidth <= 0 || availableHeight <= 0) && rootElement.parentElement) {\n        const parentWidth = rootElement.parentElement.offsetWidth;\n        const parentHeight = rootElement.parentElement.offsetHeight;\n        if (parentWidth > 0 && parentHeight > 0) {\n          // 使用父元素尺寸，确保16:9比例\n          availableWidth = parentWidth;\n          availableHeight = isThumbnail ? parentHeight : parentWidth * 9 / 16;\n\n          // 为rootElement设置明确的尺寸，使其不依赖内容尺寸\n          rootElement.style.width = `${availableWidth}px`;\n          rootElement.style.height = `${availableHeight}px`;\n        }\n      }\n\n      // 如果经过尝试后尺寸仍然为零，应用最小默认值而不是隐藏\n      if (availableWidth <= 0 || availableHeight <= 0) {\n        // 为避免完全隐藏，设置一个最小默认尺寸\n        availableWidth = availableWidth || 320; // 至少320px宽\n        availableHeight = availableHeight || 180; // 至少180px高 (16:9)\n\n        // 为rootElement设置明确的最小尺寸\n        rootElement.style.minWidth = `${availableWidth}px`;\n        rootElement.style.minHeight = `${availableHeight}px`;\n      }\n\n      // 计算缩放比例，确保内容能完整显示并保持16:9的宽高比\n      const scaleX = availableWidth / DESIGNED_WIDTH;\n      const scaleY = availableHeight / DESIGNED_HEIGHT;\n      const scale = Math.min(scaleX, scaleY); // 取较小的缩放比例以适应容器\n\n      // 确保缩放比例不为零或非常小\n      const safeScale = Math.max(scale, 0.01); // 设置一个最小缩放值\n\n      // 设置iframe的固定设计尺寸和基于左上角的缩放\n      iframe.style.width = `${DESIGNED_WIDTH}px`;\n      iframe.style.height = `${DESIGNED_HEIGHT}px`;\n      iframe.style.transformOrigin = '0 0';\n\n      // 对缩略图使用不同的缩放方法\n      if (isThumbnail) {\n        // 对缩略图保持使用transform缩放\n        iframe.style.transform = `scale(${safeScale})`;\n      } else {\n        // 标准预览使用transform缩放\n        iframe.style.transform = `scale(${safeScale})`;\n      }\n\n      // 计算缩放后iframe的实际尺寸\n      const scaledWidth = DESIGNED_WIDTH * safeScale;\n      const scaledHeight = DESIGNED_HEIGHT * safeScale;\n\n      // 计算偏移量以在父容器(rootElement)中居中iframe\n      const offsetX = (availableWidth - scaledWidth) / 2;\n      const offsetY = (availableHeight - scaledHeight) / 2;\n\n      // 应用绝对定位和偏移量\n      iframe.style.position = 'absolute';\n      iframe.style.left = `${offsetX}px`;\n      iframe.style.top = `${offsetY}px`;\n    };\n\n    // 初始设置iframe大小并位置\n    updateIframeScaleAndPosition();\n\n    // 确保在初始渲染后iframe正确显示\n    setTimeout(updateIframeScaleAndPosition, 50);\n\n    // 使用 ResizeObserver 监听根元素尺寸变化，并重新计算\n    const resizeObserver = new ResizeObserver(() => {\n      // 设置一个短暂的延迟，以确保读取到正确的尺寸\n      setTimeout(updateIframeScaleAndPosition, 10);\n    });\n    resizeObserver.observe(rootElement);\n    return () => {\n      resizeObserver.unobserve(rootElement); // 组件卸载时停止监听\n    };\n  }, [slideId, isThumbnail]); // 依赖项，当slideId或isThumbnail改变时重新执行\n\n  // 点击事件处理\n  const handleIframeWrapperClick = useCallback(e => {\n    if (onClick) {\n      onClick(e);\n    }\n  }, [onClick]);\n\n  // 如果没有HTML内容且不是缩略图，显示占位符\n  if (!slideFullHtml && !isThumbnail) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `w-full flex items-center justify-center text-gray-400 bg-gray-100 rounded-lg`,\n      style: {\n        aspectRatio: '16/9'\n      } // 保持16:9的宽高比\n      ,\n      onClick: onClick // 允许点击占位符\n      ,\n      children: \"\\u6B64\\u5E7B\\u706F\\u7247\\u6682\\u65E0\\u5185\\u5BB9\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: rendererRootRef,\n    onClick: handleIframeWrapperClick // 在根元素上处理点击\n    ,\n    className: `slide-renderer-root ${isThumbnail ? 'thumbnail-root' : 'preview-root'}`,\n    style: {\n      width: '100%',\n      // 根元素填充其父容器\n      height: '100%',\n      // 根元素填充其父容器\n      minHeight: isThumbnail ? '120px' : '180px',\n      // 确保最小高度\n      aspectRatio: '16/9',\n      // 确保始终保持16:9的宽高比，即使父容器尺寸为零\n      overflow: 'hidden',\n      // 裁剪超出部分的iframe\n      position: 'relative',\n      // 作为内部iframe绝对定位的基准\n      backgroundColor: 'transparent' // 调试时可以改为半透明色以查看边界\n    },\n    children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n      ref: iframeRef,\n      // 更明确的key\n      title: `Slide ${slideId}`,\n      style: {\n        width: `${DESIGNED_WIDTH}px`,\n        // 固定宽度为设计尺寸\n        height: `${DESIGNED_HEIGHT}px`,\n        // 固定高度为设计尺寸\n        border: 'none',\n        transformOrigin: '0 0',\n        // 从左上角开始缩放\n        // 根据是否处于编辑模式和是否为缩略图，决定iframe是否响应鼠标事件\n        pointerEvents: isAppEditingMode && !isThumbnail ? 'auto' : 'none',\n        position: 'absolute',\n        // 确保left/top定位生效\n        visibility: 'visible' // 默认可见，由JS计算时按需隐藏\n      },\n      sandbox: \"allow-scripts allow-same-origin\" // 允许脚本和同源，以便内部JS（如Chart.js）能运行\n      ,\n      scrolling: \"no\" // 明确禁止iframe内部滚动\n    }, `${slideId}-${isThumbnail ? 'thumb' : 'preview'}`, false, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 361,\n    columnNumber: 5\n  }, this);\n};\n_s(SlideRenderer, \"S6Ptts+UaaR10abwUiynWiBw3GQ=\");\n_c = SlideRenderer;\nexport default SlideRenderer;\nvar _c;\n$RefreshReg$(_c, \"SlideRenderer\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useCallback", "jsxDEV", "_jsxDEV", "DESIGNED_WIDTH", "DESIGNED_HEIGHT", "prepareSlideHtmlForIframe", "slideHtmlContent", "processedHtml", "replace", "viewportMeta", "baseStyles", "trim", "toLowerCase", "startsWith", "includes", "match", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slideId", "slideFullHtml", "isAppEditingMode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onClick", "_s", "rendererRootRef", "iframeRef", "injectEnvironmentScript", "iframeDocument", "oldScript", "getElementById", "remove", "script", "createElement", "id", "textContent", "head", "append<PERSON><PERSON><PERSON>", "body", "insertAdjacentElement", "iframe", "current", "handleLoad", "preparedHtml", "contentWindow", "document", "addEventListener", "srcdoc", "removeEventListener", "rootElement", "updateIframeScaleAndPosition", "style", "visibility", "availableWidth", "offsetWidth", "availableHeight", "offsetHeight", "parentElement", "parentWidth", "parentHeight", "width", "height", "min<PERSON><PERSON><PERSON>", "minHeight", "scaleX", "scaleY", "scale", "Math", "min", "safeScale", "max", "transform<PERSON><PERSON>in", "transform", "scaledWidth", "scaledHeight", "offsetX", "offsetY", "position", "left", "top", "setTimeout", "resizeObserver", "ResizeObserver", "observe", "unobserve", "handleIframeWrapperClick", "e", "className", "aspectRatio", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "overflow", "backgroundColor", "title", "border", "pointerEvents", "sandbox", "scrolling", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/SlideRenderer.js"], "sourcesContent": ["import React, { useEffect, useRef, useCallback } from 'react';\n\nconst DESIGNED_WIDTH = 1280;\nconst DESIGNED_HEIGHT = 720;\n\n// prepareSlideHtmlForIframe 函数 (请确保这里是你之前确认过的好版本，\n// 即能够先移除所有旧的 viewport meta 标签，再添加我们需要的那个)\nconst prepareSlideHtmlForIframe = (slideHtmlContent) => {\n  if (!slideHtmlContent) {\n    return `<!DOCTYPE html>\n      <html>\n        <head>\n          <meta name=\"viewport\" content=\"width=${DESIGNED_WIDTH}, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">\n          <style>\n            html, body {\n              width: ${DESIGNED_WIDTH}px !important;\n              height: ${DESIGNED_HEIGHT}px !important;\n              margin: 0 !important;\n              padding: 0 !important;\n              overflow: hidden !important;\n              box-sizing: border-box !important;\n            }\n            body {\n              display: flex;\n              justify-content: center;\n              align-items: center;\n              background: #eee;\n              font-family: sans-serif;\n              color: #888;\n              position: relative !important;\n            }\n          </style>\n        </head>\n        <body>\n          <div>(No Content)</div>\n        </body>\n      </html>`;\n  }\n\n  // 1. 稳定地移除已存在的 viewport meta 标签\n  let processedHtml = slideHtmlContent.replace(/<meta[^>]*name=[\"']viewport[\"'][^>]*>/gi, '');\n\n  const viewportMeta = `<meta name=\"viewport\" content=\"width=${DESIGNED_WIDTH}, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">`;\n  const baseStyles = `\n    <style>\n      html, body {\n        width: ${DESIGNED_WIDTH}px !important;\n        height: ${DESIGNED_HEIGHT}px !important;\n        margin: 0 !important;\n        padding: 0 !important;\n        overflow: hidden !important; /* 禁止内部滚动 */\n        box-sizing: border-box !important; /* 确保盒模型一致性 */\n      }\n      body {\n        position: relative !important; /* 确保 body 是定位上下文 */\n        display: block !important; /* 覆盖LLM可能在body上设置的flex/grid */\n        justify-content: normal !important;\n        align-items: normal !important;\n        transform-origin: 0 0 !important; /* 确保缩放基点正确 */\n        transform: scale(1) !important; /* body自身不应被其内部的transform再次缩放 */\n        box-sizing: border-box !important; /* 确保盒模型一致性 */\n      }\n      /* 确保幻灯片内常见的顶层容器也使用相同的盒模型 */\n      .slide-container, .slide-content, .slide-wrapper, .content-wrapper, main, section {\n        box-sizing: border-box !important;\n      }\n      /* 确保所有元素都使用相同的盒模型 */\n      * {\n        box-sizing: inherit !important;\n      }\n    </style>\n  `;\n\n  // 2. 在处理后的HTML中注入我们的meta和styles\n  if (processedHtml.trim().toLowerCase().startsWith('<!doctype html') || \n      processedHtml.trim().toLowerCase().startsWith('<html')) {\n    // 处理完整的HTML文档\n    if (processedHtml.includes('</head>')) {\n      processedHtml = processedHtml.replace('</head>', `${viewportMeta}${baseStyles}</head>`);\n    } else if (processedHtml.includes('<head>')) {\n      processedHtml = processedHtml.replace('<head>', `<head>${viewportMeta}${baseStyles}`);\n    } else {\n      // 有<html>标签但没有<head>标签的情况\n      processedHtml = processedHtml.replace(/<html[^>]*>/, match => `${match}<head>${viewportMeta}${baseStyles}</head>`);\n    }\n  } else {\n    // 如果是HTML片段，则构建完整结构\n    processedHtml = `\n      <!DOCTYPE html>\n      <html>\n        <head>\n          ${viewportMeta}\n          ${baseStyles}\n        </head>\n        <body>\n          ${processedHtml}\n        </body>\n      </html>\n    `;\n  }\n  return processedHtml;\n};\n\n\nconst SlideRenderer = ({\n  slideId,\n  slideFullHtml,\n  isAppEditingMode = false,\n  isThumbnail = false,\n  // isFullScreen = false, // 此组件主要用于预览和缩略图，全屏由FullScreenPlayer处理\n  onClick = null,\n}) => {\n  const rendererRootRef = useRef(null); // SlideRenderer的根DOM元素\n  const iframeRef = useRef(null);\n\n  // injectEnvironmentScript 函数 (保持和你原来版本一致，确保 SLIDE_IS_APP_EDITING_MODE 等变量注入)\n  const injectEnvironmentScript = useCallback((iframeDocument) => {\n    const oldScript = iframeDocument.getElementById('slide-environment-script');\n    if (oldScript) oldScript.remove();\n\n    const script = iframeDocument.createElement('script');\n    script.id = 'slide-environment-script';\n    script.textContent = `\n      window.SLIDE_IS_APP_EDITING_MODE = ${isAppEditingMode};\n      window.SLIDE_IS_THUMBNAIL = ${isThumbnail};\n      window.SLIDE_ID = \"${slideId}\"; // 确保slideId可用于postMessage\n\n      document.addEventListener('DOMContentLoaded', function() {\n        const html = document.documentElement;\n        const body = document.body;\n        const designedWidth = ${DESIGNED_WIDTH};\n        const designedHeight = ${DESIGNED_HEIGHT};\n\n        // 再次确保 html/body 尺寸正确 (与 prepareSlideHtmlForIframe 中的有点重复，但安全起见)\n        if (html) {\n          html.style.setProperty('overflow', 'hidden', 'important');\n          html.style.setProperty('width', designedWidth + 'px', 'important');\n          html.style.setProperty('height', designedHeight + 'px', 'important');\n        }\n\n        if (body) {\n          body.style.setProperty('overflow', 'hidden', 'important');\n          body.style.setProperty('width', designedWidth + 'px', 'important');\n          body.style.setProperty('height', designedHeight + 'px', 'important');\n          body.style.setProperty('position', 'relative', 'important');\n          body.style.setProperty('display', 'block', 'important');\n          body.style.setProperty('justify-content', 'normal', 'important');\n          body.style.setProperty('align-items', 'normal', 'important');\n          body.style.setProperty('transform-origin', '0 0', 'important'); \n          body.style.setProperty('transform', 'scale(1)', 'important'); \n        }\n        \n        // 【新增】为 iframe 的 document 添加键盘事件监听器\n        document.addEventListener('keydown', function(e) {\n          // 定义需要转发的按键\n          const relevantKeys = [\n            'ArrowRight', 'ArrowLeft', 'ArrowUp', 'ArrowDown',\n            'PageDown', 'PageUp', ' ', 'Escape', 'f'\n          ];\n\n          // 如果按下的键是我们关心的，就通过 postMessage 发送给父窗口\n          if (relevantKeys.includes(e.key)) {\n            // 阻止默认行为，例如按空格键滚动页面\n            e.preventDefault();\n            window.parent.postMessage({\n              type: 'iframe_keydown', // 自定义消息类型\n              key: e.key,\n              slideId: window.SLIDE_ID\n            }, '*');\n          }\n        });\n        \n        // Chart.js 图表重绘逻辑\n        if (window.Chart && typeof window.Chart.instances === 'object') {\n          setTimeout(() => {\n            Object.values(window.Chart.instances).forEach(chartInstance => {\n              if (chartInstance && typeof chartInstance.resize === 'function') {\n                try { chartInstance.resize(); chartInstance.update('none'); } catch(e) { console.warn('重绘图表时出错:', e); }\n              }\n            });\n          }, 150); // 延迟以确保DOM和样式已应用\n        }\n\n        // +++++++++++++++ 新增：全局点击监听器 +++++++++++++++\n        if (window.SLIDE_IS_APP_EDITING_MODE && !window.SLIDE_IS_THUMBNAIL) {\n          document.addEventListener('click', function(event) {\n            if (!window.SLIDE_IS_APP_EDITING_MODE) return;\n\n            const editableElement = event.target.closest('[data-editable-id]');\n            if (editableElement) {\n                event.preventDefault();\n                event.stopPropagation();\n\n                const elementId = editableElement.dataset.editableId;\n                // 使用相对于页面的坐标，而不是视口坐标，以避免滚动条影响\n                const clickX = event.pageX;\n                const clickY = event.pageY;\n                \n              window.parent.postMessage({\n                    type: 'element_clicked',\n                payload: {\n                        slideId: window.SLIDE_ID,\n                        elementId: elementId,\n                        // 将相对于iframe的点击坐标发送出去\n                        clickPosition: { x: clickX, y: clickY } \n                }\n                }, '*');\n            }\n          }, true); // 使用捕获阶段以确保能截获所有点击\n        }\n        // +++++++++++++++++++++++++++++++++++++++++++++++++++\n      });\n    `;\n    if (iframeDocument && iframeDocument.head) {\n        iframeDocument.head.appendChild(script);\n    } else if (iframeDocument && iframeDocument.body) { \n        iframeDocument.body.insertAdjacentElement('afterbegin', script);\n    } else if (iframeDocument) {\n        iframeDocument.appendChild(script);\n    }\n  }, [isAppEditingMode, isThumbnail, slideId]); // 依赖项\n\n  // 当幻灯片HTML内容变化时，或相关状态变化时，重新加载iframe内容\n  useEffect(() => {\n    const iframe = iframeRef.current;\n    let handleLoad = null;\n\n    if (iframe) {\n      const preparedHtml = prepareSlideHtmlForIframe(slideFullHtml);\n      handleLoad = () => {\n        if (iframe.contentWindow && iframe.contentWindow.document) {\n          injectEnvironmentScript(iframe.contentWindow.document);\n        }\n      };\n      iframe.addEventListener('load', handleLoad);\n      iframe.srcdoc = preparedHtml; // 使用 srcdoc 赋值\n    }\n    return () => {\n      if (iframe && handleLoad) {\n        iframe.removeEventListener('load', handleLoad);\n      }\n    };\n  }, [slideFullHtml, injectEnvironmentScript, slideId]); // 依赖项\n\n  // External scaling and positioning of scalingWrapperRef\n  useEffect(() => {\n    const rootElement = rendererRootRef.current;\n    const iframe = iframeRef.current;\n\n    if (!rootElement || !iframe) return;\n\n    const updateIframeScaleAndPosition = () => {\n      // 确保iframe默认可见状态，除非特定条件下需要隐藏\n      iframe.style.visibility = 'visible';\n      \n      let availableWidth = rootElement.offsetWidth;\n      let availableHeight = rootElement.offsetHeight;\n\n      // 如果容器尺寸为零，但有父元素，尝试从父元素获取尺寸\n      if ((availableWidth <= 0 || availableHeight <= 0) && rootElement.parentElement) {\n        const parentWidth = rootElement.parentElement.offsetWidth;\n        const parentHeight = rootElement.parentElement.offsetHeight;\n        \n        if (parentWidth > 0 && parentHeight > 0) {\n          // 使用父元素尺寸，确保16:9比例\n          availableWidth = parentWidth;\n          availableHeight = isThumbnail ? parentHeight : (parentWidth * 9) / 16;\n          \n          // 为rootElement设置明确的尺寸，使其不依赖内容尺寸\n          rootElement.style.width = `${availableWidth}px`;\n          rootElement.style.height = `${availableHeight}px`;\n        }\n      }\n\n      // 如果经过尝试后尺寸仍然为零，应用最小默认值而不是隐藏\n      if (availableWidth <= 0 || availableHeight <= 0) {\n        // 为避免完全隐藏，设置一个最小默认尺寸\n        availableWidth = availableWidth || 320; // 至少320px宽\n        availableHeight = availableHeight || 180; // 至少180px高 (16:9)\n        \n        // 为rootElement设置明确的最小尺寸\n        rootElement.style.minWidth = `${availableWidth}px`;\n        rootElement.style.minHeight = `${availableHeight}px`;\n      }\n\n      // 计算缩放比例，确保内容能完整显示并保持16:9的宽高比\n      const scaleX = availableWidth / DESIGNED_WIDTH;\n      const scaleY = availableHeight / DESIGNED_HEIGHT;\n      const scale = Math.min(scaleX, scaleY); // 取较小的缩放比例以适应容器\n      \n      // 确保缩放比例不为零或非常小\n      const safeScale = Math.max(scale, 0.01); // 设置一个最小缩放值\n\n      // 设置iframe的固定设计尺寸和基于左上角的缩放\n      iframe.style.width = `${DESIGNED_WIDTH}px`;\n      iframe.style.height = `${DESIGNED_HEIGHT}px`;\n      iframe.style.transformOrigin = '0 0';\n      \n      // 对缩略图使用不同的缩放方法\n      if (isThumbnail) {\n        // 对缩略图保持使用transform缩放\n        iframe.style.transform = `scale(${safeScale})`;\n      } else {\n        // 标准预览使用transform缩放\n        iframe.style.transform = `scale(${safeScale})`;\n      }\n\n      // 计算缩放后iframe的实际尺寸\n      const scaledWidth = DESIGNED_WIDTH * safeScale;\n      const scaledHeight = DESIGNED_HEIGHT * safeScale;\n\n      // 计算偏移量以在父容器(rootElement)中居中iframe\n      const offsetX = (availableWidth - scaledWidth) / 2;\n      const offsetY = (availableHeight - scaledHeight) / 2;\n\n      // 应用绝对定位和偏移量\n      iframe.style.position = 'absolute';\n      iframe.style.left = `${offsetX}px`;\n      iframe.style.top = `${offsetY}px`;\n    };\n\n    // 初始设置iframe大小并位置\n    updateIframeScaleAndPosition();\n    \n    // 确保在初始渲染后iframe正确显示\n    setTimeout(updateIframeScaleAndPosition, 50);\n\n    // 使用 ResizeObserver 监听根元素尺寸变化，并重新计算\n    const resizeObserver = new ResizeObserver(() => {\n      // 设置一个短暂的延迟，以确保读取到正确的尺寸\n      setTimeout(updateIframeScaleAndPosition, 10);\n    });\n    resizeObserver.observe(rootElement);\n\n    return () => {\n      resizeObserver.unobserve(rootElement); // 组件卸载时停止监听\n    };\n  }, [slideId, isThumbnail]); // 依赖项，当slideId或isThumbnail改变时重新执行\n\n  // 点击事件处理\n  const handleIframeWrapperClick = useCallback((e) => {\n    if (onClick) {\n      onClick(e);\n    }\n  }, [onClick]);\n\n  // 如果没有HTML内容且不是缩略图，显示占位符\n  if (!slideFullHtml && !isThumbnail) {\n    return (\n      <div\n        className={`w-full flex items-center justify-center text-gray-400 bg-gray-100 rounded-lg`}\n        style={{ aspectRatio: '16/9' }} // 保持16:9的宽高比\n        onClick={onClick} // 允许点击占位符\n      >\n        此幻灯片暂无内容\n      </div>\n    );\n  }\n\n  return (\n    <div\n      ref={rendererRootRef}\n      onClick={handleIframeWrapperClick} // 在根元素上处理点击\n      className={`slide-renderer-root ${isThumbnail ? 'thumbnail-root' : 'preview-root'}`}\n      style={{\n        width: '100%', // 根元素填充其父容器\n        height: '100%', // 根元素填充其父容器\n        minHeight: isThumbnail ? '120px' : '180px', // 确保最小高度\n        aspectRatio: '16/9', // 确保始终保持16:9的宽高比，即使父容器尺寸为零\n        overflow: 'hidden', // 裁剪超出部分的iframe\n        position: 'relative', // 作为内部iframe绝对定位的基准\n        backgroundColor: 'transparent', // 调试时可以改为半透明色以查看边界\n      }}\n    >\n      <iframe\n        ref={iframeRef}\n        key={`${slideId}-${isThumbnail ? 'thumb' : 'preview'}`} // 更明确的key\n        title={`Slide ${slideId}`}\n        style={{\n          width: `${DESIGNED_WIDTH}px`, // 固定宽度为设计尺寸\n          height: `${DESIGNED_HEIGHT}px`, // 固定高度为设计尺寸\n          border: 'none',\n          transformOrigin: '0 0', // 从左上角开始缩放\n          // 根据是否处于编辑模式和是否为缩略图，决定iframe是否响应鼠标事件\n          pointerEvents: (isAppEditingMode && !isThumbnail) ? 'auto' : 'none',\n          position: 'absolute', // 确保left/top定位生效\n          visibility: 'visible', // 默认可见，由JS计算时按需隐藏\n        }}\n        sandbox=\"allow-scripts allow-same-origin\" // 允许脚本和同源，以便内部JS（如Chart.js）能运行\n        scrolling=\"no\" // 明确禁止iframe内部滚动\n      />\n    </div>\n  );\n};\n\nexport default SlideRenderer;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,cAAc,GAAG,IAAI;AAC3B,MAAMC,eAAe,GAAG,GAAG;;AAE3B;AACA;AACA,MAAMC,yBAAyB,GAAIC,gBAAgB,IAAK;EACtD,IAAI,CAACA,gBAAgB,EAAE;IACrB,OAAO;AACX;AACA;AACA,iDAAiDH,cAAc;AAC/D;AACA;AACA,uBAAuBA,cAAc;AACrC,wBAAwBC,eAAe;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;EACZ;;EAEA;EACA,IAAIG,aAAa,GAAGD,gBAAgB,CAACE,OAAO,CAAC,yCAAyC,EAAE,EAAE,CAAC;EAE3F,MAAMC,YAAY,GAAG,wCAAwCN,cAAc,4DAA4D;EACvI,MAAMO,UAAU,GAAG;AACrB;AACA;AACA,iBAAiBP,cAAc;AAC/B,kBAAkBC,eAAe;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;EAED;EACA,IAAIG,aAAa,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,gBAAgB,CAAC,IAC/DN,aAAa,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,OAAO,CAAC,EAAE;IAC1D;IACA,IAAIN,aAAa,CAACO,QAAQ,CAAC,SAAS,CAAC,EAAE;MACrCP,aAAa,GAAGA,aAAa,CAACC,OAAO,CAAC,SAAS,EAAE,GAAGC,YAAY,GAAGC,UAAU,SAAS,CAAC;IACzF,CAAC,MAAM,IAAIH,aAAa,CAACO,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC3CP,aAAa,GAAGA,aAAa,CAACC,OAAO,CAAC,QAAQ,EAAE,SAASC,YAAY,GAAGC,UAAU,EAAE,CAAC;IACvF,CAAC,MAAM;MACL;MACAH,aAAa,GAAGA,aAAa,CAACC,OAAO,CAAC,aAAa,EAAEO,KAAK,IAAI,GAAGA,KAAK,SAASN,YAAY,GAAGC,UAAU,SAAS,CAAC;IACpH;EACF,CAAC,MAAM;IACL;IACAH,aAAa,GAAG;AACpB;AACA;AACA;AACA,YAAYE,YAAY;AACxB,YAAYC,UAAU;AACtB;AACA;AACA,YAAYH,aAAa;AACzB;AACA;AACA,KAAK;EACH;EACA,OAAOA,aAAa;AACtB,CAAC;AAGD,MAAMS,aAAa,GAAGA,CAAC;EACrBC,OAAO;EACPC,aAAa;EACbC,gBAAgB,GAAG,KAAK;EACxBC,WAAW,GAAG,KAAK;EACnB;EACAC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,eAAe,GAAGxB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EACtC,MAAMyB,SAAS,GAAGzB,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM0B,uBAAuB,GAAGzB,WAAW,CAAE0B,cAAc,IAAK;IAC9D,MAAMC,SAAS,GAAGD,cAAc,CAACE,cAAc,CAAC,0BAA0B,CAAC;IAC3E,IAAID,SAAS,EAAEA,SAAS,CAACE,MAAM,CAAC,CAAC;IAEjC,MAAMC,MAAM,GAAGJ,cAAc,CAACK,aAAa,CAAC,QAAQ,CAAC;IACrDD,MAAM,CAACE,EAAE,GAAG,0BAA0B;IACtCF,MAAM,CAACG,WAAW,GAAG;AACzB,2CAA2Cd,gBAAgB;AAC3D,oCAAoCC,WAAW;AAC/C,2BAA2BH,OAAO;AAClC;AACA;AACA;AACA;AACA,gCAAgCd,cAAc;AAC9C,iCAAiCC,eAAe;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IACD,IAAIsB,cAAc,IAAIA,cAAc,CAACQ,IAAI,EAAE;MACvCR,cAAc,CAACQ,IAAI,CAACC,WAAW,CAACL,MAAM,CAAC;IAC3C,CAAC,MAAM,IAAIJ,cAAc,IAAIA,cAAc,CAACU,IAAI,EAAE;MAC9CV,cAAc,CAACU,IAAI,CAACC,qBAAqB,CAAC,YAAY,EAAEP,MAAM,CAAC;IACnE,CAAC,MAAM,IAAIJ,cAAc,EAAE;MACvBA,cAAc,CAACS,WAAW,CAACL,MAAM,CAAC;IACtC;EACF,CAAC,EAAE,CAACX,gBAAgB,EAAEC,WAAW,EAAEH,OAAO,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACAnB,SAAS,CAAC,MAAM;IACd,MAAMwC,MAAM,GAAGd,SAAS,CAACe,OAAO;IAChC,IAAIC,UAAU,GAAG,IAAI;IAErB,IAAIF,MAAM,EAAE;MACV,MAAMG,YAAY,GAAGpC,yBAAyB,CAACa,aAAa,CAAC;MAC7DsB,UAAU,GAAGA,CAAA,KAAM;QACjB,IAAIF,MAAM,CAACI,aAAa,IAAIJ,MAAM,CAACI,aAAa,CAACC,QAAQ,EAAE;UACzDlB,uBAAuB,CAACa,MAAM,CAACI,aAAa,CAACC,QAAQ,CAAC;QACxD;MACF,CAAC;MACDL,MAAM,CAACM,gBAAgB,CAAC,MAAM,EAAEJ,UAAU,CAAC;MAC3CF,MAAM,CAACO,MAAM,GAAGJ,YAAY,CAAC,CAAC;IAChC;IACA,OAAO,MAAM;MACX,IAAIH,MAAM,IAAIE,UAAU,EAAE;QACxBF,MAAM,CAACQ,mBAAmB,CAAC,MAAM,EAAEN,UAAU,CAAC;MAChD;IACF,CAAC;EACH,CAAC,EAAE,CAACtB,aAAa,EAAEO,uBAAuB,EAAER,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEvD;EACAnB,SAAS,CAAC,MAAM;IACd,MAAMiD,WAAW,GAAGxB,eAAe,CAACgB,OAAO;IAC3C,MAAMD,MAAM,GAAGd,SAAS,CAACe,OAAO;IAEhC,IAAI,CAACQ,WAAW,IAAI,CAACT,MAAM,EAAE;IAE7B,MAAMU,4BAA4B,GAAGA,CAAA,KAAM;MACzC;MACAV,MAAM,CAACW,KAAK,CAACC,UAAU,GAAG,SAAS;MAEnC,IAAIC,cAAc,GAAGJ,WAAW,CAACK,WAAW;MAC5C,IAAIC,eAAe,GAAGN,WAAW,CAACO,YAAY;;MAE9C;MACA,IAAI,CAACH,cAAc,IAAI,CAAC,IAAIE,eAAe,IAAI,CAAC,KAAKN,WAAW,CAACQ,aAAa,EAAE;QAC9E,MAAMC,WAAW,GAAGT,WAAW,CAACQ,aAAa,CAACH,WAAW;QACzD,MAAMK,YAAY,GAAGV,WAAW,CAACQ,aAAa,CAACD,YAAY;QAE3D,IAAIE,WAAW,GAAG,CAAC,IAAIC,YAAY,GAAG,CAAC,EAAE;UACvC;UACAN,cAAc,GAAGK,WAAW;UAC5BH,eAAe,GAAGjC,WAAW,GAAGqC,YAAY,GAAID,WAAW,GAAG,CAAC,GAAI,EAAE;;UAErE;UACAT,WAAW,CAACE,KAAK,CAACS,KAAK,GAAG,GAAGP,cAAc,IAAI;UAC/CJ,WAAW,CAACE,KAAK,CAACU,MAAM,GAAG,GAAGN,eAAe,IAAI;QACnD;MACF;;MAEA;MACA,IAAIF,cAAc,IAAI,CAAC,IAAIE,eAAe,IAAI,CAAC,EAAE;QAC/C;QACAF,cAAc,GAAGA,cAAc,IAAI,GAAG,CAAC,CAAC;QACxCE,eAAe,GAAGA,eAAe,IAAI,GAAG,CAAC,CAAC;;QAE1C;QACAN,WAAW,CAACE,KAAK,CAACW,QAAQ,GAAG,GAAGT,cAAc,IAAI;QAClDJ,WAAW,CAACE,KAAK,CAACY,SAAS,GAAG,GAAGR,eAAe,IAAI;MACtD;;MAEA;MACA,MAAMS,MAAM,GAAGX,cAAc,GAAGhD,cAAc;MAC9C,MAAM4D,MAAM,GAAGV,eAAe,GAAGjD,eAAe;MAChD,MAAM4D,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACJ,MAAM,EAAEC,MAAM,CAAC,CAAC,CAAC;;MAExC;MACA,MAAMI,SAAS,GAAGF,IAAI,CAACG,GAAG,CAACJ,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEzC;MACA1B,MAAM,CAACW,KAAK,CAACS,KAAK,GAAG,GAAGvD,cAAc,IAAI;MAC1CmC,MAAM,CAACW,KAAK,CAACU,MAAM,GAAG,GAAGvD,eAAe,IAAI;MAC5CkC,MAAM,CAACW,KAAK,CAACoB,eAAe,GAAG,KAAK;;MAEpC;MACA,IAAIjD,WAAW,EAAE;QACf;QACAkB,MAAM,CAACW,KAAK,CAACqB,SAAS,GAAG,SAASH,SAAS,GAAG;MAChD,CAAC,MAAM;QACL;QACA7B,MAAM,CAACW,KAAK,CAACqB,SAAS,GAAG,SAASH,SAAS,GAAG;MAChD;;MAEA;MACA,MAAMI,WAAW,GAAGpE,cAAc,GAAGgE,SAAS;MAC9C,MAAMK,YAAY,GAAGpE,eAAe,GAAG+D,SAAS;;MAEhD;MACA,MAAMM,OAAO,GAAG,CAACtB,cAAc,GAAGoB,WAAW,IAAI,CAAC;MAClD,MAAMG,OAAO,GAAG,CAACrB,eAAe,GAAGmB,YAAY,IAAI,CAAC;;MAEpD;MACAlC,MAAM,CAACW,KAAK,CAAC0B,QAAQ,GAAG,UAAU;MAClCrC,MAAM,CAACW,KAAK,CAAC2B,IAAI,GAAG,GAAGH,OAAO,IAAI;MAClCnC,MAAM,CAACW,KAAK,CAAC4B,GAAG,GAAG,GAAGH,OAAO,IAAI;IACnC,CAAC;;IAED;IACA1B,4BAA4B,CAAC,CAAC;;IAE9B;IACA8B,UAAU,CAAC9B,4BAA4B,EAAE,EAAE,CAAC;;IAE5C;IACA,MAAM+B,cAAc,GAAG,IAAIC,cAAc,CAAC,MAAM;MAC9C;MACAF,UAAU,CAAC9B,4BAA4B,EAAE,EAAE,CAAC;IAC9C,CAAC,CAAC;IACF+B,cAAc,CAACE,OAAO,CAAClC,WAAW,CAAC;IAEnC,OAAO,MAAM;MACXgC,cAAc,CAACG,SAAS,CAACnC,WAAW,CAAC,CAAC,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,CAAC9B,OAAO,EAAEG,WAAW,CAAC,CAAC,CAAC,CAAC;;EAE5B;EACA,MAAM+D,wBAAwB,GAAGnF,WAAW,CAAEoF,CAAC,IAAK;IAClD,IAAI/D,OAAO,EAAE;MACXA,OAAO,CAAC+D,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAAC/D,OAAO,CAAC,CAAC;;EAEb;EACA,IAAI,CAACH,aAAa,IAAI,CAACE,WAAW,EAAE;IAClC,oBACElB,OAAA;MACEmF,SAAS,EAAE,8EAA+E;MAC1FpC,KAAK,EAAE;QAAEqC,WAAW,EAAE;MAAO,CAAE,CAAC;MAAA;MAChCjE,OAAO,EAAEA,OAAQ,CAAC;MAAA;MAAAkE,QAAA,EACnB;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACEzF,OAAA;IACE0F,GAAG,EAAErE,eAAgB;IACrBF,OAAO,EAAE8D,wBAAyB,CAAC;IAAA;IACnCE,SAAS,EAAE,uBAAuBjE,WAAW,GAAG,gBAAgB,GAAG,cAAc,EAAG;IACpF6B,KAAK,EAAE;MACLS,KAAK,EAAE,MAAM;MAAE;MACfC,MAAM,EAAE,MAAM;MAAE;MAChBE,SAAS,EAAEzC,WAAW,GAAG,OAAO,GAAG,OAAO;MAAE;MAC5CkE,WAAW,EAAE,MAAM;MAAE;MACrBO,QAAQ,EAAE,QAAQ;MAAE;MACpBlB,QAAQ,EAAE,UAAU;MAAE;MACtBmB,eAAe,EAAE,aAAa,CAAE;IAClC,CAAE;IAAAP,QAAA,eAEFrF,OAAA;MACE0F,GAAG,EAAEpE,SAAU;MACyC;MACxDuE,KAAK,EAAE,SAAS9E,OAAO,EAAG;MAC1BgC,KAAK,EAAE;QACLS,KAAK,EAAE,GAAGvD,cAAc,IAAI;QAAE;QAC9BwD,MAAM,EAAE,GAAGvD,eAAe,IAAI;QAAE;QAChC4F,MAAM,EAAE,MAAM;QACd3B,eAAe,EAAE,KAAK;QAAE;QACxB;QACA4B,aAAa,EAAG9E,gBAAgB,IAAI,CAACC,WAAW,GAAI,MAAM,GAAG,MAAM;QACnEuD,QAAQ,EAAE,UAAU;QAAE;QACtBzB,UAAU,EAAE,SAAS,CAAE;MACzB,CAAE;MACFgD,OAAO,EAAC,iCAAiC,CAAC;MAAA;MAC1CC,SAAS,EAAC,IAAI,CAAC;IAAA,GAbV,GAAGlF,OAAO,IAAIG,WAAW,GAAG,OAAO,GAAG,SAAS,EAAE;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAcvD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACrE,EAAA,CAjSIN,aAAa;AAAAoF,EAAA,GAAbpF,aAAa;AAmSnB,eAAeA,aAAa;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}